DESCRIPTIF DÉTAILLÉ DES MÉTHODES - CALCUL CONFIANCE
================================================================================

Ce fichier contient la description détaillée des méthodes de calcul de confiance
et d'incertitude du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : CALCUL CONFIANCE
Méthodes dédiées au calcul de confiance, d'incertitude et de recommandations
basées sur l'analyse des patterns et séquences consécutives.

TOTAL : 23 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES DE CALCUL CONFIANCE
================================================================================

1. __init___1.txt (ConsecutiveConfidenceCalculator.__init__ - DOUBLON - Constructeur du calculateur de confiance)
   - Lignes 1423-1427 dans hbp.py (5 lignes)
   - FONCTION : Initialise une instance de ConsecutiveConfidenceCalculator pour le calcul de confiance basé sur les patterns consécutifs
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION STATISTIQUES :** Crée un dictionnaire par défaut pour stocker les statistiques de patterns
     * **STRUCTURE PATTERN_STATS :** Chaque pattern contient total, success, consecutive_lengths et max_consecutive
     * **HISTORIQUE RÉCENT :** Initialise les listes pour les recommandations et résultats récents
     * **CONFIGURATION LIMITE :** Définit la taille maximale de l'historique récent (défaut 50)
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Prépare le calculateur pour analyser les patterns de séquences consécutives et calculer la confiance des recommandations

2. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence - Calcul de confiance avancé)
   - Lignes 1486-1579 dans hbp.py (94 lignes)
   - FONCTION : Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle en analysant les patterns et facteurs multiples
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features_vector (List[float]) - Vecteur de features pour la manche actuelle
     * current_round (int) - Numéro de la manche actuelle
     * config - Configuration du prédicteur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PLAGE :** Vérifie si la manche est dans la plage cible (target_round_min à target_round_max)
     * **EXTRACTION PATTERN :** Extrait la clé de pattern à partir du vecteur de features
     * **STATISTIQUES PATTERN :** Récupère les statistiques historiques pour ce pattern spécifique
     * **CALCUL CONFIANCE BASE :** Combine taux de succès (70%) et longueur moyenne consécutive (30%)
     * **BONUS COURBE CLOCHE :** Applique un bonus pour les manches au milieu de la plage cible
     * **FACTEURS MULTIPLICATIFS :** Calcule sequence_bonus, late_game_factor, occurrence_factor, consecutive_factor
     * **AJUSTEMENT FINAL :** Multiplie la confiance par tous les facteurs et limite entre 0 et 1
   - RETOUR : Dict - Dictionnaire contenant confidence, expected_consecutive, success_rate, et tous les facteurs calculés
   - UTILITÉ : Fournit une évaluation sophistiquée de la confiance pour optimiser les recommandations de mise

3. _analyze_sequence_context.txt (HybridBaccaratPredictor._analyze_sequence_context - Analyse contextuelle de séquence)
   - Lignes 13941-13979 dans hbp.py (39 lignes)
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles en fonction de la volatilité et des patterns
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LONGUEUR :** Vérifie qu'il y a au moins 10 éléments pour une analyse fiable
     * **CALCUL VOLATILITÉ :** Analyse les alternances dans les 10 derniers coups pour mesurer l'instabilité
     * **DÉTECTION STREAKS :** Identifie la longueur du streak actuel en remontant la séquence
     * **NORMALISATION STREAK :** Normalise le facteur de streak entre 0 et 1 (max 10 coups)
     * **COMBINAISON FACTEURS :** Combine volatilité (70%) et facteur de streak (30%)
     * **INTERPRÉTATION :** Proche de 0 = séquence stable, proche de 1 = séquence volatile
   - RETOUR : float - Facteur contextuel entre 0 et 1 indiquant la pertinence du modèle de session vs global
   - UTILITÉ : Permet d'adapter dynamiquement les poids des modèles selon le contexte de jeu actuel

4. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty - Calcul d'incertitude par variance)
   - Lignes 4386-4547 dans hbp.py (162 lignes)
   - FONCTION : Calcule un score d'incertitude basé sur la variance des prédictions des estimateurs du BaggingClassifier LGBM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * features (Optional[List[float]]) - Liste des features d'entrée
     * predicted_class (Optional[int]) - Classe prédite (0=PLAYER, 1=BANKER), optionnel
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURES :** Vérifie que features n'est pas None et a la bonne longueur
     * **INITIALISATION MODÈLE :** Tente d'initialiser lgbm_uncertainty si nécessaire avec protection récursion
     * **VÉRIFICATIONS MODÈLE :** Contrôle que le modèle et scaler sont initialisés et fittés
     * **NORMALISATION :** Applique feature_scaler.transform avec gestion d'erreurs complète
     * **PRÉDICTIONS MULTIPLES :** Collecte les prédictions de chaque estimateur du BaggingClassifier
     * **GESTION CLASSES :** Trouve l'index de la classe Banker avec cache optimisé
     * **CALCUL VARIANCE :** Calcule la variance des probabilités Banker entre estimateurs
     * **NORMALISATION SCORE :** Applique facteur de normalisation réduit et clipping [0,1]
   - RETOUR : float - Score d'incertitude entre 0 et 1 (0.5 par défaut en cas d'erreur)
   - UTILITÉ : Fournit une mesure d'incertitude épistémique pour évaluer la fiabilité des prédictions

5. calculate_bayesian_weights.txt (HybridBaccaratPredictor.calculate_bayesian_weights - Calcul poids bayésiens)
   - Lignes 8589-8622 dans hbp.py (34 lignes)
   - FONCTION : Calcule les poids bayésiens des modèles en fonction de leur confiance selon P(M|D) = P(D|M) * P(M) / P(D)
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_weights (Dict[str, float]) - Poids actuels des modèles (priors)
     * method_confidences (Dict[str, float]) - Confiance calculée pour chaque modèle (likelihood)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL PRODUIT :** Multiplie poids actuels P(M) par confidences P(D|M) pour chaque méthode
     * **NORMALISATION BAYÉSIENNE :** Divise par somme totale pour obtenir probabilités postérieures P(M|D)
     * **GESTION EPSILON :** Utilise epsilon configuré pour éviter divisions par zéro
     * **FALLBACK SÉCURISÉ :** Retourne poids originaux si somme totale trop petite
   - RETOUR : Dict[str, float] - Poids bayésiens ajustés normalisés
   - UTILITÉ : Implémente mise à jour bayésienne des poids pour adaptation dynamique basée sur performance

6. update_weights.txt (HybridBaccaratPredictor.update_weights - Mise à jour poids méthodes)
   - FONCTION : Met à jour les poids des méthodes basé sur performance prédiction vs résultat réel
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * actual_result (str) - Résultat réel observé ('player' ou 'banker')
     * predicted_result (str) - Résultat prédit par le système
     * method_predictions (Dict[str, Dict]) - Prédictions individuelles de chaque méthode
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉES :** Vérifie que les résultats sont valides et les prédictions disponibles
     * **CALCUL PERFORMANCE :** Évalue la précision de chaque méthode pour cette prédiction
     * **AJUSTEMENT POIDS :** Augmente poids des méthodes correctes, diminue celles incorrectes
     * **NORMALISATION :** Assure que la somme des poids reste égale à 1.0
     * **LISSAGE TEMPOREL :** Applique facteur d'apprentissage pour éviter changements brusques
   - RETOUR : None - Met à jour directement les poids internes
   - UTILITÉ : Ajuste dynamiquement l'importance relative des différentes méthodes de prédiction

7. calculate_model_confidence.txt (HybridBaccaratPredictor.calculate_model_confidence - Confiance modèle)
   - Lignes 8426-8489 dans hbp.py (64 lignes)
   - FONCTION : Calcule la confiance d'un modèle en utilisant plusieurs méthodes avancées avec facteurs contextuels et performance historique
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prob_banker (float) - Probabilité prédite pour 'banker' (entre 0 et 1)
     * method (str) - Nom du modèle ('markov', 'lgbm', 'lstm')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIANCE BASE :** Calcule `base_confidence = np.clip(confidence_normalization_factor * abs(prob_banker - 0.5), confidence_min_clip, confidence_max_clip)` avec facteur normalisation (défaut: 2.0)
     * **FACTEUR HISTORIQUE :** Récupère `historical_factor = self.method_performance.get(method, {}).get('accuracy', 0.5)` pour ajuster selon performance passée
     * **FACTEUR CONTEXTUEL :** Initialise `context_factor = 1.0` puis ajuste selon longueur séquence et spécificités méthode
     * **AJUSTEMENT LSTM :** Si `method == 'lstm'` et `seq_length > lstm_long_sequence_threshold`, multiplie par `min(lstm_long_sequence_factor, 1.0 + (seq_length - threshold) / 100)`
     * **AJUSTEMENT MARKOV :** Si `method == 'markov'` et `seq_length < markov_short_sequence_threshold`, multiplie par `max(markov_short_sequence_factor, seq_length / threshold)`
     * **COMBINAISON FINALE :** Calcule `final_confidence = base_confidence * historical_factor * context_factor`
     * **VALIDATION BORNES :** Retourne `np.clip(final_confidence, 0.0, 1.0)` pour assurer résultat dans [0,1]
     * **PARAMÈTRES CONFIG :** Utilise `confidence_normalization_factor=2.0`, `confidence_min_clip=0.0`, `confidence_max_clip=1.0`, `lstm_long_sequence_threshold`, `markov_short_sequence_threshold`
   - RETOUR : float - Score de confiance entre 0 et 1, combinant distance à 0.5, performance historique et facteurs contextuels
   - UTILITÉ : Évalue fiabilité prédiction selon méthode spécifique, performance passée et contexte séquence actuelle

8. analyze_context_sensitivity.txt (HybridBaccaratPredictor.analyze_context_sensitivity - Analyse sensibilité contextuelle)
   - Lignes 8491-8587 dans hbp.py (97 lignes)
   - FONCTION : Analyse la sensibilité contextuelle de la prédiction en évaluant comment elle changerait avec variations dans la séquence
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence actuelle de résultats ('player'/'banker')
     * prob_banker (float) - Probabilité prédite pour 'banker' (0.0-1.0)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not isinstance(sequence, list): return 0.5` et `if len(sequence) < 5: return 0.5` pour données insuffisantes
     * **GÉNÉRATION VARIATIONS :** Crée 3 types de variations : `modified_sequences = []`
       - **Variation dernière position :** `var1 = sequence[:-1] + ['banker' if sequence[-1] == 'player' else 'player']`
       - **Variation avant-dernière :** `var2 = sequence[:-2] + ['banker' if sequence[-2] == 'player' else 'player'] + [sequence[-1]]` si `len(sequence) >= 2`
       - **Variation 3ème position :** `var3 = sequence[:-3] + ['banker' if sequence[-3] == 'player' else 'player'] + sequence[-2:]` si `len(sequence) >= 3`
     * **CALCUL PRÉDICTIONS VARIATIONS :** Pour chaque variation, appelle `lgbm_feat, lstm_feat = self.create_hybrid_features(var_seq)` puis `var_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)`
     * **EXTRACTION PROBABILITÉS :** Récupère `var_prob = var_prediction.get('banker', 0.5)` pour chaque variation
     * **GESTION ERREURS :** Capture exceptions avec `except Exception as e:` et continue avec variations suivantes
     * **CALCUL DIFFÉRENCES :** Calcule `diffs = [abs(prob_banker - var_prob) for var_prob in variation_probs]` et `avg_diff = np.mean(diffs)`
     * **NORMALISATION SENSIBILITÉ :** Applique `sensitivity_factor = getattr(self.config, 'sensitivity_normalization_factor', 5.0)` puis `sensitivity = np.clip(avg_diff * sensitivity_factor, uncertainty_min_clip, uncertainty_max_clip)`
     * **PARAMÈTRES CONFIG :** Utilise `uncertainty_min_clip=0.0`, `uncertainty_max_clip=1.0` depuis configuration
   - RETOUR : float - Score de sensibilité entre 0 (stable) et 1 (très sensible), basé sur variance des prédictions
   - UTILITÉ : Évalue robustesse de la prédiction face aux variations contextuelles pour mesurer incertitude épistémique

9. calculate_epistemic_uncertainty.txt (HybridBaccaratPredictor.calculate_epistemic_uncertainty - Incertitude épistémique)
   - Lignes 8624-8644 dans hbp.py (21 lignes)
   - FONCTION : Calcule l'incertitude épistémique (incertitude du modèle) basée sur la variance des prédictions entre différents modèles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prob_list (List[float]) - Liste des probabilités prédites par différents modèles (markov, lgbm, lstm)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL VARIANCE :** Calcule `variance = np.var(prob_list)` pour mesurer désaccord entre modèles
     * **RÉCUPÉRATION CONFIG :** Extrait `uncertainty_normalization_factor = getattr(self.config, 'uncertainty_normalization_factor', 4.0)` (variance max = 0.25 pour valeurs [0,1])
     * **PARAMÈTRES CLIPPING :** Récupère `uncertainty_min_clip = getattr(self.config, 'uncertainty_min_clip', 0.0)` et `uncertainty_max_clip = getattr(self.config, 'uncertainty_max_clip', 1.0)`
     * **NORMALISATION VARIANCE :** Applique `normalized_variance = np.clip(variance * uncertainty_normalization_factor, uncertainty_min_clip, uncertainty_max_clip)`
     * **INTERPRÉTATION :** Variance élevée = modèles en désaccord = forte incertitude épistémique (manque de connaissances)
   - RETOUR : float - Score d'incertitude épistémique entre 0 et 1, normalisé depuis variance des prédictions
   - UTILITÉ : Quantifie l'incertitude due au manque de connaissances du modèle via désaccord entre prédicteurs

10. calculate_aleatoric_uncertainty.txt (HybridBaccaratPredictor.calculate_aleatoric_uncertainty - Incertitude aléatoire)
    - Lignes 8646-8667 dans hbp.py (22 lignes)
    - FONCTION : Calcule l'incertitude aléatoire (incertitude inhérente) basée sur l'entropie de la prédiction
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * prob (float) - Probabilité prédite pour une classe (0.0-1.0)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **RÉCUPÉRATION EPSILON :** Extrait `epsilon = getattr(self.config, 'epsilon_value', 1e-9)` pour éviter divisions par zéro
      * **SÉCURISATION PROBABILITÉ :** Calcule `prob_safe = max(epsilon, min(1.0 - epsilon, prob))` pour assurer prob dans [epsilon, 1-epsilon]
      * **CALCUL ENTROPIE BINAIRE :** Applique `entropy = -(prob_safe * np.log2(prob_safe) + (1.0 - prob_safe) * np.log2(1.0 - prob_safe))`
      * **NORMALISATION AUTOMATIQUE :** L'entropie binaire max est 1.0 (quand prob=0.5), donc pas de normalisation supplémentaire nécessaire
      * **INTERPRÉTATION :** Entropie élevée = prédiction proche de 0.5 = forte incertitude aléatoire inhérente
    - RETOUR : float - Score d'incertitude aléatoire entre 0 et 1, basé sur entropie binaire
    - UTILITÉ : Quantifie l'incertitude irréductible inhérente aux données via entropie de la prédiction

11. get_weights.txt (HybridBaccaratPredictor.get_weights - Récupération poids actuels)
    - Lignes 8416-8424 dans hbp.py (9 lignes)
    - FONCTION : Retourne les poids actuels des méthodes de prédiction avec protection thread-safe
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.weights_lock:` pour protéger l'accès concurrent aux poids
      * **COPIE DÉFENSIVE :** Retourne `self.weights.copy()` pour éviter modifications externes accidentelles
      * **ACCÈS SÉCURISÉ :** Garantit lecture atomique des poids même en cas d'accès concurrent
    - RETOUR : Dict[str, float] - Copie des poids actuels des méthodes (markov, lgbm, lstm)
    - UTILITÉ : Accès thread-safe aux poids pour affichage UI et calculs de prédiction hybride

12. consecutive_focused_metric.txt (HybridBaccaratPredictor.consecutive_focused_metric - Métrique focus consécutif)
    - Lignes 296-365 dans hbp.py (70 lignes)
    - FONCTION : Métrique personnalisée pour LGBM qui se concentre sur les recommandations NON-WAIT valides consécutives spécifiquement pour les manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * y_true - Labels réels (0=Player, 1=Banker)
      * y_pred - Probabilités prédites pour la classe positive (banker)
      * round_indices (optionnel) - Indices des manches (si None, données déjà filtrées)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONVERSION CLASSES :** Calcule `y_pred_class = (y_pred > 0.5).astype(int)` pour obtenir prédictions binaires
      * **FILTRAGE MANCHES CIBLES :** Si `round_indices` fourni, filtre `target_indices = [i for i, r in enumerate(round_indices) if 31 <= r <= 60]`
      * **VALIDATION DONNÉES :** Retourne `1e-7, 'consecutive_focused_metric'` si aucune manche 31-60 présente
      * **CALCUL ACCURACY BASE :** Calcule `correct_predictions = (y_pred_class == y_true)` et `accuracy = np.mean(correct_predictions)`
      * **SIMULATION RECOMMANDATIONS :** Calcule `confidence = np.abs(y_pred - 0.5) * 2` et `non_wait_mask = confidence >= min_confidence`
      * **CALCUL SÉQUENCES CONSÉCUTIVES :** Itère pour détecter séquences NON-WAIT correctes consécutives avec `current_consecutive += 1` si correct, reset si incorrect
      * **MÉTRIQUES SÉQUENCES :** Calcule `max_consecutive = max(consecutive_sequences)` et moyenne pondérée avec poids quadratiques
      * **SCORE FINAL :** Combine `final_score = (max_consecutive**2 * 0.8 + weighted_mean * 0.15 + accuracy * 0.05)` privilégiant fortement les séquences
    - RETOUR : tuple - (score, nom_de_la_métrique) avec score optimisé pour recommandations consécutives
    - UTILITÉ : Optimise LGBM spécifiquement pour objectif recommandations NON-WAIT valides consécutives sur manches 31-60

13. calculate_consecutive_focused_weights.txt (HybridBaccaratPredictor.calculate_consecutive_focused_weights - Poids focus consécutif)
    - Lignes 172-294 dans hbp.py (123 lignes)
    - FONCTION : Calcule des poids d'échantillons qui favorisent les recommandations NON-WAIT valides consécutives pour les manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_features (np.ndarray) - Features d'entrée pour l'entraînement
      * y (np.ndarray) - Labels cibles (0=Player, 1=Banker)
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(y), dtype=np.float32)` comme base uniforme
      * **FILTRAGE MANCHES CIBLES :** Si `sequence_positions` fourni, identifie indices avec `31 <= pos <= 60`
      * **SIMULATION PRÉDICTIONS :** Pour chaque échantillon, simule prédiction et calcule confiance avec `confidence = abs(prob_banker - 0.5) * 2`
      * **DÉTECTION NON-WAIT :** Marque échantillons avec `confidence >= min_confidence_for_recommendation`
      * **CALCUL SÉQUENCES CONSÉCUTIVES :** Parcourt échantillons pour identifier séquences NON-WAIT correctes consécutives
      * **PONDÉRATION PROGRESSIVE :** Applique poids croissants selon position dans séquence : `weight = base_weight * (1 + consecutive_bonus * position_in_sequence)`
      * **BONUS MANCHES CIBLES :** Multiplie par `target_round_weight` (défaut: 2.0) pour manches 31-60
      * **PÉNALITÉ ERREURS :** Applique `error_penalty_weight` pour échantillons NON-WAIT incorrects qui brisent séquences
      * **NORMALISATION FINALE :** Normalise poids pour maintenir distribution équilibrée avec `sample_weights = sample_weights / np.mean(sample_weights)`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés pour favoriser recommandations consécutives
    - UTILITÉ : Guide l'entraînement LGBM pour optimiser spécifiquement les recommandations NON-WAIT valides consécutives

14. evaluate_consecutive_focused.txt (HybridBaccaratPredictor.evaluate_consecutive_focused - Évaluation focus consécutif)
    - Lignes 367-500+ dans hbp.py (130+ lignes)
    - FONCTION : Évalue performance spécifique du système sur les recommandations NON-WAIT valides consécutives pour manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * num_simulations (int, optionnel) - Nombre de simulations à effectuer (défaut: 100)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **GÉNÉRATION SÉQUENCES :** Crée `num_simulations` séquences aléatoires de 60 manches avec `np.random.choice(['player', 'banker'])`
      * **EXTRACTION MANCHES CIBLES :** Isole `late_game_sequence = simulated_sequence[30:60]` pour focus sur manches 31-60
      * **PRÉDICTIONS SÉQUENTIELLES :** Pour chaque position i, utilise `current_seq = simulated_sequence[:30+i]` pour prédiction progressive
      * **EXTRACTION FEATURES :** Appelle `lgbm_features = self._extract_lgbm_features(current_seq)` et `lstm_features = self._extract_lstm_features(current_seq)`
      * **PRÉDICTION HYBRIDE :** Exécute `prediction = self.hybrid_prediction(lgbm_features, lstm_features)` pour chaque position
      * **CALCUL CONFIANCE :** Détermine `confidence = max(prediction['player'], prediction['banker'])` et compare avec `min_confidence_for_recommendation`
      * **DÉTECTION SÉQUENCES NON-WAIT :** Identifie recommandations NON-WAIT avec `confidence >= min_confidence` et vérifie exactitude
      * **CALCUL SÉQUENCES CONSÉCUTIVES :** Parcourt pour identifier séquences NON-WAIT correctes consécutives, reset si erreur
      * **MÉTRIQUES FINALES :** Calcule `max_consecutive = max(consecutive_sequences)`, moyenne pondérée avec poids quadratiques
      * **SCORE COMPOSITE :** Combine `consecutive_score = (max_consecutive**2 * CONSECUTIVE_SCORE_MAX_WEIGHT + weighted_mean * CONSECUTIVE_SCORE_WEIGHTED_MEAN_WEIGHT + precision_non_wait * CONSECUTIVE_SCORE_PRECISION_WEIGHT)`
    - RETOUR : Dict - Métriques incluant max_consecutive, precision_non_wait, consecutive_score
    - UTILITÉ : Évalue efficacité système sur objectif principal de recommandations NON-WAIT valides consécutives

15. consecutive_valid_recommendations_loss.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss - Loss recommandations consécutives)
    - FONCTION : Calcule fonction de perte pour recommandations valides consécutives
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * predictions (List[Dict]) - Liste des prédictions consécutives
      * actual_results (List[str]) - Résultats réels correspondants
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCES :** Vérifie cohérence prédictions et résultats
      * **CALCUL LOSS :** Applique fonction de perte spécialisée
      * **PONDÉRATION TEMPORELLE :** Donne plus de poids aux prédictions récentes
      * **NORMALISATION :** Standardise la loss pour comparaison
      * **AGRÉGATION :** Combine losses individuelles en score global
    - RETOUR : float - Score de loss pour recommandations consécutives
    - UTILITÉ : Optimise qualité des recommandations sur manches consécutives

16. consecutive_valid_recommendations_loss_1.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss_1 - Loss recommandations consécutives v1)
    - FONCTION : Version alternative de calcul de loss pour recommandations consécutives
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * predictions (List[Dict]) - Liste des prédictions consécutives
      * actual_results (List[str]) - Résultats réels correspondants
      * loss_type (str) - Type de fonction de perte ('mse', 'cross_entropy', 'custom')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **SÉLECTION LOSS :** Choisit fonction de perte selon paramètre
      * **CALCUL ADAPTATIF :** Applique loss avec paramètres adaptatifs
      * **RÉGULARISATION :** Ajoute terme de régularisation si nécessaire
      * **VALIDATION :** Vérifie convergence et stabilité
      * **OPTIMISATION :** Ajuste paramètres pour minimiser loss
    - RETOUR : float - Score de loss optimisé pour recommandations
    - UTILITÉ : Version améliorée de calcul de loss avec options configurables

17. uncertainty_weighted_loss.txt (HybridBaccaratPredictor.uncertainty_weighted_loss - Loss pondérée par incertitude)
    - FONCTION : Calcule loss pondérée par l'incertitude des prédictions
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * predictions (Dict[str, float]) - Prédictions avec probabilités
      * actual_result (str) - Résultat réel observé
      * uncertainty_scores (Dict[str, float]) - Scores d'incertitude associés
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL LOSS BASE :** Calcule loss standard de la prédiction
      * **PONDÉRATION INCERTITUDE :** Applique poids basé sur incertitude
      * **AJUSTEMENT ADAPTATIF :** Modifie loss selon niveau d'incertitude
      * **NORMALISATION :** Assure cohérence avec autres métriques
      * **VALIDATION :** Vérifie stabilité numérique
    - RETOUR : float - Loss pondérée par incertitude
    - UTILITÉ : Améliore apprentissage en tenant compte de l'incertitude

18. get_confidence_adjustment.txt (HybridBaccaratPredictor.get_confidence_adjustment - Ajustement confiance)
    - FONCTION : Calcule ajustement de confiance basé sur contexte actuel
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * base_confidence (float) - Confiance de base calculée
      * current_round (int) - Numéro de manche actuelle
      * sequence_context (Dict) - Contexte de la séquence actuelle
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE CONTEXTE :** Évalue contexte de jeu actuel
      * **FACTEUR MANCHE :** Applique ajustement selon numéro de manche
      * **HISTORIQUE RÉCENT :** Considère performance récente
      * **AJUSTEMENT ADAPTATIF :** Modifie confiance selon conditions
      * **VALIDATION BORNES :** Assure confiance dans limites acceptables
    - RETOUR : float - Confiance ajustée selon contexte
    - UTILITÉ : Adapte confiance aux conditions spécifiques de jeu

19. update_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.update_consecutive_confidence_calculator - MAJ calculateur confiance consécutive)
    - FONCTION : Met à jour le calculateur de confiance pour manches consécutives
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * new_result (str) - Nouveau résultat à intégrer
      * prediction_was_correct (bool) - Si la prédiction était correcte
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MISE À JOUR ÉTAT :** Intègre nouveau résultat dans calculateur
      * **RECALCUL MÉTRIQUES :** Met à jour métriques de confiance
      * **AJUSTEMENT POIDS :** Modifie poids selon performance récente
      * **VALIDATION :** Vérifie cohérence des calculs
      * **PERSISTANCE :** Sauvegarde état mis à jour
    - RETOUR : None - Met à jour directement le calculateur interne
    - UTILITÉ : Maintient calculateur de confiance à jour avec nouveaux résultats

20. update_consecutive_confidence_calculator_1.txt (HybridBaccaratPredictor.update_consecutive_confidence_calculator_1 - MAJ calculateur v1)
    - FONCTION : Version alternative de mise à jour du calculateur de confiance
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * results_batch (List[str]) - Lot de résultats à traiter
      * predictions_batch (List[Dict]) - Lot de prédictions correspondantes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **TRAITEMENT LOT :** Traite plusieurs résultats simultanément
      * **OPTIMISATION :** Calculs vectorisés pour efficacité
      * **VALIDATION BATCH :** Vérifie cohérence du lot complet
      * **MISE À JOUR ATOMIQUE :** Applique tous changements en une fois
      * **ROLLBACK :** Possibilité d'annuler si erreur détectée
    - RETOUR : bool - True si mise à jour réussie, False sinon
    - UTILITÉ : Version optimisée pour traitement par lots

21. init_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.init_consecutive_confidence_calculator - Init calculateur confiance)
    - FONCTION : Initialise le calculateur de confiance pour manches consécutives
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * config_params (Dict) - Paramètres de configuration du calculateur
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION INSTANCE :** Instancie ConsecutiveConfidenceCalculator
      * **CONFIGURATION :** Applique paramètres de configuration
      * **VALIDATION CONFIG :** Vérifie cohérence des paramètres
      * **INITIALISATION ÉTAT :** Configure état initial du calculateur
      * **INTÉGRATION :** Intègre calculateur dans système principal
    - RETOUR : bool - True si initialisation réussie
    - UTILITÉ : Configure calculateur spécialisé pour manches consécutives

22. init_wait_placement_optimizer.txt (HybridBaccaratPredictor.init_wait_placement_optimizer - Init optimiseur placement attente)
    - FONCTION : Initialise l'optimiseur de placement pour stratégies d'attente
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * optimizer_config (Dict) - Configuration de l'optimiseur
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION OPTIMISEUR :** Instancie optimiseur de placement
      * **CONFIGURATION STRATÉGIES :** Configure stratégies d'attente disponibles
      * **PARAMÈTRES OPTIMISATION :** Définit critères d'optimisation
      * **VALIDATION :** Vérifie cohérence de la configuration
      * **INTÉGRATION :** Intègre dans système de décision principal
    - RETOUR : bool - True si initialisation réussie
    - UTILITÉ : Configure optimisation des stratégies d'attente

23. get_current_wait_ratio.txt (HybridBaccaratPredictor.get_current_wait_ratio - Ratio attente actuel)
    - FONCTION : Calcule le ratio d'attente actuel basé sur conditions de jeu
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * current_confidence (float) - Confiance actuelle du système
      * recent_performance (Dict) - Performance récente des méthodes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE CONFIANCE :** Évalue niveau de confiance actuel
      * **PERFORMANCE RÉCENTE :** Analyse tendances de performance
      * **CALCUL RATIO :** Détermine ratio optimal d'attente
      * **AJUSTEMENT DYNAMIQUE :** Adapte selon conditions changeantes
      * **VALIDATION BORNES :** Assure ratio dans limites acceptables
    - RETOUR : float - Ratio d'attente optimal entre 0 et 1
    - UTILITÉ : Optimise stratégie d'attente selon conditions actuelles
