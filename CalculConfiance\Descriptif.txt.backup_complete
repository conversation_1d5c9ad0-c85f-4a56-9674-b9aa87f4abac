DESCRIPTIF DÉTAILLÉ DES MÉTHODES - CALCUL DE CONFIANCE
================================================================================

Ce fichier contient la description détaillée des méthodes de calcul de confiance
pour les séquences consécutives et l'optimisation des recommandations.

DOMAINE FONCTIONNEL : Calcul de confiance, séquences consécutives et optimisation recommandations

TOTAL : 25 MÉTHODES ANALYSÉES

================================================================================

1. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence - M<PERSON>THODE CALCUL CONFIANCE AVANCÉ)
   - Lignes 1665-2027 dans utils.py (363 lignes)
   - FONCTION : Calcule confiance pour recommandation basée sur données historiques avec facteurs multiples optimisés pour objectif 1
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features (List[float]) - Vecteur features pour position actuelle
     * game_round (int) - Numéro manche actuelle (1-indexé)
     * config (optionnel) - Configuration prédicteur pour mise à jour paramètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR CONFIG :** Met à jour paramètres depuis config si fourni (target_round_min/max, facteurs)
     * **VALIDATION MANCHES :** Vérifie si dans manches cibles (31-60), retourne confiance neutre sinon
     * **RECHERCHE PATTERNS :** find_similar_patterns() avec seuil similarité configurable
     * **LIMITATION PATTERNS :** Limite à max_similar_patterns pour éviter bruit
     * **STATISTIQUES AGRÉGÉES :** Calcule total_occurrences, total_success pondérés par similarité
     * **SÉQUENCES CONSÉCUTIVES :** Analyse consecutive_lengths avec moyenne et médiane pondérées
     * **POSITION PLAGE :** Calcule position_in_range dans manches cibles pour bonus adaptatif
     * **BONUS CLOCHE :** Applique bell_curve_bonus maximal au milieu de plage cible
     * **FACTEURS MULTIPLICATIFS :** occurrence_factor, consecutive_factor, sequence_bonus selon performances
     * **CONFIANCE PONDÉRÉE :** Combine success_rate_weight, consecutive_length_weight, pattern_frequency_weight
     * **RECOMMANDATION WAIT :** Analyse conditions pour recommander WAIT avec seuils adaptatifs
     * **ÉQUILIBRAGE RATIO :** Ajuste selon current_wait_ratio vs optimal_wait_ratio
     * **FOCUS CONSÉCUTIF :** Priorité absolue aux séquences consécutives en milieu de plage
     * **LOGGING DÉTAILLÉ :** Journalise décisions avec métriques complètes pour débogage
   - RETOUR : Dict[str, Any] - Dictionnaire complet avec confidence, expected_consecutive, similar_patterns_count, success_rate, wait_recommendation, etc.
   - UTILITÉ : Cœur du système de confiance avec optimisations spécifiques pour maximiser séquences consécutives NON-WAIT



2. find_similar_patterns.txt (ConsecutiveConfidenceCalculator.find_similar_patterns - RECHERCHE PATTERNS SIMILAIRES)
   - Lignes 1665-1727 dans utils.py (63 lignes)
   - FONCTION : Recherche patterns historiques similaires au pattern actuel avec calcul similarité
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * current_pattern (List[float]) - Pattern actuel à comparer
     * similarity_threshold (float, défaut=0.8) - Seuil minimum similarité
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION CLÉ :** Utilise _extract_pattern_key pour normalisation pattern
     * **PARCOURS HISTORIQUE :** Itère sur historical_data pour comparaisons
     * **CALCUL SIMILARITÉ :** Distance euclidienne normalisée entre patterns
     * **FILTRAGE SEUIL :** Retient uniquement patterns avec similarité >= threshold
     * **LIMITATION RÉSULTATS :** Limite à max_similar_patterns pour performance
     * **TRI PERTINENCE :** Ordonne par similarité décroissante
   - RETOUR : List[Dict] - Liste patterns similaires avec métadonnées (similarity, success, consecutive_length)
   - UTILITÉ : Base de l'analyse prédictive par comparaison patterns historiques

3. find_similar_patterns_1.txt (ConsecutiveConfidenceCalculator.find_similar_patterns - RECHERCHE PATTERNS SIMILAIRES - DOUBLON 1)
   - Lignes 1665-1727 dans utils.py (63 lignes)
   - FONCTION : Recherche patterns historiques similaires au pattern actuel avec calcul similarité - Version identique
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * current_pattern (List[float]) - Pattern actuel à comparer
     * similarity_threshold (float, défaut=0.8) - Seuil minimum similarité
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION CLÉ :** Utilise _extract_pattern_key pour normalisation pattern
     * **PARCOURS HISTORIQUE :** Itère sur historical_data pour comparaisons
     * **CALCUL SIMILARITÉ :** Distance euclidienne normalisée entre patterns
     * **FILTRAGE SEUIL :** Retient uniquement patterns avec similarité >= threshold
     * **LIMITATION RÉSULTATS :** Limite à max_similar_patterns pour performance
     * **TRI PERTINENCE :** Ordonne par similarité décroissante
   - RETOUR : List[Dict] - Liste patterns similaires avec métadonnées (similarity, success, consecutive_length)
   - UTILITÉ : Base de l'analyse prédictive par comparaison patterns historiques

4. should_wait.txt (WaitPlacementOptimizer.should_wait - DÉCISION RECOMMANDATION WAIT)
   - Lignes 3529-3642 dans utils.py (114 lignes)
   - FONCTION : Détermine si recommandation WAIT devrait être faite pour position actuelle avec analyse multi-facteurs
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * features (List[float]) - Vecteur features pour position actuelle
     * current_consecutive_valid (int, défaut=0) - Nombre recommandations NON-WAIT valides consécutives
     * round_num (int, défaut=0) - Numéro manche actuelle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MANCHES :** Vérifie si dans manches cibles (target_round_min/max), logique simplifiée sinon
     * **CRÉATION PATTERN :** Utilise _create_pattern_key pour identifier pattern actuel
     * **PROBABILITÉ ERREUR :** Calcule error_probability depuis error_patterns avec seuil min_pattern_occurrences
     * **DÉTECTION TRANSITION :** Analyse outcome_history pour identifier transitions et probabilité erreur associée
     * **EFFICACITÉ WAIT :** Calcule recent_wait_efficiency depuis wait_efficiency_history sur fenêtre récente
     * **FACTEUR CONSÉCUTIF :** consecutive_factor = 1.0 + (current_consecutive_valid * 0.1 * consecutive_priority_factor)
     * **SCORE WAIT :** Combine error_component et transition_component pondérés par poids respectifs
     * **AJUSTEMENT CONSÉCUTIF :** Divise wait_score par consecutive_factor pour favoriser séquences
     * **AJUSTEMENT EFFICACITÉ :** Réduit score si recent_wait_efficiency < wait_efficiency_threshold
     * **ÉQUILIBRAGE RATIO :** Applique pénalité/bonus selon current_wait_ratio vs wait_ratio_min/max
     * **DÉCISION FINALE :** should_wait = wait_score > error_pattern_threshold
     * **RAISON DÉTAILLÉE :** Identifie cause principale décision (pattern erreur, transition, ratio, séquence)
   - RETOUR : Dict[str, Any] - Dictionnaire complet avec should_wait, wait_score, probabilités, facteurs, raison
   - UTILITÉ : Décision intelligente WAIT avec optimisation séquences consécutives et équilibrage performance



5. update_with_result.txt (WaitPlacementOptimizer.update_with_result - MISE À JOUR RÉSULTAT)
   - Lignes 3644-3720 dans utils.py (77 lignes)
   - FONCTION : Met à jour optimiseur avec résultat recommandation pour apprentissage adaptatif
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * features (List[float]) - Vecteur features utilisé pour recommandation
     * was_wait (bool) - Si recommandation était WAIT
     * was_correct (bool) - Si recommandation était correcte
     * actual_outcome (str) - Résultat réel ('banker', 'player', 'tie')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR HISTORIQUE :** Ajoute actual_outcome à outcome_history avec limitation taille
     * **PATTERN ERREUR :** Met à jour error_patterns si was_wait=False et was_correct=False
     * **PATTERN TRANSITION :** Analyse transitions dans outcome_history pour mise à jour transition_patterns
     * **EFFICACITÉ WAIT :** Calcule et stocke efficacité si was_wait=True dans wait_efficiency_history
     * **RATIO WAIT :** Recalcule current_wait_ratio basé sur historique récent
     * **ADAPTATION SEUILS :** Appelle _adapt_thresholds pour ajustement dynamique paramètres
   - RETOUR : None (mise à jour interne)
   - UTILITÉ : Apprentissage continu pour optimisation adaptative placement WAIT

6. _extract_pattern_key.txt (ConsecutiveConfidenceCalculator._extract_pattern_key - EXTRACTION CLÉ PATTERN)
   - Lignes 622-666 dans utils.py (45 lignes)
   - FONCTION : Extrait clé pattern à partir vecteur features pour identification et comparaison patterns
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features (List[float]) - Vecteur features à convertir en clé pattern
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE :** Vérifie features non None, type valide (list/tuple/ndarray) et non vide
     * **LIMITATION FEATURES :** Limite à max_pattern_length pour éviter clés trop longues
     * **SÉLECTION FEATURES :** Prend features[:max_features] pour standardisation
     * **DISCRÉTISATION ADAPTATIVE :** Traitement différencié selon plage valeurs
     * **FEATURES NORMALISÉES :** Si 0≤feature≤1, discrétise en 5 niveaux (0.0, 0.25, 0.5, 0.75, 1.0)
     * **AUTRES FEATURES :** Arrondit à l'entier le plus proche pour réduction variabilité
     * **FEATURES NON-NUMÉRIQUES :** Conversion str() pour compatibilité
     * **CRÉATION CLÉ :** Joint discretized_features avec "_" comme séparateur
     * **GESTION ERREURS :** Try/catch avec retour "error_pattern" si exception
     * **LOGGING :** Journalise erreurs avec exc_info=True pour débogage
   - RETOUR : str - Clé pattern discrétisée ("empty_pattern" si vide, "error_pattern" si erreur)
   - UTILITÉ : Normalisation features en clés patterns pour recherche similarité et stockage efficace

7. _extract_pattern_key_1.txt (ConsecutiveConfidenceCalculator._extract_pattern_key - EXTRACTION CLÉ PATTERN - DOUBLON 1)
   - Lignes 622-666 dans utils.py (45 lignes)
   - FONCTION : Extrait clé pattern à partir vecteur features pour identification et comparaison patterns - Version identique
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features (List[float]) - Vecteur features à convertir en clé pattern
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE :** Vérifie features non None, type valide (list/tuple/ndarray) et non vide
     * **LIMITATION FEATURES :** Limite à max_pattern_length pour éviter clés trop longues
     * **SÉLECTION FEATURES :** Prend features[:max_features] pour standardisation
     * **DISCRÉTISATION ADAPTATIVE :** Traitement différencié selon plage valeurs
     * **FEATURES NORMALISÉES :** Si 0≤feature≤1, discrétise en 5 niveaux (0.0, 0.25, 0.5, 0.75, 1.0)
     * **AUTRES FEATURES :** Arrondit à l'entier le plus proche pour réduction variabilité
     * **FEATURES NON-NUMÉRIQUES :** Conversion str() pour compatibilité
     * **CRÉATION CLÉ :** Joint discretized_features avec "_" comme séparateur
     * **GESTION ERREURS :** Try/catch avec retour "error_pattern" si exception
     * **LOGGING :** Journalise erreurs avec exc_info=True pour débogage
   - RETOUR : str - Clé pattern discrétisée ("empty_pattern" si vide, "error_pattern" si erreur)
   - UTILITÉ : Normalisation features en clés patterns pour recherche similarité et stockage efficace

8. _adapt_thresholds_1.txt (WaitPlacementOptimizer._adapt_thresholds - ADAPTATION SEUILS AVANCÉE - DOUBLON 1)
   - Lignes 4174-4253 dans utils.py (80 lignes)
   - FONCTION : Adapte seuils décision WAIT avec algorithme sophistiqué multi-facteurs pour optimisation performance
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL TAUX SUCCÈS :** wait_success_rate = correct_wait_decisions/total_waits, non_wait_success_rate similaire
     * **EFFICACITÉ WAIT :** wait_efficiency = effective_waits/total_waits pour mesurer évitement erreurs
     * **SÉQUENCES MOYENNES :** avg_consecutive_valid = max_consecutive_valid/2 pour analyse longueur
     * **AJUSTEMENT EFFICACITÉ :** Si wait_efficiency > 0.7, efficiency_adjustment = (wait_efficiency - 0.7) * 0.3
     * **AJUSTEMENT SUCCÈS :** Si wait_success_rate > non_wait_success_rate * 1.2, success_adjustment = 0.05
     * **AJUSTEMENT CONSÉCUTIF :** Si avg_consecutive_valid < 3, consecutive_adjustment = 0.05 (prudence)
     * **RATIO OPTIMAL :** base_optimal_ratio + efficiency_adjustment + success_adjustment + consecutive_adjustment
     * **LEARNING RATE ADAPTATIF :** adaptive_learning_rate = learning_rate * (1 + 2 * abs(current_wait_ratio - optimal_ratio))
     * **AJUSTEMENT SEUILS :** Si current_wait_ratio < optimal_ratio, réduit error_pattern_threshold, confidence_threshold, uncertainty_threshold
     * **SYNCHRONISATION :** transition_uncertainty_threshold = error_pattern_threshold
     * **LOGGING DÉTAILLÉ :** Journalise tous seuils avec optimal_ratio, wait_efficiency, avg_consecutive_valid
   - RETOUR : None (modification interne seuils adaptatifs)
   - UTILITÉ : Adaptation intelligente seuils avec algorithme multi-facteurs pour optimisation continue performance WAIT

9. _calculate_recent_success_rate.txt (ConsecutiveConfidenceCalculator._calculate_recent_success_rate - CALCUL TAUX SUCCÈS RÉCENT)
   - Lignes 1277-1309 dans utils.py (33 lignes)
   - FONCTION : Calcule taux succès recommandations récentes avec gestion robuste erreurs et comparaisons insensibles casse
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ATTRIBUTS :** Vérifie existence recent_recommendations et recent_outcomes, retourne 0.5 si absent
     * **COHÉRENCE DIMENSIONS :** Vérifie len(recent_recommendations) == len(recent_outcomes), warning si incohérent
     * **FILTRAGE NON-WAIT :** non_wait_indices = [i for rec if rec.lower() != 'wait'] avec isinstance(rec, str)
     * **GESTION VIDE :** Retourne 0.5 si aucune recommandation NON-WAIT trouvée
     * **COMPTAGE SUCCÈS :** success_count pour i in non_wait_indices avec comparaison insensible casse
     * **COMPARAISON ROBUSTE :** recent_recommendations[i].lower() == recent_outcomes[i].lower() avec isinstance vérifications
     * **LOGGING DEBUG :** "Taux succès récent: X/Y = Z" pour traçabilité calculs
     * **CALCUL FINAL :** success_count / len(non_wait_indices) pour taux précis
   - RETOUR : float - Taux succès entre 0.0 et 1.0 (0.5 par défaut si problème)
   - UTILITÉ : Mesure performance récente robuste pour adaptation dynamique paramètres confiance

10. get_confidence_adjustment.txt (ConsecutiveConfidenceCalculator.get_confidence_adjustment - AJUSTEMENT CONFIANCE ADAPTATIF)
   - Lignes 1226-1275 dans utils.py (50 lignes)
   - FONCTION : Calcule ajustement confiance basé performances récentes avec interpolation linéaire et équilibrage ratio WAIT
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Retourne 0.0 si recent_outcomes absent ou < 5 éléments
     * **TAUX SUCCÈS RÉCENT :** Appelle _calculate_recent_success_rate() pour performance actuelle
     * **AJUSTEMENT BASE :** Si recent_success_rate >= 0.7, base_adjustment = -0.03 (agressif)
     * **AJUSTEMENT CONSERVATEUR :** Si recent_success_rate <= 0.3, base_adjustment = 0.03 (prudent)
     * **INTERPOLATION LINÉAIRE :** normalized_rate = (recent_success_rate - 0.3) / 0.4 pour zone 0.3-0.7
     * **CALCUL INTERPOLÉ :** base_adjustment = 0.03 - (normalized_rate * 0.06) pour transition douce
     * **RATIO WAIT ACTUEL :** wait_ratio = get_current_wait_ratio() pour équilibrage
     * **RATIO OPTIMAL :** optimal_wait_ratio depuis attribut ou 0.4 par défaut
     * **AJUSTEMENT RATIO :** Si abs(wait_ratio - optimal_wait_ratio) > 0.1, ratio_adjustment ±0.02
     * **COMBINAISON :** total_adjustment = base_adjustment + ratio_adjustment
     * **LIMITATION :** np.clip(total_adjustment, -0.05, 0.05) pour plage sécurisée
   - RETOUR : float - Ajustement confiance entre -0.05 et 0.05
   - UTILITÉ : Adaptation fine confiance avec équilibrage performance/prudence et ratio WAIT optimal

11. get_current_wait_ratio.txt (ConsecutiveConfidenceCalculator.get_current_wait_ratio - CALCUL RATIO WAIT ACTUEL)
   - Lignes 841-880 dans utils.py (40 lignes)
   - FONCTION : Calcule ratio WAIT actuel depuis recommandations récentes avec gestion robuste et bornage epsilon
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * config (optionnel) - Configuration pour optimal_wait_ratio alternatif
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EPSILON SÉCURITÉ :** epsilon = 1e-6 pour éviter divisions par zéro
     * **RATIO OPTIMAL :** optimal_ratio_candidate depuis config.optimal_wait_ratio ou self.optimal_wait_ratio
     * **FALLBACK :** Si optimal_ratio_candidate None, utilise 0.4 avec warning
     * **BORNAGE OPTIMAL :** bounded_optimal_ratio entre epsilon et 1.0-epsilon
     * **VALIDATION DONNÉES :** Retourne bounded_optimal_ratio si recent_recommendations vide
     * **COMPTAGE WAIT :** wait_count = sum(1 for rec if rec.lower() == 'wait') avec isinstance(rec, str)
     * **CALCUL RATIO :** ratio = wait_count / total_count avec vérification total_count > 0
     * **BORNAGE FINAL :** final_ratio entre epsilon et 1.0-epsilon avec logging détaillé
     * **LOGGING DEBUG :** Messages détaillés pour chaque étape et ajustement
   - RETOUR : float - Ratio WAIT entre epsilon et 1.0-epsilon
   - UTILITÉ : Mesure robuste ratio WAIT actuel pour équilibrage et adaptation dynamique