# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 4174 à 4253
# Type: Méthode de la classe WaitPlacementOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _adapt_thresholds(self):
        """
        Adapte les seuils en fonction des performances récentes.
        Optimisé pour maximiser le score des recommandations NON-WAIT valides.
        """
        # Calculer les taux de succès
        wait_success_rate = self.correct_wait_decisions / self.total_waits if self.total_waits > 0 else 0
        non_wait_success_rate = self.correct_non_wait_decisions / (self.total_decisions - self.total_waits) if (self.total_decisions - self.total_waits) > 0 else 0

        # Calculer l'efficacité des WAIT (proportion de WAIT qui ont évité une erreur)
        wait_efficiency = self.effective_waits / self.total_waits if self.total_waits > 0 else 0

        # Calculer le nombre moyen de recommandations NON-WAIT valides consécutives
        avg_consecutive_valid = self.max_consecutive_valid / 2 if self.max_consecutive_valid > 0 else 0

        # Calculer le ratio WAIT optimal en fonction de plusieurs facteurs
        # 1. Efficacité des WAIT
        if wait_efficiency > 0.7:
            # Si les WAIT sont très efficaces, augmenter le ratio optimal
            efficiency_adjustment = (wait_efficiency - 0.7) * 0.3
        else:
            # Si les WAIT sont peu efficaces, réduire le ratio optimal
            efficiency_adjustment = (wait_efficiency - 0.7) * 0.2

        # 2. Taux de succès relatif
        if wait_success_rate > non_wait_success_rate * 1.2:
            # Si les WAIT sont beaucoup plus efficaces que les NON-WAIT, augmenter le ratio
            success_adjustment = 0.05
        elif wait_success_rate < non_wait_success_rate * 0.8:
            # Si les WAIT sont beaucoup moins efficaces que les NON-WAIT, réduire le ratio
            success_adjustment = -0.05
        else:
            # Sinon, ajustement neutre
            success_adjustment = 0

        # 3. Séquences consécutives
        if avg_consecutive_valid < 3:
            # Si les séquences sont courtes, augmenter le ratio pour être plus prudent
            consecutive_adjustment = 0.05
        elif avg_consecutive_valid > 6:
            # Si les séquences sont longues, réduire le ratio pour être plus agressif
            consecutive_adjustment = -0.05
        else:
            # Sinon, ajustement neutre
            consecutive_adjustment = 0

        # Calculer le ratio optimal en combinant tous les facteurs
        base_optimal_ratio = (self.wait_ratio_min + self.wait_ratio_max) / 2
        optimal_ratio = base_optimal_ratio + efficiency_adjustment + success_adjustment + consecutive_adjustment

        # S'assurer que le ratio reste dans les limites
        optimal_ratio = max(self.wait_ratio_min, min(self.wait_ratio_max, optimal_ratio))

        # Calculer le taux d'apprentissage adaptatif
        # Plus on est loin du ratio optimal, plus on ajuste rapidement
        adaptive_learning_rate = self.learning_rate * (1 + 2 * abs(self.current_wait_ratio - optimal_ratio))

        # Ajuster les seuils en fonction du ratio optimal
        if self.current_wait_ratio < optimal_ratio:
            # Nous voulons plus de WAIT, réduire les seuils
            self.error_pattern_threshold = max(0.25, self.error_pattern_threshold - adaptive_learning_rate)
            self.confidence_threshold = max(0.5, self.confidence_threshold - adaptive_learning_rate * 0.5)
            self.uncertainty_threshold = max(0.2, self.uncertainty_threshold - adaptive_learning_rate * 0.5)
        elif self.current_wait_ratio > optimal_ratio:
            # Nous voulons moins de WAIT, augmenter les seuils
            self.error_pattern_threshold = min(0.85, self.error_pattern_threshold + adaptive_learning_rate)
            self.confidence_threshold = min(0.9, self.confidence_threshold + adaptive_learning_rate * 0.5)
            self.uncertainty_threshold = min(0.6, self.uncertainty_threshold + adaptive_learning_rate * 0.5)

        # Ajuster le seuil de transition
        self.transition_uncertainty_threshold = self.error_pattern_threshold

        # Journaliser les ajustements
        self.logger.debug(f"Seuils adaptés: error_pattern_threshold={self.error_pattern_threshold:.4f}, "
                         f"transition_uncertainty_threshold={self.transition_uncertainty_threshold:.4f}, "
                         f"confidence_threshold={self.confidence_threshold:.4f}, "
                         f"uncertainty_threshold={self.uncertainty_threshold:.4f}, "
                         f"optimal_ratio={optimal_ratio:.4f}, "
                         f"wait_efficiency={wait_efficiency:.4f}, "
                         f"avg_consecutive_valid={avg_consecutive_valid:.2f}")