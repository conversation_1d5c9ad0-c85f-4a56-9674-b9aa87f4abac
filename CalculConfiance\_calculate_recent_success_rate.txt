# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1277 à 1309
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def _calculate_recent_success_rate(self) -> float:
        """
        Calcule le taux de succès des recommandations récentes.

        Returns:
            float: Taux de succès (entre 0 et 1)
        """
        if not hasattr(self, 'recent_recommendations') or not hasattr(self, 'recent_outcomes'):
            return 0.5  # Valeur par défaut

        if len(self.recent_recommendations) != len(self.recent_outcomes):
            logger.warning("Dimensions incohérentes entre recommandations et résultats")
            return 0.5

        # Ne considérer que les recommandations NON-WAIT
        # Correction: Utiliser une comparaison insensible à la casse pour 'wait' et 'WAIT'
        non_wait_indices = [i for i, rec in enumerate(self.recent_recommendations)
                           if isinstance(rec, str) and rec.lower() != 'wait']

        if not non_wait_indices:
            return 0.5  # Aucune recommandation NON-WAIT

        # Compter les succès
        success_count = 0
        for i in non_wait_indices:
            # Comparer les recommandations et résultats en ignorant la casse
            if isinstance(self.recent_recommendations[i], str) and isinstance(self.recent_outcomes[i], str) and \
               self.recent_recommendations[i].lower() == self.recent_outcomes[i].lower():
                success_count += 1

        # Ajouter un log pour déboguer
        logger.debug(f"Taux de succès récent: {success_count}/{len(non_wait_indices)} = {success_count/len(non_wait_indices):.2f}")
        return success_count / len(non_wait_indices)