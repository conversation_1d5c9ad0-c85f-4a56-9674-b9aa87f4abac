# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3644 à 3670
# Type: Méthode de la classe WaitPlacementOptimizer

    def _create_pattern_key(self, features):
        """
        Crée une clé de pattern à partir des features.

        Args:
            features: Liste des features

        Returns:
            str: Clé de pattern
        """
        # Simplifier les features pour créer une clé de pattern
        # On utilise une approche de discrétisation
        if not features:
            return "empty_pattern"

        # Utiliser les 5 premières features ou moins si pas assez
        num_features = min(5, len(features))
        discretized = []

        for i in range(num_features):
            # Discrétiser chaque feature en 10 niveaux
            if i < len(features):
                value = features[i]
                # Arrondir à 1 décimale
                discretized.append(f"{i}_{round(value * 10) / 10}")

        return "_".join(discretized)