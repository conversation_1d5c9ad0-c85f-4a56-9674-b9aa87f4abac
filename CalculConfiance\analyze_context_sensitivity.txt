# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8669 à 8793
# Type: Méthode de la classe HybridBaccaratPredictor

    def analyze_context_sensitivity(self, sequence: List[str], prob_banker: float) -> float:
        """
        Analyse la sensibilité contextuelle de la prédiction en évaluant
        comment la prédiction changerait avec de petites variations dans la séquence.

        Args:
            sequence (List[str]): Séquence actuelle
            prob_banker (float): Probabilité prédite pour 'banker'

        Returns:
            float: Score de sensibilité entre 0 et 1 (0 = stable, 1 = très sensible)
        """
        # Vérifier que la séquence est valide
        if not isinstance(sequence, list):
            logger.warning("analyze_context_sensitivity: sequence n'est pas une liste. Retour 0.5.")
            return 0.5

        if len(sequence) < 5:
            return 0.5  # Pas assez de données pour évaluer la sensibilité

        # Vérifier que tous les éléments de la séquence sont valides
        for i, item in enumerate(sequence):
            if not isinstance(item, str) or item not in ('player', 'banker'):
                logger.warning(f"analyze_context_sensitivity: élément invalide à l'index {i}: {item}. Retour 0.5.")
                return 0.5

        # Créer des variations de la séquence en modifiant les derniers éléments
        variations = []

        # Variation 1: Inverser l'avant-dernier élément
        var1 = sequence.copy()
        if len(var1) >= 2:
            var1[-2] = 'player' if var1[-2] == 'banker' else 'banker'
            variations.append(var1)

        # Variation 2: Inverser le troisième dernier élément
        var2 = sequence.copy()
        if len(var2) >= 3:
            var2[-3] = 'player' if var2[-3] == 'banker' else 'banker'
            variations.append(var2)

        # Si nous n'avons pas de variations, retourner une valeur par défaut
        if not variations:
            return 0.5

        # Calculer les prédictions pour chaque variation
        variation_probs = []

        # Vérifier si le modèle Markov est correctement initialisé, entraîné et activé
        use_markov_model = getattr(self.config, 'use_markov_model', True)
        markov_is_valid = use_markov_model and hasattr(self, 'markov') and self.markov is not None

        # Si le modèle Markov n'est pas valide ou est désactivé, utiliser une sensibilité moyenne
        if not markov_is_valid:
            if not use_markov_model:
                logger.debug("analyze_context_sensitivity: Modèle Markov désactivé (use_markov_model=False). Utilisation de sensibilité moyenne.")
            else:
                logger.warning("analyze_context_sensitivity: Modèle Markov non initialisé. Utilisation de sensibilité moyenne.")
            # Retourner une sensibilité moyenne (0.5) qui n'affectera pas trop les décisions
            return 0.5

        with self.markov_lock:
            for var_seq in variations:
                if self.markov:
                    try:
                        # Vérifier que la séquence de variation est valide
                        if not isinstance(var_seq, list) or len(var_seq) < 1:
                            logger.warning(f"analyze_context_sensitivity: Séquence de variation invalide: {var_seq}. Utilisation de prédiction 50/50.")
                            variation_probs.append(0.5)
                            continue

                        # Vérifier que tous les éléments de la séquence sont valides
                        valid_sequence = True
                        for i, item in enumerate(var_seq):
                            if not isinstance(item, str) or item not in ('player', 'banker'):
                                logger.warning(f"analyze_context_sensitivity: Élément invalide dans la séquence de variation à l'index {i}: {item}. Utilisation de prédiction 50/50.")
                                valid_sequence = False
                                break

                        if valid_sequence:
                            # Vérifier que la séquence de variation est suffisamment longue
                            if hasattr(self.markov, 'max_order'):
                                # Utiliser la partie entière de max_order pour la comparaison
                                max_order_int = int(self.markov.max_order)

                                if len(var_seq) < max_order_int:
                                    logger.warning(f"analyze_context_sensitivity: Séquence de variation trop courte pour max_order={self.markov.max_order} (int={max_order_int}). Utilisation de prédiction 50/50.")
                                    variation_probs.append(0.5)
                                    continue

                            # Utiliser les paramètres Markov de la configuration
                            markov_global_weight = getattr(self.config, 'markov_global_weight', None)
                            markov_context_weight = getattr(self.config, 'markov_context_weight', None)
                            markov_decay_factor = getattr(self.config, 'markov_decay_factor', None)

                            # Créer un dictionnaire avec les paramètres non None
                            markov_params = {}
                            if markov_global_weight is not None:
                                markov_params['global_weight'] = markov_global_weight
                            if markov_context_weight is not None:
                                markov_params['context_weight'] = markov_context_weight
                            if markov_decay_factor is not None:
                                markov_params['decay_factor'] = markov_decay_factor

                            var_pred = self.markov.get_combined_probs(var_seq, **markov_params)
                            variation_probs.append(var_pred.get('banker', 0.5))
                        else:
                            variation_probs.append(0.5)
                    except Exception as e:
                        logger.warning(f"analyze_context_sensitivity: Erreur lors de la prédiction Markov: {e}")
                        variation_probs.append(0.5)
                else:
                    variation_probs.append(0.5)

        # Calculer la différence moyenne entre la prédiction originale et les variations
        diffs = [abs(prob_banker - var_prob) for var_prob in variation_probs]
        avg_diff = np.mean(diffs) if diffs else 0.0

        # Normaliser la différence
        sensitivity_factor = getattr(self.config, 'sensitivity_normalization_factor', 5.0)  # Une différence de 0.2 ou plus est considérée comme très sensible
        uncertainty_min_clip = getattr(self.config, 'uncertainty_min_clip', 0.0)
        uncertainty_max_clip = getattr(self.config, 'uncertainty_max_clip', 1.0)
        sensitivity = np.clip(avg_diff * sensitivity_factor, uncertainty_min_clip, uncertainty_max_clip)

        return sensitivity