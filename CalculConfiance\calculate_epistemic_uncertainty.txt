# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8624 à 8644
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_epistemic_uncertainty(self, prob_list: List[float]) -> float:
        """
        Calcule l'incertitude épistémique (incertitude du modèle) basée sur
        la variance des prédictions entre différents modèles.

        Args:
            prob_list (List[float]): Liste des probabilités prédites par différents modèles

        Returns:
            float: Score d'incertitude épistémique entre 0 et 1
        """
        # L'incertitude épistémique est mesurée par la variance des prédictions
        variance = np.var(prob_list)

        # Normaliser la variance (la variance max pour des valeurs dans [0,1] est 0.25)
        uncertainty_normalization_factor = getattr(self.config, 'uncertainty_normalization_factor', 4.0)
        uncertainty_min_clip = getattr(self.config, 'uncertainty_min_clip', 0.0)
        uncertainty_max_clip = getattr(self.config, 'uncertainty_max_clip', 1.0)
        normalized_variance = np.clip(variance * uncertainty_normalization_factor, uncertainty_min_clip, uncertainty_max_clip)

        return normalized_variance