# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 296 à 365
# Type: Méthode de la classe HybridBaccaratPredictor

    def consecutive_focused_metric(self, y_true, y_pred, round_indices=None):
        """
        Métrique personnalisée pour LGBM qui se concentre sur les recommandations NON-WAIT valides consécutives
        spécifiquement pour les manches 31-60.

        Args:
            y_true: Labels réels
            y_pred: Probabilités prédites pour la classe positive (banker)
            round_indices: Indices des manches (si None, on suppose que les données sont déjà filtrées)

        Returns:
            tuple: (score, nom_de_la_métrique)
        """
        # Convertir les probabilités en classes prédites
        y_pred_class = (y_pred > 0.5).astype(int)

        # Filtrer pour ne considérer que les manches 31-60 si round_indices est fourni
        target_indices = []
        if round_indices is not None:
            target_indices = [i for i, r in enumerate(round_indices) if 31 <= r <= 60]
            if not target_indices:
                # Si aucune manche 31-60 n'est présente, retourner un score minimal
                return 1e-7, 'consecutive_focused_metric'

            # Filtrer les données pour ne garder que les manches 31-60
            y_true = np.array([y_true[i] for i in target_indices])
            y_pred = np.array([y_pred[i] for i in target_indices])
            y_pred_class = (y_pred > 0.5).astype(int)

        # Calculer l'accuracy de base
        correct_predictions = (y_pred_class == y_true)
        accuracy = np.mean(correct_predictions)

        # Simuler des recommandations et calculer les séquences consécutives
        confidence = np.abs(y_pred - 0.5) * 2  # Normaliser entre 0 et 1
        min_confidence = self.config.min_confidence_for_recommendation
        non_wait_mask = confidence >= min_confidence

        # Calculer les séquences consécutives
        consecutive_sequences = []
        current_consecutive = 0

        for i in range(len(y_true)):
            if non_wait_mask[i]:
                if y_pred_class[i] == y_true[i]:
                    current_consecutive += 1
                else:
                    if current_consecutive > 0:
                        consecutive_sequences.append(current_consecutive)
                        current_consecutive = 0
            # Pour WAIT, ne pas réinitialiser le compteur

        # Ne pas oublier la dernière séquence
        if current_consecutive > 0:
            consecutive_sequences.append(current_consecutive)

        # Calculer le maximum de recommandations NON-WAIT valides consécutives
        max_consecutive = max(consecutive_sequences) if consecutive_sequences else 0

        # Calculer une moyenne pondérée des séquences consécutives
        weighted_mean = 0
        if consecutive_sequences:
            weights = [seq**2 for seq in consecutive_sequences]
            weighted_mean = sum(seq * weight for seq, weight in zip(consecutive_sequences, weights)) / sum(weights)

        # Score final: TRÈS fortement axé sur les séquences consécutives (objectif 1)
        # avec une contribution minimale de l'accuracy générale
        final_score = (max_consecutive**2 * 0.8 + weighted_mean * 0.15 + accuracy * 0.05)

        return final_score, 'consecutive_focused_metric'