# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 367 à 539
# Type: Méthode de la classe HybridBaccaratPredictor

    def evaluate_consecutive_focused(self, model_type='all', num_simulations=5):
        """
        Évalue les modèles en simulant des séquences, en se concentrant exclusivement sur l'objectif N°1
        (maximiser les recommandations NON-WAIT valides consécutives pour les manches 31-60).

        Args:
            model_type (str): Type de modèle à évaluer ('all', 'lgbm', 'lstm', 'markov')
            num_simulations (int): Nombre de simulations à effectuer

        Returns:
            dict: Métriques d'évaluation
        """
        logger.info(f"Évaluation focalisée sur l'objectif N°1 ({model_type}, {num_simulations} simulations)...")

        # Vérifier que les modèles sont disponibles
        if model_type in ('all', 'lgbm') and self.lgbm_base is None:
            logger.warning("Modèle LGBM non disponible pour l'évaluation.")
            return None

        if model_type in ('all', 'lstm') and self.lstm is None:
            logger.warning("Modèle LSTM non disponible pour l'évaluation.")
            return None

        # Initialiser les métriques en utilisant les noms standardisés de config.py
        metrics = {
            self.config.METRIC_CONSECUTIVE_SCORE: 0.0,
            self.config.METRIC_MAX_CONSECUTIVE: 0,
            'avg_max_consecutive': 0.0,
            self.config.METRIC_PRECISION_NON_WAIT: 0.0,
            self.config.METRIC_PRECISION_WAIT: 0.0,
            self.config.METRIC_RECOMMENDATION_RATE: 0.0
        }

        all_metrics = []

        # Effectuer plusieurs simulations
        for sim_idx in range(num_simulations):
            # Simuler une séquence de 60 manches
            simulated_sequence = self._simulate_game_sequence(60, seed=42 + sim_idx)

            if not simulated_sequence or len(simulated_sequence) < 60:
                logger.warning(f"Simulation {sim_idx+1}/{num_simulations}: Séquence incomplète, ignorée")
                continue

            # Nous nous intéressons particulièrement aux manches 31 à 60
            late_game_sequence = simulated_sequence[30:60]

            # Prédire pour chaque position dans la séquence
            predictions = []
            confidences = []

            for i in range(len(late_game_sequence)):
                # Utiliser les 30 premiers éléments + les éléments actuels jusqu'à i
                current_seq = simulated_sequence[:30+i]

                # Extraire les features pour cette séquence
                lgbm_features = self._extract_lgbm_features(current_seq)
                lstm_features = self._extract_lstm_features(current_seq)

                # Faire une prédiction hybride
                prediction = self.hybrid_prediction(lgbm_features, lstm_features)

                predictions.append(prediction)
                confidences.append(max(prediction['player'], prediction['banker']))

            # Convertir les prédictions en recommandations
            min_confidence = self.config.min_confidence_for_recommendation
            recommendations = []

            for i, pred in enumerate(predictions):
                if max(pred['player'], pred['banker']) >= min_confidence:
                    # Recommandation NON-WAIT
                    if pred['player'] > pred['banker']:
                        recommendations.append('player')
                    else:
                        recommendations.append('banker')
                else:
                    # Recommandation WAIT
                    recommendations.append('WAIT')

            # Calculer les métriques
            non_wait_mask = [r != 'WAIT' for r in recommendations]
            total_non_wait = sum(non_wait_mask)

            # Calculer la précision des recommandations NON-WAIT
            precision_non_wait = 0.0
            if total_non_wait > 0:
                correct_non_wait = 0
                for i in range(len(recommendations)):
                    if recommendations[i] != 'WAIT' and recommendations[i] == late_game_sequence[i]:
                        correct_non_wait += 1

                precision_non_wait = correct_non_wait / total_non_wait

            # Calculer la précision des recommandations WAIT
            wait_mask = [r == 'WAIT' for r in recommendations]
            total_wait = sum(wait_mask)
            precision_wait = self.config.WAIT_PRECISION_DEFAULT  # Par défaut, les recommandations WAIT sont considérées comme valides

            # Calculer le taux de recommandation
            recommendation_rate = total_non_wait / len(recommendations)

            # Calculer les séquences de recommandations NON-WAIT valides consécutives
            consecutive_sequences = []
            current_consecutive = 0

            for i in range(len(recommendations)):
                if recommendations[i] != 'WAIT':
                    if recommendations[i] == late_game_sequence[i]:
                        # Recommandation NON-WAIT valide
                        current_consecutive += 1
                    else:
                        # Recommandation NON-WAIT invalide - cela brise la séquence
                        if current_consecutive > 0:
                            consecutive_sequences.append(current_consecutive)
                            current_consecutive = 0
                # Pour WAIT, ne pas réinitialiser le compteur

            # Ne pas oublier la dernière séquence
            if current_consecutive > 0:
                consecutive_sequences.append(current_consecutive)

            # Calculer le maximum de recommandations NON-WAIT valides consécutives
            max_consecutive = max(consecutive_sequences) if consecutive_sequences else 0

            # Calculer une moyenne pondérée des séquences consécutives
            weighted_mean = 0
            if consecutive_sequences:
                weights = [seq**2 for seq in consecutive_sequences]
                weighted_mean = sum(seq * weight for seq, weight in zip(consecutive_sequences, weights)) / sum(weights)

            # Calculer un score composite TRÈS fortement axé sur les séquences consécutives (objectif 1)
            consecutive_score = (
                max_consecutive**2 * self.config.CONSECUTIVE_SCORE_MAX_WEIGHT +
                weighted_mean * self.config.CONSECUTIVE_SCORE_WEIGHTED_MEAN_WEIGHT +
                precision_non_wait * self.config.CONSECUTIVE_SCORE_PRECISION_WEIGHT
            )

            # Stocker les métriques pour cette simulation en utilisant les noms standardisés de config.py
            sim_metrics = {
                self.config.METRIC_CONSECUTIVE_SCORE: consecutive_score,
                self.config.METRIC_MAX_CONSECUTIVE: max_consecutive,
                self.config.METRIC_PRECISION_NON_WAIT: precision_non_wait,
                self.config.METRIC_PRECISION_WAIT: precision_wait,
                self.config.METRIC_RECOMMENDATION_RATE: recommendation_rate
            }

            all_metrics.append(sim_metrics)

            logger.debug(f"Simulation {sim_idx+1}/{num_simulations}: Score={consecutive_score:.4f}, Max consécutif={max_consecutive}")

        # Calculer les moyennes des métriques
        if all_metrics:
            # Utiliser une moyenne pondérée des scores consécutifs
            weights = [m[self.config.METRIC_CONSECUTIVE_SCORE]**2 for m in all_metrics]
            total_weight = sum(weights)

            if total_weight > 0:
                for key in metrics.keys():
                    metrics[key] = sum(m[key] * w for m, w in zip(all_metrics, weights)) / total_weight
            else:
                # Si tous les poids sont nuls, utiliser une moyenne simple
                for key in metrics.keys():
                    metrics[key] = sum(m[key] for m in all_metrics) / len(all_metrics)

            # Calculer la moyenne des max consécutifs
            metrics['avg_max_consecutive'] = sum(m[self.config.METRIC_MAX_CONSECUTIVE] for m in all_metrics) / len(all_metrics)

            logger.info(f"Évaluation terminée: Score consécutif={metrics[self.config.METRIC_CONSECUTIVE_SCORE]:.4f}, Max consécutif={metrics[self.config.METRIC_MAX_CONSECUTIVE]}")
        else:
            logger.warning("Aucune simulation réussie pour l'évaluation.")

        return metrics