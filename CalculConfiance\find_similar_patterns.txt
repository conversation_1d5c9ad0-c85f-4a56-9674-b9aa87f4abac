# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 739 à 839
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def find_similar_patterns(self, features: List[float], threshold: float = 0.7) -> List[Tuple[str, float]]:
        """
        Trouve des patterns similaires au vecteur de features donné.

        Cette méthode recherche dans les patterns enregistrés ceux qui sont similaires
        au vecteur de features fourni, en utilisant une mesure de similarité.

        Args:
            features: Vecteur de features pour lequel chercher des patterns similaires
            threshold: Seuil de similarité minimum (entre 0 et 1)

        Returns:
            Liste de tuples (pattern_key, similarité) triés par similarité décroissante
        """
        if self.pattern_stats is None or len(self.pattern_stats) == 0:
            return []

        if features is None or len(features) == 0:
            return []

        try:
            # Extraire la clé de pattern pour les features actuelles
            current_pattern_key = self._extract_pattern_key(features)

            # Si la clé existe exactement dans les statistiques, la retourner avec similarité maximale
            if current_pattern_key in self.pattern_stats:
                return [(current_pattern_key, 1.0)]

            # Sinon, chercher des patterns similaires
            similar_patterns = []

            # Convertir les features en valeurs discrétisées pour la comparaison
            discretized_features = []
            for feature in features[:self.max_pattern_length]:
                if isinstance(feature, (int, float)):
                    if 0 <= feature <= 1:
                        discretized = round(feature * 4) / 4
                    else:
                        discretized = round(feature)
                    discretized_features.append(discretized)
                else:
                    # Pour les features non numériques, utiliser la valeur telle quelle
                    discretized_features.append(feature)

            # Parcourir tous les patterns enregistrés
            for pattern_key in self.pattern_stats.keys():
                # Extraire les valeurs discrétisées du pattern
                pattern_values = []
                for value_str in pattern_key.split('_'):
                    try:
                        # Convertir en float si possible
                        pattern_values.append(float(value_str))
                    except ValueError:
                        # Sinon garder la chaîne
                        pattern_values.append(value_str)

                # Calculer la similarité entre les deux patterns
                # Utiliser uniquement les features communes aux deux patterns
                common_length = min(len(discretized_features), len(pattern_values))
                if common_length == 0:
                    continue

                # Calculer la similarité pour chaque feature commune
                feature_similarities = []
                for i in range(common_length):
                    if isinstance(discretized_features[i], (int, float)) and isinstance(pattern_values[i], (int, float)):
                        # Pour les features numériques, calculer la similarité basée sur la différence
                        if discretized_features[i] == pattern_values[i]:
                            feature_similarities.append(1.0)  # Similarité maximale si égaux
                        else:
                            # Calculer la similarité en fonction de la différence
                            # Plus la différence est petite, plus la similarité est grande
                            diff = abs(discretized_features[i] - pattern_values[i])
                            if 0 <= discretized_features[i] <= 1 and 0 <= pattern_values[i] <= 1:
                                # Pour les features normalisées, la différence maximale est 1
                                feature_similarities.append(max(0.0, 1.0 - diff))
                            else:
                                # Pour les autres features, utiliser une échelle adaptée
                                feature_similarities.append(max(0.0, 1.0 - min(1.0, diff / 5.0)))
                    elif discretized_features[i] == pattern_values[i]:
                        # Pour les features non numériques, similarité 1.0 si égaux, 0.0 sinon
                        feature_similarities.append(1.0)
                    else:
                        feature_similarities.append(0.0)

                # Calculer la similarité globale (moyenne pondérée des similarités de features)
                # Donner plus de poids aux premières features (plus importantes)
                weights = [1.0 - 0.05 * i for i in range(common_length)]  # Poids décroissants
                weighted_similarities = [sim * weight for sim, weight in zip(feature_similarities, weights)]
                similarity = sum(weighted_similarities) / sum(weights) if sum(weights) > 0 else 0.0

                # Si la similarité est supérieure au seuil, ajouter à la liste
                if similarity >= threshold:
                    similar_patterns.append((pattern_key, similarity))

            # Trier par similarité décroissante
            return sorted(similar_patterns, key=lambda x: x[1], reverse=True)

        except Exception as e:
            logger.error(f"Erreur lors de la recherche de patterns similaires: {e}", exc_info=True)
            return []