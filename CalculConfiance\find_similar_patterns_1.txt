# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1134 à 1198
# Type: Méthode de la classe ConsecutiveConfidenceCalculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def find_similar_patterns(self, features: List[float], threshold: float = 0.7) -> List[Tuple[str, float]]:
        """
        Trouve des patterns similaires au vecteur de features donné.

        Args:
            features: Vecteur de features pour lequel chercher des patterns similaires
            threshold: Seuil de similarité pour considérer un pattern comme similaire

        Returns:
            Liste de tuples (pattern_key, score_similarité) triés par similarité décroissante
        """
        if not hasattr(self, 'pattern_stats') or self.pattern_stats is None or len(self.pattern_stats) == 0:
            return []

        # Extraire la clé de pattern pour les features actuelles
        current_pattern_key = self._extract_pattern_key(features)

        # Si la clé existe exactement dans les statistiques, la retourner avec similarité maximale
        if current_pattern_key in self.pattern_stats:
            return [(current_pattern_key, 1.0)]

        # Sinon, chercher des clés similaires
        similar_patterns = []

        # Extraire les parties numériques des clés de pattern
        current_parts = current_pattern_key.split('_')
        current_values = []
        for p in current_parts:
            if p and p != "empty_features":
                try:
                    current_values.append(float(p))
                except ValueError:
                    # Si la conversion échoue, ignorer cette partie
                    logger.debug(f"Impossible de convertir '{p}' en float, partie ignorée")
                    continue

        if not current_values:
            return []

        for pattern_key in self.pattern_stats:
            # Extraire les parties numériques de la clé de pattern
            pattern_parts = pattern_key.split('_')
            pattern_values = []
            for p in pattern_parts:
                if p and p != "empty_features":
                    try:
                        pattern_values.append(float(p))
                    except ValueError:
                        # Si la conversion échoue, ignorer cette partie
                        logger.debug(f"Impossible de convertir '{p}' en float, partie ignorée")
                        continue

            if not pattern_values or len(pattern_values) != len(current_values):
                continue

            # Calculer la similarité comme la distance euclidienne normalisée
            diff_sum = sum((a - b) ** 2 for a, b in zip(current_values, pattern_values))
            max_diff = len(current_values) * 4.0  # Différence maximale possible (supposant des valeurs entre -1 et 1)
            similarity = 1.0 - min(1.0, diff_sum / max_diff)

            if similarity >= threshold:
                similar_patterns.append((pattern_key, similarity))

        # Trier par similarité décroissante
        return sorted(similar_patterns, key=lambda x: x[1], reverse=True)