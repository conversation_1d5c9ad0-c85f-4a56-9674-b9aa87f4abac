# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1226 à 1275
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def get_confidence_adjustment(self) -> float:
        """
        Calcule un ajustement de confiance basé sur les performances récentes.

        Returns:
            float: Ajustement de confiance (valeur positive ou négative)
        """
        # Si nous n'avons pas assez de données, retourner un ajustement neutre
        if not hasattr(self, 'recent_outcomes') or len(getattr(self, 'recent_outcomes', [])) < 5:
            return 0.0

        # Calculer le taux de succès récent
        recent_success_rate = self._calculate_recent_success_rate()

        # Calculer l'ajustement en fonction du taux de succès
        # Si le taux de succès est élevé, réduire le seuil (ajustement négatif)
        # Si le taux de succès est faible, augmenter le seuil (ajustement positif)
        base_adjustment = 0.0

        if recent_success_rate >= 0.7:  # Taux de succès élevé
            base_adjustment = -0.03  # Réduire le seuil pour être plus agressif
        elif recent_success_rate <= 0.3:  # Taux de succès faible
            base_adjustment = 0.03  # Augmenter le seuil pour être plus conservateur
        else:
            # Interpolation linéaire entre 0.3 et 0.7
            normalized_rate = (recent_success_rate - 0.3) / 0.4  # 0 à 1
            base_adjustment = 0.03 - (normalized_rate * 0.06)  # 0.03 à -0.03

        # Ajuster en fonction du ratio WAIT actuel par rapport à l'optimal
        wait_ratio = self.get_current_wait_ratio()
        # Récupérer le ratio optimal depuis la configuration ou utiliser une valeur par défaut
        if hasattr(self, 'optimal_wait_ratio'):
            optimal_wait_ratio = self.optimal_wait_ratio
        else:
            optimal_wait_ratio = 0.4  # Valeur par défaut

        ratio_adjustment = 0.0
        if abs(wait_ratio - optimal_wait_ratio) > 0.1:  # Si l'écart est significatif
            if wait_ratio > optimal_wait_ratio:
                # Trop de WAIT, réduire le seuil
                ratio_adjustment = -0.02
            else:
                # Pas assez de WAIT, augmenter le seuil
                ratio_adjustment = 0.02

        # Combiner les ajustements
        total_adjustment = base_adjustment + ratio_adjustment

        # Limiter l'ajustement à une plage raisonnable
        return np.clip(total_adjustment, -0.05, 0.05)