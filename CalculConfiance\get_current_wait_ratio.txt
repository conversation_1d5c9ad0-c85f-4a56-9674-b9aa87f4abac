# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 841 à 880
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def get_current_wait_ratio(self, config=None) -> float:
        epsilon = 1e-6

        optimal_ratio_candidate = getattr(config, 'optimal_wait_ratio', self.optimal_wait_ratio) if config else self.optimal_wait_ratio

        if optimal_ratio_candidate is None:
            optimal_ratio_candidate = 0.4
            logger.warning(f"get_current_wait_ratio: optimal_wait_ratio était None, utilisation de la valeur de secours {optimal_ratio_candidate}")

        if optimal_ratio_candidate <= epsilon:
            bounded_optimal_ratio = epsilon
        elif optimal_ratio_candidate >= 1.0 - epsilon:
            bounded_optimal_ratio = 1.0 - epsilon
        else:
            bounded_optimal_ratio = optimal_ratio_candidate

        if not hasattr(self, 'recent_recommendations') or not self.recent_recommendations or len(self.recent_recommendations) == 0:
            logger.debug(f"get_current_wait_ratio: Pas de données récentes, retour du ratio optimal borné: {bounded_optimal_ratio:.4f}")
            return bounded_optimal_ratio

        wait_count = sum(1 for rec in self.recent_recommendations if isinstance(rec, str) and rec.lower() == 'wait')
        total_count = len(self.recent_recommendations)

        if total_count == 0:
            logger.debug(f"get_current_wait_ratio: total_count est 0 après vérification des données récentes, retour du ratio optimal borné: {bounded_optimal_ratio:.4f}")
            return bounded_optimal_ratio

        ratio = wait_count / total_count

        if ratio <= epsilon:
            final_ratio = epsilon
            logger.debug(f"Ratio WAIT original ({ratio:.4f} = {wait_count}/{total_count}) trop bas, ajusté à {final_ratio:.4f}")
        elif ratio >= 1.0 - epsilon:
            final_ratio = 1.0 - epsilon
            logger.debug(f"Ratio WAIT original ({ratio:.4f} = {wait_count}/{total_count}) trop haut, ajusté à {final_ratio:.4f}")
        else:
            final_ratio = ratio
            logger.debug(f"Ratio WAIT original ({ratio:.4f} = {wait_count}/{total_count}) utilisé tel quel.")

        return final_ratio