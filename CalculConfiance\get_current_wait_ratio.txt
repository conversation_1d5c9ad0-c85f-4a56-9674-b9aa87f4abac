# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1452 à 1458
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

                def get_current_wait_ratio(self):
                    """Calcule le ratio WAIT/NON-WAIT actuel."""
                    if not self.recent_recommendations:
                        return getattr(config_ref, 'default_wait_ratio', 0.4)  # Valeur par défaut

                    wait_count = sum(1 for rec in self.recent_recommendations if rec == 'wait')
                    return wait_count / len(self.recent_recommendations)