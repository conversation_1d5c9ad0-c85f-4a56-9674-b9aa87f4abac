# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1200 à 1224
# Type: Méthode de la classe ConsecutiveConfidenceCalculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def get_current_wait_ratio(self) -> float:
        """
        Calcule le ratio actuel de recommandations WAIT par rapport au total des recommandations.

        Returns:
            float: Ratio de recommandations WAIT (entre 0 et 1)
        """
        # Récupérer les statistiques des dernières recommandations
        if not hasattr(self, 'recent_recommendations'):
            # Si aucune recommandation n'a été enregistrée, retourner une valeur par défaut
            return 0.4  # Valeur par défaut (40% de WAIT)

        # Compter le nombre de recommandations WAIT et NON-WAIT
        # Correction: Utiliser une comparaison insensible à la casse pour 'wait' et 'WAIT'
        wait_count = sum(1 for rec in self.recent_recommendations if isinstance(rec, str) and rec.lower() == 'wait')
        total_count = len(self.recent_recommendations)

        # Calculer le ratio
        if total_count > 0:
            ratio = wait_count / total_count
            # Ajouter un log pour déboguer
            logger.debug(f"Ratio WAIT actuel (méthode 2): {ratio:.2f} ({wait_count}/{total_count})")
            return ratio
        else:
            return 0.4  # Valeur par défaut si aucune recommandation