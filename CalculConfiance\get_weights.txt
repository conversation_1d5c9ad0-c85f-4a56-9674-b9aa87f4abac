# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8416 à 8424
# Type: Méthode de la classe HybridBaccaratPredictor

    def get_weights(self) -> Dict[str, float]:
        """
        Retourne les poids actuels des méthodes de prédiction.

        Returns:
            Dict[str, float]: Un dictionnaire contenant les poids des méthodes.
        """
        with self.weights_lock:  # Protéger l'accès aux poids
            return self.weights.copy()