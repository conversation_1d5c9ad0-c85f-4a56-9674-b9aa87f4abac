# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3529 à 3642
# Type: Méthode de la classe WaitPlacementOptimizer

    def should_wait(self, features, current_consecutive_valid=0, round_num=0):
        """
        Détermine si une recommandation WAIT devrait être faite pour la position actuelle.

        Args:
            features: Vecteur de features pour la position actuelle
            current_consecutive_valid: Nombre actuel de recommandations NON-WAIT valides consécutives
            round_num: Numéro de la manche actuelle

        Returns:
            Dict contenant la décision et les métriques associées
        """
        # Vérifier si nous sommes dans les manches cibles
        is_target_round = self.target_round_min <= round_num <= self.target_round_max

        # Si nous ne sommes pas dans les manches cibles, utiliser une logique simplifiée
        if not is_target_round:
            return {
                "should_wait": False,
                "wait_score": 0.0,
                "error_probability": 0.0,
                "transition_probability": 0.0,
                "consecutive_factor": 1.0,
                "recent_wait_efficiency": 0.0,
                "is_target_round": is_target_round,
                "reason": "Hors manches cibles"
            }

        # Créer une clé de pattern
        pattern_key = self._create_pattern_key(features)

        # Vérifier si ce pattern est associé à des erreurs
        error_probability = 0.0
        if pattern_key in self.error_patterns:
            pattern_stats = self.error_patterns[pattern_key]
            if pattern_stats['total'] >= self.min_pattern_occurrences:
                error_probability = pattern_stats['errors'] / pattern_stats['total']

        # Vérifier si nous sommes dans une transition
        transition_probability = 0.0
        if len(self.outcome_history) >= 2:
            last_outcomes = self.outcome_history[-2:]
            if last_outcomes[0] != last_outcomes[1]:
                # C'est une transition
                transition_key = f"{last_outcomes[0]}_{last_outcomes[1]}"

                if transition_key in self.transition_patterns:
                    transition_stats = self.transition_patterns[transition_key]
                    if transition_stats['total'] >= self.min_pattern_occurrences:
                        transition_probability = transition_stats['errors'] / transition_stats['total']

        # Calculer l'efficacité récente des WAIT
        recent_wait_efficiency = 0.0
        if self.wait_efficiency_history:
            recent_window = min(self.recent_history_window, len(self.wait_efficiency_history))
            recent_wait_efficiency = sum(self.wait_efficiency_history[-recent_window:]) / recent_window

        # Facteur de priorité pour les séquences consécutives
        consecutive_factor = 1.0
        if current_consecutive_valid > 0:
            # Plus la séquence est longue, plus nous voulons éviter les WAIT
            consecutive_factor = 1.0 + (current_consecutive_valid * 0.1 * self.consecutive_priority_factor)

        # Calculer le score final pour la recommandation WAIT
        error_component = error_probability * self.error_weight
        transition_component = transition_probability * self.transition_weight

        # Score de base pour WAIT
        wait_score = (error_component + transition_component)

        # Ajuster en fonction des séquences consécutives
        wait_score = wait_score / consecutive_factor

        # Ajuster en fonction de l'efficacité récente des WAIT
        if recent_wait_efficiency < self.wait_efficiency_threshold:
            # Si les WAIT récents n'ont pas été efficaces, réduire le score
            wait_score *= (recent_wait_efficiency / self.wait_efficiency_threshold)

        # Ajuster en fonction du ratio WAIT actuel
        if self.current_wait_ratio > self.wait_ratio_max:
            # Si nous avons trop de WAIT, réduire le score
            ratio_penalty = (self.current_wait_ratio - self.wait_ratio_max) / (1.0 - self.wait_ratio_max)
            wait_score *= (1.0 - ratio_penalty)
        elif self.current_wait_ratio < self.wait_ratio_min:
            # Si nous avons trop peu de WAIT, augmenter le score
            ratio_boost = (self.wait_ratio_min - self.current_wait_ratio) / self.wait_ratio_min
            wait_score *= (1.0 + ratio_boost)

        # Décision finale
        should_wait_decision = wait_score > self.error_pattern_threshold

        # Déterminer la raison de la décision
        reason = "Score combiné"
        if error_probability > self.error_pattern_threshold:
            reason = "Pattern d'erreur détecté"
        elif transition_probability > self.transition_uncertainty_threshold:
            reason = "Transition incertaine détectée"
        elif self.current_wait_ratio < self.wait_ratio_min:
            reason = "Équilibrage du ratio WAIT (trop bas)"
        elif current_consecutive_valid > 0 and not should_wait_decision:
            reason = "Maintien de la séquence consécutive"

        # Préparer les métriques pour le retour
        return {
            "should_wait": should_wait_decision,
            "wait_score": wait_score,
            "error_probability": error_probability,
            "transition_probability": transition_probability,
            "consecutive_factor": consecutive_factor,
            "recent_wait_efficiency": recent_wait_efficiency,
            "is_target_round": is_target_round,
            "current_wait_ratio": self.current_wait_ratio,
            "reason": reason
        }