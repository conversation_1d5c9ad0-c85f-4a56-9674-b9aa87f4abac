# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3897 à 4056
# Type: Méthode de la classe WaitPlacementOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def should_wait(self, features, prediction, confidence, uncertainty, round_num):
        """
        Détermine si une recommandation WAIT devrait être faite pour la position actuelle.
        Optimisé pour maximiser le score des recommandations NON-WAIT valides.

        Args:
            features (List[float]): Vecteur de features pour la position actuelle
            prediction (str): Prédiction actuelle ('player' ou 'banker')
            confidence (float): Niveau de confiance dans la prédiction
            uncertainty (float): Niveau d'incertitude dans la prédiction
            round_num (int): Numéro de la manche actuelle

        Returns:
            bool: True si une recommandation WAIT devrait être faite, False sinon
            float: Score de décision (plus élevé = plus de raisons de faire WAIT)
            Dict: Facteurs qui ont influencé la décision
        """
        # Extraire une clé de pattern à partir des features
        pattern_key = self._extract_pattern_key(features)

        # Initialiser le score de décision et les facteurs
        decision_score = 0.0
        decision_factors = {}

        # 1. Vérifier si ce pattern est associé à des erreurs fréquentes
        error_factor = 0.0
        if pattern_key in self.error_patterns:
            pattern_stats = self.error_patterns[pattern_key]
            error_rate = pattern_stats['errors'] / pattern_stats['total'] if pattern_stats['total'] > 0 else 0

            # Réduire le seuil pour être plus agressif dans la détection des patterns d'erreur
            adjusted_error_threshold = self.error_pattern_threshold * 0.9
            if error_rate > adjusted_error_threshold:
                error_factor = error_rate * 1.2  # Amplifier l'impact des patterns d'erreur
                decision_score += error_factor
                decision_factors['error_pattern'] = error_factor

        # Si nous n'avons pas assez de données pour ce pattern, être plus prudent
        elif len(self.error_patterns) > 0:
            # Ajouter un facteur de prudence pour les patterns inconnus
            caution_factor = 0.2
            decision_score += caution_factor
            decision_factors['unknown_pattern'] = caution_factor

        # 2. Vérifier si nous sommes dans une transition (changement de résultat)
        transition_factor = 0.0
        if len(self.outcome_history) >= 2:
            last_outcomes = self.outcome_history[-2:]
            if last_outcomes[0] != last_outcomes[1]:
                # C'est une transition
                transition_key = f"{last_outcomes[0]}_{last_outcomes[1]}"

                if transition_key in self.transition_patterns:
                    transition_stats = self.transition_patterns[transition_key]
                    transition_error_rate = transition_stats['errors'] / transition_stats['total'] if transition_stats['total'] > 0 else 0

                    # Réduire le seuil pour être plus agressif dans la détection des transitions problématiques
                    adjusted_transition_threshold = self.transition_uncertainty_threshold * 0.9
                    if transition_error_rate > adjusted_transition_threshold:
                        transition_factor = transition_error_rate * 1.2  # Amplifier l'impact des transitions
                        decision_score += transition_factor
                        decision_factors['transition'] = transition_factor
                else:
                    # Transition inconnue, être prudent
                    caution_factor = 0.15
                    decision_score += caution_factor
                    decision_factors['unknown_transition'] = caution_factor

        # 3. Vérifier la confiance et l'incertitude
        confidence_factor = 0.0
        # Augmenter le seuil de confiance pour être plus exigeant
        adjusted_confidence_threshold = self.confidence_threshold * 1.1
        if confidence < adjusted_confidence_threshold:
            confidence_factor = (adjusted_confidence_threshold - confidence) / adjusted_confidence_threshold
            confidence_factor *= 1.2  # Amplifier l'impact de la faible confiance
            decision_score += confidence_factor
            decision_factors['low_confidence'] = confidence_factor

        uncertainty_factor = 0.0
        # Réduire le seuil d'incertitude pour être plus sensible
        adjusted_uncertainty_threshold = self.uncertainty_threshold * 0.9
        if uncertainty > adjusted_uncertainty_threshold:
            uncertainty_factor = (uncertainty - adjusted_uncertainty_threshold) / (1 - adjusted_uncertainty_threshold)
            uncertainty_factor *= 1.2  # Amplifier l'impact de l'incertitude élevée
            decision_score += uncertainty_factor
            decision_factors['high_uncertainty'] = uncertainty_factor

        # 4. Ne plus vérifier le ratio WAIT actuel
        # Laisser le système décider naturellement en fonction de la qualité des prédictions
        ratio_factor = 0.0
        decision_factors['wait_ratio_check'] = "Désactivé - Recommandations naturelles"

        # 5. Vérifier si nous sommes dans la plage de manches cibles (31-60)
        target_factor = 0.0
        if self.target_round_min <= round_num <= self.target_round_max:
            # Dans la plage cible, être plus conservateur pour maximiser les NON-WAIT valides

            # Ajouter un facteur de base pour les manches cibles
            base_target_factor = 0.1
            decision_score += base_target_factor
            decision_factors['target_round_base'] = base_target_factor

            if self.current_consecutive_valid >= 2:  # Réduire le seuil pour protéger les séquences plus tôt
                # Si nous avons déjà une bonne séquence, être plus prudent
                streak_protection_factor = 0.15 + (self.current_consecutive_valid * 0.07)  # Augmenter l'impact
                decision_score += streak_protection_factor
                decision_factors['protect_streak'] = streak_protection_factor

                # Ajouter un facteur supplémentaire pour les longues séquences
                if self.current_consecutive_valid >= 4:
                    long_streak_factor = 0.2
                    decision_score += long_streak_factor
                    decision_factors['protect_long_streak'] = long_streak_factor

        # 6. Analyser l'historique récent des prédictions
        if len(self.prediction_history) >= 3 and len(self.outcome_history) >= 3:
            recent_accuracy = sum(1 for i in range(min(5, len(self.prediction_history)))
                                if i < len(self.outcome_history) and self.prediction_history[-(i+1)] == self.outcome_history[-(i+1)]) / min(5, len(self.prediction_history))

            # Si l'exactitude récente est faible, être plus prudent
            if recent_accuracy < 0.6:
                recent_accuracy_factor = (0.6 - recent_accuracy) * 1.5
                decision_score += recent_accuracy_factor
                decision_factors['low_recent_accuracy'] = recent_accuracy_factor

        # 7. Considérer l'efficacité des WAIT précédents
        if self.total_waits > 0:
            wait_efficiency = self.effective_waits / self.total_waits

            # Si les WAIT ont été efficaces, être plus enclin à en faire
            if wait_efficiency > 0.7:
                efficiency_factor = (wait_efficiency - 0.7) * 0.5
                decision_score += efficiency_factor
                decision_factors['high_wait_efficiency'] = efficiency_factor

        # Normaliser le score de décision
        decision_score = min(1.0, max(0.0, decision_score))

        # Calculer un seuil adaptatif basé sur la qualité des prédictions récentes
        adaptive_threshold = 0.5  # Valeur par défaut

        # Si nous avons des données sur la précision récente, ajuster le seuil
        if hasattr(self, 'recent_accuracy') and self.recent_accuracy is not None:
            # Si la précision récente est élevée, être moins conservateur (seuil plus élevé)
            if self.recent_accuracy > 0.7:
                adaptive_threshold = 0.55
                decision_factors['adaptive_threshold_high_accuracy'] = f"Précision élevée ({self.recent_accuracy:.2f}): seuil augmenté à {adaptive_threshold:.2f}"
            # Si la précision récente est faible, être plus conservateur (seuil plus bas)
            elif self.recent_accuracy < 0.6:
                adaptive_threshold = 0.45
                decision_factors['adaptive_threshold_low_accuracy'] = f"Précision faible ({self.recent_accuracy:.2f}): seuil réduit à {adaptive_threshold:.2f}"

        # Décider si nous devrions faire WAIT en utilisant le seuil adaptatif
        should_make_wait = decision_score >= adaptive_threshold
        decision_factors['adaptive_threshold'] = adaptive_threshold

        # Journaliser la décision
        self.logger.debug(f"Décision WAIT: {should_make_wait} (score={decision_score:.4f}, facteurs={decision_factors})")

        return should_make_wait, decision_score, decision_factors