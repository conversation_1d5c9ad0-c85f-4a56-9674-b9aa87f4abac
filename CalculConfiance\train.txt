# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 668 à 737
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def train(self, training_data: List[Dict[str, Any]]) -> None:
        """
        Entraîne le calculateur avec des données d'entraînement.

        Args:
            training_data: Liste de dictionnaires contenant les données d'entraînement
                Chaque dictionnaire doit contenir:
                - 'round_num': Numéro de la manche
                - 'features': Liste des features
                - 'outcome': R<PERSON><PERSON><PERSON> réel ('player' ou 'banker')
                - 'recommendation': Recommandation faite ('player', 'banker', 'WAIT')
                - 'is_valid': Booléen indiquant si la recommandation était valide
                - 'confidence': Score de confiance (optionnel)
        """
        if not training_data:
            logger.warning("Aucune donnée d'entraînement fournie.")
            return

        # Réinitialiser les statistiques
        self.pattern_stats = defaultdict(lambda: {"total": 0, "success": 0, "consecutive_lengths": [], "max_consecutive": 0})

        # Compteur pour les séquences consécutives
        consecutive_valid_count = 0

        # Parcourir les données d'entraînement
        for i, sample in enumerate(training_data):
            # Extraire les données
            features = sample.get('features', [])
            round_num = sample.get('round_num', i + 1)
            recommendation = sample.get('recommendation', 'WAIT')
            outcome = sample.get('outcome', '')
            is_valid = sample.get('is_valid', False)

            # Créer une clé de pattern à partir des features
            pattern_key = self._extract_pattern_key(features)

            # Ne traiter que les recommandations NON-WAIT
            if recommendation != 'WAIT':
                # Mettre à jour les statistiques
                self.pattern_stats[pattern_key]["total"] += 1

                if is_valid:
                    self.pattern_stats[pattern_key]["success"] += 1
                    consecutive_valid_count += 1

                    # Enregistrer la longueur de la séquence consécutive
                    self.pattern_stats[pattern_key]["consecutive_lengths"].append(consecutive_valid_count)

                    # Mettre à jour la longueur maximale
                    self.pattern_stats[pattern_key]["max_consecutive"] = max(
                        self.pattern_stats[pattern_key].get("max_consecutive", 0),
                        consecutive_valid_count
                    )
                else:
                    # Réinitialiser le compteur de séquences consécutives
                    consecutive_valid_count = 0
            # Pour les recommandations WAIT, ne pas réinitialiser le compteur

        # Calculer des statistiques globales
        total_patterns = len(self.pattern_stats)
        total_samples = sum(stats["total"] for stats in self.pattern_stats.values())
        total_success = sum(stats["success"] for stats in self.pattern_stats.values())
        success_rate = total_success / total_samples if total_samples > 0 else 0.0

        # Calculer la longueur maximale des séquences consécutives
        max_consecutive_lengths = [stats.get("max_consecutive", 0) for stats in self.pattern_stats.values()]
        max_consecutive = max(max_consecutive_lengths) if max_consecutive_lengths else 0

        logger.info(f"Entraînement terminé: {total_patterns} patterns uniques, {total_samples} échantillons, "
                   f"taux de succès global: {success_rate:.2%}, max consécutives: {max_consecutive}")