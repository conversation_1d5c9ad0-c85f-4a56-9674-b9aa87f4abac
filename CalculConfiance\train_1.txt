# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3412 à 3527
# Type: Méthode de la classe WaitPlacementOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def train(self, training_data):
        """
        Entraîne l'optimiseur avec des données historiques.

        Args:
            training_data: Liste de dictionnaires contenant les données d'entraînement
                Chaque dictionnaire doit contenir:
                - 'features': Liste des features
                - 'outcome': R<PERSON><PERSON><PERSON> réel ('player' ou 'banker')
                - 'recommendation': Recommandation faite ('player', 'banker', 'WAIT')
                - 'prediction': Prédiction du modèle ('player' ou 'banker')
                - 'round_num': <PERSON><PERSON><PERSON><PERSON> de la manche (optionnel)
        """
        if not training_data:
            self.logger.warning("Aucune donnée d'entraînement fournie.")
            return

        self.logger.info(f"Entraînement de l'optimiseur de placement WAIT avec {len(training_data)} échantillons.")

        # Réinitialiser les compteurs
        self.error_patterns = {}
        self.transition_patterns = {}
        self.pattern_history = []
        self.outcome_history = []
        self.recommendation_history = []

        # Analyser les séquences pour identifier les patterns d'erreur
        for i in range(len(training_data) - 1):
            current_sample = training_data[i]
            next_sample = training_data[i + 1]

            # Extraire les features et résultats
            features = current_sample.get('features', [])
            next_features = next_sample.get('features', [])

            # Créer une clé de pattern
            pattern_key = self._create_pattern_key(features)

            # Ajouter à l'historique
            self.pattern_history.append(pattern_key)
            self.outcome_history.append(current_sample.get('outcome', ''))
            self.recommendation_history.append(current_sample.get('recommendation', ''))

            # Vérifier si le prochain échantillon aurait été une erreur
            next_prediction = next_sample.get('prediction', '')
            next_outcome = next_sample.get('outcome', '')
            would_be_error = (next_prediction != next_outcome and next_prediction != 'WAIT')

            # Mettre à jour les patterns d'erreur
            if pattern_key not in self.error_patterns:
                self.error_patterns[pattern_key] = {'total': 0, 'errors': 0}

            self.error_patterns[pattern_key]['total'] += 1
            if would_be_error:
                self.error_patterns[pattern_key]['errors'] += 1

            # Analyser les transitions
            if i > 0:
                prev_sample = training_data[i - 1]
                prev_outcome = prev_sample.get('outcome', '')
                current_outcome = current_sample.get('outcome', '')

                if prev_outcome != current_outcome:
                    # C'est une transition
                    transition_key = f"{prev_outcome}_{current_outcome}"

                    if transition_key not in self.transition_patterns:
                        self.transition_patterns[transition_key] = {'total': 0, 'errors': 0}

                    self.transition_patterns[transition_key]['total'] += 1
                    if would_be_error:
                        self.transition_patterns[transition_key]['errors'] += 1

        # Limiter la taille des historiques
        if len(self.pattern_history) > self.max_pattern_history:
            self.pattern_history = self.pattern_history[-self.max_pattern_history:]
            self.outcome_history = self.outcome_history[-self.max_pattern_history:]
            self.recommendation_history = self.recommendation_history[-self.max_pattern_history:]

        # Analyser l'efficacité des WAIT
        wait_indices = [i for i, sample in enumerate(training_data)
                       if sample.get('recommendation', '') == 'WAIT' and i + 1 < len(training_data)]

        self.total_waits = len(wait_indices)
        self.effective_waits = 0
        self.missed_opportunities = 0

        for i in wait_indices:
            next_sample = training_data[i + 1]
            next_prediction = next_sample.get('prediction', '')
            next_outcome = next_sample.get('outcome', '')

            # Un WAIT est efficace si la prédiction suivante aurait été incorrecte
            if next_prediction != next_outcome and next_prediction != 'WAIT':
                self.effective_waits += 1
            else:
                self.missed_opportunities += 1

        # Calculer l'efficacité globale des WAIT
        wait_efficiency = self.effective_waits / self.total_waits if self.total_waits > 0 else 0
        self.wait_efficiency_history.append(wait_efficiency)

        # Calculer le ratio WAIT actuel
        wait_count = sum(1 for rec in self.recommendation_history if rec == 'WAIT')
        total_count = len(self.recommendation_history)
        self.current_wait_ratio = wait_count / total_count if total_count > 0 else 0

        # Adapter les seuils si nécessaire
        if self.adaptive_thresholds and len(training_data) > 50:
            self._adapt_thresholds()

        self.logger.info(f"Optimiseur de placement WAIT entraîné avec succès.")
        self.logger.info(f"Patterns d'erreur identifiés: {len(self.error_patterns)}")
        self.logger.info(f"Patterns de transition identifiés: {len(self.transition_patterns)}")
        self.logger.info(f"Efficacité des WAIT: {wait_efficiency:.4f} ({self.effective_waits}/{self.total_waits})")
        self.logger.info(f"Ratio WAIT actuel: {self.current_wait_ratio:.4f}")