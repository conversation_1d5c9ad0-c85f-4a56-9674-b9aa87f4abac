# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 955 à 1081
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def train_consecutive_confidence_calculator(self, X_lgbm=None, y_lgbm=None, train_indices=None, val_indices=None, training_data=None):
        """
        Entraîne le calculateur de confiance basé sur les séquences consécutives.

        Args:
            X_lgbm: Features LGBM (ignoré si training_data est fourni, peut être None)
            y_lgbm: Labels LGBM (ignoré si training_data est fourni, peut être None)
            train_indices: Indices d'entraînement (ignoré si training_data est fourni, peut être None)
            val_indices: Indices de validation (ignoré si training_data est fourni, peut être None)
            training_data: Données d'entraînement (si None, utilise les données historiques ou génère à partir de X_lgbm et y_lgbm)

        Returns:
            bool: True si l'entraînement a réussi, False sinon
        """
        logger.info("Entraînement du calculateur de confiance consécutive...")

        if training_data is None:
            # Si X_lgbm et y_lgbm sont fournis, générer les données d'entraînement à partir de ces données
            if X_lgbm is not None and y_lgbm is not None and isinstance(X_lgbm, (list, tuple, np.ndarray)) and len(X_lgbm) > 0:
                logger.info(f"Génération des données d'entraînement à partir de X_lgbm ({len(X_lgbm)} échantillons) et y_lgbm")
                training_data = []

                # Utiliser les indices d'entraînement si fournis, sinon utiliser tous les indices
                indices_to_use = train_indices if train_indices is not None else range(len(X_lgbm))

                for i in indices_to_use:
                    if i < len(X_lgbm) and i < len(y_lgbm) and X_lgbm[i] is not None and len(X_lgbm[i]) > 0:
                        features = X_lgbm[i]
                        actual_outcome = 'banker' if y_lgbm[i] == 0 else 'player'

                        # Simuler une prédiction et une confiance pour cet échantillon
                        # (dans un cas réel, on utiliserait le modèle pour prédire)
                        prediction = 'banker' if y_lgbm[i] == 0 else 'player'  # Utiliser la vraie valeur comme prédiction
                        confidence = 0.7  # Confiance fixe pour l'entraînement

                        # Déterminer si c'est une recommandation NON-WAIT
                        min_confidence = getattr(self.config, 'min_confidence_for_recommendation', 0.6) if hasattr(self, 'config') else 0.6
                        is_non_wait = confidence >= min_confidence

                        # Déterminer si la prédiction est valide
                        is_valid = True  # Toujours valide car nous utilisons la vraie valeur

                        # Créer l'échantillon d'entraînement
                        sample = {
                            'round_num': i + 1,
                            'features': features,
                            'outcome': actual_outcome,
                            'recommendation': prediction if is_non_wait else 'WAIT',
                            'is_valid': is_valid,
                            'confidence': confidence
                        }

                        training_data.append(sample)

                logger.info(f"Données d'entraînement générées: {len(training_data)} échantillons")
            # Sinon, utiliser les données historiques si disponibles
            elif hasattr(self, 'historical_data') and self.historical_data:
                logger.info("Utilisation des données historiques pour l'entraînement")
                training_data = []
            else:
                logger.warning("Aucune donnée disponible pour l'entraînement du calculateur de confiance.")
                return False

            for game_idx, game in enumerate(self.historical_data):
                # Ignorer les jeux trop courts
                if len(game) < 10:
                    continue

                # Simuler des prédictions pour chaque position dans le jeu
                for i in range(10, len(game)):
                    # Extraire la séquence jusqu'à la position actuelle
                    current_seq = game[:i]

                    # Extraire les features pour cette séquence
                    features = self._extract_lgbm_features(current_seq) if hasattr(self, '_extract_lgbm_features') else []

                    # Faire une prédiction
                    prediction = None
                    confidence = 0.5

                    if hasattr(self, 'lgbm_base') and self.lgbm_base is not None:
                        try:
                            # Utiliser le modèle LGBM pour prédire
                            if features is not None and len(features) > 0:
                                proba = self.lgbm_base.predict_proba([features])[0]
                                prediction = 'player' if proba[1] > 0.5 else 'banker'
                                confidence = max(proba)
                            else:
                                # Si les features sont vides, utiliser une prédiction aléatoire
                                prediction = None
                                confidence = 0.5
                        except Exception as e:
                            logger.error(f"Erreur lors de la prédiction LGBM: {e}")

                    # Si pas de prédiction LGBM, utiliser une prédiction aléatoire
                    if prediction is None:
                        prediction = random.choice(['player', 'banker'])
                        confidence = random.uniform(0.5, 0.7)

                    # Déterminer si c'est une recommandation NON-WAIT
                    min_confidence = getattr(self.config, 'min_confidence_for_recommendation', 0.6)
                    is_non_wait = confidence >= min_confidence

                    # Déterminer si la prédiction est valide
                    actual_outcome = game[i]
                    is_valid = prediction == actual_outcome

                    # Créer l'échantillon d'entraînement
                    sample = {
                        'round_num': i + 1,
                        'features': features,
                        'outcome': actual_outcome,
                        'recommendation': prediction if is_non_wait else 'WAIT',
                        'is_valid': is_valid,
                        'confidence': confidence
                    }

                    training_data.append(sample)

        # Entraîner le calculateur avec les données préparées
        if hasattr(self, 'consecutive_confidence_calculator'):
            self.consecutive_confidence_calculator.train(training_data)
            logger.info(f"Calculateur de confiance consécutive entraîné avec {len(training_data)} échantillons.")
            return True
        else:
            logger.warning("Calculateur de confiance consécutive non initialisé.")
            return False