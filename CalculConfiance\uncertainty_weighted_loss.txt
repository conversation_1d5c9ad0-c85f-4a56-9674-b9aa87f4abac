# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10172 à 10276
# Type: Méthode de la classe HybridBaccaratPredictor

    def uncertainty_weighted_loss(self, outputs, targets, weights=None, sequence_positions=None):
        """
        Fonction de perte personnalisée qui prend en compte l'incertitude et les positions de séquence.

        Cette fonction utilise CrossEntropyLoss comme base, mais pondère la perte
        en fonction des poids d'échantillons qui reflètent la confiance et l'incertitude,
        ainsi que les positions de séquence pour donner plus d'importance aux manches 31-60.

        IMPORTANT: Cette fonction utilise le système zero-based standard de PyTorch:
        - 0 = Player
        - 1 = Banker

        Args:
            outputs (torch.Tensor): Sorties du modèle (logits)
            targets (torch.Tensor): Labels cibles (0 = Player, 1 = Banker)
            weights (torch.Tensor, optional): Poids des échantillons
            sequence_positions (torch.Tensor, optional): Positions des échantillons dans la séquence

        Returns:
            torch.Tensor: Valeur de perte pondérée
        """
        # Utiliser CrossEntropyLoss comme base avec le système zero-based standard
        base_criterion = nn.CrossEntropyLoss(reduction='none')

        # Vérifier que les cibles sont bien des indices 0-based
        if targets.min() < 0 or targets.max() > 1:
            import logging
            logging.getLogger(__name__).warning(
                f"ATTENTION: Indices invalides détectés dans les cibles: min={targets.min().item()}, max={targets.max().item()}"
            )

        # Calculer la perte pour chaque échantillon
        per_sample_losses = base_criterion(outputs, targets)

        # Initialiser les poids combinés à 1.0
        combined_weights = torch.ones_like(per_sample_losses)

        # Si des poids d'échantillons sont fournis, les appliquer
        if weights is not None:
            # S'assurer que les poids ont la bonne forme
            if weights.dim() != per_sample_losses.dim():
                weights = weights.view(-1, 1)

            # Appliquer les poids d'échantillons
            combined_weights = combined_weights * weights

        # Si des positions de séquence sont fournies, les utiliser pour pondérer davantage les manches 31-60
        if sequence_positions is not None:
            # Paramètres configurables
            late_game_weight_factor = getattr(self.config, 'late_game_weight_factor', 5.0)
            target_round_min = getattr(self.config, 'target_round_min', 31)
            target_round_max = getattr(self.config, 'target_round_max', 60)

            # Convertir les positions 0-indexées en positions 1-indexées pour correspondre aux numéros de manches
            positions_1_indexed = sequence_positions + 1

            # Créer un masque pour les manches cibles (31-60)
            late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)

            # Créer un facteur de position
            position_factor = torch.ones_like(per_sample_losses)

            # Appliquer un poids progressif qui augmente à mesure qu'on approche de la manche 60
            if torch.any(late_game_mask):
                target_positions = positions_1_indexed[late_game_mask]
                normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)
                # Fonction qui augmente progressivement le poids (de 1.0 à late_game_weight_factor)
                progressive_weights = 1.0 + normalized_positions * (late_game_weight_factor - 1.0)
                position_factor[late_game_mask] = progressive_weights

                # Appliquer un poids supplémentaire pour les erreurs consécutives
                # Cela pénalise davantage les erreurs qui surviennent après d'autres erreurs
                # dans les manches cibles, encourageant des prédictions consécutives correctes
                _, predicted = torch.max(outputs, 1)
                errors = (predicted != targets).float()

                # Calculer les erreurs consécutives (uniquement pour les manches cibles)
                consecutive_errors = torch.zeros_like(errors)
                consecutive_count = 0

                for i in range(len(errors)):
                    if late_game_mask[i]:
                        if errors[i] > 0.5:  # C'est une erreur
                            consecutive_count += 1
                            # Plus l'erreur est consécutive, plus la pénalité est grande
                            consecutive_errors[i] = consecutive_count
                        else:
                            consecutive_count = 0

                # Normaliser et appliquer la pénalité pour les erreurs consécutives
                if torch.max(consecutive_errors) > 0:
                    consecutive_penalty = 1.0 + consecutive_errors * 0.5  # Augmente de 50% par erreur consécutive
                    position_factor = position_factor * consecutive_penalty
            else:
                # Si aucune position n'est dans la plage cible, utiliser le comportement par défaut
                position_factor[late_game_mask] = late_game_weight_factor

            # Combiner avec les poids existants
            combined_weights = combined_weights * position_factor

        # Appliquer les poids combinés
        weighted_losses = per_sample_losses * combined_weights

        # Retourner la moyenne des pertes pondérées
        return weighted_losses.mean()