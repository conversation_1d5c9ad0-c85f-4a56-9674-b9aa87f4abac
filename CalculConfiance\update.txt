# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 4058 à 4153
# Type: Méthode de la classe WaitPlacementOptimizer

    def update(self, features, prediction, recommendation, outcome, confidence, uncertainty):
        """
        Met à jour l'optimiseur avec les résultats d'une décision.

        Args:
            features (List[float]): Vecteur de features utilisé pour la prédiction
            prediction (str): Prédiction brute ('player' ou 'banker')
            recommendation (str): Recommandation finale ('player', 'banker' ou 'wait')
            outcome (str): R<PERSON>ultat réel ('player' ou 'banker')
            confidence (float): Niveau de confiance dans la prédiction
            uncertainty (float): Niveau d'incertitude dans la prédiction
        """
        # Extraire une clé de pattern à partir des features
        pattern_key = self._extract_pattern_key(features)

        # Mettre à jour les historiques
        self.recommendation_history.append(recommendation)
        self.prediction_history.append(prediction)
        self.outcome_history.append(outcome)
        self.confidence_history.append(confidence)
        self.uncertainty_history.append(uncertainty)

        # Limiter la taille des historiques
        if len(self.recommendation_history) > self.max_history_size:
            self.recommendation_history.pop(0)
            self.prediction_history.pop(0)
            self.outcome_history.pop(0)
            self.confidence_history.pop(0)
            self.uncertainty_history.pop(0)
            if len(self.wait_efficiency_history) > self.max_history_size:
                self.wait_efficiency_history.pop(0)

        # Incrémenter le compteur total de décisions
        self.total_decisions += 1

        # Mettre à jour les statistiques de séquences consécutives
        if recommendation != 'wait':
            # C'est une recommandation NON-WAIT
            is_correct = (recommendation == outcome)

            if is_correct:
                # Recommandation NON-WAIT correcte
                self.current_consecutive_valid += 1
                self.correct_non_wait_decisions += 1

                # Mettre à jour le maximum de recommandations consécutives valides
                if self.current_consecutive_valid > self.max_consecutive_valid:
                    self.max_consecutive_valid = self.current_consecutive_valid
            else:
                # Recommandation NON-WAIT incorrecte - réinitialiser le compteur
                self.current_consecutive_valid = 0
        else:
            # C'est une recommandation WAIT
            self.total_waits += 1

            # Un WAIT est efficace si la prédiction aurait été incorrecte
            if prediction != outcome:
                self.effective_waits += 1
                self.correct_wait_decisions += 1
            else:
                self.missed_opportunities += 1

        # Calculer l'efficacité des WAIT
        wait_efficiency = self.effective_waits / self.total_waits if self.total_waits > 0 else 0
        self.wait_efficiency_history.append(wait_efficiency)

        # Calculer le ratio WAIT actuel
        wait_count = sum(1 for rec in self.recommendation_history if rec == 'wait')
        total_count = len(self.recommendation_history)
        self.current_wait_ratio = wait_count / total_count if total_count > 0 else 0

        # Mettre à jour les patterns d'erreur
        if pattern_key not in self.error_patterns:
            self.error_patterns[pattern_key] = {'total': 0, 'errors': 0}

        self.error_patterns[pattern_key]['total'] += 1
        if prediction != outcome:
            self.error_patterns[pattern_key]['errors'] += 1

        # Mettre à jour les patterns de transition
        if len(self.outcome_history) >= 2:
            last_outcomes = self.outcome_history[-2:]
            if last_outcomes[0] != last_outcomes[1]:
                # C'est une transition
                transition_key = f"{last_outcomes[0]}_{last_outcomes[1]}"

                if transition_key not in self.transition_patterns:
                    self.transition_patterns[transition_key] = {'total': 0, 'errors': 0}

                self.transition_patterns[transition_key]['total'] += 1
                if prediction != outcome:
                    self.transition_patterns[transition_key]['errors'] += 1

        # Adapter les seuils si nécessaire
        if self.adaptive_thresholds and self.total_decisions % 50 == 0:
            self._adapt_thresholds()