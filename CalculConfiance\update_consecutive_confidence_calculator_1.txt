# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10443 à 10544
# Type: Méthode de la classe HybridBaccaratPredictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def update_consecutive_confidence_calculator(self, round_num: int, recommendation: str, actual_outcome: str) -> None:
        """
        Met à jour le calculateur de confiance consécutive pour les recommandations NON-WAIT.
        Cette méthode est utilisée pour suivre les recommandations NON-WAIT valides consécutives,
        particulièrement pour les manches 31-60.

        Args:
            round_num (int): Numéro de la manche actuelle
            recommendation (str): Recommandation faite ('player', 'banker', 'wait')
            actual_outcome (str): R<PERSON>ultat réel ('player', 'banker')
        """
        # Vérifier si nous sommes dans la plage de manches cibles (31-60)
        target_round_min = getattr(self.config, 'target_round_min', 31)
        target_round_max = getattr(self.config, 'target_round_max', 60)
        is_target_round = target_round_min <= round_num <= target_round_max

        # Initialiser les attributs s'ils n'existent pas encore
        if not hasattr(self, 'total_nonwait'):
            self.total_nonwait = 0
            self.total_nonwait_valid = 0
            self.total_wait = 0
            self.consecutive_nonwait_valid = 0
            self.max_consecutive_nonwait_valid = 0
            logger.info("Compteurs de recommandations consécutives initialisés")

        # Mettre à jour les compteurs
        if recommendation != 'wait':
            self.total_nonwait += 1
            # Vérifier si la recommandation était correcte
            # Convertir en minuscules pour assurer la cohérence
            recommendation_lower = recommendation.lower()
            actual_outcome_lower = actual_outcome.lower()
            is_valid = (recommendation_lower == 'player' and actual_outcome_lower == 'player') or \
                       (recommendation_lower == 'banker' and actual_outcome_lower == 'banker')

            if is_valid:
                self.total_nonwait_valid += 1
                self.consecutive_nonwait_valid += 1

                # Mettre à jour le maximum de recommandations NON-WAIT valides consécutives
                self.max_consecutive_nonwait_valid = max(
                    self.max_consecutive_nonwait_valid,
                    self.consecutive_nonwait_valid
                )

                logger.debug(f"Manche {round_num}: Recommandation NON-WAIT valide. Séquence consécutive: {self.consecutive_nonwait_valid}")
            else:
                # Réinitialiser le compteur de recommandations NON-WAIT valides consécutives
                logger.debug(f"Manche {round_num}: Recommandation NON-WAIT invalide. Réinitialisation séquence (était: {self.consecutive_nonwait_valid})")
                self.consecutive_nonwait_valid = 0
        else:  # Recommandation WAIT
            self.total_wait += 1
            # Ne pas réinitialiser le compteur de recommandations NON-WAIT valides consécutives
            # car les recommandations WAIT n'interrompent pas la séquence
            logger.debug(f"Manche {round_num}: Recommandation WAIT. Séquence consécutive maintenue: {self.consecutive_nonwait_valid}")

        # Loguer des statistiques supplémentaires si nous sommes dans la plage cible
        if is_target_round:
            total_rounds = self.total_nonwait + self.total_wait
            nonwait_ratio = self.total_nonwait / total_rounds if total_rounds > 0 else 0
            valid_ratio = self.total_nonwait_valid / self.total_nonwait if self.total_nonwait > 0 else 0

            logger.info(f"Stats manches {target_round_min}-{target_round_max}: NON-WAIT: {self.total_nonwait}/{total_rounds} ({nonwait_ratio:.2%}), "
                       f"Valides: {self.total_nonwait_valid}/{self.total_nonwait} ({valid_ratio:.2%}), "
                       f"Max consécutives: {self.max_consecutive_nonwait_valid}")

            # Mettre à jour le calculateur de confiance consécutive avec les nouvelles données
            if hasattr(self, 'consecutive_confidence_calculator') and self.consecutive_confidence_calculator:
                try:
                    # Extraire les features pour le calculateur
                    features_vector = self._extract_features_for_consecutive_calculator()

                    # Mettre à jour les données récentes
                    self.consecutive_confidence_calculator.update_recent_data(
                        recommendation=recommendation,
                        outcome=actual_outcome
                    )

                    # Si nous avons un pattern valide, l'ajouter aux statistiques
                    if recommendation != 'wait':
                        pattern_key = self.consecutive_confidence_calculator._extract_pattern_key(features_vector)
                        pattern_stats = self.consecutive_confidence_calculator.pattern_stats[pattern_key]

                        # Incrémenter le compteur total
                        pattern_stats["total"] += 1

                        # Si la recommandation était valide, incrémenter le compteur de succès
                        if is_valid:
                            pattern_stats["success"] += 1

                            # Ajouter la longueur de la séquence consécutive actuelle
                            pattern_stats["consecutive_lengths"].append(self.consecutive_nonwait_valid)

                            # Mettre à jour la longueur maximale
                            pattern_stats["max_consecutive"] = max(
                                pattern_stats.get("max_consecutive", 0),
                                self.consecutive_nonwait_valid
                            )

                    logger.debug(f"Calculateur de confiance consécutive mis à jour pour la manche {round_num}")
                except Exception as e_update:
                    logger.warning(f"Erreur lors de la mise à jour du calculateur de confiance consécutive: {e_update}")