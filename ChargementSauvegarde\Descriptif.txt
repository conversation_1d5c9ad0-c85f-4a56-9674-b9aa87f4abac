DESCRIPTIF DES MÉTHODES - DOSSIER CHARGEMENTSAUVEGARDE
======================================================

Ce dossier contient toutes les méthodes liées au chargement et à la sauvegarde des modèles, données historiques, paramètres et états du système.

MÉTHODES ANALYSÉES :

1. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data)
   - Lignes 4831-4886 dans hbp.py
   - FONCTION : Charge les données historiques depuis un fichier .txt
   - FONCTIONNEMENT :
     * Vérifie qu'aucun entraînement n'est en cours
     * Ouvre une boîte de dialogue pour sélectionner le fichier historique
     * Utilise _load_historical_txt pour le chargement interne
     * Affiche les statistiques de chargement (parties valides, longueur moyenne, total coups)
     * Met à jour les modèles <PERSON>ov globaux
     * Propose de réinitialiser la session en cours
     * Gestion d'erreurs avec messages informatifs
   - RETOUR : None (interface utilisateur)
   - UTILITÉ : Interface utilisateur pour charger l'historique de parties depuis un fichier

2. unified_save.txt (HybridBaccaratPredictor.unified_save)
   - Lignes 5004-5053 dans hbp.py
   - FONCTION : Sauvegarde manuelle de l'état actuel via une boîte de dialogue
   - FONCTIONNEMENT :
     * Vérifie qu'aucun processus ML n'est en cours
     * Ouvre une boîte de dialogue pour choisir l'emplacement de sauvegarde
     * Propose .joblib comme format par défaut, autorise aussi .pkl
     * Génère un nom de fichier avec timestamp
     * Utilise _perform_save pour la logique de sauvegarde réelle
     * Ajoute automatiquement l'extension si manquante
     * Affiche les résultats avec messages informatifs
   - RETOUR : None (interface utilisateur)
   - UTILITÉ : Interface utilisateur pour sauvegarder manuellement l'état complet du prédicteur

3. save_optimized_models.txt (HybridBaccaratPredictor.save_optimized_models)
   - Lignes 4905-5002 dans hbp.py
   - FONCTION : Sauvegarde les modèles entraînés avec les hyperparamètres optimisés
   - PARAMÈTRES : params_file_path (str) - chemin vers le fichier JSON des paramètres optimisés
   - FONCTIONNEMENT :
     * Charge les paramètres optimisés depuis le fichier JSON
     * Crée une configuration optimisée avec apply_params_to_config
     * Sauvegarde la configuration optimisée en JSON
     * Applique temporairement la configuration optimisée
     * Sauvegarde les modèles avec _perform_save
     * Restaure la configuration originale
     * Génère un nom de fichier avec le score et timestamp
   - RETOUR : bool - True si succès, False sinon
   - UTILITÉ : Sauvegarde spécialisée pour les modèles optimisés par Optuna

AUTRES MÉTHODES IMPORTANTES :

4. _find_latest_state_file.txt (HybridBaccaratPredictor._find_latest_state_file)
   - Lignes 11124-11155 dans hbp.py
   - FONCTION : Recherche le fichier d'état (.joblib ou .pkl) le plus récent
   - PARAMÈTRES : save_dir (str) - répertoire de sauvegarde
   - FONCTIONNEMENT :
     * Vérifie l'existence du répertoire de sauvegarde
     * Liste tous les fichiers .joblib et .pkl du répertoire
     * Récupère la date de modification de chaque fichier
     * Trie par date de modification (plus récent en premier)
     * Gestion robuste des erreurs d'accès fichiers
   - RETOUR : Tuple[Optional[str], Optional[str]] - (chemin_fichier, extension)
   - UTILITÉ : Identifie automatiquement le dernier état sauvegardé pour chargement

5. _load_historical_txt.txt (HybridBaccaratPredictor._load_historical_txt)
   - Lignes 11157-11238 dans hbp.py
   - FONCTION : Logique interne de chargement des données historiques depuis fichier .txt
   - PARAMÈTRES : filepath (str) - chemin vers le fichier historique
   - FONCTIONNEMENT :
     * Parse chaque ligne du fichier avec gestion d'encodage UTF-8
     * Convertit les symboles (b/banker/1→'banker', p/player/0→'player')
     * Ignore les TIEs et symboles invalides avec logging détaillé
     * Met à jour historical_data, loaded_historical, historical_games_at_startup_or_reset
     * Utilise verrous (sequence_lock, markov_lock) pour cohérence multi-thread
     * Met à jour le modèle Markov global avec nouvelles données
     * Compte parties valides, courtes, lignes traitées pour statistiques
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Cœur du système de chargement historique avec parsing robuste

6. _load_latest_state.txt (HybridBaccaratPredictor._load_latest_state)
   - Lignes 10992-11122 dans hbp.py
   - FONCTION : Charge automatiquement le dernier état (.joblib ou .pkl) au démarrage
   - FONCTIONNEMENT :
     * Recherche le fichier d'état le plus récent dans MODEL_SAVE_DIR
     * Priorité .joblib (nécessite historical_data.txt) puis fallback .pkl
     * Pour .joblib : charge d'abord l'historique puis l'état des modèles
     * Pour .pkl : charge directement l'état (historique inclus)
     * Gestion progressive avec barre de progression UI
     * Si échec : initialise session vide avec modèles par défaut (pas de hard reset)
     * Met à jour l'affichage avec statut final (succès vert ou erreur)
   - RETOUR : None (met à jour l'état interne)
   - UTILITÉ : Automatise la restauration de l'état précédent au démarrage

6bis. _load_latest_state_1.txt (HybridBaccaratPredictor._load_latest_state - version 2)
   - Lignes 12214-12342 dans hbp.py (128 lignes)
   - FONCTION : Version modifiée du chargement automatique d'état (pas de hard reset)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RECHERCHE :** _find_latest_state_file(MODEL_SAVE_DIR)
     * **SI AUCUN ÉTAT :** init_ml_models(reset_weights=True) + reset_data('soft')
     * **CHARGEMENT .JOBLIB :**
       - Vérifie existence historical_data.txt
       - _load_historical_txt() puis load_trained_models()
       - Si succès : _reset_session_display() + _update_weights_display()
     * **FALLBACK .PKL :** si .joblib échoue ou .pkl plus récent
     * **RÉINITIALISATION VISUELLE :** root.after() pour UI thread-safe
     * **PROGRESSION :** messages détaillés selon étapes et résultats
     * **ÉCHEC TOTAL :** init_ml_models() + reset_data('soft') sans hard reset
   - RETOUR : None
   - UTILITÉ : Version améliorée évitant hard reset avec gestion visuelle optimisée
7. _load_selected_model.txt (HybridBaccaratPredictor._load_selected_model)
   - Lignes 3520-3556 dans hbp.py
   - FONCTION : Charge un modèle spécifique sélectionné depuis le tableau de bord
   - PARAMÈTRES : tree (ttk.Treeview) - widget contenant la liste des modèles
   - FONCTIONNEMENT :
     * Vérifie qu'un élément est sélectionné dans le Treeview
     * Récupère le nom du fichier depuis les valeurs de l'élément sélectionné
     * Construit le chemin complet vers le modèle dans le dossier "models"
     * Vérifie l'existence du fichier modèle
     * Demande confirmation utilisateur avant chargement
     * Appelle load_trained_models() pour charger le modèle sélectionné
   - RETOUR : None
   - UTILITÉ : Interface utilisateur pour charger un modèle spécifique depuis le tableau de bord
8. _perform_save.txt (HybridBaccaratPredictor._perform_save)
   - Lignes 1797-2019 dans hbp.py
   - FONCTION : Logique interne de sauvegarde de l'état complet via joblib
   - PARAMÈTRES : filepath (str, optionnel) - chemin de sauvegarde
   - FONCTIONNEMENT :
     * Génère automatiquement un nom de fichier informatif si filepath=None
     * Acquiert tous les verrous (sequence, model, markov, weights) pour cohérence
     * Vérifie que les modèles essentiels sont 'fit' avant sauvegarde
     * Construit un package complet : modèles ML, état Markov, LSTM state_dict
     * Inclut métadonnées, configuration, hyperparamètres, historique session
     * Sauvegarde via joblib avec compression=3
     * Sauvegarde métadonnées associées en JSON
     * Gestion robuste des erreurs (validation, sérialisation, I/O)
     * Libération sécurisée des verrous en finally
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Cœur du système de persistance avec package complet et cohérent
9. _save_lgbm_training_plots.txt (HybridBaccaratPredictor._save_lgbm_training_plots)
   - Lignes 6716-6845 dans hbp.py
   - FONCTION : Crée et sauvegarde des graphiques des métriques d'entraînement LGBM
   - PARAMÈTRES : eval_result (Dict) - résultats d'évaluation LGBM
   - FONCTIONNEMENT :
     * Force l'utilisation du backend matplotlib 'Agg' (non-interactif)
     * Crée le dossier "logs/training_plots" si nécessaire
     * Génère timestamp pour noms de fichiers uniques
     * Crée graphiques pour chaque métrique (train/validation curves)
     * Graphique d'importance des features (top 20, barres horizontales)
     * Matrice de confusion avec valeurs dans cellules
     * Courbe ROC avec score AUC si disponible
     * Sauvegarde tous les graphiques en PNG avec timestamp
   - RETOUR : None
   - UTILITÉ : Visualisation et archivage des performances d'entraînement LGBM
10. _save_lstm_training_plots.txt (HybridBaccaratPredictor._save_lstm_training_plots)
   - Lignes 6847-6953 dans hbp.py
   - FONCTION : Crée et sauvegarde des graphiques des métriques d'entraînement LSTM
   - FONCTIONNEMENT :
     * Force l'utilisation du backend matplotlib 'Agg' (non-interactif)
     * Vérifie la disponibilité des métriques LSTM
     * Crée le dossier "logs/training_plots" si nécessaire
     * Génère timestamp pour noms de fichiers uniques
     * Graphique des pertes (train/validation loss par époque)
     * Graphique des exactitudes (train/validation accuracy par époque)
     * Matrice de confusion avec valeurs dans cellules
     * Graphique des métriques de classification (precision, recall, f1, accuracy)
     * Sauvegarde tous les graphiques en PNG avec timestamp
   - RETOUR : None
   - UTILITÉ : Visualisation et archivage des performances d'entraînement LSTM
11. _save_model_metadata.txt (HybridBaccaratPredictor._save_model_metadata)
   - Lignes 2359-2461 dans hbp.py
   - FONCTION : Sauvegarde les métadonnées du modèle dans un fichier JSON associé
   - PARAMÈTRES : model_filepath (str), package (Dict[str, Any])
   - FONCTIONNEMENT :
     * Extrait les informations pertinentes du package de sauvegarde
     * Calcule les métriques de performance (best_accuracy, method_performance)
     * Crée un dictionnaire complet avec timestamp, type de modèle, version
     * Inclut tous les hyperparamètres (LSTM, LGBM, Markov, seuils)
     * Ajoute les poids actuels et optimaux des modèles
     * Inclut les informations d'entraînement (temps, phase d'optimisation)
     * Filtre les valeurs None pour un JSON propre
     * Sauvegarde en JSON avec même nom de base que le modèle
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Documentation complète des modèles pour traçabilité et analyse
12. _save_params_to_file.txt (HybridBaccaratPredictor._save_params_to_file)
   - Lignes 2333-2357 dans hbp.py
   - FONCTION : Sauvegarde les paramètres optimisés dans un fichier params.txt
   - PARAMÈTRES : params (Dict[str, Any]) - dictionnaire des paramètres à sauvegarder
   - FONCTIONNEMENT :
     * Vérifie la validité des paramètres (non vide, type dict)
     * Crée le fichier "params.txt" avec encodage UTF-8
     * Sauvegarde en JSON avec indentation et tri des clés
     * Log du nombre de paramètres sauvegardés
     * Gestion d'erreurs avec logging détaillé
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Sauvegarde simple des paramètres optimisés pour réutilisation
13. _save_state_to_models_dir.txt (HybridBaccaratPredictor._save_state_to_models_dir)
   - Lignes 10966-10990 dans hbp.py
   - FONCTION : Sauvegarde automatiquement l'état actuel dans MODEL_SAVE_DIR
   - FONCTIONNEMENT :
     * Crée le répertoire MODEL_SAVE_DIR si nécessaire
     * Génère un timestamp pour nom de fichier unique
     * Crée nom de fichier : "predictor_state_{timestamp}.joblib"
     * Construit le chemin complet dans le répertoire de sauvegarde
     * Appelle _perform_save() pour la logique interne de sauvegarde
     * Gestion d'erreurs OSError (création dossier) et exceptions générales
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Interface simplifiée pour sauvegarde automatique avec timestamp
14. load_optimized_models.txt (HybridBaccaratPredictor.load_optimized_models)
   - Lignes 2212-2331 dans hbp.py
   - FONCTION : Charge les modèles entraînés avec les hyperparamètres optimisés (post-Optuna)
   - PARAMÈTRES : params_file_path (str, optionnel) - chemin vers fichier JSON des paramètres
   - FONCTIONNEMENT :
     * Vérifie qu'aucun entraînement n'est en cours
     * Ouvre dialogue de sélection si params_file_path=None
     * Charge les paramètres optimisés depuis fichier JSON
     * Si models_path existe : charge directement les modèles pré-entraînés
     * Sinon : applique paramètres à la configuration et réinitialise modèles
     * Utilise apply_params_to_config() pour mise à jour configuration
     * Informe utilisateur du statut (modèles prêts ou entraînement requis)
     * Gestion complète des erreurs avec messages UI appropriés
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Interface principale pour utiliser les résultats d'optimisation Optuna
15. load_optimized_params.txt (HybridBaccaratPredictor.load_optimized_params)
   - Lignes 2172-2210 dans hbp.py
   - FONCTION : Charge les paramètres optimisés depuis params.txt et les applique à la configuration
   - FONCTIONNEMENT :
     * Utilise load_params_from_file() pour charger depuis "params.txt"
     * Applique les paramètres via apply_params_to_config()
     * Sépare les paramètres de poids (weight_*) des autres paramètres
     * Affiche message de succès détaillé avec comptage des paramètres
     * Gestion d'erreurs avec messages UI appropriés
   - RETOUR : None (met à jour la configuration)
   - UTILITÉ : Interface simple pour appliquer les paramètres optimisés sauvegardés
16. load_trained_models.txt (HybridBaccaratPredictor.load_trained_models)
   - Lignes 3558-4076 dans hbp.py (518 lignes)
   - FONCTION : Charge l'état complet des modèles entraînés depuis fichier .joblib/.pkl
   - PARAMÈTRES : filepath (Optional[str]) - chemin du fichier, dialogue si None
   - FONCTIONNEMENT :
     * Définit flag _loading_existing_model pour adaptation longueur séquence LSTM
     * Acquiert tous les verrous pour chargement thread-safe
     * Charge via joblib puis fallback pickle si échec
     * Valide et charge : feature_scaler, lgbm_base, calibrated_lgbm, lgbm_uncertainty
     * Synchronise paramètres LGBM avec configuration
     * Recrée modèle LSTM avec paramètres du fichier, adapte dimensions si nécessaire
     * Charge état Markov et synchronise paramètres
     * Restaure état session : sequence, prediction_history, weights, method_performance
     * Détecte incohérences hyperparamètres et propose résolution à l'utilisateur
     * Gestion exhaustive d'erreurs avec messages UI détaillés
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Fonction centrale de restauration complète de l'état du prédicteur
17. save_optimization_report.txt (HybridBaccaratPredictor.save_optimization_report)
   - Lignes 1090-1119 dans hbp.py
   - FONCTION : Sauvegarde le rapport d'optimisation dans un fichier
   - PARAMÈTRES : report (str), study_name (str, optionnel)
   - FONCTIONNEMENT :
     * Crée le dossier "optimization_reports" si nécessaire
     * Génère timestamp pour nom de fichier unique
     * Construit nom : "optimization_report{_study_name}_{timestamp}.txt"
     * Sauvegarde le rapport en UTF-8
     * Gestion d'erreurs avec logging détaillé
   - RETOUR : str - chemin du fichier créé, None si erreur
   - UTILITÉ : Archive les rapports d'optimisation Optuna pour analyse ultérieure

18. _load_all_historical_data.txt (OptunaOptimizer._load_all_historical_data - MÉTHODE CHARGEMENT DONNÉES HISTORIQUES COMPLEXE)
   - Lignes 7496-7741 dans optuna_optimizer.py (246 lignes)
   - FONCTION : Chargement optimisé des données historiques avec cache avancé, traitement parallèle et échantillonnage stratifié
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * sample_percentage (float, défaut=0.10) - Pourcentage des données à échantillonner (forcé à 10%)
     * is_viability_check (bool, défaut=False) - Indique si c'est une vérification de viabilité (ignoré)
     * use_stratified (bool, défaut=True) - Utiliser échantillonnage stratifié au lieu d'aléatoire
     * use_parallel (bool, défaut=True) - Utiliser traitement parallèle pour accélération
     * max_cache_size_gb (float, défaut=5) - Taille maximale du cache en Go
     * force_sequential (bool, défaut=False) - Forcer traitement séquentiel même si parallèle activé
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FORÇAGE ÉCHANTILLONNAGE :** Force sample_percentage à 10% pour garantir utilisation optimale
     * **ANALYSE TAILLE FICHIER :** Mesure taille historical_data.txt en MB/GB pour stratégie adaptative
     * **CACHE INTELLIGENT :** Vérifie existence _cached_historical_data pour éviter rechargements
     * **ÉCHANTILLONNAGE CACHE :** Applique échantillonnage sur données en cache si disponibles
     * **ÉCHANTILLONNAGE STRATIFIÉ :** Utilise _stratified_sampling() pour distribution représentative
     * **CONVERSION INDICES :** Utilise _convert_indices_to_sequences() pour séquences complètes
     * **ÉCHANTILLONNAGE ALÉATOIRE :** Fallback vers random.sample() si stratifié non demandé
     * **DÉTECTION FICHIER :** Vérifie existence historical_data.txt avec gestion erreurs
     * **ANALYSE VOLUMÉTRIE :** Calcule taille en bytes/GB pour décisions de traitement
     * **MODE STREAMING :** Bascule vers _load_historical_data_streaming() pour gros fichiers
     * **TRAITEMENT PARALLÈLE :** Utilise ProcessPoolExecutor avec 8 cœurs maximum
     * **MÉTHODE STATIQUE :** Utilise _process_line_static() pour éviter problèmes sérialisation
     * **PROGRESSION DÉTAILLÉE :** Affiche progression par tranches de 10% avec compteurs
     * **GESTION ERREURS PARALLÈLE :** Try/catch robuste avec fallback séquentiel automatique
     * **TRAITEMENT SÉQUENTIEL :** Mode de secours avec progression et gestion erreurs
     * **MESURE PERFORMANCE :** Chronomètre temps de chargement avec logging détaillé
     * **ESTIMATION MÉMOIRE :** Calcule taille estimée en mémoire avec sys.getsizeof()
     * **CACHE CONDITIONNEL :** Met en cache uniquement si taille < max_cache_size_gb
     * **LOGGING INTELLIGENT :** Utilise attributs de classe pour éviter logs répétitifs
     * **SÉQUENCES COMPLÈTES :** Conserve séquence 1-60 avec identification manches 31-60
     * **VALIDATION RÉSULTATS :** Compte séquences valides vs total lignes traitées
     * **GESTION EXCEPTIONS :** Try/except global avec traceback détaillé pour débogage
   - RETOUR : List[Dict] - Liste de dictionnaires avec full_sequence, target_sequence, sequence_data
   - UTILITÉ : Chargement haute performance des données historiques avec optimisations mémoire et parallélisme

19. _load_historical_data_streaming.txt (OptunaOptimizer._load_historical_data_streaming - MÉTHODE CHARGEMENT STREAMING)
   - Lignes 7743-7859 dans optuna_optimizer.py (117 lignes)
   - FONCTION : Chargement données historiques en mode streaming pour économiser mémoire sur gros fichiers
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * file_path - Chemin du fichier historical_data.txt
     * sample_percentage (float, optionnel) - Pourcentage des données à échantillonner
     * use_stratified (bool, défaut=True) - Utiliser échantillonnage stratifié
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTAGE LIGNES :** Première passe pour compter total_lines dans le fichier
     * **CALCUL ÉCHANTILLON :** Calcule sample_size = total_lines * sample_percentage
     * **SÉLECTION ALÉATOIRE :** Utilise random.sample() pour sélectionner indices lignes à traiter
     * **TRAITEMENT STREAMING :** Lit fichier ligne par ligne sans charger tout en mémoire
     * **FILTRAGE INDICES :** Traite uniquement lignes avec line_idx in selected_indices
     * **VALIDATION SÉQUENCES :** Vérifie len(full_sequence) >= 60 pour séquences complètes
     * **EXTRACTION MANCHES :** Sépare manches 1-30 (training) et 31-60 (target)
     * **CONVERSION FORMAT :** Convertit P/B vers player/banker en lowercase standard
     * **STRUCTURE DONNÉES :** Crée dictionnaires avec training_sequence, target_sequence, sequence_data
     * **PROGRESSION AFFICHAGE :** Log progression tous les 1000 séquences valides
     * **ÉCHANTILLONNAGE STRATIFIÉ :** Applique _stratified_sampling() sur données déjà échantillonnées
     * **CONVERSION INDICES :** Utilise _convert_indices_to_sequences() pour séquences finales
     * **GESTION ERREURS :** Try/catch par ligne avec logging détaillé des erreurs
     * **OPTIMISATION MÉMOIRE :** Évite chargement complet fichier en mémoire
   - RETOUR : List[Dict] - Liste dictionnaires avec training_sequence, target_sequence, sequence_data
   - UTILITÉ : Chargement efficace gros fichiers historiques avec contrôle mémoire et échantillonnage

RÉSUMÉ : Ce dossier centralise toutes les opérations de persistance des données, incluant le chargement/sauvegarde des modèles, paramètres, données historiques, et états du système. Il gère les formats joblib/pickle et assure la cohérence des données entre les sessions.
