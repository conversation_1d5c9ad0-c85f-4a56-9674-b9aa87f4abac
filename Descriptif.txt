DESCRIPTIF DÉTAILLÉ DES MÉTHODES - SYSTÈME ML BACCARAT
================================================================================

Ce fichier contient la description détaillée de toutes les méthodes du système
ML de prédiction Baccarat (hbp.py), organisées par sections fonctionnelles.

STRUCTURE DU SYSTÈME (basée sur l'analyse architecturale) :
- **CalculConfiance** : Méthodes de calcul de confiance et d'incertitude
- **ReseauxNeuronaux** : Méthodes LSTM et modèles de deep learning
- **OptimisationEntrainement** : Méthodes d'optimisation Optuna et d'entraînement
- **GestionDonnees** : Chargement, préparation et gestion des données
- **InterfaceUtilisateur** : Configuration UI et affichage
- **EvaluationMetriques** : Méthodes d'évaluation et de métriques
- **UtilitairesFonctions** : Fonctions utilitaires et helpers
- **anciennesclasses** : Définitions de classes complètes

TOTAL : [À COMPLÉTER] MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
SECTION 1 : CALCULCONFIANCE (4 MÉTHODES)
================================================================================

Méthodes dédiées au calcul de confiance, d'incertitude et de recommandations
basées sur l'analyse des patterns et séquences consécutives.

1. __init___1.txt (ConsecutiveConfidenceCalculator.__init__ - DOUBLON - Constructeur du calculateur de confiance)
   - Lignes 1423-1427 dans hbp.py (5 lignes)
   - FONCTION : Initialise une instance de ConsecutiveConfidenceCalculator pour le calcul de confiance basé sur les patterns consécutifs
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION STATISTIQUES :** Crée un dictionnaire par défaut pour stocker les statistiques de patterns
     * **STRUCTURE PATTERN_STATS :** Chaque pattern contient total, success, consecutive_lengths et max_consecutive
     * **HISTORIQUE RÉCENT :** Initialise les listes pour les recommandations et résultats récents
     * **CONFIGURATION LIMITE :** Définit la taille maximale de l'historique récent (défaut 50)
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Prépare le calculateur pour analyser les patterns de séquences consécutives et calculer la confiance des recommandations

2. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence - Calcul de confiance avancé)
   - Lignes 1486-1579 dans hbp.py (94 lignes)
   - FONCTION : Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle en analysant les patterns et facteurs multiples
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features_vector (List[float]) - Vecteur de features pour la manche actuelle
     * current_round (int) - Numéro de la manche actuelle
     * config - Configuration du prédicteur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PLAGE :** Vérifie si la manche est dans la plage cible (target_round_min à target_round_max)
     * **EXTRACTION PATTERN :** Extrait la clé de pattern à partir du vecteur de features
     * **STATISTIQUES PATTERN :** Récupère les statistiques historiques pour ce pattern spécifique
     * **CALCUL CONFIANCE BASE :** Combine taux de succès (70%) et longueur moyenne consécutive (30%)
     * **BONUS COURBE CLOCHE :** Applique un bonus pour les manches au milieu de la plage cible
     * **FACTEURS MULTIPLICATIFS :** Calcule sequence_bonus, late_game_factor, occurrence_factor, consecutive_factor
     * **AJUSTEMENT FINAL :** Multiplie la confiance par tous les facteurs et limite entre 0 et 1
   - RETOUR : Dict - Dictionnaire contenant confidence, expected_consecutive, success_rate, et tous les facteurs calculés
   - UTILITÉ : Fournit une évaluation sophistiquée de la confiance pour optimiser les recommandations de mise

3. _analyze_sequence_context.txt (HybridBaccaratPredictor._analyze_sequence_context - Analyse contextuelle de séquence)
   - Lignes 13941-13979 dans hbp.py (39 lignes)
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles en fonction de la volatilité et des patterns
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LONGUEUR :** Vérifie qu'il y a au moins 10 éléments pour une analyse fiable
     * **CALCUL VOLATILITÉ :** Analyse les alternances dans les 10 derniers coups pour mesurer l'instabilité
     * **DÉTECTION STREAKS :** Identifie la longueur du streak actuel en remontant la séquence
     * **NORMALISATION STREAK :** Normalise le facteur de streak entre 0 et 1 (max 10 coups)
     * **COMBINAISON FACTEURS :** Combine volatilité (70%) et facteur de streak (30%)
     * **INTERPRÉTATION :** Proche de 0 = séquence stable, proche de 1 = séquence volatile
   - RETOUR : float - Facteur contextuel entre 0 et 1 indiquant la pertinence du modèle de session vs global
   - UTILITÉ : Permet d'adapter dynamiquement les poids des modèles selon le contexte de jeu actuel

4. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty - Calcul d'incertitude par variance)
   - Lignes 4386-4547 dans hbp.py (162 lignes)
   - FONCTION : Calcule un score d'incertitude basé sur la variance des prédictions des estimateurs du BaggingClassifier LGBM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * features (Optional[List[float]]) - Liste des features d'entrée
     * predicted_class (Optional[int]) - Classe prédite (0=PLAYER, 1=BANKER), optionnel
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURES :** Vérifie que features n'est pas None et a la bonne longueur
     * **INITIALISATION MODÈLE :** Tente d'initialiser lgbm_uncertainty si nécessaire avec protection récursion
     * **VÉRIFICATIONS MODÈLE :** Contrôle que le modèle et scaler sont initialisés et fittés
     * **NORMALISATION :** Applique feature_scaler.transform avec gestion d'erreurs complète
     * **PRÉDICTIONS MULTIPLES :** Collecte les prédictions de chaque estimateur du BaggingClassifier
     * **GESTION CLASSES :** Trouve l'index de la classe Banker avec cache optimisé
     * **CALCUL VARIANCE :** Calcule la variance des probabilités Banker entre estimateurs
     * **NORMALISATION SCORE :** Applique facteur de normalisation réduit et clipping [0,1]
   - RETOUR : float - Score d'incertitude entre 0 et 1 (0.5 par défaut en cas d'erreur)
   - UTILITÉ : Fournit une mesure d'incertitude épistémique pour évaluer la fiabilité des prédictions

================================================================================
SECTION 2 : RESEAUXNEURONAUX (3 MÉTHODES)
================================================================================

Méthodes relatives aux réseaux de neurones LSTM, prédictions et modèles
de deep learning pour l'analyse séquentielle.

1. predict_with_lgbm.txt (HybridBaccaratPredictor.predict_with_lgbm - Prédiction LGBM calibrée)
   - Lignes 8298-8414 dans hbp.py (117 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LGBM calibré avec gestion complète des erreurs et logging intelligent
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * feature (Optional[List[float]]) - Vecteur de features pour la prédiction LGBM
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURE :** Vérifie que le vecteur de features n'est pas None
     * **PROTECTION MODÈLE :** Utilise model_lock pour accès thread-safe au modèle calibré
     * **DÉTECTION PHASE :** Identifie si en phase d'entraînement ou optimisation Optuna pour adapter le logging
     * **VÉRIFICATION MODÈLE :** Contrôle que le modèle calibré et le scaler sont initialisés et fittés
     * **NORMALISATION FEATURES :** Applique le feature_scaler pour normaliser les données d'entrée
     * **PRÉDICTION PROBABILISTE :** Utilise predict_proba pour obtenir les probabilités Player/Banker
     * **NORMALISATION PROBABILITÉS :** Vérifie et normalise la somme des probabilités si nécessaire
     * **GESTION ERREURS :** Traite NotFittedError, ValueError et autres exceptions avec logging adaptatif
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' (défaut 0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LGBM robustes avec gestion intelligente des cas d'erreur et logging adapté au contexte

2. predict_with_lstm.txt (HybridBaccaratPredictor.predict_with_lstm - Prédiction LSTM optimisée)
   - Lignes 8113-8225 dans hbp.py (113 lignes)
   - FONCTION : Effectue une prédiction LSTM avec approche optimisée pour réduire la latence et cache intelligent
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lstm_features (Optional[np.ndarray]) - Features LSTM préparées ou None pour génération automatique
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** Vérifie que le modèle LSTM est initialisé avec logging adaptatif
     * **GESTION FEATURES :** Utilise features fournies ou génère via handle_short_sequence si nécessaire
     * **VALIDATION SHAPE :** Contrôle que les features ont la forme attendue (sequence_length, input_size)
     * **CACHE INTELLIGENT :** Utilise hash des features comme clé pour éviter recalculs identiques
     * **PRÉDICTION DIRECTE :** Évite DataLoader pour réduire latence, utilise torch.no_grad()
     * **GESTION DEVICE :** Déplace automatiquement les tensors sur le device approprié (CPU/GPU)
     * **SOFTMAX PROBABILITÉS :** Applique softmax pour obtenir probabilités normalisées
     * **GESTION CACHE :** Limite taille cache et supprime entrées anciennes si nécessaire
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' (0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LSTM rapides avec cache pour optimiser les performances en temps réel

3. hybrid_prediction.txt (HybridBaccaratPredictor.hybrid_prediction - Prédiction hybride avancée)
   - Lignes 8795-9937 dans hbp.py (1143 lignes)
   - FONCTION : Effectue une prédiction hybride sophistiquée combinant Markov, LGBM et LSTM avec pondération bayésienne et calcul d'incertitude multi-niveaux
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lgbm_feat (Optional[List[float]]) - Features pour le modèle LGBM
     * lstm_feat (Optional[np.ndarray]) - Features pour le modèle LSTM
     * optimization_phase (Optional[int]) - Phase d'optimisation (1=équilibre WAIT/NON-WAIT, 2=recommandations consécutives, None=normal)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PRÉDICTIONS INDIVIDUELLES :** Collecte prédictions Markov, LGBM et LSTM avec gestion d'erreurs robuste
     * **VALIDATION MODÈLES :** Vérifie quels modèles sont entraînés et disponibles pour la combinaison
     * **PONDÉRATION BAYÉSIENNE :** Calcule poids adaptatifs basés sur performance historique et confiance
     * **COMBINAISON PONDÉRÉE :** Fusionne prédictions avec poids effectifs ajustés par confiance
     * **INCERTITUDE ÉPISTÉMIQUE :** Mesure désaccord entre modèles pour évaluer fiabilité
     * **INCERTITUDE ALÉATOIRE :** Calcule entropie de la prédiction finale
     * **SENSIBILITÉ CONTEXTUELLE :** Analyse adaptation aux patterns de séquence
     * **CONFIANCE CONSÉCUTIVE :** Utilise calculateur spécialisé pour manches 31-60
     * **SYSTÈME DÉCISION :** Détermine recommandation finale avec seuils adaptatifs
   - RETOUR : Dict - Dictionnaire complet avec prédictions, recommandation, incertitudes et métriques détaillées
   - UTILITÉ : Cœur du système de prédiction avec intelligence artificielle avancée et gestion d'incertitude sophistiquée

================================================================================
SECTION 3 : OPTIMISATIONENTRAINEMENT (1 MÉTHODE)
================================================================================

Méthodes d'optimisation des hyperparamètres avec Optuna, entraînement
des modèles et gestion des processus d'apprentissage.

1. run_hyperparameter_optimization.txt (HybridBaccaratPredictor.run_hyperparameter_optimization - Optimisation Optuna multi-niveaux)
   - Lignes 12836-13032 dans hbp.py (197 lignes)
   - FONCTION : Lance l'optimisation des hyperparamètres avec stratégie multi-niveaux adaptée CPU via OptunaThreadManager
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * n_trials (int, optionnel) - Nombre d'essais à effectuer (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATIONS PRÉALABLES :** Contrôle état training, données historiques et suffisance des données (min 10 jeux)
     * **PRÉPARATION DONNÉES :** Appelle _prepare_training_data avec force_use_historical=True pour package complet
     * **VALIDATION ÉCHANTILLONS :** Vérifie qu'au moins 20 échantillons sont générés pour l'entraînement
     * **CONFIGURATION AVANCÉE :** Active multi-level, régularisation adaptative, SWA, méta-apprentissage, CV temporelle
     * **INITIALISATION OPTUNA :** Crée OptunaOptimizer avec réinitialisation des compteurs et transmission des options
     * **GESTIONNAIRE THREAD :** Utilise OptunaThreadManager pour exécution asynchrone avec callbacks
     * **CALLBACKS SYSTÈME :** Configure success_callback, error_callback et progress_callback pour UI
     * **LANCEMENT ASYNCHRONE :** Démarre l'optimisation dans thread séparé avec gestion complète des erreurs
   - RETOUR : None - Méthode asynchrone ne retourne rien
   - UTILITÉ : Point d'entrée principal pour l'optimisation sophistiquée des hyperparamètres avec interface non-bloquante

================================================================================
SECTION 4 : GESTIONDONNEES (3 MÉTHODES)
================================================================================

Méthodes de chargement, préparation, transformation et gestion des données
historiques et en temps réel.

1. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt - Sauvegarde session historique)
   - Lignes 12746-12806 dans hbp.py (61 lignes)
   - FONCTION : Ajoute la séquence de session actuelle au fichier historique en format 0/1 avec gestion intelligente des sauts de ligne
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * filepath (str, optionnel) - Chemin vers le fichier historical_data.txt (défaut: "historical_data.txt")
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour copier la séquence de manière thread-safe
     * **VALIDATION SÉQUENCE :** Vérifie que la séquence n'est pas vide avant traitement
     * **CONVERSION FORMAT :** Convertit Player→'0' et Banker→'1' pour cohérence avec le format standard
     * **GESTION FICHIER :** Ouvre en mode 'a+' pour append et lecture simultanée
     * **DÉTECTION DERNIER CARACTÈRE :** Vérifie si le fichier se termine par un saut de ligne
     * **AJOUT INTELLIGENT :** Ajoute un saut de ligne si nécessaire pour éviter les données collées
     * **ÉCRITURE SÉCURISÉE :** Écrit la séquence avec gestion d'erreurs I/O complète
   - RETOUR : bool - True si succès ou séquence vide, False en cas d'erreur
   - UTILITÉ : Maintient l'historique des sessions pour l'entraînement futur des modèles avec format standardisé

2. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data - Chargement données historiques)
   - Lignes 4831-4886 dans hbp.py (56 lignes)
   - FONCTION : Charge les données historiques depuis un fichier .txt avec interface utilisateur et validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle qu'aucune tâche ML n'est en cours avant chargement
     * **SÉLECTION FICHIER :** Ouvre dialogue de sélection avec filtres pour fichiers .txt
     * **CHARGEMENT INTERNE :** Utilise _load_historical_txt pour traitement du fichier
     * **CALCUL STATISTIQUES :** Détermine nombre de parties, longueur moyenne, total coups
     * **AFFICHAGE RÉSULTATS :** Présente statistiques détaillées dans messagebox
     * **GESTION SESSION :** Propose réinitialisation de la session en cours si applicable
     * **MISE À JOUR MODÈLES :** Met à jour automatiquement les modèles Markov globaux
     * **GESTION ERREURS :** Affiche messages d'erreur spécifiques selon le type de problème
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour charger l'historique avec validation et feedback utilisateur complet

3. _create_lgbm_features.txt (HybridBaccaratPredictor._create_lgbm_features - Création features LGBM optimisées)
   - Lignes 13748-13801 dans hbp.py (54 lignes)
   - FONCTION : Crée un vecteur de features optimisé pour le modèle LGBM à partir d'une séquence de résultats
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTEURS BASIQUES :** Calcule banker_count et player_count dans la séquence
     * **CALCUL STREAKS :** Utilise _calculate_streaks pour analyser les séries consécutives
     * **CALCUL ALTERNANCES :** Utilise _calculate_alternates pour détecter les patterns d'alternance
     * **FEATURES DECAY :** Calcule banker_decay et player_decay avec pondération temporelle
     * **ASSEMBLAGE FEATURES :** Combine compteurs, ratios, streaks et decay dans l'ordre attendu
     * **STREAKS SPÉCIFIQUES :** Ajoute les longueurs de streaks 2-7 pour banker et player
     * **INFOS ALTERNANCE :** Intègre alternate_count_2, alternate_count_3, alternate_ratio
     * **OPTIMISATION OPTUNA :** Réduit de 28 à 25 features en supprimant 3 features spécifiques
   - RETOUR : List[float] - Vecteur de 25 features normalisées pour LGBM
   - UTILITÉ : Fournit un vecteur de features optimisé et standardisé pour les prédictions LGBM

================================================================================
SECTION 5 : INTERFACEUTILISATEUR (2 MÉTHODES)
================================================================================

Méthodes de configuration et gestion de l'interface utilisateur Tkinter,
affichage et interactions.

1. setup_ui.txt (HybridBaccaratPredictor.setup_ui - Configuration interface utilisateur principale)
   - Lignes 2587-2769 dans hbp.py (183 lignes)
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter, incluant tous les widgets, styles et layouts
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION STYLE :** Détecte la plateforme et applique le thème TTK approprié (vista/aqua/clam)
     * **GESTION COULEURS :** Récupère les couleurs TTK et définit des couleurs fixes pour Matplotlib
     * **STRUCTURE LAYOUT :** Crée la structure principale avec frames gauche/droite et organisation modulaire
     * **PANNEAU MODÈLES :** Configure les boutons de gestion des modèles et données (charger, sauvegarder, dashboard)
     * **PANNEAU ENTRAÎNEMENT :** Met en place les contrôles d'entraînement et d'optimisation Optuna
     * **PANNEAU CONFIGURATION :** Initialise le panneau de configuration des ressources
     * **CONTRÔLES PRÉDICTION :** Crée les boutons Player/Banker et annulation avec layout en grille
     * **AFFICHAGE TEMPS RÉEL :** Configure les labels de prédiction avec couleurs et polices spécifiques
     * **GRAPHIQUE MATPLOTLIB :** Intègre le graphique de tendance avec couleurs fixes et canvas Tkinter
     * **STATISTIQUES :** Met en place le panneau d'analyse avec métriques détaillées
   - RETOUR : None - Méthode de configuration ne retourne rien
   - UTILITÉ : Point d'entrée pour créer l'interface utilisateur complète avec tous les composants visuels et interactifs

2. update_display.txt (HybridBaccaratPredictor.update_display - Mise à jour affichage complet)
   - Lignes 11454-11527 dans hbp.py (74 lignes)
   - FONCTION : Met à jour tous les éléments de l'interface utilisateur avec adaptation contextuelle aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour accès sécurisé à l'historique des prédictions
     * **MISE À JOUR PRÉDICTIONS :** Affiche manche actuelle, probabilités Player/Banker et recommandation
     * **DÉTECTION PLAGE CIBLE :** Vérifie si dans la plage 31-60 pour adaptation de l'affichage
     * **AJUSTEMENT CONFIANCE :** Applique formule d'ajustement pour ramener vers 50% en affichage
     * **VÉRIFICATION MODÈLES :** Contrôle si les modèles sont entraînés pour adapter le niveau de confiance
     * **CLASSIFICATION CONFIANCE :** Détermine niveau (Faible/Moyenne/Élevée) selon seuils adaptatifs
     * **APPEL STATISTIQUES :** Déclenche update_statistics() pour métriques avancées
     * **GESTION GRAPHIQUE :** Met à jour le graphique de tendance si visible
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Orchestre la mise à jour complète de l'interface avec adaptation intelligente au contexte

================================================================================
SECTION 6 : EVALUATIONMETRIQUES (1 MÉTHODE)
================================================================================

Méthodes d'évaluation des performances, calcul de métriques et validation
des modèles.

1. update_statistics.txt (HybridBaccaratPredictor.update_statistics - Mise à jour statistiques interface)
   - Lignes 11533-11664 dans hbp.py (132 lignes)
   - FONCTION : Met à jour les labels de statistiques dans l'interface utilisateur avec métriques complètes et adaptation aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock et model_lock pour accès sécurisé aux données
     * **CALCUL SÉRIE :** Détermine la série actuelle (Player/Banker) et sa longueur
     * **PRÉCISION SESSION :** Calcule la précision basée sur les recommandations non-wait vs résultats réels
     * **DÉTECTION PLAGE CIBLE :** Vérifie si la manche actuelle est dans la plage 31-60 pour adaptation affichage
     * **MÉTRIQUES MÉTHODES :** Calcule précision et confiance pour chaque méthode (LGBM, LSTM, etc.)
     * **STATISTIQUES PARTIE :** Affiche répartition Player/Banker avec pourcentages
     * **INCERTITUDE DÉTAILLÉE :** Présente épistémique, aléatoire et sensibilité contextuelle
     * **SEUIL ADAPTATIF :** Affiche le seuil de confiance adaptatif actuel
     * **POIDS BAYÉSIENS :** Montre la pondération bayésienne des différentes méthodes
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Fournit un tableau de bord complet des performances en temps réel avec adaptation contextuelle

================================================================================
SECTION 7 : UTILITAIRESFONCTIONS (1 MÉTHODE)
================================================================================

Fonctions utilitaires, helpers et méthodes de support pour le système.

1. __init__.txt (HybridBaccaratPredictor.__init__ - Constructeur principal de la classe)
   - Lignes 541-779 dans hbp.py (239 lignes)
   - FONCTION : Initialise une instance de HybridBaccaratPredictor avec configuration complète des modèles ML, interface utilisateur et gestion des ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * root_or_config (Union[tk.Tk, PredictorConfig]) - Objet root Tkinter pour mode UI ou objet PredictorConfig pour mode Optuna
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OPTIMISATION MÉMOIRE :** Optimise l'utilisation mémoire PyTorch avant toute opération
     * **CONFIGURATION LOGGER :** Initialise le système de logging avec handlers console et fichier
     * **DÉTECTION MODE :** Détermine si c'est un mode UI (Tkinter) ou mode Optuna (sans UI)
     * **INITIALISATION ÉTAT :** Configure les attributs d'état (séquences, historique, cache LGBM)
     * **MODÈLES ML :** Initialise les placeholders pour LGBM, LSTM, optimiseurs et schedulers
     * **MODÈLE MARKOV :** Configure le modèle PersistentMarkov avec paramètres adaptatifs
     * **GESTION PERFORMANCE :** Initialise le suivi des performances et poids des méthodes
     * **VERROUS THREADING :** Crée les verrous pour accès concurrent sécurisé
     * **CONFIGURATION DEVICE :** Détecte et configure l'utilisation CPU/GPU
     * **INTERFACE UTILISATEUR :** Configure l'UI Tkinter si en mode interface
     * **CHARGEMENT AUTO :** Tente le chargement automatique de l'état précédent
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Point d'entrée principal pour créer une instance fonctionnelle du système de prédiction avec tous les composants initialisés

================================================================================
SECTION 8 : ANCIENNESCLASSES ([À COMPLÉTER] MÉTHODES)
================================================================================

Définitions complètes des classes du système.

[MÉTHODES À AJOUTER]
