DESCRIPTIF DÉTAILLÉ DES MÉTHODES - HBP.PY
================================================================================

Ce fichier contient la description détaillée de toutes les méthodes du système, organisées par sections fonctionnelles.

STRUCTURE DU SYSTÈME (basée sur l'analyse architecturale) :
- **CalculConfiance** : Calculs de confiance, incertitudes et pondération bayésienne
- **GestionDonnees** : Préparation données, features engineering et gestion historique
- **OptimisationEntrainement** : Optimisation hyperparamètres et entraînement modèles
- **InterfaceUtilisateur** : Configuration UI, affichage temps réel et contrôles interactifs
- **EvaluationMetriques** : Évaluation performance, visualisations et rapports détaillés
- **UtilitairesFonctions** : Fonctions utilitaires, sauvegarde/chargement et maintenance
- **ReseauxNeuronaux** : Méthodes relatives aux réseaux LSTM et prédictions ML
- **anciennesclasses** : Définitions classes principales et architecture système

TOTAL : 139+ MÉTHODES ANALYSÉES

Dernière mise à jour: Création plateforme maintenance

================================================================================
SECTION 1 : CALCULCONFIANCE (25 MÉTHODES)
================================================================================

Méthodes de calcul de confiance, gestion des incertitudes épistémiques et
aléatoires, pondération bayésienne et optimisation des recommandations.

1. __init___1.txt (ConsecutiveConfidenceCalculator.__init__ - Constructeur calculateur confiance)
   - Lignes 1422-1485 dans hbp.py (64 lignes)
   - FONCTION : Constructeur du calculateur de confiance pour recommandations NON-WAIT consécutives avec analyse de patterns
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION STATISTIQUES :** Crée un dictionnaire par défaut pour stocker les statistiques de patterns
     * **STRUCTURE PATTERN_STATS :** Chaque pattern contient total, success, consecutive_lengths et max_consecutive
     * **HISTORIQUE RÉCENT :** Initialise les listes pour les recommandations et résultats récents
     * **CONFIGURATION LIMITE :** Définit la taille maximale de l'historique récent (défaut 50)
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Prépare le calculateur pour analyser les patterns de séquences consécutives et calculer la confiance des recommandations

2. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence - Calcul de confiance avancé)
   - Lignes 1486-1579 dans hbp.py (94 lignes)
   - FONCTION : Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle en analysant les patterns et facteurs multiples
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features_vector (List[float]) - Vecteur de features pour la manche actuelle
     * current_round (int) - Numéro de la manche actuelle
     * config - Configuration du prédicteur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PLAGE :** Vérifie si la manche est dans la plage cible (target_round_min à target_round_max)
     * **EXTRACTION PATTERN :** Extrait la clé de pattern à partir du vecteur de features
     * **STATISTIQUES PATTERN :** Récupère les statistiques historiques pour ce pattern spécifique
     * **CALCUL CONFIANCE BASE :** Combine taux de succès (70%) et longueur moyenne consécutive (30%)
     * **BONUS COURBE CLOCHE :** Applique un bonus pour les manches au milieu de la plage cible
     * **FACTEURS MULTIPLICATIFS :** Calcule sequence_bonus, late_game_factor, occurrence_factor, consecutive_factor
     * **AJUSTEMENT FINAL :** Multiplie la confiance par tous les facteurs et limite entre 0 et 1
   - RETOUR : Dict - Dictionnaire contenant confidence, expected_consecutive, success_rate, et tous les facteurs calculés
   - UTILITÉ : Fournit une évaluation sophistiquée de la confiance pour optimiser les recommandations de mise

3. _analyze_sequence_context.txt (HybridBaccaratPredictor._analyze_sequence_context - Analyse contextuelle de séquence)
   - Lignes 13941-13979 dans hbp.py (39 lignes)
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles en fonction de la volatilité et des patterns
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LONGUEUR :** Vérifie qu'il y a au moins 10 éléments pour une analyse fiable
     * **CALCUL VOLATILITÉ :** Analyse les alternances dans les 10 derniers coups pour mesurer l'instabilité
     * **DÉTECTION STREAKS :** Identifie la longueur du streak actuel en remontant la séquence
     * **NORMALISATION STREAK :** Normalise le facteur de streak entre 0 et 1 (max 10 coups)
     * **COMBINAISON FACTEURS :** Combine volatilité (70%) et facteur de streak (30%)
     * **INTERPRÉTATION :** Proche de 0 = séquence stable, proche de 1 = séquence volatile
   - RETOUR : float - Facteur contextuel entre 0 et 1 indiquant la pertinence du modèle de session vs global
   - UTILITÉ : Permet d'adapter dynamiquement les poids des modèles selon le contexte de jeu actuel

📋 **MÉTHODES 4-25 CALCULCONFIANCE** - Consultez CalculConfiance/Descriptif.txt pour détails complets :
   4. **calculate_uncertainty** - Calcul incertitude par variance BaggingClassifier (lignes 4386-4547)
   5. **calculate_bayesian_weights** - Calcul poids bayésiens P(M|D) (lignes 8589-8622)
   6. **update_weights** - MAJ poids méthodes thread-safe (lignes 10546-10740)
   7. **calculate_model_confidence** - Confiance modèle avec facteurs contextuels (lignes 8426-8489)
   8. **analyze_context_sensitivity** - Analyse sensibilité variations séquence (lignes 8491-8587)
   9. **calculate_epistemic_uncertainty** - Incertitude épistémique variance modèles (lignes 8624-8644)
   10. **calculate_aleatoric_uncertainty** - Incertitude aléatoire entropie (lignes 8646-8667)
   11. **get_weights** - Récupération poids thread-safe (lignes 8416-8424)
   12. **consecutive_focused_metric** - Métrique LGBM focus consécutif (lignes 296-365)
   13. **calculate_consecutive_focused_weights** - Poids échantillons consécutifs (lignes 172-294)
   14. **evaluate_consecutive_focused** - Évaluation recommandations consécutives (lignes 367-500+)
   15. **consecutive_valid_recommendations_loss** - Loss recommandations consécutives (lignes 84-170)
   16. **consecutive_valid_recommendations_loss_1** - Loss v1 pénalité progressive (lignes 10278-10348)
   17. **uncertainty_weighted_loss** - Loss pondérée incertitude (lignes 10172-10276)
   18. **get_confidence_adjustment** - Ajustement confiance contextuel
   19. **update_consecutive_confidence_calculator** - MAJ calculateur manches 31-61 (lignes 8491-8587)
   20. **update_consecutive_confidence_calculator_1** - MAJ calculateur v1 optimisée
   21. **init_consecutive_confidence_calculator** - Init calculateur classe interne (lignes 1410-1587)
   22. **init_wait_placement_optimizer** - Init optimiseur placement attente
   23. **get_current_wait_ratio** - Ratio attente optimal conditions actuelles

================================================================================
SECTION 2 : GESTIONDONNEES (23 MÉTHODES)
================================================================================

Méthodes de préparation des données, feature engineering, gestion historique
et optimisation des structures de données pour l'entraînement ML.

