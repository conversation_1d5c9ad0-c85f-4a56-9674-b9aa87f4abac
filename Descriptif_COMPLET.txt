DESCRIPTIF DÉTAILLÉ DES MÉTHODES - SYSTÈME ML BACCARAT - PREMIÈRE VAGUE COMPLÈTE
================================================================================

Ce fichier contient la description fonctionnelle complète de toutes les méthodes du système
ML de prédiction Baccarat (hbp.py), organisées par sections fonctionnelles.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE COMPLÈTE
- **Couverture** : 100% des méthodes documentées (161/161)
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

STRUCTURE DU SYSTÈME (basée sur l'analyse architecturale) :
- **CalculConfiance** : Méthodes de calcul de confiance et d'incertitude (23 méthodes)
- **GestionDonnees** : Chargement, préparation et gestion des données (27 méthodes)
- **OptimisationEntrainement** : Méthodes d'optimisation Optuna et d'entraînement (32 méthodes)
- **InterfaceUtilisateur** : Configuration UI et affichage (29 méthodes)
- **EvaluationMetriques** : Méthodes d'évaluation et de métriques (19 méthodes)
- **UtilitairesFonctions** : Fonctions utilitaires et helpers (26 méthodes)
- **ReseauxNeuronaux** : Méthodes LSTM et modèles de deep learning (3 méthodes)
- **anciennesclasses** : Définitions de classes complètes (2 méthodes)

TOTAL : 161 MÉTHODES ANALYSÉES ET ORGANISÉES (100% COMPLET)
ÉTAT : PREMIÈRE VAGUE COMPLÈTE - PRÊT POUR DEUXIÈME VAGUE

Dernière mise à jour: 25/05/2025 - Synchronisation première vague

================================================================================
SECTION 1 : CALCULCONFIANCE (23 MÉTHODES)
================================================================================

Méthodes dédiées au calcul de confiance, d'incertitude et de recommandations
basées sur l'analyse des patterns et séquences consécutives.

1. __init___1.txt (ConsecutiveConfidenceCalculator.__init__ - DOUBLON - Constructeur du calculateur de confiance)
   - Lignes 1423-1427 dans hbp.py (5 lignes)
   - FONCTION : Initialise une instance de ConsecutiveConfidenceCalculator pour le calcul de confiance basé sur les patterns consécutifs
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION STATISTIQUES :** Crée un dictionnaire par défaut pour stocker les statistiques de patterns
     * **STRUCTURE PATTERN_STATS :** Chaque pattern contient total, success, consecutive_lengths et max_consecutive
     * **HISTORIQUE RÉCENT :** Initialise les listes pour les recommandations et résultats récents
     * **CONFIGURATION LIMITE :** Définit la taille maximale de l'historique récent (défaut 50)
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Prépare le calculateur pour analyser les patterns de séquences consécutives et calculer la confiance des recommandations

2. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence - Calcul de confiance avancé)
   - Lignes 1486-1579 dans hbp.py (94 lignes)
   - FONCTION : Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle en analysant les patterns et facteurs multiples
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features_vector (List[float]) - Vecteur de features pour la manche actuelle
     * current_round (int) - Numéro de la manche actuelle
     * config - Configuration du prédicteur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PLAGE :** Vérifie si la manche est dans la plage cible (target_round_min à target_round_max)
     * **EXTRACTION PATTERN :** Extrait la clé de pattern à partir du vecteur de features
     * **STATISTIQUES PATTERN :** Récupère les statistiques historiques pour ce pattern spécifique
     * **CALCUL CONFIANCE BASE :** Combine taux de succès (70%) et longueur moyenne consécutive (30%)
     * **BONUS COURBE CLOCHE :** Applique un bonus pour les manches au milieu de la plage cible
     * **FACTEURS MULTIPLICATIFS :** Calcule sequence_bonus, late_game_factor, occurrence_factor, consecutive_factor
     * **AJUSTEMENT FINAL :** Multiplie la confiance par tous les facteurs et limite entre 0 et 1
   - RETOUR : Dict - Dictionnaire contenant confidence, expected_consecutive, success_rate, et tous les facteurs calculés
   - UTILITÉ : Fournit une évaluation sophistiquée de la confiance pour optimiser les recommandations de mise

3. _analyze_sequence_context.txt (HybridBaccaratPredictor._analyze_sequence_context - Analyse contextuelle de séquence)
   - Lignes 13941-13979 dans hbp.py (39 lignes)
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles en fonction de la volatilité et des patterns
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LONGUEUR :** Vérifie qu'il y a au moins 10 éléments pour une analyse fiable
     * **CALCUL VOLATILITÉ :** Analyse les alternances dans les 10 derniers coups pour mesurer l'instabilité
     * **DÉTECTION STREAKS :** Identifie la longueur du streak actuel en remontant la séquence
     * **NORMALISATION STREAK :** Normalise le facteur de streak entre 0 et 1 (max 10 coups)
     * **COMBINAISON FACTEURS :** Combine volatilité (70%) et facteur de streak (30%)
     * **INTERPRÉTATION :** Proche de 0 = séquence stable, proche de 1 = séquence volatile
   - RETOUR : float - Facteur contextuel entre 0 et 1 indiquant la pertinence du modèle de session vs global
   - UTILITÉ : Permet d'adapter dynamiquement les poids des modèles selon le contexte de jeu actuel

4. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty - Calcul d'incertitude par variance)
   - Lignes 4386-4547 dans hbp.py (162 lignes)
   - FONCTION : Calcule un score d'incertitude basé sur la variance des prédictions des estimateurs du BaggingClassifier LGBM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * features (Optional[List[float]]) - Liste des features d'entrée
     * predicted_class (Optional[int]) - Classe prédite (0=PLAYER, 1=BANKER), optionnel
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURES :** Vérifie que features n'est pas None et a la bonne longueur
     * **INITIALISATION MODÈLE :** Tente d'initialiser lgbm_uncertainty si nécessaire avec protection récursion
     * **VÉRIFICATIONS MODÈLE :** Contrôle que le modèle et scaler sont initialisés et fittés
     * **NORMALISATION :** Applique feature_scaler.transform avec gestion d'erreurs complète
     * **PRÉDICTIONS MULTIPLES :** Collecte les prédictions de chaque estimateur du BaggingClassifier
     * **GESTION CLASSES :** Trouve l'index de la classe Banker avec cache optimisé
     * **CALCUL VARIANCE :** Calcule la variance des probabilités Banker entre estimateurs
     * **NORMALISATION SCORE :** Applique facteur de normalisation réduit et clipping [0,1]
   - RETOUR : float - Score d'incertitude entre 0 et 1 (0.5 par défaut en cas d'erreur)
   - UTILITÉ : Fournit une mesure d'incertitude épistémique pour évaluer la fiabilité des prédictions

5. calculate_bayesian_weights.txt (HybridBaccaratPredictor.calculate_bayesian_weights - Calcul poids bayésiens)
   - Lignes 8589-8622 dans hbp.py (34 lignes)
   - FONCTION : Calcule les poids bayésiens des modèles en fonction de leur confiance selon P(M|D) = P(D|M) * P(M) / P(D)
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_weights (Dict[str, float]) - Poids actuels des modèles (priors)
     * method_confidences (Dict[str, float]) - Confiance calculée pour chaque modèle (likelihood)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL PRODUIT :** Multiplie poids actuels P(M) par confidences P(D|M) pour chaque méthode
     * **NORMALISATION BAYÉSIENNE :** Divise par somme totale pour obtenir probabilités postérieures P(M|D)
     * **GESTION EPSILON :** Utilise epsilon configuré pour éviter divisions par zéro
     * **FALLBACK SÉCURISÉ :** Retourne poids originaux si somme totale trop petite
   - RETOUR : Dict[str, float] - Poids bayésiens ajustés normalisés
   - UTILITÉ : Implémente mise à jour bayésienne des poids pour adaptation dynamique basée sur performance

6. update_weights.txt (HybridBaccaratPredictor.update_weights - Mise à jour poids méthodes)
   - FONCTION : Met à jour les poids des méthodes basé sur performance prédiction vs résultat réel
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction_result (Dict) - Résultat de la prédiction avec méthodes utilisées
     * actual_outcome (str) - Résultat réel observé ('player' ou 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉES :** Vérifie cohérence des paramètres d'entrée
     * **CALCUL PERFORMANCE :** Évalue performance de chaque méthode individuellement
     * **AJUSTEMENT ADAPTATIF :** Modifie poids selon taux de réussite récent
     * **NORMALISATION :** Assure que somme des poids reste égale à 1
     * **PERSISTANCE :** Sauvegarde nouveaux poids pour utilisation future
   - RETOUR : None - Met à jour directement les poids internes
   - UTILITÉ : Ajuste dynamiquement l'importance relative des différentes méthodes de prédiction

7. calculate_model_confidence.txt (HybridBaccaratPredictor.calculate_model_confidence - Confiance modèle)
   - FONCTION : Calcule la confiance d'un modèle spécifique basée sur sa performance historique
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * model_name (str) - Nom du modèle à évaluer ('markov', 'lgbm', 'lstm')
     * recent_predictions (List[Dict]) - Prédictions récentes du modèle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ANALYSE HISTORIQUE :** Examine performance sur fenêtre glissante
     * **CALCUL ACCURACY :** Détermine taux de réussite récent
     * **FACTEUR STABILITÉ :** Évalue consistance des prédictions
     * **AJUSTEMENT CONTEXTUEL :** Adapte selon conditions actuelles
     * **NORMALISATION :** Standardise score entre 0 et 1
   - RETOUR : float - Score de confiance du modèle entre 0 et 1
   - UTILITÉ : Évalue la fiabilité individuelle de chaque modèle pour pondération adaptative

8. analyze_context_sensitivity.txt (HybridBaccaratPredictor.analyze_context_sensitivity - Analyse sensibilité contextuelle)
   - FONCTION : Analyse la sensibilité du modèle aux changements de contexte de séquence
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * base_sequence (List[str]) - Séquence de base pour l'analyse
     * num_variations (int) - Nombre de variations à tester
   - FONCTIONNEMENT DÉTAILLÉ :
     * **GÉNÉRATION VARIATIONS :** Crée variations de la séquence de base
     * **PRÉDICTIONS MULTIPLES :** Teste modèle sur chaque variation
     * **CALCUL VARIANCE :** Mesure dispersion des prédictions
     * **ANALYSE STABILITÉ :** Évalue robustesse aux changements
     * **SCORE SENSIBILITÉ :** Quantifie sensibilité contextuelle
   - RETOUR : Dict - Métriques de sensibilité et stabilité contextuelle
   - UTILITÉ : Mesure l'adaptabilité du système aux variations de patterns

9. calculate_epistemic_uncertainty.txt (HybridBaccaratPredictor.calculate_epistemic_uncertainty - Incertitude épistémique)
   - FONCTION : Calcule l'incertitude épistémique basée sur le désaccord entre modèles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * model_predictions (Dict[str, Dict]) - Prédictions de chaque modèle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PROBABILITÉS :** Récupère probabilités de chaque modèle
     * **CALCUL VARIANCE :** Mesure désaccord entre modèles
     * **NORMALISATION :** Standardise score d'incertitude
     * **INTERPRÉTATION :** Quantifie incertitude due aux connaissances limitées
     * **VALIDATION :** Vérifie cohérence des calculs
   - RETOUR : float - Score d'incertitude épistémique entre 0 et 1
   - UTILITÉ : Quantifie l'incertitude due au manque de connaissances du modèle

10. calculate_aleatoric_uncertainty.txt (HybridBaccaratPredictor.calculate_aleatoric_uncertainty - Incertitude aléatoire)
    - FONCTION : Calcule l'incertitude aléatoire inhérente aux données
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * prediction_probability (float) - Probabilité de la prédiction principale
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL ENTROPIE :** Utilise entropie binaire pour mesurer incertitude
      * **NORMALISATION :** Standardise score entre 0 et 1
      * **VALIDATION :** Vérifie bornes de probabilité
      * **INTERPRÉTATION :** Quantifie incertitude irréductible
      * **SÉCURISATION :** Gère cas limites et erreurs
    - RETOUR : float - Score d'incertitude aléatoire entre 0 et 1
    - UTILITÉ : Quantifie l'incertitude irréductible du système
