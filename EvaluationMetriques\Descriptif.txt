DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ÉVALUATION MÉTRIQUES
================================================================================

Ce fichier contient la description détaillée des méthodes d'évaluation et
de métriques du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : ÉVALUATION MÉTRIQUES
Méthodes d'évaluation des performances, calcul de métriques et validation
des modèles.

TOTAL : 19 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES ÉVALUATION MÉTRIQUES
================================================================================

1. update_statistics.txt (HybridBaccaratPredictor.update_statistics - Mise à jour statistiques interface)
   - Lignes 11533-11664 dans hbp.py (132 lignes)
   - FONCTION : Met à jour les labels de statistiques dans l'interface utilisateur avec métriques complètes et adaptation aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock et model_lock pour accès sécurisé aux données
     * **CALCUL SÉRIE :** Détermine la série actuelle (Player/Banker) et sa longueur
     * **PRÉCISION SESSION :** Calcule la précision basée sur les recommandations non-wait vs résultats réels
     * **DÉTECTION PLAGE CIBLE :** Vérifie si la manche actuelle est dans la plage 31-60 pour adaptation affichage
     * **MÉTRIQUES MÉTHODES :** Calcule précision et confiance pour chaque méthode (LGBM, LSTM, etc.)
     * **STATISTIQUES PARTIE :** Affiche répartition Player/Banker avec pourcentages
     * **INCERTITUDE DÉTAILLÉE :** Présente épistémique, aléatoire et sensibilité contextuelle
     * **SEUIL ADAPTATIF :** Affiche le seuil de confiance adaptatif actuel
     * **POIDS BAYÉSIENS :** Montre la pondération bayésienne des différentes méthodes
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Fournit un tableau de bord complet des performances en temps réel avec adaptation contextuelle

2. _create_metrics_dashboard.txt (HybridBaccaratPredictor._create_metrics_dashboard - Création tableau de bord métriques)
   - FONCTION : Crée tableau de bord interactif pour visualisation métriques détaillées
   - UTILITÉ : Interface complète pour analyse performance avec graphiques

3. _refresh_metrics_dashboard.txt (HybridBaccaratPredictor._refresh_metrics_dashboard - Rafraîchissement tableau de bord)
   - FONCTION : Rafraîchit données du tableau de bord métriques avec nouvelles valeurs
   - UTILITÉ : Mise à jour temps réel des visualisations métriques

4. _fill_combined_metrics_tab.txt (HybridBaccaratPredictor._fill_combined_metrics_tab - Remplissage onglet métriques combinées)
   - FONCTION : Remplit onglet avec métriques combinées de tous les modèles
   - UTILITÉ : Vue unifiée performance ensemble des modèles

5. _fill_lgbm_metrics_tab.txt (HybridBaccaratPredictor._fill_lgbm_metrics_tab - Remplissage onglet métriques LGBM)
   - FONCTION : Remplit onglet spécialisé pour métriques du modèle LGBM
   - UTILITÉ : Analyse détaillée performance modèle LGBM

6. _fill_lstm_metrics_tab.txt (HybridBaccaratPredictor._fill_lstm_metrics_tab - Remplissage onglet métriques LSTM)
   - FONCTION : Remplit onglet spécialisé pour métriques du modèle LSTM
   - UTILITÉ : Analyse détaillée performance modèle LSTM

7. _fill_plots_tab.txt (HybridBaccaratPredictor._fill_plots_tab - Remplissage onglet graphiques)
   - FONCTION : Remplit onglet avec graphiques et visualisations métriques
   - UTILITÉ : Visualisation graphique des tendances performance

8. _update_combined_metrics.txt (HybridBaccaratPredictor._update_combined_metrics - MAJ métriques combinées)
   - FONCTION : Met à jour métriques combinées de tous les modèles
   - UTILITÉ : Synchronisation métriques ensemble système

9. _update_lgbm_metrics.txt (HybridBaccaratPredictor._update_lgbm_metrics - MAJ métriques LGBM)
   - Lignes 7061-7095 dans hbp.py (35 lignes)
   - FONCTION : Met à jour les métriques LGBM dans le tableau de bord interface avec affichage formaté
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MÉTRIQUES :** Vérifie `if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics: return` pour éviter erreurs
     * **MISE À JOUR MÉTRIQUES BASE :** Met à jour variables Tkinter pour chaque métrique disponible :
       - `self.lgbm_metric_vars['precision'].set(f"Précision: {self.lgbm_metrics['precision']:.4f}")` si 'precision' dans métriques
       - `self.lgbm_metric_vars['recall'].set(f"Rappel: {self.lgbm_metrics['recall']:.4f}")` si 'recall' dans métriques
       - `self.lgbm_metric_vars['f1'].set(f"F1-Score: {self.lgbm_metrics['f1']:.4f}")` si 'f1' dans métriques
       - `self.lgbm_metric_vars['auc_roc'].set(f"AUC-ROC: {self.lgbm_metrics['auc_roc']:.4f}")` si 'auc_roc' dans métriques
     * **MISE À JOUR MATRICE CONFUSION :** Si 'confusion_matrix' disponible, itère `for i in range(2): for j in range(2):` et met à jour `self.lgbm_cm_vars[i][j].set(str(cm[i, j]))`
     * **MISE À JOUR IMPORTANCE FEATURES :** Si 'feature_importance' et `hasattr(self, 'feature_names')` :
       - Efface anciennes données avec `self.lgbm_feature_tree.delete(item)` pour tous enfants
       - Trie features par importance avec `sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)`
       - Insère nouvelles données avec `self.lgbm_feature_tree.insert('', 'end', values=(feature, f"{importance:.6f}"))`
   - RETOUR : None - Méthode de mise à jour interface ne retourne rien
   - UTILITÉ : Affichage temps réel performance modèle LGBM avec métriques formatées et importance features

10. _update_lstm_metrics.txt (HybridBaccaratPredictor._update_lstm_metrics - MAJ métriques LSTM)
    - FONCTION : Met à jour métriques spécifiques au modèle LSTM
    - UTILITÉ : Suivi performance dédié modèle LSTM

10.5. _update_combined_metrics.txt (HybridBaccaratPredictor._update_combined_metrics - MAJ métriques combinées)
    - Lignes 7473-7524 dans hbp.py (52 lignes)
    - FONCTION : Met à jour les métriques combinées dans le tableau de bord avec calculs de performance globale
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MISE À JOUR INCERTITUDES :** Si `hasattr(self, 'prediction_history') and self.prediction_history:`, récupère `last_prediction = self.prediction_history[-1]`
      * **MÉTRIQUES CONFIANCE :** Extrait `metrics = last_prediction['confidence_metrics']` et met à jour variables Tkinter :
        - `self.combined_metric_vars['epistemic_uncertainty'].set(f"Incertitude Épistémique: {metrics['epistemic_uncertainty']:.4f}")`
        - `self.combined_metric_vars['aleatoric_uncertainty'].set(f"Incertitude Aléatoire: {metrics['aleatoric_uncertainty']:.4f}")`
        - `self.combined_metric_vars['context_sensitivity'].set(f"Sensibilité Contextuelle: {metrics['context_sensitivity']:.4f}")`
        - `self.combined_metric_vars['total_uncertainty'].set(f"Incertitude Totale: {metrics['total_uncertainty']:.4f}")`
      * **POIDS MODÈLES :** Met à jour affichage poids avec `self.config.weight_markov`, `self.config.weight_lgbm`, `self.config.weight_lstm`
      * **CALCUL PERFORMANCE COMBINÉE :** Parcourt `self.prediction_history` pour calculer :
        - `correct_predictions` et `total_predictions` avec comparaison `pred['actual_outcome'] == pred['combined_prediction']`
        - `recommendations` en comptant `pred['recommendation'] is not None`
        - `total_confidence` en sommant `pred['combined_confidence']`
      * **MÉTRIQUES FINALES :** Calcule et affiche :
        - `accuracy = correct_predictions / total_predictions` pour exactitude combinée
        - `recommendation_rate = recommendations / len(self.prediction_history)` pour taux de recommandation
        - `avg_confidence = total_confidence / len(self.prediction_history)` pour confiance moyenne
    - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
    - UTILITÉ : Affichage synthétique performance globale système avec métriques d'incertitude et statistiques combinées

11. _draw_confusion_matrix.txt (HybridBaccaratPredictor._draw_confusion_matrix - Dessin matrice confusion)
    - FONCTION : Dessine matrice de confusion pour évaluation classification
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * y_true (array) - Vraies étiquettes de classification
      * y_pred (array) - Prédictions du modèle
      * model_name (str) - Nom du modèle évalué
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL MATRICE :** Calcule matrice confusion depuis prédictions
      * **NORMALISATION :** Normalise valeurs pour pourcentages
      * **CRÉATION GRAPHIQUE :** Génère heatmap avec matplotlib
      * **ANNOTATION VALEURS :** Ajoute valeurs numériques sur graphique
      * **FORMATAGE AXES :** Configure labels et titre appropriés
    - RETOUR : matplotlib.Figure - Figure contenant matrice confusion
    - UTILITÉ : Visualisation détaillée performance classification par classe

12. _draw_curve.txt (HybridBaccaratPredictor._draw_curve - Dessin courbes performance)
    - Lignes 7405-7420 dans hbp.py (16 lignes)
    - FONCTION : Dessine une courbe de données sur canvas Tkinter avec normalisation automatique et lissage
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * canvas - Canvas Tkinter où dessiner la courbe
      * data (List[float]) - Données à tracer (valeurs Y)
      * min_val (float) - Valeur minimale pour normalisation Y
      * max_val (float) - Valeur maximale pour normalisation Y
      * x_offset (int) - Décalage horizontal de départ
      * y_offset (int) - Décalage vertical de départ
      * width (int) - Largeur de la zone de tracé
      * height (int) - Hauteur de la zone de tracé
      * color (str, optionnel) - Couleur de la courbe (défaut: "blue")
      * label (str, optionnel) - Label pour légende (défaut: "")
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if not data: return` pour éviter tracé vide
      * **CALCUL POINTS :** Itère sur données avec `for i, val in enumerate(data):`
      * **NORMALISATION X :** Calcule `x = x_offset + (i / (len(data) - 1 if len(data) > 1 else 1)) * width` pour répartition uniforme
      * **NORMALISATION Y :** Calcule `y = y_offset + height - ((val - min_val) / (max_val - min_val if max_val > min_val else 1)) * height` avec inversion axe Y
      * **CONSTRUCTION LISTE :** Ajoute `points.append(x)` puis `points.append(y)` pour format Tkinter
      * **TRACÉ COURBE :** Si `len(points) >= 4:`, appelle `canvas.create_line(points, fill=color, width=2, smooth=True)` avec lissage
    - RETOUR : None - Dessine directement sur le canvas
    - UTILITÉ : Visualisation courbes métriques d'entraînement avec normalisation automatique et rendu lisse

13. _draw_lstm_learning_curves.txt (HybridBaccaratPredictor._draw_lstm_learning_curves - Courbes apprentissage LSTM)
    - FONCTION : Dessine courbes d'apprentissage spécifiques au modèle LSTM
    - UTILITÉ : Visualisation convergence et performance LSTM

14. _show_confusion_matrices.txt (HybridBaccaratPredictor._show_confusion_matrices - Affichage matrices confusion)
    - FONCTION : Affiche matrices de confusion pour tous les modèles
    - UTILITÉ : Comparaison visuelle performance classification modèles

15. _show_feature_importance.txt (HybridBaccaratPredictor._show_feature_importance - Affichage importance features)
    - FONCTION : Affiche importance des features pour modèles tree-based
    - UTILITÉ : Analyse contribution features pour interprétabilité

16. _show_lgbm_learning_curves.txt (HybridBaccaratPredictor._show_lgbm_learning_curves - Courbes apprentissage LGBM)
    - FONCTION : Affiche courbes d'apprentissage du modèle LGBM
    - UTILITÉ : Visualisation convergence et validation LGBM

17. _show_lstm_learning_curves.txt (HybridBaccaratPredictor._show_lstm_learning_curves - Affichage courbes LSTM)
    - FONCTION : Affiche courbes d'apprentissage LSTM dans interface
    - UTILITÉ : Interface visualisation performance LSTM

18. _save_lgbm_training_plots.txt (HybridBaccaratPredictor._save_lgbm_training_plots - Sauvegarde graphiques LGBM)
    - FONCTION : Sauvegarde graphiques d'entraînement LGBM sur disque
    - UTILITÉ : Persistance visualisations pour analyse ultérieure

19. _save_lstm_training_plots.txt (HybridBaccaratPredictor._save_lstm_training_plots - Sauvegarde graphiques LSTM)
    - FONCTION : Sauvegarde graphiques d'entraînement LSTM sur disque avec métadonnées complètes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * plots_data (Dict) - Données des graphiques à sauvegarder (loss, accuracy, validation curves)
      * output_dir (str, optionnel) - Répertoire de sauvegarde (défaut: ./plots/)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if not plots_data or not isinstance(plots_data, dict): return []`
      * **CRÉATION RÉPERTOIRE :** Appelle `os.makedirs(output_dir, exist_ok=True)` pour créer dossier si inexistant
      * **GÉNÉRATION TIMESTAMP :** Crée `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` pour noms uniques
      * **GÉNÉRATION GRAPHIQUES :** Itère sur `plots_data.items()` pour créer `plt.figure(figsize=(10, 6))` par métrique
      * **CONFIGURATION AXES :** Définit `plt.xlabel('Epochs')`, `plt.ylabel(metric_name)`, `plt.title(f'LSTM {metric_name} - {timestamp}')`
      * **TRACÉ COURBES :** Plot données avec `plt.plot(data, label=f'{metric_name}')` et `plt.legend()`
      * **FORMATAGE QUALITÉ :** Configure `plt.tight_layout()` et résolution `dpi=300` pour qualité publication
      * **SAUVEGARDE FICHIERS :** Sauvegarde avec `plt.savefig(f"{output_dir}/lstm_{metric_name}_{timestamp}.png", dpi=300, bbox_inches='tight')`
      * **MÉTADONNÉES JSON :** Crée fichier `metadata.json` avec `{"timestamp": timestamp, "model_type": "LSTM", "metrics": list(plots_data.keys())}`
      * **NETTOYAGE MÉMOIRE :** Appelle `plt.close()` après chaque graphique pour libérer mémoire
    - RETOUR : List[str] - Liste chemins fichiers sauvegardés (PNG + JSON metadata)
    - UTILITÉ : Persistance visualisations LSTM avec métadonnées pour documentation, analyse et traçabilité complète
