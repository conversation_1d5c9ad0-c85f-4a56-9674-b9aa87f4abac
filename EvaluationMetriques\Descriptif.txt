DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ÉVALUATION MÉTRIQUES
================================================================================

Ce fichier contient la description détaillée des méthodes d'évaluation et
de métriques du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : ÉVALUATION MÉTRIQUES
Méthodes d'évaluation des performances, calcul de métriques et validation
des modèles.

TOTAL : 19 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES ÉVALUATION MÉTRIQUES
================================================================================

1. update_statistics.txt (HybridBaccaratPredictor.update_statistics - Mise à jour statistiques interface)
   - Lignes 11533-11664 dans hbp.py (132 lignes)
   - FONCTION : Met à jour les labels de statistiques dans l'interface utilisateur avec métriques complètes et adaptation aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock et model_lock pour accès sécurisé aux données
     * **CALCUL SÉRIE :** Détermine la série actuelle (Player/Banker) et sa longueur
     * **PRÉCISION SESSION :** Calcule la précision basée sur les recommandations non-wait vs résultats réels
     * **DÉTECTION PLAGE CIBLE :** Vérifie si la manche actuelle est dans la plage 31-60 pour adaptation affichage
     * **MÉTRIQUES MÉTHODES :** Calcule précision et confiance pour chaque méthode (LGBM, LSTM, etc.)
     * **STATISTIQUES PARTIE :** Affiche répartition Player/Banker avec pourcentages
     * **INCERTITUDE DÉTAILLÉE :** Présente épistémique, aléatoire et sensibilité contextuelle
     * **SEUIL ADAPTATIF :** Affiche le seuil de confiance adaptatif actuel
     * **POIDS BAYÉSIENS :** Montre la pondération bayésienne des différentes méthodes
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Fournit un tableau de bord complet des performances en temps réel avec adaptation contextuelle

2. _create_metrics_dashboard.txt (HybridBaccaratPredictor._create_metrics_dashboard - Création tableau de bord métriques)
   - Lignes 6955-7003 dans hbp.py (49 lignes)
   - FONCTION : Crée tableau de bord interactif pour visualisation métriques détaillées avec interface à onglets
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec warning et retour si interface indisponible
     * **CRÉATION FENÊTRE :** Instancie `metrics_window = tk.Toplevel(self.root)` avec configuration :
       - `metrics_window.title("Tableau de Bord des Métriques d'Entraînement")`
       - `metrics_window.geometry("800x600")` pour taille initiale
       - `metrics_window.minsize(600, 400)` pour taille minimale
     * **CRÉATION NOTEBOOK :** Instancie `notebook = ttk.Notebook(metrics_window)` avec `notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)`
     * **ONGLET LGBM :** Crée `lgbm_frame = ttk.Frame(notebook)` puis `notebook.add(lgbm_frame, text="LGBM")` pour métriques LGBM
     * **ONGLET LSTM :** Crée `lstm_frame = ttk.Frame(notebook)` puis `notebook.add(lstm_frame, text="LSTM")` pour métriques LSTM
     * **ONGLET COMBINÉ :** Crée `combined_frame = ttk.Frame(notebook)` puis `notebook.add(combined_frame, text="Combiné")` pour métriques hybrides
     * **ONGLET GRAPHIQUES :** Crée `plots_frame = ttk.Frame(notebook)` puis `notebook.add(plots_frame, text="Graphiques")` pour visualisations
     * **REMPLISSAGE ONGLETS :** Appelle méthodes spécialisées :
       - `self._fill_lgbm_metrics_tab(lgbm_frame)` pour métriques LGBM détaillées
       - `self._fill_lstm_metrics_tab(lstm_frame)` pour métriques LSTM détaillées
       - `self._fill_combined_metrics_tab(combined_frame)` pour métriques combinées
       - `self._fill_plots_tab(plots_frame)` pour graphiques et visualisations
     * **BOUTON RAFRAÎCHISSEMENT :** Crée `refresh_button = ttk.Button(metrics_window, text="Rafraîchir les Métriques", command=lambda: self._refresh_metrics_dashboard(lgbm_frame, lstm_frame, combined_frame, plots_frame))` avec `refresh_button.pack(pady=10)`
   - RETOUR : None - Affiche fenêtre modale avec tableau de bord
   - UTILITÉ : Interface complète pour analyse performance avec onglets spécialisés, métriques détaillées et rafraîchissement temps réel

3. _refresh_metrics_dashboard.txt (HybridBaccaratPredictor._refresh_metrics_dashboard - Rafraîchissement tableau de bord)
   - Lignes 8107-8111 dans hbp.py (5 lignes)
   - FONCTION : Rafraîchit toutes les métriques dans le tableau de bord en appelant les méthodes de mise à jour spécialisées
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lgbm_frame - Frame Tkinter pour métriques LGBM
     * lstm_frame - Frame Tkinter pour métriques LSTM
     * combined_frame - Frame Tkinter pour métriques combinées
     * plots_frame - Frame Tkinter pour graphiques
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR LGBM :** Appelle `self._update_lgbm_metrics()` pour rafraîchir métriques LGBM (précision, rappel, F1, AUC-ROC, matrice confusion, importance features)
     * **MISE À JOUR LSTM :** Appelle `self._update_lstm_metrics()` pour rafraîchir métriques LSTM (loss, accuracy, courbes d'apprentissage)
     * **MISE À JOUR COMBINÉES :** Appelle `self._update_combined_metrics()` pour rafraîchir métriques hybrides (incertitudes, performance globale, poids bayésiens)
     * **SYNCHRONISATION INTERFACE :** Assure cohérence entre toutes les vues du tableau de bord
   - RETOUR : None - Met à jour directement l'interface utilisateur
   - UTILITÉ : Synchronisation complète du tableau de bord avec données actuelles pour monitoring temps réel

4. _fill_combined_metrics_tab.txt (HybridBaccaratPredictor._fill_combined_metrics_tab - Remplissage onglet métriques combinées)
   - Lignes 7422-7471 dans hbp.py (50 lignes)
   - FONCTION : Remplit onglet avec métriques combinées incluant incertitudes, poids modèles et performance globale
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SECTION INCERTITUDES :** Crée `uncertainty_frame = ttk.LabelFrame(parent_frame, text="Métriques d'Incertitude")` avec variables :
       - `'epistemic_uncertainty': tk.StringVar(value="Incertitude Épistémique: N/A")` pour désaccord entre modèles
       - `'aleatoric_uncertainty': tk.StringVar(value="Incertitude Aléatoire: N/A")` pour incertitude intrinsèque données
       - `'context_sensitivity': tk.StringVar(value="Sensibilité Contextuelle: N/A")` pour sensibilité au contexte
       - `'total_uncertainty': tk.StringVar(value="Incertitude Totale: N/A")` pour incertitude combinée
     * **DISPOSITION INCERTITUDES :** Place labels avec `ttk.Label(uncertainty_frame, textvariable=var).grid(row=i//2, column=i%2, padx=10, pady=5, sticky=tk.W)` en grille 2x2
     * **SECTION POIDS MODÈLES :** Crée `weights_frame = ttk.LabelFrame(parent_frame, text="Poids des Modèles")` avec variables :
       - `'markov_weight': tk.StringVar(value="Poids Markov: N/A")` pour pondération Markov
       - `'lgbm_weight': tk.StringVar(value="Poids LGBM: N/A")` pour pondération LGBM
       - `'lstm_weight': tk.StringVar(value="Poids LSTM: N/A")` pour pondération LSTM
     * **DISPOSITION POIDS :** Place labels avec `ttk.Label(weights_frame, textvariable=var).grid(row=0, column=i, padx=10, pady=5, sticky=tk.W)` en ligne horizontale
     * **SECTION PERFORMANCE :** Crée `performance_frame = ttk.LabelFrame(parent_frame, text="Performance Combinée")` avec variables :
       - `'combined_accuracy': tk.StringVar(value="Exactitude Combinée: N/A")` pour précision globale
       - `'recommendation_rate': tk.StringVar(value="Taux de Recommandation: N/A")` pour fréquence recommandations
       - `'average_confidence': tk.StringVar(value="Confiance Moyenne: N/A")` pour confiance moyenne
     * **DISPOSITION PERFORMANCE :** Place labels avec même système de grille pour affichage structuré
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Vue unifiée performance ensemble des modèles avec métriques d'incertitude, pondération et performance globale

5. _fill_lgbm_metrics_tab.txt (HybridBaccaratPredictor._fill_lgbm_metrics_tab - Remplissage onglet métriques LGBM)
   - Lignes 7005-7059 dans hbp.py (55 lignes)
   - FONCTION : Remplit onglet spécialisé pour métriques LGBM avec métriques base, matrice confusion et importance features
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTRIQUES BASE :** Crée `basic_metrics_frame = ttk.LabelFrame(parent_frame, text="Métriques de Base")` avec variables :
       - `'accuracy': tk.StringVar(value="Exactitude: N/A")` pour précision globale
       - `'precision': tk.StringVar(value="Précision: N/A")` pour précision classe positive
       - `'recall': tk.StringVar(value="Rappel: N/A")` pour sensibilité
       - `'f1': tk.StringVar(value="F1-Score: N/A")` pour moyenne harmonique
       - `'auc_roc': tk.StringVar(value="AUC-ROC: N/A")` pour aire sous courbe ROC
     * **DISPOSITION MÉTRIQUES :** Place labels avec `ttk.Label(basic_metrics_frame, textvariable=var).grid(row=i//2, column=i%2, padx=10, pady=5, sticky=tk.W)` en grille 2x3
     * **MATRICE CONFUSION :** Crée `confusion_matrix_frame = ttk.LabelFrame(parent_frame, text="Matrice de Confusion")` avec variables 2x2 :
       - `self.lgbm_cm_vars = [[tk.StringVar(value="0") for _ in range(2)] for _ in range(2)]` pour cellules matrice
       - Place avec `ttk.Label(confusion_matrix_frame, textvariable=self.lgbm_cm_vars[i][j]).grid(row=i+1, column=j+1, padx=5, pady=5)`
     * **LABELS MATRICE :** Ajoute `ttk.Label(confusion_matrix_frame, text="Prédiction").grid(row=0, column=1, columnspan=2)` et labels axes
     * **IMPORTANCE FEATURES :** Crée `feature_importance_frame = ttk.LabelFrame(parent_frame, text="Importance des Caractéristiques")` avec Treeview :
       - `self.lgbm_feature_tree = ttk.Treeview(feature_importance_frame, columns=('Feature', 'Importance'), show='headings', height=8)`
       - Configure colonnes avec `self.lgbm_feature_tree.heading('Feature', text='Caractéristique')` et `self.lgbm_feature_tree.heading('Importance', text='Importance')`
       - Ajoute scrollbar avec `scrollbar = ttk.Scrollbar(feature_importance_frame, orient=tk.VERTICAL, command=self.lgbm_feature_tree.yview)`
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Interface complète métriques LGBM avec visualisation matrice confusion et classement importance features

6. _fill_lstm_metrics_tab.txt (HybridBaccaratPredictor._fill_lstm_metrics_tab - Remplissage onglet métriques LSTM)
   - Lignes 7097-7151 dans hbp.py (55 lignes)
   - FONCTION : Remplit onglet spécialisé pour métriques LSTM avec métriques d'entraînement, validation et objectifs spécifiques
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTRIQUES BASE :** Crée `basic_metrics_frame = ttk.LabelFrame(parent_frame, text="Métriques de Base")` avec variables complètes :
       - `'train_loss': tk.StringVar(value="Perte d'entraînement: N/A")` pour loss entraînement
       - `'val_loss': tk.StringVar(value="Perte de validation: N/A")` pour loss validation
       - `'train_accuracy': tk.StringVar(value="Exactitude d'entraînement: N/A")` pour accuracy entraînement
       - `'val_accuracy': tk.StringVar(value="Exactitude de validation: N/A")` pour accuracy validation
       - `'precision': tk.StringVar(value="Précision: N/A")`, `'recall': tk.StringVar(value="Rappel: N/A")`, `'f1': tk.StringVar(value="F1-Score: N/A")` pour métriques classification
       - `'objective1': tk.StringVar(value="Obj1 (Consécutives 31-60): N/A")` pour objectif consécutives manches cibles
       - `'objective2': tk.StringVar(value="Obj2 (Précision 31-60): N/A")` pour objectif précision manches cibles
     * **DISPOSITION MÉTRIQUES :** Place labels avec `ttk.Label(basic_metrics_frame, textvariable=var).grid(row=i//3, column=i%3, padx=10, pady=5, sticky=tk.W)` en grille 3x3
     * **MATRICE CONFUSION :** Crée `confusion_frame = ttk.LabelFrame(parent_frame, text="Matrice de Confusion")` avec grille 2x2 :
       - `self.lstm_cm_vars = []` puis boucles `for i in range(2): for j in range(2):` pour créer `tk.StringVar(value="N/A")`
       - Place avec `ttk.Label(confusion_frame, textvariable=var, width=10, anchor='center', borderwidth=1, relief="solid").grid(row=i+1, column=j+1, padx=5, pady=5)`
     * **LABELS AXES :** Ajoute `ttk.Label(confusion_frame, text="Réel", font=('Arial', 10, 'bold')).grid(row=0, column=0)` et labels pour axes prédiction/réel
     * **CANVAS COURBES :** Crée `curves_frame = ttk.LabelFrame(parent_frame, text="Courbes d'Apprentissage")` avec `self.lstm_curves_canvas = tk.Canvas(curves_frame, width=600, height=400, bg='white')` pour visualisation courbes
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Interface complète métriques LSTM avec entraînement/validation, objectifs spécifiques manches 31-60 et visualisation courbes

7. _fill_plots_tab.txt (HybridBaccaratPredictor._fill_plots_tab - Remplissage onglet graphiques)
   - Lignes 7526-7547 dans hbp.py (22 lignes)
   - FONCTION : Remplit onglet avec boutons pour affichage graphiques et visualisations métriques avec zone d'affichage
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FRAME BOUTONS :** Crée `buttons_frame = ttk.Frame(parent_frame)` avec `buttons_frame.pack(fill=tk.X, padx=10, pady=5)` pour barre boutons
     * **BOUTON COURBES LGBM :** Crée `ttk.Button(buttons_frame, text="Courbes d'Apprentissage LGBM", command=self._show_lgbm_learning_curves).pack(side=tk.LEFT, padx=5)` pour affichage courbes LGBM
     * **BOUTON COURBES LSTM :** Crée `ttk.Button(buttons_frame, text="Courbes d'Apprentissage LSTM", command=self._show_lstm_learning_curves).pack(side=tk.LEFT, padx=5)` pour affichage courbes LSTM
     * **BOUTON MATRICES CONFUSION :** Crée `ttk.Button(buttons_frame, text="Matrices de Confusion", command=self._show_confusion_matrices).pack(side=tk.LEFT, padx=5)` pour affichage matrices
     * **BOUTON IMPORTANCE FEATURES :** Crée `ttk.Button(buttons_frame, text="Importance des Features", command=self._show_feature_importance).pack(side=tk.LEFT, padx=5)` pour importance LGBM
     * **ZONE AFFICHAGE :** Crée `self.plot_display_frame = ttk.Frame(parent_frame)` avec `self.plot_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)` pour contenu graphiques
     * **LABEL INITIAL :** Ajoute `ttk.Label(self.plot_display_frame, text="Sélectionnez un type de graphique à afficher").pack(pady=50)` comme placeholder
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Interface navigation graphiques avec boutons spécialisés et zone affichage dynamique pour visualisations métriques

8. _update_combined_metrics.txt (HybridBaccaratPredictor._update_combined_metrics - MAJ métriques combinées)
   - Lignes 7097-7151 dans hbp.py (55 lignes)
   - FONCTION : Met à jour métriques combinées de tous les modèles avec calcul performance globale et incertitudes
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION VARIABLES :** Vérifie `if not hasattr(self, 'combined_metric_vars'): return` pour éviter erreurs si interface non initialisée
     * **MÉTRIQUES INCERTITUDE :** Si `hasattr(self, 'uncertainty_metrics') and self.uncertainty_metrics:` :
       - Met à jour `self.combined_metric_vars['epistemic_uncertainty'].set(f"Incertitude Épistémique: {metrics['epistemic_uncertainty']:.4f}")` pour incertitude modèle
       - Met à jour `self.combined_metric_vars['aleatoric_uncertainty'].set(f"Incertitude Aléatoire: {metrics['aleatoric_uncertainty']:.4f}")` pour incertitude données
       - Met à jour `self.combined_metric_vars['context_sensitivity'].set(f"Sensibilité Contextuelle: {metrics['context_sensitivity']:.4f}")` pour adaptation contexte
       - Met à jour `self.combined_metric_vars['total_uncertainty'].set(f"Incertitude Totale: {metrics['total_uncertainty']:.4f}")` pour incertitude globale
     * **POIDS MODÈLES :** Met à jour affichage poids avec `self.config.weight_markov`, `self.config.weight_lgbm`, `self.config.weight_lstm` formatés en pourcentages
     * **CALCUL PERFORMANCE COMBINÉE :** Si `hasattr(self, 'prediction_history') and self.prediction_history:` :
       - Initialise compteurs `correct_predictions = 0`, `total_predictions = len(self.prediction_history)`, `recommendations = 0`, `total_confidence = 0.0`
       - Parcourt `for pred in self.prediction_history:` pour analyser chaque prédiction :
         - Incrémente `correct_predictions` si `pred['actual_outcome'] == pred['combined_prediction']` pour exactitude
         - Incrémente `recommendations` si `pred['recommendation'] is not None` pour taux recommandation
         - Ajoute `total_confidence += pred['combined_confidence']` pour confiance moyenne
     * **MÉTRIQUES FINALES :** Calcule et affiche :
       - `accuracy = correct_predictions / total_predictions` pour exactitude combinée globale
       - `recommendation_rate = recommendations / len(self.prediction_history)` pour pourcentage recommandations émises
       - `avg_confidence = total_confidence / len(self.prediction_history)` pour confiance moyenne système
     * **MISE À JOUR AFFICHAGE :** Met à jour variables Tkinter avec formatage approprié pour chaque métrique calculée
   - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
   - UTILITÉ : Affichage synthétique performance globale système avec métriques d'incertitude et statistiques combinées pour évaluation holistique

9. _update_lgbm_metrics.txt (HybridBaccaratPredictor._update_lgbm_metrics - MAJ métriques LGBM)
   - Lignes 7061-7095 dans hbp.py (35 lignes)
   - FONCTION : Met à jour les métriques LGBM dans le tableau de bord interface avec affichage formaté et importance features
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MÉTRIQUES :** Vérifie `if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics: return` pour éviter erreurs si métriques non disponibles
     * **MISE À JOUR MÉTRIQUES BASE :** Met à jour variables Tkinter pour chaque métrique disponible avec formatage précis :
       - `self.lgbm_metric_vars['precision'].set(f"Précision: {self.lgbm_metrics['precision']:.4f}")` si 'precision' dans métriques
       - `self.lgbm_metric_vars['recall'].set(f"Rappel: {self.lgbm_metrics['recall']:.4f}")` si 'recall' disponible
       - `self.lgbm_metric_vars['f1'].set(f"F1-Score: {self.lgbm_metrics['f1']:.4f}")` si 'f1' disponible
       - `self.lgbm_metric_vars['auc_roc'].set(f"AUC-ROC: {self.lgbm_metrics['auc_roc']:.4f}")` si 'auc_roc' disponible
       - `self.lgbm_metric_vars['accuracy'].set(f"Exactitude: {self.lgbm_metrics['accuracy']:.4f}")` si 'accuracy' disponible
     * **MISE À JOUR IMPORTANCE FEATURES :** Si `'feature_importance' in self.lgbm_metrics and hasattr(self, 'feature_names'):` :
       - Efface anciennes données avec `for item in self.lgbm_feature_tree.get_children(): self.lgbm_feature_tree.delete(item)`
       - Récupère `feature_importance = self.lgbm_metrics['feature_importance']` pour valeurs importance
       - Valide cohérence avec `if len(feature_importance) > 0 and len(self.feature_names) == len(feature_importance):`
       - Trie features par importance avec `sorted_features = sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)`
       - Insère dans Treeview avec `self.lgbm_feature_tree.insert('', 'end', values=(feature, f"{importance:.6f}"))` pour chaque feature
     * **PROTECTION ERREURS :** Utilise conditions pour éviter erreurs si variables UI non initialisées ou métriques manquantes
     * **FORMATAGE PRÉCISION :** Utilise formatage `.4f` pour métriques et `.6f` pour importance features pour lisibilité optimale
   - RETOUR : None - Méthode de mise à jour interface ne retourne rien
   - UTILITÉ : Synchronisation complète métriques LGBM avec interface utilisateur incluant importance features triées

10. _update_lstm_metrics.txt (HybridBaccaratPredictor._update_lstm_metrics - MAJ métriques LSTM)
    - Lignes 7153-7193 dans hbp.py (41 lignes)
    - FONCTION : Met à jour métriques LSTM dans tableau de bord avec loss, accuracy, métriques classification et matrice confusion
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MÉTRIQUES :** Vérifie `if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics: return` pour éviter erreurs
      * **MÉTRIQUES CLASSIFICATION :** Met à jour variables Tkinter pour métriques disponibles :
        - `self.lstm_metric_vars['precision'].set(f"Précision: {self.lstm_metrics['precision']:.4f}")` si 'precision' dans métriques
        - `self.lstm_metric_vars['recall'].set(f"Rappel: {self.lstm_metrics['recall']:.4f}")` si 'recall' dans métriques
        - `self.lstm_metric_vars['f1'].set(f"F1-Score: {self.lstm_metrics['f1']:.4f}")` si 'f1' dans métriques
      * **OBJECTIFS SPÉCIFIQUES :** Met à jour objectifs manches cibles 31-60 :
        - `self.lstm_metric_vars['objective1'].set(f"Obj1 (Consécutives 31-60): {self.lstm_metrics['objective1']:.4f}")` si disponible
        - `self.lstm_metric_vars['objective2'].set(f"Obj2 (Précision 31-60): {self.lstm_metrics['objective2']:.4f}")` si disponible
      * **LOSS ET ACCURACY :** Met à jour métriques d'entraînement :
        - Si `'train_losses' in self.lstm_metrics and self.lstm_metrics['train_losses']:`, récupère `last_train_loss = self.lstm_metrics['train_losses'][-1]` et met à jour
        - Si `'train_accuracies' in self.lstm_metrics and self.lstm_metrics['train_accuracies']:`, récupère `last_train_acc = self.lstm_metrics['train_accuracies'][-1]` et met à jour
      * **MATRICE CONFUSION :** Si `'confusion_matrix' in self.lstm_metrics:`, itère `for i in range(2): for j in range(2):` et met à jour `self.lstm_cm_vars[i][j].set(str(cm[i, j]))`
    - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
    - UTILITÉ : Affichage temps réel performance LSTM avec loss/accuracy, métriques classification et objectifs spécifiques manches cibles

10.5. _update_combined_metrics.txt (HybridBaccaratPredictor._update_combined_metrics - MAJ métriques combinées)
    - Lignes 7473-7524 dans hbp.py (52 lignes)
    - FONCTION : Met à jour les métriques combinées dans le tableau de bord avec calculs de performance globale
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MISE À JOUR INCERTITUDES :** Si `hasattr(self, 'prediction_history') and self.prediction_history:`, récupère `last_prediction = self.prediction_history[-1]`
      * **MÉTRIQUES CONFIANCE :** Extrait `metrics = last_prediction['confidence_metrics']` et met à jour variables Tkinter :
        - `self.combined_metric_vars['epistemic_uncertainty'].set(f"Incertitude Épistémique: {metrics['epistemic_uncertainty']:.4f}")`
        - `self.combined_metric_vars['aleatoric_uncertainty'].set(f"Incertitude Aléatoire: {metrics['aleatoric_uncertainty']:.4f}")`
        - `self.combined_metric_vars['context_sensitivity'].set(f"Sensibilité Contextuelle: {metrics['context_sensitivity']:.4f}")`
        - `self.combined_metric_vars['total_uncertainty'].set(f"Incertitude Totale: {metrics['total_uncertainty']:.4f}")`
      * **POIDS MODÈLES :** Met à jour affichage poids avec `self.config.weight_markov`, `self.config.weight_lgbm`, `self.config.weight_lstm`
      * **CALCUL PERFORMANCE COMBINÉE :** Parcourt `self.prediction_history` pour calculer :
        - `correct_predictions` et `total_predictions` avec comparaison `pred['actual_outcome'] == pred['combined_prediction']`
        - `recommendations` en comptant `pred['recommendation'] is not None`
        - `total_confidence` en sommant `pred['combined_confidence']`
      * **MÉTRIQUES FINALES :** Calcule et affiche :
        - `accuracy = correct_predictions / total_predictions` pour exactitude combinée
        - `recommendation_rate = recommendations / len(self.prediction_history)` pour taux de recommandation
        - `avg_confidence = total_confidence / len(self.prediction_history)` pour confiance moyenne
    - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
    - UTILITÉ : Affichage synthétique performance globale système avec métriques d'incertitude et statistiques combinées

11. _draw_confusion_matrix.txt (HybridBaccaratPredictor._draw_confusion_matrix - Dessin matrice confusion)
    - Lignes 8031-8099 dans hbp.py (69 lignes)
    - FONCTION : Dessine matrice de confusion sur canvas Tkinter avec couleurs graduées et valeurs pour visualisation performance classification
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * parent_frame - Frame Tkinter parent pour intégration canvas
      * cm (numpy.ndarray) - Matrice de confusion 2x2 avec valeurs entières
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(parent_frame, bg='white')` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)` pour intégration
      * **CALCUL DIMENSIONS :** Utilise `canvas.update()` puis `canvas_width = canvas.winfo_width()` et `canvas_height = canvas.winfo_height()` avec fallback 300x300
      * **CALCUL CELLULES :** Détermine `margin = 20`, `available_size = min(canvas_width, canvas_height) - 2 * margin`, `cell_size = available_size // 2` pour cellules carrées
      * **NORMALISATION COULEURS :** Calcule `max_val = max(cm[0, 0], cm[0, 1], cm[1, 0], cm[1, 1])` pour intensité maximale
      * **DESSIN CELLULES :** Double boucle `for i in range(2): for j in range(2):` pour chaque cellule matrice :
        - Calcule position `x1 = margin + j * cell_size`, `y1 = margin + i * cell_size`, `x2 = x1 + cell_size`, `y2 = y1 + cell_size`
        - Détermine intensité `intensity = cm[i, j] / max_val if max_val > 0 else 0` pour gradient couleur
        - Appelle `color = self._get_color_for_intensity(intensity)` pour couleur appropriée
        - Dessine rectangle `canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")` avec bordure
      * **AJOUT VALEURS :** Pour chaque cellule, calcule centre `(x1 + x2) / 2`, `(y1 + y2) / 2` puis :
        - Ajoute texte `canvas.create_text(center_x, center_y, text=str(cm[i, j]), fill="white" if intensity > 0.5 else "black", font=("Arial", 12, "bold"))` avec contraste adaptatif
      * **LABELS AXES :** Ajoute labels classes avec `canvas.create_text()` pour "Banker" et "Player" sur axes appropriés
      * **MÉTHODE COULEUR :** `_get_color_for_intensity()` retourne couleur hexadécimale basée sur intensité (bleu clair à bleu foncé)
    - RETOUR : None - Dessine directement sur canvas fourni
    - UTILITÉ : Visualisation intuitive performance classification avec couleurs graduées et valeurs numériques pour analyse rapide confusion

12. _draw_curve.txt (HybridBaccaratPredictor._draw_curve - Dessin courbes performance)
    - Lignes 7405-7420 dans hbp.py (16 lignes)
    - FONCTION : Dessine une courbe de données sur canvas Tkinter avec normalisation automatique et lissage
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * canvas - Canvas Tkinter où dessiner la courbe
      * data (List[float]) - Données à tracer (valeurs Y)
      * min_val (float) - Valeur minimale pour normalisation Y
      * max_val (float) - Valeur maximale pour normalisation Y
      * x_offset (int) - Décalage horizontal de départ
      * y_offset (int) - Décalage vertical de départ
      * width (int) - Largeur de la zone de tracé
      * height (int) - Hauteur de la zone de tracé
      * color (str, optionnel) - Couleur de la courbe (défaut: "blue")
      * label (str, optionnel) - Label pour légende (défaut: "")
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if not data: return` pour éviter tracé vide
      * **CALCUL POINTS :** Itère sur données avec `for i, val in enumerate(data):`
      * **NORMALISATION X :** Calcule `x = x_offset + (i / (len(data) - 1 if len(data) > 1 else 1)) * width` pour répartition uniforme
      * **NORMALISATION Y :** Calcule `y = y_offset + height - ((val - min_val) / (max_val - min_val if max_val > min_val else 1)) * height` avec inversion axe Y
      * **CONSTRUCTION LISTE :** Ajoute `points.append(x)` puis `points.append(y)` pour format Tkinter
      * **TRACÉ COURBE :** Si `len(points) >= 4:`, appelle `canvas.create_line(points, fill=color, width=2, smooth=True)` avec lissage
    - RETOUR : None - Dessine directement sur le canvas
    - UTILITÉ : Visualisation courbes métriques d'entraînement avec normalisation automatique et rendu lisse

13. _draw_lstm_learning_curves.txt (HybridBaccaratPredictor._draw_lstm_learning_curves - Courbes apprentissage LSTM)
    - Lignes 7195-7403 dans hbp.py (209 lignes)
    - FONCTION : Dessine courbes d'apprentissage LSTM sur canvas avec gestion automatique des échelles et légendes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PRÉREQUIS :** Vérifie `if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics or not hasattr(self, 'lstm_curves_canvas'): return`
      * **NETTOYAGE CANVAS :** Appelle `self.lstm_curves_canvas.delete("all")` pour effacer contenu précédent
      * **CALCUL DIMENSIONS :** Détermine `canvas_width = self.lstm_curves_canvas.winfo_width()`, `canvas_height = self.lstm_curves_canvas.winfo_height()` avec fallback 400x300
      * **DÉFINITION MARGES :** Configure `margin_left = 50`, `margin_top = 30`, `margin_right = 30`, `margin_bottom = 50` pour espacement
      * **CALCUL ZONE TRACÉ :** Calcule `plot_width = canvas_width - margin_left - margin_right`, `plot_height = canvas_height - margin_top - margin_bottom`
      * **VÉRIFICATION MÉTRIQUES :** Teste existence `has_train_loss = 'train_losses' in self.lstm_metrics`, `has_val_loss = 'val_losses' in self.lstm_metrics`, etc.
      * **TRACÉ COURBES PERTE :** Si données disponibles, calcule `min_loss = min(all_losses)`, `max_loss = max(all_losses)` avec marge 10%
      * **APPEL _draw_curve PERTE :** Utilise `self._draw_curve(self.lstm_curves_canvas, train_losses, min_loss, max_loss, margin_left, margin_top, plot_width, plot_height, color="blue")`
      * **TRACÉ COURBES EXACTITUDE :** Même processus pour accuracy avec `min_acc`, `max_acc` et position `margin_top + plot_height // 2`
      * **GÉNÉRATION LÉGENDE :** Crée légende avec `legend_x = margin_left + plot_width - 150`, `legend_y = margin_top + 10`
      * **ÉLÉMENTS LÉGENDE :** Pour chaque courbe, dessine ligne avec `canvas.create_line()` et texte avec `canvas.create_text()`
      * **AXES ET LABELS :** Dessine axes avec `canvas.create_line()` et labels avec `canvas.create_text()` pour graduation
    - RETOUR : None - Dessine directement sur le canvas LSTM
    - UTILITÉ : Visualisation complète convergence LSTM avec perte et exactitude, échelles automatiques et légendes

14. _show_confusion_matrices.txt (HybridBaccaratPredictor._show_confusion_matrices - Affichage matrices confusion)
    - Lignes 8006-8029 dans hbp.py (24 lignes)
    - FONCTION : Affiche matrices de confusion pour tous les modèles entraînés dans l'interface graphique
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VÉRIFICATION LGBM :** Teste `has_lgbm_cm = hasattr(self, 'lgbm_metrics') and self.lgbm_metrics and 'confusion_matrix' in self.lgbm_metrics`
      * **VÉRIFICATION LSTM :** Teste `has_lstm_cm = hasattr(self, 'lstm_metrics') and self.lstm_metrics and 'confusion_matrix' in self.lstm_metrics`
      * **VALIDATION DONNÉES :** Si `not (has_lgbm_cm or has_lstm_cm):`, affiche `ttk.Label(self.plot_display_frame, text="Aucune matrice de confusion disponible").pack(pady=50)` et retourne
      * **CRÉATION FRAME LGBM :** Si `has_lgbm_cm:`, crée `lgbm_frame = ttk.LabelFrame(self.plot_display_frame, text="Matrice de Confusion LGBM")` avec `pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=5)`
      * **AFFICHAGE LGBM :** Appelle `self._draw_confusion_matrix(lgbm_frame, self.lgbm_metrics['confusion_matrix'])` pour rendu visuel
      * **CRÉATION FRAME LSTM :** Si `has_lstm_cm:`, crée `lstm_frame = ttk.LabelFrame(self.plot_display_frame, text="Matrice de Confusion LSTM")` avec même configuration
      * **AFFICHAGE LSTM :** Appelle `self._draw_confusion_matrix(lstm_frame, self.lstm_metrics['confusion_matrix'])` pour rendu visuel
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Comparaison visuelle côte-à-côte des performances de classification LGBM et LSTM avec matrices colorées

15. _show_feature_importance.txt (HybridBaccaratPredictor._show_feature_importance - Affichage importance features)
    - Lignes 7916-8004 dans hbp.py (89 lignes)
    - FONCTION : Affiche graphique barres horizontales importance features LGBM avec tri et limitation top 15
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VALIDATION DONNÉES :** Vérifie `if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics or 'feature_importance' not in self.lgbm_metrics:` avec message d'erreur
      * **VALIDATION NOMS :** Contrôle `if not hasattr(self, 'feature_names') or not self.feature_names:` avec message "Noms des caractéristiques non disponibles"
      * **VALIDATION COHÉRENCE :** Teste `if len(feature_importance) == 0 or len(self.feature_names) != len(feature_importance):` avec message "Données d'importance des caractéristiques invalides"
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(self.plot_display_frame, bg='white')` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)`
      * **CALCUL DIMENSIONS :** Détermine `canvas_width = canvas.winfo_width()`, `canvas_height = canvas.winfo_height()` avec fallback 600x400
      * **DÉFINITION MARGES :** Configure `margin_left = 150`, `margin_right = 20`, `margin_top = 20`, `margin_bottom = 20` pour espacement
      * **TRI FEATURES :** Utilise `sorted_features = sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)` pour classement décroissant
      * **LIMITATION TOP 15 :** Applique `top_n = min(15, len(sorted_features))` puis `sorted_features = sorted_features[:top_n]` pour lisibilité
      * **CALCUL BARRES :** Détermine `bar_height = plot_height / top_n`, `max_importance = max(importance for _, importance in sorted_features)` pour normalisation
      * **DESSIN BARRES :** Pour chaque feature, calcule coordonnées `x1 = margin_left`, `y1 = margin_top + i * bar_height`, `x2 = margin_left + (importance / max_importance) * plot_width`, `y2 = y1 + bar_height * 0.8`
      * **RENDU VISUEL :** Dessine avec `canvas.create_rectangle(x1, y1, x2, y2, fill="blue")` pour barre, `canvas.create_text()` pour nom feature (ancré à droite) et valeur importance (ancré à gauche)
      * **TITRE GRAPHIQUE :** Ajoute `canvas.create_text(canvas_width // 2, margin_top // 2, text="Importance des Caractéristiques LGBM", fill="black", font=("Arial", 12, "bold"))`
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Visualisation graphique importance features LGBM avec barres horizontales, tri décroissant et limitation top 15 pour interprétabilité

16. _show_lgbm_learning_curves.txt (HybridBaccaratPredictor._show_lgbm_learning_curves - Courbes apprentissage LGBM)
    - Lignes 7549-7680 dans hbp.py (132 lignes)
    - FONCTION : Affiche courbes d'apprentissage LGBM avec canvas Tkinter et échelles automatiques pour métriques d'entraînement
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VALIDATION DONNÉES :** Vérifie `if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics or 'eval_result' not in self.lgbm_metrics:` avec message "Aucune donnée de courbe d'apprentissage LGBM disponible"
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(self.plot_display_frame, width=800, height=600, bg="white")` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)`
      * **CONFIGURATION DIMENSIONS :** Définit `canvas_width = 800`, `canvas_height = 600`, `margin_left = 80`, `margin_right = 50`, `margin_top = 50`, `margin_bottom = 80`, `plot_width = canvas_width - margin_left - margin_right`, `plot_height = canvas_height - margin_top - margin_bottom`
      * **EXTRACTION DONNÉES :** Récupère `eval_result = self.lgbm_metrics['eval_result']` puis extrait métriques disponibles (train/valid loss, accuracy, etc.)
      * **AXES PRINCIPAUX :** Dessine axes avec `canvas.create_line(margin_left, margin_top, margin_left, canvas_height - margin_bottom, fill="black", width=2)` et `canvas.create_line(margin_left, canvas_height - margin_bottom, canvas_width - margin_right, canvas_height - margin_bottom, fill="black", width=2)`
      * **CALCUL ÉCHELLES :** Détermine `min_val = min(all_values)`, `max_val = max(all_values)` avec marge 10% pour normalisation
      * **TRACÉ COURBES :** Pour chaque métrique disponible, appelle `self._draw_curve()` avec couleurs spécifiques (bleu train, rouge validation)
      * **LÉGENDES :** Ajoute légendes avec `canvas.create_line()` et `canvas.create_text()` pour chaque courbe tracée
      * **ÉTIQUETTES AXES :** Ajoute "Itération" en bas et "Valeur" à gauche avec `canvas.create_text()`
      * **TITRE GRAPHIQUE :** Ajoute titre centré "Courbes d'Apprentissage LGBM" avec police bold
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Visualisation convergence LGBM avec courbes train/validation, échelles automatiques et interface Tkinter native

17. _show_lstm_learning_curves.txt (HybridBaccaratPredictor._show_lstm_learning_curves - Affichage courbes LSTM)
    - Lignes 7682-7914 dans hbp.py (233 lignes)
    - FONCTION : Affiche courbes d'apprentissage LSTM dans interface graphique avec canvas Tkinter et échelles automatiques pour perte et exactitude
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VALIDATION DONNÉES :** Vérifie `if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics:` avec affichage `ttk.Label(self.plot_display_frame, text="Aucune donnée de courbe d'apprentissage LSTM disponible").pack(pady=50)` et retour
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(self.plot_display_frame, width=800, height=600, bg='white')` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)`
      * **CONFIGURATION LAYOUT :** Définit `margin_left = 80`, `margin_top = 50`, `margin_right = 50`, `margin_bottom = 80` pour espacement et `plot_width = 800 - margin_left - margin_right`, `plot_height = 250` pour zone tracé
      * **VÉRIFICATION DONNÉES PERTE :** Teste `has_train_loss = 'train_losses' in self.lstm_metrics and self.lstm_metrics['train_losses']` et `has_val_loss = 'val_losses' in self.lstm_metrics and self.lstm_metrics['val_losses']`
      * **TRACÉ COURBES PERTE :** Si `has_train_loss or has_val_loss:` :
        - Calcule échelles avec `all_losses = []`, ajoute données disponibles et détermine `min_loss = min(all_losses)`, `max_loss = max(all_losses)`
        - Appelle `self._draw_curve(canvas, self.lstm_metrics['train_losses'], min_loss, max_loss, margin_left, margin_top, plot_width, plot_height, color="blue")` pour perte entraînement
        - Appelle `self._draw_curve(canvas, self.lstm_metrics['val_losses'], min_loss, max_loss, margin_left, margin_top, plot_width, plot_height, color="red")` pour perte validation si disponible
      * **VÉRIFICATION DONNÉES EXACTITUDE :** Teste `has_train_acc = 'train_accuracies' in self.lstm_metrics and self.lstm_metrics['train_accuracies']` et `has_val_acc = 'val_accuracies' in self.lstm_metrics and self.lstm_metrics['val_accuracies']`
      * **TRACÉ COURBES EXACTITUDE :** Si `has_train_acc or has_val_acc:` :
        - Même processus avec position décalée `margin_top + plot_height + 40` pour séparation visuelle
        - Utilise couleurs vert pour entraînement et violet pour validation
      * **GÉNÉRATION LÉGENDES :** Ajoute légendes avec `canvas.create_line()` et `canvas.create_text()` pour chaque courbe disponible avec couleurs correspondantes
      * **AXES ET LABELS :** Dessine axes avec `canvas.create_line()` et ajoute labels "Époque" en bas et "Valeur" à gauche avec rotation si supportée
      * **TITRE GRAPHIQUE :** Ajoute titre centré "Courbes d'Apprentissage LSTM" avec police bold
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Visualisation complète convergence LSTM avec perte et exactitude, échelles automatiques, légendes et interface Tkinter native pour analyse performance

18. _save_lgbm_training_plots.txt (HybridBaccaratPredictor._save_lgbm_training_plots - Sauvegarde graphiques LGBM)
    - Lignes 6716-6845 dans hbp.py (130 lignes)
    - FONCTION : Crée et sauvegarde graphiques matplotlib pour métriques LGBM avec courbes, importance features, matrice confusion et ROC
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * eval_result (Dict) - Dictionnaire résultats évaluation LGBM avec métriques train/validation
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION MATPLOTLIB :** Force `matplotlib.use('Agg', force=True)` pour backend non-interactif et éviter problèmes thread
      * **CRÉATION DOSSIER :** Utilise `plots_dir = os.path.join("logs", "training_plots")` puis `os.makedirs(plots_dir, exist_ok=True)` pour structure
      * **TIMESTAMP :** Génère `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` pour noms fichiers uniques
      * **EXTRACTION MÉTRIQUES :** Récupère `metrics = set()` puis itère `for split in ['train', 'val']:` et `for metric in eval_result[split].keys():` pour collecter métriques disponibles
      * **GRAPHIQUES MÉTRIQUES :** Pour chaque métrique, crée `plt.figure(figsize=(10, 5))` puis trace :
        - `plt.plot(eval_result['train'][metric], label=f'Train {metric}')` si disponible
        - `plt.plot(eval_result['val'][metric], label=f'Validation {metric}')` si disponible
        - Configure titre, axes, légende et grille puis sauvegarde avec `plt.savefig(metric_plot_path)` et `plt.close()`
      * **IMPORTANCE FEATURES :** Si `'feature_importance' in self.lgbm_metrics and hasattr(self, 'feature_names'):` :
        - Trie avec `indices = np.argsort(feature_importance)[::-1]` et limite `top_n = min(20, len(indices))`
        - Crée `plt.figure(figsize=(12, 8))` avec `plt.barh()` pour barres horizontales
        - Configure `plt.yticks(range(top_n), [self.feature_names[i] for i in indices[:top_n]])` pour labels
      * **MATRICE CONFUSION :** Si `'confusion_matrix' in self.lgbm_metrics:` :
        - Crée `plt.figure(figsize=(8, 6))` avec `plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)`
        - Ajoute valeurs cellules avec boucles `for i in range(cm.shape[0]): for j in range(cm.shape[1]):` et `plt.text()`
      * **COURBE ROC :** Si `'auc_roc' in self.lgbm_metrics:` :
        - Trace ligne diagonale `plt.plot([0, 1], [0, 1], 'k--')` et configure axes `plt.xlim([0.0, 1.0])`, `plt.ylim([0.0, 1.05])`
        - Ajoute titre avec AUC `plt.title(f'Courbe ROC (AUC = {auc_roc:.4f})')`
      * **GESTION ERREURS :** Capture exceptions avec logging détaillé pour chaque étape de création graphique
    - RETOUR : None - Sauvegarde fichiers PNG sur disque
    - UTILITÉ : Persistance complète visualisations LGBM avec matplotlib pour analyse post-entraînement et documentation

19. _save_lstm_training_plots.txt (HybridBaccaratPredictor._save_lstm_training_plots - Sauvegarde graphiques LSTM)
    - Lignes 6847-6953 dans hbp.py (107 lignes)
    - FONCTION : Crée et sauvegarde graphiques matplotlib pour métriques LSTM avec courbes loss/accuracy et matrice confusion
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION MATPLOTLIB :** Force `matplotlib.use('Agg', force=True)` pour backend non-interactif et éviter problèmes thread
      * **VALIDATION MÉTRIQUES :** Vérifie `if not hasattr(self, 'lstm_metrics') or not isinstance(self.lstm_metrics, dict) or not self.lstm_metrics:` avec warning et retour
      * **CRÉATION DOSSIER :** Utilise `plots_dir = os.path.join("logs", "training_plots")` puis `os.makedirs(plots_dir, exist_ok=True)` pour structure
      * **TIMESTAMP :** Génère `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` pour noms fichiers uniques
      * **GRAPHIQUE PERTES :** Si `'train_losses' in self.lstm_metrics:` :
        - Crée `plt.figure(figsize=(10, 5))` avec `plt.plot(self.lstm_metrics['train_losses'], label='Train Loss')`
        - Ajoute validation si `'val_losses' in self.lstm_metrics:` avec `plt.plot(self.lstm_metrics['val_losses'], label='Validation Loss')`
        - Configure titre "Évolution de la perte pendant l'entraînement LSTM", axes "Époque"/"Perte", légende et grille
        - Sauvegarde avec `loss_plot_path = os.path.join(plots_dir, f"lstm_loss_plot_{timestamp}.png")` et `plt.savefig(loss_plot_path)`
      * **GRAPHIQUE EXACTITUDES :** Si `'train_accuracies' in self.lstm_metrics:` :
        - Crée `plt.figure(figsize=(10, 5))` avec `plt.plot(self.lstm_metrics['train_accuracies'], label='Train Accuracy')`
        - Ajoute validation si `'val_accuracies' in self.lstm_metrics:` avec `plt.plot(self.lstm_metrics['val_accuracies'], label='Validation Accuracy')`
        - Configure titre "Évolution de l'exactitude pendant l'entraînement LSTM", axes "Époque"/"Exactitude"
      * **MATRICE CONFUSION :** Si `'confusion_matrix' in self.lstm_metrics:` :
        - Crée `plt.figure(figsize=(8, 6))` avec `plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)`
        - Configure classes `['Banker', 'Player']` avec `plt.xticks()` et `plt.yticks()`
        - Ajoute valeurs cellules avec boucles `for i, j in range(cm.shape):` et `plt.text(j, i, format(cm[i, j], 'd'))`
      * **GESTION ERREURS :** Capture `except Exception as e:` avec `logger.error(f"Erreur lors de la création des graphiques LSTM: {e}", exc_info=True)`
    - RETOUR : None - Sauvegarde fichiers PNG sur disque
    - UTILITÉ : Persistance visualisations LSTM avec matplotlib pour analyse convergence et performance post-entraînement
