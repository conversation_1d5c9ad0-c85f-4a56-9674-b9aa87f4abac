# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8006 à 8029
# Type: Méthode de la classe HybridBaccaratPredictor

    def _show_confusion_matrices(self):
        """Affiche les matrices de confusion dans l'onglet des graphiques."""
        # Effacer le contenu précédent
        for widget in self.plot_display_frame.winfo_children():
            widget.destroy()

        # Vérifier si les matrices de confusion sont disponibles
        has_lgbm_cm = hasattr(self, 'lgbm_metrics') and self.lgbm_metrics and 'confusion_matrix' in self.lgbm_metrics
        has_lstm_cm = hasattr(self, 'lstm_metrics') and self.lstm_metrics and 'confusion_matrix' in self.lstm_metrics

        if not (has_lgbm_cm or has_lstm_cm):
            ttk.Label(self.plot_display_frame, text="Aucune matrice de confusion disponible").pack(pady=50)
            return

        # Créer un cadre pour chaque matrice de confusion
        if has_lgbm_cm:
            lgbm_frame = ttk.LabelFrame(self.plot_display_frame, text="Matrice de Confusion LGBM")
            lgbm_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=5)
            self._draw_confusion_matrix(lgbm_frame, self.lgbm_metrics['confusion_matrix'])

        if has_lstm_cm:
            lstm_frame = ttk.LabelFrame(self.plot_display_frame, text="Matrice de Confusion LSTM")
            lstm_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=5)
            self._draw_confusion_matrix(lstm_frame, self.lstm_metrics['confusion_matrix'])