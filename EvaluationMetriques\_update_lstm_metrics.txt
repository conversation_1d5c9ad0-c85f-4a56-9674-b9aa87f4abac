# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7153 à 7193
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_lstm_metrics(self):
        """Met à jour les métriques LSTM dans le tableau de bord."""
        if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics:
            return

        # Mettre à jour les métriques de base
        if 'loss' in self.lstm_metrics:
            self.lstm_metric_vars['val_loss'].set(f"Perte de validation: {self.lstm_metrics['loss']:.4f}")
        if 'accuracy' in self.lstm_metrics:
            self.lstm_metric_vars['val_accuracy'].set(f"Exactitude de validation: {self.lstm_metrics['accuracy']:.4f}")
        if 'precision' in self.lstm_metrics:
            self.lstm_metric_vars['precision'].set(f"Précision: {self.lstm_metrics['precision']:.4f}")
        if 'recall' in self.lstm_metrics:
            self.lstm_metric_vars['recall'].set(f"Rappel: {self.lstm_metrics['recall']:.4f}")
        if 'f1' in self.lstm_metrics:
            self.lstm_metric_vars['f1'].set(f"F1-Score: {self.lstm_metrics['f1']:.4f}")

        # Mettre à jour les métriques des objectifs 1 et 2
        if 'objective1_metric' in self.lstm_metrics:
            self.lstm_metric_vars['objective1'].set(f"Obj1 (Consécutives 31-60): {self.lstm_metrics['objective1_metric']}")
        if 'objective2_metric' in self.lstm_metrics:
            self.lstm_metric_vars['objective2'].set(f"Obj2 (Précision 31-60): {self.lstm_metrics['objective2_metric']:.4f}")

        # Mettre à jour les pertes d'entraînement et de validation
        if 'train_losses' in self.lstm_metrics and self.lstm_metrics['train_losses']:
            last_train_loss = self.lstm_metrics['train_losses'][-1]
            self.lstm_metric_vars['train_loss'].set(f"Perte d'entraînement: {last_train_loss:.4f}")

            if 'train_accuracies' in self.lstm_metrics and self.lstm_metrics['train_accuracies']:
                last_train_acc = self.lstm_metrics['train_accuracies'][-1]
                self.lstm_metric_vars['train_accuracy'].set(f"Exactitude d'entraînement: {last_train_acc:.4f}")

        # Mettre à jour la matrice de confusion
        if 'confusion_matrix' in self.lstm_metrics:
            cm = self.lstm_metrics['confusion_matrix']
            for i in range(2):
                for j in range(2):
                    self.lstm_cm_vars[i][j].set(str(cm[i, j]))

        # Dessiner les courbes d'apprentissage
        self._draw_lstm_learning_curves()