# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 2029 à 2337
# Type: Méthode

def evaluate_kpis(y_true, y_pred, probas, recommendations, target_rounds=None, window_size=None, stability_threshold=None, context_analysis=True, config=None):
    """
    Évalue les KPIs avancés pour les prédictions.

    Args:
        y_true: Valeurs réelles (0 pour banker, 1 pour player)
        y_pred: Valeurs prédites (0 pour banker, 1 pour player)
        probas: Probabilités prédites pour banker
        recommendations: Liste des recommandations ('banker', 'player', 'WAIT')
        target_rounds: Plage de manches à considérer (None pour toutes)
        window_size: Taille de la fenêtre pour les métriques de stabilité (optionnel)
        stability_threshold: Seuil pour considérer une variation comme instable (optionnel)
        context_analysis: Activer l'analyse contextuelle
        config: Configuration du prédicteur (optionnel, pour mise à jour des paramètres)

    Returns:
        Dictionnaire contenant les métriques avancées
    """
    # Récupérer les paramètres depuis la configuration
    if config:
        window_size = window_size or getattr(config, 'kpi_window_size', 10)
        stability_threshold = stability_threshold or getattr(config, 'stability_threshold', 0.1)
    else:
        window_size = window_size or 10
        stability_threshold = stability_threshold or 0.1
    import numpy as np
    import logging

    logger = logging.getLogger(__name__)

    # Vérifier les dimensions
    if len(y_true) != len(y_pred) or len(y_pred) != len(probas) or len(probas) != len(recommendations):
        logger.error(f"Dimensions incohérentes: y_true={len(y_true)}, y_pred={len(y_pred)}, probas={len(probas)}, recommendations={len(recommendations)}")
        return {}

    # Filtrer par plage de manches si spécifié
    if target_rounds is not None:
        start, end = target_rounds
        if start < 0 or end >= len(y_true) or start > end:
            logger.warning(f"Plage de manches invalide: {target_rounds}")
            target_rounds = None

    if target_rounds is not None:
        start, end = target_rounds
        indices = list(range(start, min(end + 1, len(y_true))))
        y_true = y_true[indices]
        y_pred = y_pred[indices]
        probas = probas[indices]
        recommendations = [recommendations[i] for i in indices]

    # 1. Métriques de distribution des performances
    performance_distribution = {}

    if len(y_true) >= window_size:
        # Calculer l'accuracy sur des fenêtres glissantes
        window_accuracies = []
        for i in range(len(y_true) - window_size + 1):
            window_y_true = y_true[i:i+window_size]
            window_y_pred = y_pred[i:i+window_size]
            window_acc = np.mean(window_y_true == window_y_pred)
            window_accuracies.append(window_acc)

        performance_distribution["accuracy_mean"] = np.mean(window_accuracies)
        performance_distribution["accuracy_std"] = np.std(window_accuracies)
        performance_distribution["accuracy_min"] = np.min(window_accuracies)
        performance_distribution["accuracy_max"] = np.max(window_accuracies)
        performance_distribution["accuracy_range"] = performance_distribution["accuracy_max"] - performance_distribution["accuracy_min"]

    # 2. Métriques de performance contextuelle
    context_performance = {}

    if context_analysis and len(y_true) > 1:
        # Contexte d'alternance (banker puis player ou vice versa)
        alternating_indices = []
        for i in range(1, len(y_true)):
            if y_true[i] != y_true[i-1]:
                alternating_indices.append(i)

        if alternating_indices:
            alternating_accuracy = np.mean([1 if y_true[i] == y_pred[i] else 0 for i in alternating_indices])
            context_performance["alternating_accuracy"] = alternating_accuracy

        # Contexte de répétition (banker puis banker ou player puis player)
        repeating_indices = []
        for i in range(1, len(y_true)):
            if y_true[i] == y_true[i-1]:
                repeating_indices.append(i)

        if repeating_indices:
            repeating_accuracy = np.mean([1 if y_true[i] == y_pred[i] else 0 for i in repeating_indices])
            context_performance["repeating_accuracy"] = repeating_accuracy

        # Performance après des séries de banker ou player
        banker_series_indices = []
        player_series_indices = []

        for i in range(3, len(y_true)):
            # Après 3+ banker consécutifs
            if y_true[i-3] == 0 and y_true[i-2] == 0 and y_true[i-1] == 0:
                banker_series_indices.append(i)

            # Après 3+ player consécutifs
            if y_true[i-3] == 1 and y_true[i-2] == 1 and y_true[i-1] == 1:
                player_series_indices.append(i)

        if banker_series_indices:
            after_banker_series_acc = np.mean([1 if y_true[i] == y_pred[i] else 0 for i in banker_series_indices])
            context_performance["after_banker_series_acc"] = after_banker_series_acc

        if player_series_indices:
            after_player_series_acc = np.mean([1 if y_true[i] == y_pred[i] else 0 for i in player_series_indices])
            context_performance["after_player_series_acc"] = after_player_series_acc

    # 3. Stabilité des prédictions au fil du temps
    stability_metrics = {}

    if len(probas) >= window_size:
        # Calculer la variance des probabilités sur des fenêtres glissantes
        proba_variances = []
        for i in range(len(probas) - window_size + 1):
            window_probas = probas[i:i+window_size]
            proba_variances.append(np.var(window_probas))

        stability_metrics["proba_variance_mean"] = np.mean(proba_variances)
        stability_metrics["proba_variance_max"] = np.max(proba_variances)

        # Calculer le nombre de changements de prédiction
        prediction_changes = sum(1 for i in range(1, len(y_pred)) if y_pred[i] != y_pred[i-1])
        stability_metrics["prediction_change_rate"] = prediction_changes / (len(y_pred) - 1) if len(y_pred) > 1 else 0.0

        # Calculer la stabilité des recommandations
        stable_recommendations = 0
        for i in range(1, len(recommendations)):
            if recommendations[i] == recommendations[i-1]:
                stable_recommendations += 1

        stability_metrics["recommendation_stability"] = stable_recommendations / (len(recommendations) - 1) if len(recommendations) > 1 else 0.0

        # Identifier les zones de haute stabilité (prédictions constantes sur plusieurs manches)
        stable_zones = []
        current_stable_length = 1

        for i in range(1, len(y_pred)):
            if y_pred[i] == y_pred[i-1]:
                current_stable_length += 1
            else:
                if current_stable_length >= 3:  # Considérer comme stable si au moins 3 prédictions identiques consécutives
                    stable_zones.append(current_stable_length)
                current_stable_length = 1

        # Ajouter la dernière zone stable si elle existe
        if current_stable_length >= 3:
            stable_zones.append(current_stable_length)

        stability_metrics["stable_zones_count"] = len(stable_zones)
        stability_metrics["stable_zones_max_length"] = max(stable_zones) if stable_zones else 0
        stability_metrics["stable_zones_avg_length"] = np.mean(stable_zones) if stable_zones else 0.0

        # Métriques avancées pour l'objectif 1 (en tenant compte que les WAIT n'interrompent pas les séquences)
        # 1. Identifier les séquences de recommandations NON-WAIT valides consécutives (en ignorant les WAIT)
        valid_non_wait_sequences = []
        current_valid_positions = []

        for i in range(len(recommendations)):
            if recommendations[i] == 'WAIT':
                # Les WAIT sont ignorés (ils ne brisent pas la séquence)
                continue
            elif y_true[i] == y_pred[i]:
                # Recommandation NON-WAIT correcte
                current_valid_positions.append(i)
            else:
                # Recommandation NON-WAIT incorrecte - brise la séquence
                if len(current_valid_positions) >= 1:
                    valid_non_wait_sequences.append(current_valid_positions)
                current_valid_positions = []

        # Ajouter la dernière séquence si elle existe
        if len(current_valid_positions) >= 1:
            valid_non_wait_sequences.append(current_valid_positions)

        # Calculer les statistiques sur ces séquences
        stability_metrics["valid_non_wait_sequences_count"] = len(valid_non_wait_sequences)
        stability_metrics["max_valid_non_wait_sequence"] = max([len(seq) for seq in valid_non_wait_sequences]) if valid_non_wait_sequences else 0
        stability_metrics["avg_valid_sequence_length"] = np.mean([len(seq) for seq in valid_non_wait_sequences]) if valid_non_wait_sequences else 0.0

        # 2. Calculer le taux d'erreur des recommandations NON-WAIT
        non_wait_indices = [i for i, rec in enumerate(recommendations) if rec != 'WAIT']
        non_wait_correct = sum(1 for i in non_wait_indices if y_true[i] == y_pred[i])
        non_wait_total = len(non_wait_indices)

        stability_metrics["non_wait_error_rate"] = 1.0 - (non_wait_correct / non_wait_total if non_wait_total > 0 else 1.0)
        stability_metrics["precision_non_wait"] = non_wait_correct / non_wait_total if non_wait_total > 0 else 0.0

        # 3. Calculer le nombre de séquences interrompues
        broken_sequences = 0
        current_length = 0

        for i in range(len(recommendations)):
            if recommendations[i] == 'WAIT':
                continue
            elif y_true[i] == y_pred[i]:
                current_length += 1
            else:
                if current_length > 0:
                    broken_sequences += 1
                current_length = 0

        stability_metrics["broken_sequences_count"] = broken_sequences

        # 4. Métriques pour l'objectif 2 (équilibre WAIT/NON-WAIT)
        wait_indices = [i for i, rec in enumerate(recommendations) if rec == 'WAIT']
        wait_total = len(wait_indices)

        # Calculer le ratio WAIT/NON-WAIT
        stability_metrics["wait_ratio"] = wait_total / len(recommendations) if len(recommendations) > 0 else 0.0

        # Calculer la précision des décisions WAIT (si une recommandation NON-WAIT aurait été incorrecte)
        wait_correct_decisions = 0
        for i in wait_indices:
            if y_true[i] != y_pred[i]:  # La prédiction aurait été incorrecte
                wait_correct_decisions += 1

        stability_metrics["wait_decision_accuracy"] = wait_correct_decisions / wait_total if wait_total > 0 else 1.0

        # Calculer les opportunités manquées (WAIT alors qu'une recommandation NON-WAIT aurait été correcte)
        missed_opportunities = sum(1 for i in wait_indices if y_true[i] == y_pred[i])
        stability_metrics["missed_opportunities"] = missed_opportunities

        # Calculer l'efficacité des WAIT
        stability_metrics["wait_efficiency"] = wait_correct_decisions / wait_total if wait_total > 0 else 0.0

        # 5. Calculer le taux de récupération après WAIT
        recovery_after_wait = 0
        total_after_wait = 0

        for i in range(1, len(recommendations)):
            if recommendations[i-1] == 'WAIT' and recommendations[i] != 'WAIT':
                total_after_wait += 1
                if y_true[i] == y_pred[i]:
                    recovery_after_wait += 1

        stability_metrics["recovery_rate_after_wait"] = recovery_after_wait / total_after_wait if total_after_wait > 0 else 0.0

        # 6. Calculer la densité de recommandations valides
        stability_metrics["valid_recommendation_density"] = non_wait_correct / len(recommendations) if len(recommendations) > 0 else 0.0

        # 7. Score composite d'efficacité des séquences
        max_sequence = stability_metrics["max_valid_non_wait_sequence"]
        precision_non_wait = stability_metrics["precision_non_wait"]
        wait_efficiency = stability_metrics["wait_efficiency"]

        stability_metrics["sequence_efficiency_score"] = (
            0.5 * max_sequence +
            0.3 * precision_non_wait +
            0.2 * wait_efficiency
        )

        # 8. Calculer un score de stabilité orienté objectif 1
        # Ce score favorise les modèles qui produisent de longues séquences de recommandations correctes
        stability_metrics["objective1_stability_score"] = (
            0.7 * max_sequence +
            0.2 * stability_metrics["avg_valid_sequence_length"] +
            0.1 * (1.0 - stability_metrics["non_wait_error_rate"])
        ) * (1.0 - 0.3 * stability_metrics["proba_variance_mean"])

    # 4. Métriques d'excellence (où le modèle excelle particulièrement)
    excellence_metrics = {}

    # Récupérer le seuil de confiance depuis la configuration
    high_confidence_threshold = getattr(config, 'high_confidence_threshold', 0.7) if config else 0.7

    # Identifier les prédictions à haute confiance
    high_confidence_indices = [i for i, p in enumerate(probas) if p <= (1 - high_confidence_threshold) or p >= high_confidence_threshold]

    if high_confidence_indices:
        high_confidence_preds = [y_pred[i] for i in high_confidence_indices]
        high_confidence_true = [y_true[i] for i in high_confidence_indices]
        high_confidence_accuracy = np.mean([1 if high_confidence_true[i] == high_confidence_preds[i] else 0 for i in range(len(high_confidence_indices))])

        excellence_metrics["high_confidence_accuracy"] = high_confidence_accuracy
        excellence_metrics["high_confidence_rate"] = len(high_confidence_indices) / len(y_true)

    # Identifier les séquences de prédictions correctes consécutives
    correct_sequences = []
    current_correct = 0

    for i in range(len(y_true)):
        if y_true[i] == y_pred[i]:
            current_correct += 1
        else:
            if current_correct > 0:
                correct_sequences.append(current_correct)
            current_correct = 0

    # Ajouter la dernière séquence
    if current_correct > 0:
        correct_sequences.append(current_correct)

    if correct_sequences:
        excellence_metrics["correct_sequences_max"] = max(correct_sequences)
        excellence_metrics["correct_sequences_avg"] = np.mean(correct_sequences)

    # Assembler toutes les métriques
    return {
        "performance_distribution": performance_distribution,
        "context_performance": context_performance,
        "stability_metrics": stability_metrics,
        "excellence_metrics": excellence_metrics
    }