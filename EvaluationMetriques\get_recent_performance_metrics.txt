# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1424 à 1523
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def get_recent_performance_metrics(self, config=None) -> Dict[str, float]:
        """
        Calcule des métriques de performance basées sur les données récentes.

        Args:
            config: Configuration du prédicteur (optionnel, pour mise à jour des paramètres)

        Returns:
            Dict[str, float]: Dictionnaire contenant diverses métriques de performance
        """
        # Récupérer le nombre minimum d'échantillons depuis la configuration
        min_samples = getattr(config, 'recent_performance_min_samples', 5) if config else 5

        if not hasattr(self, 'recent_recommendations') or not self.recent_outcomes or len(self.recent_recommendations) < min_samples:
            return {
                "non_wait_accuracy": 0.5,
                "consecutive_valid_count": 0,
                "wait_efficiency": 0.0,
                "recovery_rate_after_wait": 0.0
            }

        # Calculer la précision des recommandations NON-WAIT
        non_wait_indices = [i for i, rec in enumerate(self.recent_recommendations)
                           if isinstance(rec, str) and rec.lower() != 'wait' and i < len(self.recent_outcomes)]

        if not non_wait_indices:
            return {
                "non_wait_accuracy": 0.5,
                "consecutive_valid_count": 0,
                "wait_efficiency": 0.0,
                "recovery_rate_after_wait": 0.0
            }

        # Calculer la précision des recommandations NON-WAIT
        correct_count = 0
        for i in non_wait_indices:
            rec = self.recent_recommendations[i].lower()
            outcome = self.recent_outcomes[i].lower()
            if rec == outcome:
                correct_count += 1

        non_wait_accuracy = correct_count / len(non_wait_indices) if non_wait_indices else 0.5

        # Calculer le nombre de recommandations NON-WAIT valides consécutives
        max_consecutive = 0
        current_consecutive = 0

        for i in range(len(self.recent_recommendations)):
            if i >= len(self.recent_outcomes):
                break

            rec = self.recent_recommendations[i].lower() if isinstance(self.recent_recommendations[i], str) else self.recent_recommendations[i]
            outcome = self.recent_outcomes[i].lower() if isinstance(self.recent_outcomes[i], str) else self.recent_outcomes[i]

            if rec == 'wait':
                # Les WAIT ne brisent pas la séquence
                continue
            elif rec == outcome:
                # Recommandation correcte
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                # Recommandation incorrecte
                current_consecutive = 0

        # Calculer l'efficacité des WAIT (taux de succès après un WAIT)
        wait_indices = [i for i, rec in enumerate(self.recent_recommendations)
                       if isinstance(rec, str) and rec.lower() == 'wait' and i+1 < len(self.recent_recommendations) and i+1 < len(self.recent_outcomes)]

        success_after_wait = 0
        for i in wait_indices:
            next_rec = self.recent_recommendations[i+1].lower() if isinstance(self.recent_recommendations[i+1], str) else self.recent_recommendations[i+1]
            next_outcome = self.recent_outcomes[i+1].lower() if isinstance(self.recent_outcomes[i+1], str) else self.recent_outcomes[i+1]

            if next_rec != 'wait' and next_rec == next_outcome:
                success_after_wait += 1

        wait_efficiency = success_after_wait / len(wait_indices) if wait_indices else 0.0

        # Calculer le taux de récupération après un WAIT
        recovery_count = 0
        for i in range(len(self.recent_recommendations) - 1):
            if i+1 >= len(self.recent_outcomes):
                break

            current_rec = self.recent_recommendations[i].lower() if isinstance(self.recent_recommendations[i], str) else self.recent_recommendations[i]
            next_rec = self.recent_recommendations[i+1].lower() if isinstance(self.recent_recommendations[i+1], str) else self.recent_recommendations[i+1]
            next_outcome = self.recent_outcomes[i+1].lower() if isinstance(self.recent_outcomes[i+1], str) else self.recent_outcomes[i+1]

            if current_rec == 'wait' and next_rec != 'wait' and next_rec == next_outcome:
                recovery_count += 1

        recovery_rate = recovery_count / len(wait_indices) if wait_indices else 0.0

        return {
            "non_wait_accuracy": non_wait_accuracy,
            "consecutive_valid_count": max_consecutive,
            "wait_efficiency": wait_efficiency,
            "recovery_rate_after_wait": recovery_rate
        }