# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 4255 à 4307
# Type: Méthode de la classe WaitPlacementOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def get_stats(self):
        """
        Retourne les statistiques de l'optimiseur.

        Returns:
            Dict: Statistiques de l'optimiseur
        """
        # Calculer les taux de succès
        wait_success_rate = self.correct_wait_decisions / self.total_waits if self.total_waits > 0 else 0
        non_wait_success_rate = self.correct_non_wait_decisions / (self.total_decisions - self.total_waits) if (self.total_decisions - self.total_waits) > 0 else 0

        # Calculer l'efficacité des WAIT
        wait_efficiency = self.effective_waits / self.total_waits if self.total_waits > 0 else 0

        # Calculer le score global (proportion de décisions correctes)
        total_correct = self.correct_wait_decisions + self.correct_non_wait_decisions
        global_score = total_correct / self.total_decisions if self.total_decisions > 0 else 0

        # Calculer le score de recommandations NON-WAIT valides
        non_wait_count = self.total_decisions - self.total_waits
        non_wait_valid_score = self.correct_non_wait_decisions / non_wait_count if non_wait_count > 0 else 0

        return {
            # Statistiques de base
            "total_decisions": self.total_decisions,
            "total_waits": self.total_waits,
            "effective_waits": self.effective_waits,
            "missed_opportunities": self.missed_opportunities,

            # Efficacité et scores
            "wait_efficiency": wait_efficiency,
            "wait_success_rate": wait_success_rate,
            "non_wait_success_rate": non_wait_success_rate,
            "global_score": global_score,
            "non_wait_valid_score": non_wait_valid_score,

            # Séquences consécutives
            "current_consecutive_valid": self.current_consecutive_valid,
            "max_consecutive_valid": self.max_consecutive_valid,

            # Ratios et seuils
            "current_wait_ratio": self.current_wait_ratio,
            "wait_ratio_min": self.wait_ratio_min,
            "wait_ratio_max": self.wait_ratio_max,
            "error_pattern_threshold": self.error_pattern_threshold,
            "transition_uncertainty_threshold": self.transition_uncertainty_threshold,
            "confidence_threshold": self.confidence_threshold,
            "uncertainty_threshold": self.uncertainty_threshold,

            # Patterns
            "error_patterns_count": len(self.error_patterns),
            "transition_patterns_count": len(self.transition_patterns)
        }