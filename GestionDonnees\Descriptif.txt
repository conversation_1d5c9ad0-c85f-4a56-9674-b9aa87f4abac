DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTION DONNÉES
================================================================================

Ce fichier contient la description détaillée des méthodes de gestion des données
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : GESTION DONNÉES
Méthodes de chargement, préparation, transformation et gestion des données
historiques et en temps réel.

TOTAL : 27 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES GESTION DONNÉES
================================================================================

1. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt - Sauvegarde session historique)
   - Lignes 12746-12806 dans hbp.py (61 lignes)
   - FONCTION : Ajoute la séquence de session actuelle au fichier historique en format 0/1 avec gestion intelligente des sauts de ligne
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * filepath (str, optionnel) - Chemin vers le fichier historical_data.txt (défaut: "historical_data.txt")
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour copier la séquence de manière thread-safe
     * **VALIDATION SÉQUENCE :** Vérifie que la séquence n'est pas vide avant traitement
     * **CONVERSION FORMAT :** Convertit Player→'0' et Banker→'1' pour cohérence avec le format standard
     * **GESTION FICHIER :** Ouvre en mode 'a+' pour append et lecture simultanée
     * **DÉTECTION DERNIER CARACTÈRE :** Vérifie si le fichier se termine par un saut de ligne
     * **AJOUT INTELLIGENT :** Ajoute un saut de ligne si nécessaire pour éviter les données collées
     * **ÉCRITURE SÉCURISÉE :** Écrit la séquence avec gestion d'erreurs I/O complète
   - RETOUR : bool - True si succès ou séquence vide, False en cas d'erreur
   - UTILITÉ : Maintient l'historique des sessions pour l'entraînement futur des modèles avec format standardisé

2. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data - Chargement données historiques)
   - Lignes 4831-4886 dans hbp.py (56 lignes)
   - FONCTION : Charge les données historiques depuis un fichier .txt avec interface utilisateur et validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle qu'aucune tâche ML n'est en cours avant chargement
     * **SÉLECTION FICHIER :** Ouvre dialogue de sélection avec filtres pour fichiers .txt
     * **CHARGEMENT INTERNE :** Utilise _load_historical_txt pour traitement du fichier
     * **CALCUL STATISTIQUES :** Détermine nombre de parties, longueur moyenne, total coups
     * **AFFICHAGE RÉSULTATS :** Présente statistiques détaillées dans messagebox
     * **GESTION SESSION :** Propose réinitialisation de la session en cours si applicable
     * **MISE À JOUR MODÈLES :** Met à jour automatiquement les modèles Markov globaux
     * **GESTION ERREURS :** Affiche messages d'erreur spécifiques selon le type de problème
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour charger l'historique avec validation et feedback utilisateur complet

3. _create_lgbm_features.txt (HybridBaccaratPredictor._create_lgbm_features - Création features LGBM optimisées)
   - Lignes 13748-13801 dans hbp.py (54 lignes)
   - FONCTION : Crée un vecteur de features optimisé pour le modèle LGBM à partir d'une séquence de résultats
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTEURS BASIQUES :** Calcule banker_count et player_count dans la séquence
     * **CALCUL STREAKS :** Utilise _calculate_streaks pour analyser les séries consécutives
     * **CALCUL ALTERNANCES :** Utilise _calculate_alternates pour détecter les patterns d'alternance
     * **FEATURES DECAY :** Calcule banker_decay et player_decay avec pondération temporelle
     * **ASSEMBLAGE FEATURES :** Combine compteurs, ratios, streaks et decay dans l'ordre attendu
     * **STREAKS SPÉCIFIQUES :** Ajoute les longueurs de streaks 2-7 pour banker et player
     * **INFOS ALTERNANCE :** Intègre alternate_count_2, alternate_count_3, alternate_ratio
     * **OPTIMISATION OPTUNA :** Réduit de 28 à 25 features en supprimant 3 features spécifiques
   - RETOUR : List[float] - Vecteur de 25 features normalisées pour LGBM
   - UTILITÉ : Fournit un vecteur de features optimisé et standardisé pour les prédictions LGBM

4. handle_short_sequence.txt (HybridBaccaratPredictor.handle_short_sequence - Gestion séquences courtes LSTM)
   - Lignes 5107-5233 dans hbp.py (127 lignes)
   - FONCTION : Gère séquences trop courtes pour LSTM avec padding intelligent et génération features
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (Optional[List[str]]) - Séquence résultats potentiellement courte ou None
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LONGUEUR :** Vérifie longueur cible vs séquence actuelle
     * **PADDING INTELLIGENT :** Ajoute zeros au début pour séquences courtes
     * **GÉNÉRATION FEATURES :** Crée features step-by-step avec position, ratios, streaks
     * **FEATURES CONFIGURABLES :** Utilise lstm_base_features_count depuis configuration
     * **VALIDATION SHAPE :** Assure shape finale (lstm_sequence_length, lstm_input_size)
   - RETOUR : np.ndarray - Array shape (lstm_sequence_length, lstm_input_size) prêt pour LSTM
   - UTILITÉ : Permet utilisation LSTM même avec séquences insuffisantes via padding intelligent

5. create_lstm_sequence_features.txt (HybridBaccaratPredictor.create_lstm_sequence_features - Création features LSTM)
   - FONCTION : Crée features séquentielles optimisées pour modèle LSTM avec encodage temporel
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats pour génération features
     * sequence_length (int) - Longueur cible de la séquence
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie format et longueur de la séquence
     * **ENCODAGE TEMPOREL :** Encode position temporelle de chaque élément
     * **FEATURES CONTEXTUELLES :** Génère features basées sur contexte local
     * **NORMALISATION :** Standardise features pour stabilité LSTM
     * **RESHAPE FINAL :** Formate pour compatibilité architecture LSTM
   - RETOUR : np.ndarray - Features séquentielles formatées pour LSTM
   - UTILITÉ : Génère représentation séquentielle adaptée à l'architecture LSTM

6. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features - Création features hybrides)
   - FONCTION : Génère features pour LGBM et LSTM à partir d'une séquence de résultats
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats source
     * target_result (str) - Résultat cible pour supervision
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PARALLÈLE :** Génère features LGBM et LSTM simultanément
     * **VALIDATION COHÉRENCE :** Assure cohérence entre les deux types de features
     * **OPTIMISATION MÉMOIRE :** Utilise structures efficaces pour grandes séquences
     * **SYNCHRONISATION :** Maintient alignement temporel entre features
     * **VALIDATION FINALE :** Vérifie intégrité des features générées
   - RETOUR : Tuple[np.ndarray, np.ndarray] - (features_lgbm, features_lstm)
   - UTILITÉ : Point d'entrée unifié pour génération de features multi-modèles

7. _extract_lstm_features.txt (HybridBaccaratPredictor._extract_lstm_features - Extraction features LSTM)
   - FONCTION : Extrait et formate features spécifiques pour modèle LSTM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence source pour extraction
     * feature_config (Dict) - Configuration des features à extraire
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SÉLECTION FEATURES :** Choisit features optimales pour LSTM
     * **EXTRACTION SÉQUENTIELLE :** Traite séquence de manière temporelle
     * **NORMALISATION ADAPTÉE :** Applique normalisation spécifique LSTM
     * **GESTION MÉMOIRE :** Optimise utilisation mémoire pour longues séquences
     * **VALIDATION SHAPE :** Assure compatibilité avec architecture LSTM
   - RETOUR : np.ndarray - Features optimisées pour modèle LSTM
   - UTILITÉ : Préparation données optimisée pour architecture LSTM
