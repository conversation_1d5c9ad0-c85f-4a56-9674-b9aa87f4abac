DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTION DONNÉES
================================================================================

Ce fichier contient la description détaillée des méthodes de gestion des données
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : GESTION DONNÉES
Méthodes de chargement, préparation, transformation et gestion des données
historiques et en temps réel.

TOTAL : 27 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES GESTION DONNÉES
================================================================================

1. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt - Sauvegarde session historique)
   - Lignes 12746-12806 dans hbp.py (61 lignes)
   - FONCTION : Ajoute la séquence de session actuelle au fichier historique en format 0/1 avec gestion intelligente des sauts de ligne
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * filepath (str, optionnel) - Chemin vers le fichier historical_data.txt (défaut: "historical_data.txt")
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour copier la séquence de manière thread-safe
     * **VALIDATION SÉQUENCE :** Vérifie que la séquence n'est pas vide avant traitement
     * **CONVERSION FORMAT :** Convertit Player→'0' et Banker→'1' pour cohérence avec le format standard
     * **GESTION FICHIER :** Ouvre en mode 'a+' pour append et lecture simultanée
     * **DÉTECTION DERNIER CARACTÈRE :** Vérifie si le fichier se termine par un saut de ligne
     * **AJOUT INTELLIGENT :** Ajoute un saut de ligne si nécessaire pour éviter les données collées
     * **ÉCRITURE SÉCURISÉE :** Écrit la séquence avec gestion d'erreurs I/O complète
   - RETOUR : bool - True si succès ou séquence vide, False en cas d'erreur
   - UTILITÉ : Maintient l'historique des sessions pour l'entraînement futur des modèles avec format standardisé

2. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data - Chargement données historiques)
   - Lignes 4831-4886 dans hbp.py (56 lignes)
   - FONCTION : Charge les données historiques depuis un fichier .txt avec interface utilisateur et validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle qu'aucune tâche ML n'est en cours avant chargement
     * **SÉLECTION FICHIER :** Ouvre dialogue de sélection avec filtres pour fichiers .txt
     * **CHARGEMENT INTERNE :** Utilise _load_historical_txt pour traitement du fichier
     * **CALCUL STATISTIQUES :** Détermine nombre de parties, longueur moyenne, total coups
     * **AFFICHAGE RÉSULTATS :** Présente statistiques détaillées dans messagebox
     * **GESTION SESSION :** Propose réinitialisation de la session en cours si applicable
     * **MISE À JOUR MODÈLES :** Met à jour automatiquement les modèles Markov globaux
     * **GESTION ERREURS :** Affiche messages d'erreur spécifiques selon le type de problème
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour charger l'historique avec validation et feedback utilisateur complet

3. _create_lgbm_features.txt (HybridBaccaratPredictor._create_lgbm_features - Création features LGBM optimisées)
   - Lignes 13748-13801 dans hbp.py (54 lignes)
   - FONCTION : Crée un vecteur de features optimisé pour le modèle LGBM à partir d'une séquence de résultats
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTEURS BASIQUES :** Calcule banker_count et player_count dans la séquence
     * **CALCUL STREAKS :** Utilise _calculate_streaks pour analyser les séries consécutives
     * **CALCUL ALTERNANCES :** Utilise _calculate_alternates pour détecter les patterns d'alternance
     * **FEATURES DECAY :** Calcule banker_decay et player_decay avec pondération temporelle
     * **ASSEMBLAGE FEATURES :** Combine compteurs, ratios, streaks et decay dans l'ordre attendu
     * **STREAKS SPÉCIFIQUES :** Ajoute les longueurs de streaks 2-7 pour banker et player
     * **INFOS ALTERNANCE :** Intègre alternate_count_2, alternate_count_3, alternate_ratio
     * **OPTIMISATION OPTUNA :** Réduit de 28 à 25 features en supprimant 3 features spécifiques
   - RETOUR : List[float] - Vecteur de 25 features normalisées pour LGBM
   - UTILITÉ : Fournit un vecteur de features optimisé et standardisé pour les prédictions LGBM

4. handle_short_sequence.txt (HybridBaccaratPredictor.handle_short_sequence - Gestion séquences courtes LSTM)
   - Lignes 5107-5233 dans hbp.py (127 lignes)
   - FONCTION : Gère séquences trop courtes pour LSTM avec padding intelligent et génération features step-by-step
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (Optional[List[str]]) - Séquence résultats ('player'/'banker') potentiellement None ou courte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION CIBLE :** Récupère `target_length = self.config.lstm_sequence_length` et `num_features = self.config.lstm_input_size`
     * **VALIDATION MODÈLE :** Vérifie cohérence `self.lstm.input_size != num_features` avec warning si incohérence
     * **CAS SÉQUENCE VIDE :** Si `not sequence`, retourne `np.zeros((target_length, num_features), dtype=np.float32)`
     * **CAS SÉQUENCE LONGUE :** Si `current_length >= target_length`, appelle `self.create_hybrid_features(sequence)` et retourne `lstm_features`
     * **CAS SÉQUENCE COURTE :** Calcule `num_padding = target_length - current_length` et crée `padding_array = np.zeros((num_padding, num_features))`
     * **GÉNÉRATION FEATURES STEP :** Pour chaque `idx in range(current_length)`, calcule 12 features :
       - `pos_norm = (num_padding + idx) / float(target_length)` (position globale normalisée)
       - `is_banker = 1.0 if outcome == 'banker' else 0.0` (encodage binaire)
       - `ratio_banker = sub_sequence.count('banker') / sub_len` (ratio cumulatif)
       - `ratio_player = 1.0 - ratio_banker` (ratio complémentaire)
       - `is_repeat = 1.0 if idx > 0 and sequence[idx] == sequence[idx-1] else 0.0` (détection répétition)
       - `recent_3_count_banker` (comptage banker sur 3 derniers coups)
       - `imbalance = ratio_banker - 0.5` (déséquilibre par rapport à 50/50)
       - `streak_length` (longueur streak actuel avec calcul lookback)
       - `seq_odd_even = (num_padding + idx) % 2` (position paire/impaire)
       - 3 features Optuna par défaut : `0.5, 0.5, 0.5` (confidence, error_pattern_threshold, transition_uncertainty_threshold)
     * **FEATURES CONFIGURABLES :** Utilise `base_features_count = getattr(self.config, 'lstm_base_features_count', 9)` pour limiter features
     * **PADDING FEATURES :** Ajoute `zeros_to_add = num_features - len(current_step_features)` pour compléter à `num_features`
     * **CONCATÉNATION :** Combine `np.concatenate((padding_array, actual_features_array), axis=0)` avec padding au début
     * **VALIDATION FINALE :** Vérifie `padded_sequence_features.shape == (target_length, num_features)` avant retour
   - RETOUR : np.ndarray - Array shape (lstm_sequence_length, lstm_input_size) avec padding zeros au début et features réelles à la fin
   - UTILITÉ : Permet utilisation LSTM même avec séquences insuffisantes via padding intelligent et génération features détaillées

5. create_lstm_sequence_features.txt (HybridBaccaratPredictor.create_lstm_sequence_features - Création features LSTM)
   - Lignes 13570-13745 dans hbp.py (176 lignes)
   - FONCTION : Crée une matrice de features pour le modèle LSTM à partir d'une séquence avec fenêtre adaptative et taille de sortie fixe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
     * keep_history_length (int, optionnel) - Taille maximale de la matrice de sortie (défaut: config.lstm_sequence_length)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence: return None` pour éviter erreurs
     * **DÉTERMINATION LONGUEUR :** Utilise `keep_history_length = self.config.lstm_sequence_length` si None fourni
     * **FENÊTRE ADAPTATIVE :** Utilise `effective_sequence = sequence[:]` (toute la séquence disponible)
     * **INITIALISATION MATRICE :** Crée `sequence_features = np.zeros((keep_history_length, features_count), dtype=np.float32)`
     * **GESTION LONGUEUR :** Si `seq_length <= keep_history_length`, utilise toute séquence avec padding au début ; sinon prend les derniers éléments
     * **CALCUL INDICES :** Détermine `matrix_indices` et `indices_to_use` pour mapping séquence → matrice
     * **GÉNÉRATION FEATURES (12 features par position) :**
       - **Feature 1 :** `(seq_idx + 1) / seq_length` (position relative normalisée)
       - **Feature 2 :** `1 if outcome == 'banker' else 0` (encodage binaire banker/player)
       - **Features 3-4 :** `banker_count / total` et `player_count / total` (ratios cumulatifs)
       - **Feature 5 :** `1 if outcome == previous else 0` (détection répétition)
       - **Feature 6 :** Comptage banker sur 3 derniers coups
       - **Feature 7 :** `banker_count / total - 0.5` (déséquilibre par rapport à 50/50)
       - **Feature 8 :** `alternance_count / seq_idx` (fréquence alternances)
       - **Feature 9 :** Longueur streak actuel avec calcul lookback
       - **Feature 10 :** `(seq_idx + 1) % 2` (position paire/impaire)
       - **Feature 11 :** Proximité du dernier changement de streak normalisée
       - **Feature 12 :** Réservée pour extensions futures
     * **OPTIMISATION MÉMOIRE :** Utilise dtype=np.float32 pour réduire empreinte mémoire
   - RETOUR : Optional[np.ndarray] - Matrice (keep_history_length, lstm_input_size) ou None si erreur
   - UTILITÉ : Génère représentation séquentielle riche avec fenêtre adaptative pour architecture LSTM

6. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features - Création features hybrides)
   - Lignes 13803-13857 dans hbp.py (55 lignes)
   - FONCTION : Fonction centralisée pour la création de features hybrides (LGBM et LSTM) avec fenêtre adaptative
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence or len(sequence) < 2:` avec warning et retour `(None, None)`
     * **INITIALISATION VARIABLES :** Définit `lgbm_features = None` et `lstm_features = None`
     * **CRÉATION FEATURES LGBM :** Appelle `lgbm_features = self._create_lgbm_features(sequence)` avec gestion exception
     * **CRÉATION FEATURES LSTM :**
       - Récupère `lstm_sequence_length = self.config.lstm_sequence_length`
       - Appelle `lstm_features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)`
       - Gestion logging conditionnel pour éviter spam pendant optimisation Optuna
     * **GESTION ERREURS :** Capture exceptions séparément pour LGBM et LSTM avec `logger.error` et `exc_info=True`
     * **VALIDATION FINALE :** Vérifie `if lgbm_features is None and lstm_features is None:` avec warning
     * **FENÊTRE ADAPTATIVE :** Utilise toute la séquence disponible pour calculer probabilités manche N depuis N-1 manches précédentes
   - RETOUR : Tuple[Optional[List[float]], Optional[np.ndarray]] - (features LGBM, features LSTM) ou None pour type en erreur
   - UTILITÉ : Point d'entrée unifié pour génération de features multi-modèles avec gestion robuste des erreurs

7. _extract_lstm_features.txt (HybridBaccaratPredictor._extract_lstm_features - Extraction features LSTM)
   - Lignes 13994-14024 dans hbp.py (31 lignes)
   - FONCTION : Méthode interne wrapper pour extraire les features LSTM avec gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION CONFIG :** Utilise `lstm_sequence_length = self.config.lstm_sequence_length` pour longueur cible
     * **LOGGING DEBUG :** Enregistre `logger.info(f"_extract_lstm_features: Séquence de longueur {len(sequence)}, lstm_sequence_length={lstm_sequence_length}")`
     * **APPEL PRINCIPAL :** Exécute `features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)` avec gestion exception
     * **VALIDATION RETOUR :** Vérifie `if features is None:` avec logging erreur et création fallback
     * **FALLBACK SÉCURISÉ :** Crée `features = np.zeros((lstm_sequence_length, self.config.lstm_input_size), dtype=np.float32)` si échec
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"_extract_lstm_features: Features créées avec succès, shape={features.shape}")` si réussite
     * **GESTION EXCEPTION :** Capture toutes exceptions avec `logger.error(f"_extract_lstm_features: Erreur lors de la création des features LSTM: {e}", exc_info=True)`
   - RETOUR : np.ndarray - Matrice features (lstm_sequence_length, lstm_input_size) ou matrice zéros si erreur
   - UTILITÉ : Wrapper sécurisé pour extraction features LSTM avec fallback robuste et logging détaillé

8. _extract_lgbm_features.txt (HybridBaccaratPredictor._extract_lgbm_features - Extraction features LGBM)
   - Lignes 13981-13992 dans hbp.py (12 lignes)
   - FONCTION : Méthode interne wrapper simple pour extraire les features LGBM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **APPEL DIRECT :** Exécute simplement `return self._create_lgbm_features(sequence)` sans traitement supplémentaire
     * **WRAPPER SIMPLE :** Fournit interface cohérente avec _extract_lstm_features pour uniformité API
   - RETOUR : List[float] - Liste des 25 features LGBM normalisées
   - UTILITÉ : Wrapper simple pour extraction features LGBM avec interface uniforme

9. _calculate_sample_weights.txt (HybridBaccaratPredictor._calculate_sample_weights - Calcul poids échantillons)
   - FONCTION : Calcule poids d'échantillonnage pour entraînement équilibré
   - UTILITÉ : Améliore performance modèles via pondération intelligente

10. _calculate_streaks.txt (HybridBaccaratPredictor._calculate_streaks - Calcul séries consécutives)
    - Lignes 13803-13869 dans hbp.py (67 lignes)
    - FONCTION : Analyse et calcule statistiques complètes des séries consécutives (streaks) pour une séquence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Retourne dictionnaire vide avec toutes clés à 0 si `not sequence`
      * **INITIALISATION COMPTEURS :** Crée `streak_counts` avec 16 clés : banker_streaks, player_streaks, banker_streak_2 à 7, player_streak_2 à 7, max_banker_streak, max_player_streak
      * **VARIABLES ÉTAT :** Initialise `current_type = None` et `current_length = 0` pour suivi streak actuel
      * **PARCOURS SÉQUENCE :** Pour chaque `outcome` dans séquence :
        - **VALIDATION OUTCOME :** Si `outcome not in ('banker', 'player')`, reset `current_type = None` et `current_length = 0`
        - **DÉTECTION CHANGEMENT :** Si `outcome != current_type`, traite streak précédent et démarre nouveau
        - **CONTINUATION STREAK :** Si même type, incrémente `current_length += 1`
      * **TRAITEMENT STREAK TERMINÉ :** Si `current_length > 1` :
        - Incrémente `streak_counts[f'{current_type}_streaks'] += 1`
        - Pour longueurs 2-7, incrémente compteur spécifique si `current_length == length`
        - Met à jour `max_banker_streak` ou `max_player_streak` avec `max(current_value, current_length)`
      * **TRAITEMENT DERNIER STREAK :** Répète logique de traitement pour streak final après boucle
    - RETOUR : Dict[str, int] - Dictionnaire avec 16 statistiques de streaks (compteurs et maximums)
    - UTILITÉ : Génère features avancées de streaks pour modèles ML avec analyse complète patterns consécutifs

11. _calculate_alternates.txt (HybridBaccaratPredictor._calculate_alternates - Calcul alternances)
    - Lignes 13871-13911 dans hbp.py (41 lignes)
    - FONCTION : Analyse et calcule statistiques des patterns d'alternance dans une séquence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Retourne `{'alternate_count_2': 0, 'alternate_count_3': 0, 'alternate_ratio': 0.5}` si `not sequence or len(sequence) < 2`
      * **INITIALISATION :** Définit `alternates = 0` et `last_outcome = sequence[0]` pour suivi alternances
      * **PARCOURS SÉQUENCE :** Pour chaque `outcome` dans `sequence[1:]` :
        - **DÉTECTION ALTERNANCE :** Si `outcome != last_outcome and outcome in ('banker', 'player')`, incrémente `alternates += 1`
        - **MISE À JOUR ÉTAT :** Met à jour `last_outcome = outcome` si valide, sinon conserve précédent
      * **CALCUL RATIO :** Calcule `alternate_ratio = alternates / (len(sequence) - 1)` pour fréquence alternances
      * **CALCUL ALTERNANCES SPÉCIFIQUES :**
        - **Alternances 2 :** Compte patterns AB-AB avec `alternate_count_2 += 1` si détecté
        - **Alternances 3 :** Compte patterns ABC-ABC avec `alternate_count_3 += 1` si détecté
      * **NORMALISATION :** Assure `alternate_ratio` dans [0, 1] avec `min(1.0, max(0.0, alternate_ratio))`
    - RETOUR : Dict[str, float] - Dictionnaire avec alternate_count_2, alternate_count_3, alternate_ratio
    - UTILITÉ : Génère features d'alternance pour détecter comportements cycliques et patterns répétitifs

12. _load_historical_txt.txt (HybridBaccaratPredictor._load_historical_txt - Chargement historique TXT)
    - Lignes 11157-11238 dans hbp.py (82 lignes)
    - FONCTION : Charge et parse fichiers historiques au format texte avec validation complète et mise à jour Markov global
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * filepath (str) - Chemin vers le fichier historical_data.txt à charger
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FICHIER :** Vérifie `if not os.path.exists(filepath):` avec logging erreur et mise à jour progress si UI disponible
      * **LECTURE SÉCURISÉE :** Ouvre `with open(filepath, 'r', encoding='utf-8') as f:` pour lecture UTF-8
      * **PARSING LIGNES :** Itère sur `for line_num, line in enumerate(f, 1):` avec nettoyage `line = line.strip()`
      * **VALIDATION FORMAT :** Vérifie que ligne contient uniquement '0', '1', 'B', 'P' avec `if not all(c in '01BP' for c in line):`
      * **CONVERSION STANDARDISÉE :** Convertit format avec `line = line.replace('B', '1').replace('P', '0')` puis `game_sequence = ['banker' if c == '1' else 'player' for c in line]`
      * **FILTRAGE LONGUEUR :** Ignore parties trop courtes avec `if len(game_sequence) < 2:` et logging warning
      * **ACCUMULATION DONNÉES :** Ajoute à `new_historical_data.append(game_sequence)` pour construction dataset
      * **MISE À JOUR THREAD-SAFE :** Utilise `with self.sequence_lock, self.markov_lock:` pour modifier état partagé
      * **ASSIGNATION ÉTAT :** Met à jour `self.historical_data = new_historical_data`, `self.loaded_historical = True`, `self.historical_games_at_startup_or_reset = num_games_loaded`
      * **CALCUL STATISTIQUES :** Détermine `total_rounds = sum(len(g) for g in new_historical_data)` pour logging
      * **MISE À JOUR MARKOV :** Si `self.markov:`, appelle `self.markov.update_global(self.historical_data)` avec gestion d'erreurs
      * **GESTION ERREURS :** Capture `UnicodeDecodeError` pour problèmes encodage et `Exception` générale avec reset compteur
      * **LOGGING DÉTAILLÉ :** Enregistre succès avec `logger.info(f"_load_historical_txt: Succès ({num_games_loaded} parties chargées, {total_rounds} coups totaux).")`
    - RETOUR : bool - True si chargement réussi, False en cas d'erreur
    - UTILITÉ : Import robuste données historiques avec validation format, conversion standardisée et mise à jour Markov global

13. _apply_data_sampling.txt (HybridBaccaratPredictor._apply_data_sampling - Application échantillonnage données)
    - Lignes 4271-4303 dans hbp.py (33 lignes)
    - FONCTION : Applique techniques d'échantillonnage pour équilibrage dataset avec désactivation pour garantir manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * original_historical_data (List[List[str]]) - Données historiques originales
      * max_games (Optional[int]) - Limite maximale de jeux à traiter
      * sampling_fraction (Optional[float]) - Fraction d'échantillonnage (0.0-1.0)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))`, `num_original_games = len(original_historical_data)`, `data_to_process = original_historical_data`
      * **DÉSACTIVATION ÉCHANTILLONNAGE :** Utilise `sampling_applied_info = f"Échantillonnage désactivé pour garantir l'utilisation de toutes les manches 31-60. Total: {num_original_games} jeux."` pour préserver toutes les données
      * **LOGIQUE ORIGINALE COMMENTÉE :** Conserve code original en commentaire pour référence :
        - `if max_games is not None and max_games > 0:` avec `data_to_process = original_historical_data[-max_games:]` pour jeux récents
        - `elif sampling_fraction is not None and 0 < sampling_fraction <= 1.0:` avec `sampled_indices = np.random.choice(num_original_games, num_sampled_games, replace=False)` pour échantillonnage aléatoire
        - `data_to_process = [original_historical_data[i] for i in sampled_indices]` pour sélection indices
      * **JUSTIFICATION DÉSACTIVATION :** Assure que toutes les manches 31-60 sont disponibles pour entraînement optimal des modèles
      * **RETOUR COMPLET :** Retourne `(data_to_process, sampling_applied_info)` avec données complètes et message informatif
    - RETOUR : Tuple[List[List[str]], str] - (données à traiter, message d'information sur échantillonnage)
    - UTILITÉ : Préserve intégrité des données pour optimisation manches 31-60 tout en maintenant interface d'échantillonnage

14. _calculate_decay_feature.txt (HybridBaccaratPredictor._calculate_decay_feature - Calcul feature decay)
    - Lignes 13913-13939 dans hbp.py (27 lignes)
    - FONCTION : Calcule feature avec pondération temporelle décroissante pour donner plus de poids aux résultats récents
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('banker' ou 'player')
      * target_outcome (str) - Résultat cible à analyser ('banker' ou 'player')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Vérifie `if not sequence: return 0.0` pour éviter division par zéro
      * **RÉCUPÉRATION CONFIG :** Utilise `decay_factor = self.config.decay_factor` pour facteur de décroissance temporelle
      * **INITIALISATION COMPTEURS :** Définit `total_weight = 0` et `weighted_sum = 0` pour accumulation pondérée
      * **PARCOURS SÉQUENCE :** Pour chaque `i, outcome in enumerate(sequence):` :
        - **CALCUL POIDS :** Détermine `weight = decay_factor ** (len(sequence) - i - 1)` où les éléments récents ont poids plus élevé
        - **ACCUMULATION POIDS :** Ajoute `total_weight += weight` pour normalisation finale
        - **ACCUMULATION CIBLE :** Si `outcome == target_outcome:`, ajoute `weighted_sum += weight` pour comptage pondéré
      * **NORMALISATION FINALE :** Retourne `weighted_sum / total_weight if total_weight > 0 else 0.0` pour ratio pondéré
      * **LOGIQUE TEMPORELLE :** Plus l'indice est élevé (récent), plus le poids est important avec `decay_factor^(distance_from_end)`
    - RETOUR : float - Valeur feature avec decay entre 0.0 et 1.0
    - UTILITÉ : Génère features temporellement pondérées pour modèles ML avec emphasis sur événements récents

15. _create_temporal_split.txt (HybridBaccaratPredictor._create_temporal_split - Division temporelle)
    - Lignes 4331-4349 dans hbp.py (19 lignes)
    - FONCTION : Crée division temporelle des données pour validation avec TimeSeriesSplit et sélection du dernier split
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lgbm_all (np.ndarray) - Données features LGBM pour déterminer taille dataset
    - FONCTIONNEMENT DÉTAILLÉ :
      * **RÉCUPÉRATION CONFIG :** Utilise `n_splits = max(2, getattr(self.config, 'lgbm_cv_splits', 5))` pour nombre de splits
      * **INITIALISATION SPLITTER :** Crée `tscv = TimeSeriesSplit(n_splits=n_splits)` pour division temporelle
      * **INITIALISATION LISTES :** Définit `train_indices_list = []` et `val_indices_list = []` pour accumulation
      * **GÉNÉRATION SPLITS :** Itère `for train_idx, val_idx in tscv.split(X_lgbm_all):` pour créer tous les splits possibles
      * **ACCUMULATION INDICES :** Ajoute `train_indices_list.append(train_idx)` et `val_indices_list.append(val_idx)` pour chaque split
      * **VALIDATION GÉNÉRATION :** Vérifie `if not train_indices_list or not val_indices_list:` avec logging erreur et retour `(None, None)`
      * **SÉLECTION DERNIER SPLIT :** Utilise `train_indices = train_indices_list[-1]` et `val_indices = val_indices_list[-1]` pour split le plus récent
      * **LOGIQUE TEMPORELLE :** TimeSeriesSplit assure que validation utilise toujours données plus récentes que entraînement
    - RETOUR : Tuple[np.ndarray, np.ndarray] - (indices_train, indices_validation) ou (None, None) si erreur
    - UTILITÉ : Assure validation temporellement cohérente avec données futures pour test réaliste des modèles

16. _validate_data_shapes.txt (HybridBaccaratPredictor._validate_data_shapes - Validation formes données)
    - FONCTION : Valide cohérence des dimensions des données
    - UTILITÉ : Prévient erreurs de compatibilité entre modèles

17. _get_cumulative_new_data.txt (HybridBaccaratPredictor._get_cumulative_new_data - Données nouvelles cumulatives)
    - FONCTION : Récupère données nouvelles de manière cumulative
    - UTILITÉ : Gestion efficace des mises à jour incrémentales

18. _get_historical_data_for_refit.txt (HybridBaccaratPredictor._get_historical_data_for_refit - Données historiques pour re-fit)
    - FONCTION : Prépare données historiques pour re-entraînement
    - UTILITÉ : Optimise sélection données pour mise à jour modèles

19. _get_recent_session_data.txt (HybridBaccaratPredictor._get_recent_session_data - Données session récente)
    - FONCTION : Extrait données de la session récente pour analyse
    - UTILITÉ : Focus sur performance récente pour ajustements

20. calculate_lstm_sample_weights.txt (HybridBaccaratPredictor.calculate_lstm_sample_weights - Poids échantillons LSTM)
    - Lignes 10027-10170 dans hbp.py (144 lignes)
    - FONCTION : Calcule les poids d'échantillons pour LSTM basés sur métriques de confiance et incertitude avec focus manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lstm (np.ndarray) - Features LSTM (séquences temporelles)
      * y_lstm (np.ndarray) - Labels correspondants (0=Player, 1=Banker)
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if X_lstm is None or len(X_lstm) == 0 or y_lstm is None or len(y_lstm) == 0: return None`
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(X_lstm), dtype=np.float32)` comme base
      * **FACTEUR TEMPOREL :** Calcule `temporal_factor = np.linspace(temporal_factor_min, temporal_factor_max, len(X_lstm))` avec `temporal_factor_min=0.5`, `temporal_factor_max=1.0` pour favoriser échantillons récents
      * **FACTEUR DIFFICULTÉ :** Initialise `difficulty_factor = np.ones_like(sample_weights)` puis si LSTM entraîné :
        - Met modèle en mode évaluation avec `self.lstm.eval()`
        - Convertit features avec `X_tensor = torch.FloatTensor(X_lstm).to(self.device)`
        - Calcule prédictions avec `outputs = self.lstm(X_tensor)` puis `probas = torch.softmax(outputs, dim=1).cpu().numpy()`
        - Détermine confiance avec `confidence = np.abs(pred_probas - 0.5) * 2.0`
        - Inverse pour difficulté avec `difficulty_factor = 1.0 - 0.5 * confidence`
      * **FACTEUR POSITION :** Si `sequence_positions` fourni, extrait `target_round_min=31`, `target_round_max=60`, `late_game_weight_factor`
      * **MASQUE MANCHES CIBLES :** Crée `late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **POIDS PROGRESSIFS :** Calcule `normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)` puis `progressive_weights = 1.0 + (np.exp(exponential_factor * normalized_positions) - 1) / (np.exp(exponential_factor) - 1) * (late_game_weight_factor - 1.0)`
      * **ÉQUILIBRAGE CLASSES :** Calcule `class_weights = (len(target_y) / (len(class_counts) * class_counts)) ** 1.5` pour classes minoritaires
      * **FACTEUR TRANSITIONS :** Détecte changements avec `transitions = np.diff(target_y, prepend=target_y[0])` et applique bonus `1.5` aux points de transition
      * **COMBINAISON FINALE :** Multiplie `combined_weights = temporal_factor * difficulty_factor * position_factor`
      * **NORMALISATION :** Applique `normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))` puis clipping avec `min_sample_weight=0.2`, `max_sample_weight=5.0`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés pour LSTM avec focus manches 31-60
    - UTILITÉ : Optimise apprentissage LSTM avec pondération temporelle, difficulté, et focus spécial sur manches cibles avec équilibrage classes

21. calculate_sample_weights_from_metrics.txt (HybridBaccaratPredictor.calculate_sample_weights_from_metrics - Poids depuis métriques)
    - Lignes 9896-10025 dans hbp.py (130 lignes)
    - FONCTION : Calcule poids échantillons basés sur métriques de confiance et incertitude LGBM avec pondération adaptative
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_features (np.ndarray) - Features d'entrée pour calcul métriques
      * y_labels (np.ndarray) - Labels correspondants
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MODÈLES :** Vérifie `if self.feature_scaler is None or self.lgbm_base is None: return None`
      * **VÉRIFICATION ENTRAÎNEMENT :** Utilise `check_is_fitted(self.lgbm_base)` pour confirmer modèle LGBM entraîné
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(X_features), dtype=np.float32)` comme base
      * **CALCUL CONFIANCE :** Si LGBM entraîné, calcule `y_pred_proba = self.lgbm_base.predict_proba(X_features)` puis `confidence_scores = np.abs(y_pred_proba[:, 1] - 0.5) * 2.0`
      * **CALCUL INCERTITUDE :** Si `self.lgbm_uncertainty` disponible :
        - Vérifie entraînement avec `check_is_fitted(self.lgbm_uncertainty)`
        - Extrait prédictions estimateurs avec `estimator_probas = np.array([estimator.predict_proba(X_features)[:, 1] for estimator in self.lgbm_uncertainty.estimators_])`
        - Calcule variance avec `uncertainty_scores = np.var(estimator_probas, axis=0)`
        - Normalise avec `uncertainty_scores = (uncertainty_scores - np.min(uncertainty_scores)) / (np.max(uncertainty_scores) - np.min(uncertainty_scores) + 1e-8)`
      * **PONDÉRATION CONFIANCE :** Applique `confidence_weight_factor = getattr(self.config, 'confidence_weight_factor', 1.5)` avec `confidence_weights = 1.0 + (1.0 - confidence_scores) * confidence_weight_factor`
      * **PONDÉRATION INCERTITUDE :** Applique `uncertainty_weight_factor = getattr(self.config, 'uncertainty_weight_factor', 1.2)` avec `uncertainty_weights = 1.0 + uncertainty_scores * uncertainty_weight_factor`
      * **FACTEUR POSITION :** Si `sequence_positions` fourni, extrait `target_round_min=31`, `target_round_max=60`, `late_game_weight_factor`
      * **MASQUE MANCHES CIBLES :** Crée `late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **POIDS PROGRESSIFS :** Pour manches cibles, calcule `normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)` puis `progressive_weights = 1.0 + normalized_positions * (late_game_weight_factor - 1.0)`
      * **COMBINAISON FINALE :** Multiplie `combined_weights = confidence_weights * uncertainty_weights * position_weights`
      * **NORMALISATION :** Applique `normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))` puis clipping avec `min_sample_weight=0.1`, `max_sample_weight=10.0`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés basés sur métriques LGBM
    - UTILITÉ : Pondération intelligente selon confiance, incertitude et position avec focus manches 31-60 pour optimiser qualité prédictions

22. _extract_features_for_consecutive_calculator.txt (HybridBaccaratPredictor._extract_features_for_consecutive_calculator - Features calculateur consécutif)
    - Lignes 10742-10767 dans hbp.py (26 lignes)
    - FONCTION : Extrait vecteur de features spécialisé pour calculateur de confiance consécutive avec 10 features optimisées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé à la séquence
      * **VALIDATION SÉQUENCE :** Vérifie `if len(self.sequence) < 5:` avec retour features par défaut si insuffisant
      * **SÉQUENCE RÉCENTE :** Extrait `recent_sequence = self.sequence[-10:]` pour analyse patterns récents
      * **FEATURES BASIQUES :** Calcule ratios avec `banker_ratio = recent_sequence.count('banker') / len(recent_sequence)`, `player_ratio = recent_sequence.count('player') / len(recent_sequence)`
      * **FEATURES STREAKS :** Détermine `current_streak_length` et `current_streak_type` en analysant fin de séquence
      * **FEATURES ALTERNANCE :** Calcule `alternation_ratio` en comptant changements consécutifs dans séquence récente
      * **FEATURES PATTERNS :** Détecte patterns répétitifs avec boucles imbriquées `for pattern_length in [2, 3]:` et `for i in range(len(recent_sequence) - 2 * pattern_length):`
      * **NORMALISATION MANCHE :** Ajoute `current_round / getattr(self.config, 'round_normalization_factor', 100.0)` pour position relative
      * **VECTEUR FINAL :** Retourne liste de 10 features : `[banker_ratio, player_ratio, current_streak_length/10.0, current_streak_type, alternation_ratio, volatility, trend_strength, momentum, pattern_strength, normalized_round]`
      * **GESTION ERREURS :** Capture exceptions avec fallback vers `[default_feature_value] * 10` où `default_feature_value = getattr(self.config, 'default_feature_value', 0.5)`
    - RETOUR : List[float] - Vecteur de 10 features normalisées pour calculateur confiance
    - UTILITÉ : Optimise features pour analyse patterns et confiance manches consécutives avec protection complète

23. _extract_pattern_key.txt (HybridBaccaratPredictor._extract_pattern_key - Extraction clé pattern)
    - FONCTION : Extrait clé de pattern pour indexation et recherche
    - UTILITÉ : Identification rapide des patterns récurrents

24. _update_pattern_counts.txt (HybridBaccaratPredictor._update_pattern_counts - MAJ compteurs patterns)
    - Lignes 10769-10776 dans hbp.py (8 lignes)
    - FONCTION : Met à jour les compteurs de motifs basés sur les 4 derniers coups avec protection thread-safe
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * last_outcome (str) - Résultat du dernier coup ('player' ou 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION LONGUEUR :** Vérifie `if len(self.sequence) >= 4:` pour s'assurer d'avoir suffisamment de données
      * **EXTRACTION PATTERN :** Crée `pattern = tuple(self.sequence[-4:])` incluant le dernier coup pour motif de 4 éléments
      * **DÉFINITION CLÉ :** Utilise `dict_key = last_outcome` comme clé de dictionnaire pour indexation
      * **MISE À JOUR COMPTEUR :** Si `dict_key in self.pattern_counts:`, incrémente `self.pattern_counts[dict_key][pattern] += 1`
      * **PROTECTION THREAD :** DOIT être appelée avec `self.sequence_lock` déjà acquis pour éviter conditions de course
      * **STRUCTURE DONNÉES :** Maintient dictionnaire imbriqué {outcome: {pattern: count}} pour statistiques
    - RETOUR : None - Met à jour directement les compteurs internes
    - UTILITÉ : Maintient statistiques patterns à jour pour analyse prédictive et détection motifs récurrents

25. update_recent_data.txt (HybridBaccaratPredictor.update_recent_data - MAJ données récentes)
    - FONCTION : Met à jour cache des données récentes pour optimisation
    - UTILITÉ : Accès rapide aux données les plus pertinentes

26. _get_cached_lgbm_prediction.txt (HybridBaccaratPredictor._get_cached_lgbm_prediction - Prédiction LGBM cachée)
    - Lignes 8227-8296 dans hbp.py (70 lignes)
    - FONCTION : Récupère prédiction LGBM depuis cache ou calcule si nécessaire avec système de cache double (deque + dict)
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * lgbm_feat (Optional[List[float]]) - Vecteur de features pour prédiction LGBM
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FEATURES :** Vérifie `if lgbm_feat is None:` avec retour `{'player': 0.5, 'banker': 0.5}` par défaut
      * **CONVERSION CLÉ :** Convertit `feat_tuple = tuple(lgbm_feat)` pour utilisation comme clé dictionnaire
      * **INITIALISATION CACHE DICT :** Si `not hasattr(self, 'lgbm_cache_dict'):`, convertit cache deque existant en dictionnaire avec `for feat, pred in self.lgbm_cache: self.lgbm_cache_dict[tuple(feat)] = pred`
      * **RECHERCHE CACHE :** Vérifie `if feat_tuple in self.lgbm_cache_dict:` pour hit cache
      * **HIT CACHE :** Si trouvé, récupère `cached_pred = self.lgbm_cache_dict[feat_tuple]`, appelle `self._update_prediction_progress()` et retourne prédiction
      * **MISS CACHE :** Si non trouvé, appelle `self._update_prediction_progress()` puis `lgbm_pred = self.predict_with_lgbm(lgbm_feat)`
      * **MISE À JOUR CACHE :** Ajoute au dictionnaire `self.lgbm_cache_dict[feat_tuple] = lgbm_pred` et à la deque `self.lgbm_cache.append((lgbm_feat, lgbm_pred))`
      * **GESTION TAILLE :** Récupère `max_cache_size = getattr(self.config, 'lgbm_cache_max_size', 1000)` et si `len(self.lgbm_cache) > max_cache_size:`, supprime plus ancien avec `oldest_feat, _ = self.lgbm_cache.popleft()` et `del self.lgbm_cache_dict[tuple(oldest_feat)]`
      * **GESTION ERREURS :** Capture `NotFittedError` avec logging adaptatif selon phase (training/optuna) et `Exception` générale avec logging complet
      * **MESURE PERFORMANCE :** Utilise `start_time = time.time()` et `elapsed = time.time() - start_time` pour monitoring temps exécution
    - RETOUR : Dict[str, float] - Probabilités {'player': float, 'banker': float} depuis cache ou calcul
    - UTILITÉ : Optimisation performance avec cache double (deque FIFO + dict O(1)) pour éviter recalculs coûteux LGBM

27. prepare_training_data.txt (HybridBaccaratPredictor.prepare_training_data - Préparation données entraînement)
    - Lignes 4137-4269 dans hbp.py (133 lignes)
    - FONCTION : Prépare les données d'entraînement via BaccaratSequenceManager avec échantillonnage et validation complète
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_use_historical (bool, optionnel) - Force utilisation données historiques (défaut: False)
      * max_games (Optional[int]) - Limite nombre de jeux à traiter pour optimisation mémoire
      * sampling_fraction (Optional[float]) - Fraction d'échantillonnage des données (0.0-1.0)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LECTURE CONFIG :** Récupère `min_target_idx = getattr(self.config, 'min_target_hand_index_training', 30)`, `lstm_seq_len_cfg`, `lstm_feat_count_cfg`, `lgbm_feat_count_cfg`
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.loaded_historical or not self.historical_data` avec retour (None×8) si échec
      * **ÉCHANTILLONNAGE :** Appelle `data_to_process, sampling_applied_info = self._apply_data_sampling(original_historical_data, max_games, sampling_fraction)`
      * **MANAGER SÉQUENCES :** Instancie `BaccaratSequenceManager(sequence_length=lstm_seq_len_cfg, min_target_hand_index=min_target_idx, hybrid_feature_creator=self.create_hybrid_features, lgbm_feature_count=lgbm_feat_count_cfg, lstm_seq_len=lstm_seq_len_cfg, lstm_feature_count=lstm_feat_count_cfg)`
      * **GÉNÉRATION DONNÉES :** Appelle `manager_results = manager.prepare_data_for_model(data_to_process)` qui retourne tuple de 5 éléments
      * **UNPACK RÉSULTATS :** Extrait `X_lgbm_all, y_labels_all, X_lstm_all, list_of_all_prefix_sequences, list_of_all_origins = manager_results`
      * **VALIDATION ÉCHANTILLONS :** Vérifie `final_num_samples = X_lgbm_all.shape[0]` contre `min_samples_required = getattr(self.config, 'min_samples_for_training', 100)`
      * **CALCUL POIDS :** Génère `sample_weights_all = self._calculate_sample_weights(list_of_all_origins, final_num_samples)` avec decay factor
      * **SPLIT TEMPOREL :** Crée `train_indices, val_indices = self._create_temporal_split(X_lgbm_all)` via TimeSeriesSplit
      * **VALIDATION SHAPES :** Appelle `(is_valid, message) = self._validate_data_shapes(X_lgbm_all, y_labels_all, X_lstm_all, sample_weights_all, list_of_all_prefix_sequences, list_of_all_origins, final_num_samples)`
      * **LOGGING FINAL :** Enregistre `logger_instance.info(f"_prepare_training_data: Succès! {final_num_samples} échantillons préparés")`
    - RETOUR : Tuple[8] - (X_lgbm, y_labels, X_lstm, sample_weights, train_indices, val_indices, sequences, origins) ou (None×8) si erreur
    - UTILITÉ : Pipeline complet de préparation données avec BaccaratSequenceManager, échantillonnage intelligent, et validation robuste pour entraînement ML
