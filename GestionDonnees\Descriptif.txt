DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTION DONNÉES
================================================================================

Ce fichier contient la description détaillée des méthodes de gestion des données
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : GESTION DONNÉES
Méthodes de chargement, préparation, transformation et gestion des données
historiques et en temps réel.

TOTAL : 27 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES GESTION DONNÉES
================================================================================

1. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt - Sauvegarde session historique)
   - Lignes 12746-12806 dans hbp.py (61 lignes)
   - FONCTION : Ajoute la séquence de session actuelle au fichier historique en format 0/1 avec gestion intelligente des sauts de ligne
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * filepath (str, optionnel) - Chemin vers le fichier historical_data.txt (défaut: "historical_data.txt")
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour copier la séquence de manière thread-safe
     * **VALIDATION SÉQUENCE :** Vérifie que la séquence n'est pas vide avant traitement
     * **CONVERSION FORMAT :** Convertit Player→'0' et Banker→'1' pour cohérence avec le format standard
     * **GESTION FICHIER :** Ouvre en mode 'a+' pour append et lecture simultanée
     * **DÉTECTION DERNIER CARACTÈRE :** Vérifie si le fichier se termine par un saut de ligne
     * **AJOUT INTELLIGENT :** Ajoute un saut de ligne si nécessaire pour éviter les données collées
     * **ÉCRITURE SÉCURISÉE :** Écrit la séquence avec gestion d'erreurs I/O complète
   - RETOUR : bool - True si succès ou séquence vide, False en cas d'erreur
   - UTILITÉ : Maintient l'historique des sessions pour l'entraînement futur des modèles avec format standardisé

2. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data - Chargement données historiques)
   - Lignes 4831-4886 dans hbp.py (56 lignes)
   - FONCTION : Charge les données historiques depuis un fichier .txt avec interface utilisateur et validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle qu'aucune tâche ML n'est en cours avant chargement
     * **SÉLECTION FICHIER :** Ouvre dialogue de sélection avec filtres pour fichiers .txt
     * **CHARGEMENT INTERNE :** Utilise _load_historical_txt pour traitement du fichier
     * **CALCUL STATISTIQUES :** Détermine nombre de parties, longueur moyenne, total coups
     * **AFFICHAGE RÉSULTATS :** Présente statistiques détaillées dans messagebox
     * **GESTION SESSION :** Propose réinitialisation de la session en cours si applicable
     * **MISE À JOUR MODÈLES :** Met à jour automatiquement les modèles Markov globaux
     * **GESTION ERREURS :** Affiche messages d'erreur spécifiques selon le type de problème
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour charger l'historique avec validation et feedback utilisateur complet

3. _create_lgbm_features.txt (HybridBaccaratPredictor._create_lgbm_features - Création features LGBM optimisées)
   - Lignes 13748-13801 dans hbp.py (54 lignes)
   - FONCTION : Crée un vecteur de features optimisé pour le modèle LGBM à partir d'une séquence de résultats
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTEURS BASIQUES :** Calcule banker_count et player_count dans la séquence
     * **CALCUL STREAKS :** Utilise _calculate_streaks pour analyser les séries consécutives
     * **CALCUL ALTERNANCES :** Utilise _calculate_alternates pour détecter les patterns d'alternance
     * **FEATURES DECAY :** Calcule banker_decay et player_decay avec pondération temporelle
     * **ASSEMBLAGE FEATURES :** Combine compteurs, ratios, streaks et decay dans l'ordre attendu
     * **STREAKS SPÉCIFIQUES :** Ajoute les longueurs de streaks 2-7 pour banker et player
     * **INFOS ALTERNANCE :** Intègre alternate_count_2, alternate_count_3, alternate_ratio
     * **OPTIMISATION OPTUNA :** Réduit de 28 à 25 features en supprimant 3 features spécifiques
   - RETOUR : List[float] - Vecteur de 25 features normalisées pour LGBM
   - UTILITÉ : Fournit un vecteur de features optimisé et standardisé pour les prédictions LGBM

4. handle_short_sequence.txt (HybridBaccaratPredictor.handle_short_sequence - Gestion séquences courtes LSTM)
   - Lignes 5107-5233 dans hbp.py (127 lignes)
   - FONCTION : Gère séquences trop courtes pour LSTM avec padding intelligent et génération features
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (Optional[List[str]]) - Séquence résultats potentiellement courte ou None
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LONGUEUR :** Vérifie longueur cible vs séquence actuelle
     * **PADDING INTELLIGENT :** Ajoute zeros au début pour séquences courtes
     * **GÉNÉRATION FEATURES :** Crée features step-by-step avec position, ratios, streaks
     * **FEATURES CONFIGURABLES :** Utilise lstm_base_features_count depuis configuration
     * **VALIDATION SHAPE :** Assure shape finale (lstm_sequence_length, lstm_input_size)
   - RETOUR : np.ndarray - Array shape (lstm_sequence_length, lstm_input_size) prêt pour LSTM
   - UTILITÉ : Permet utilisation LSTM même avec séquences insuffisantes via padding intelligent

5. create_lstm_sequence_features.txt (HybridBaccaratPredictor.create_lstm_sequence_features - Création features LSTM)
   - FONCTION : Crée features séquentielles optimisées pour modèle LSTM avec encodage temporel
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats pour génération features
     * sequence_length (int) - Longueur cible de la séquence
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie format et longueur de la séquence
     * **ENCODAGE TEMPOREL :** Encode position temporelle de chaque élément
     * **FEATURES CONTEXTUELLES :** Génère features basées sur contexte local
     * **NORMALISATION :** Standardise features pour stabilité LSTM
     * **RESHAPE FINAL :** Formate pour compatibilité architecture LSTM
   - RETOUR : np.ndarray - Features séquentielles formatées pour LSTM
   - UTILITÉ : Génère représentation séquentielle adaptée à l'architecture LSTM

6. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features - Création features hybrides)
   - FONCTION : Génère features pour LGBM et LSTM à partir d'une séquence de résultats
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats source
     * target_result (str) - Résultat cible pour supervision
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PARALLÈLE :** Génère features LGBM et LSTM simultanément
     * **VALIDATION COHÉRENCE :** Assure cohérence entre les deux types de features
     * **OPTIMISATION MÉMOIRE :** Utilise structures efficaces pour grandes séquences
     * **SYNCHRONISATION :** Maintient alignement temporel entre features
     * **VALIDATION FINALE :** Vérifie intégrité des features générées
   - RETOUR : Tuple[np.ndarray, np.ndarray] - (features_lgbm, features_lstm)
   - UTILITÉ : Point d'entrée unifié pour génération de features multi-modèles

7. _extract_lstm_features.txt (HybridBaccaratPredictor._extract_lstm_features - Extraction features LSTM)
   - FONCTION : Extrait et formate features spécifiques pour modèle LSTM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence source pour extraction
     * feature_config (Dict) - Configuration des features à extraire
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SÉLECTION FEATURES :** Choisit features optimales pour LSTM
     * **EXTRACTION SÉQUENTIELLE :** Traite séquence de manière temporelle
     * **NORMALISATION ADAPTÉE :** Applique normalisation spécifique LSTM
     * **GESTION MÉMOIRE :** Optimise utilisation mémoire pour longues séquences
     * **VALIDATION SHAPE :** Assure compatibilité avec architecture LSTM
   - RETOUR : np.ndarray - Features optimisées pour modèle LSTM
   - UTILITÉ : Préparation données optimisée pour architecture LSTM

8. _extract_lgbm_features.txt (HybridBaccaratPredictor._extract_lgbm_features - Extraction features LGBM)
   - FONCTION : Extrait et formate features spécifiques pour modèle LGBM
   - UTILITÉ : Préparation données optimisée pour modèle gradient boosting

9. _calculate_sample_weights.txt (HybridBaccaratPredictor._calculate_sample_weights - Calcul poids échantillons)
   - FONCTION : Calcule poids d'échantillonnage pour entraînement équilibré
   - UTILITÉ : Améliore performance modèles via pondération intelligente

10. _calculate_streaks.txt (HybridBaccaratPredictor._calculate_streaks - Calcul séries consécutives)
    - FONCTION : Analyse et calcule statistiques des séries consécutives
    - UTILITÉ : Détection patterns de répétition pour features avancées

11. _calculate_alternates.txt (HybridBaccaratPredictor._calculate_alternates - Calcul alternances)
    - FONCTION : Analyse patterns d'alternance dans les séquences
    - UTILITÉ : Détection comportements d'alternance pour prédiction

12. _load_historical_txt.txt (HybridBaccaratPredictor._load_historical_txt - Chargement historique TXT)
    - FONCTION : Charge et parse fichiers historiques au format texte
    - UTILITÉ : Import données historiques avec validation et nettoyage

13. _apply_data_sampling.txt (HybridBaccaratPredictor._apply_data_sampling - Application échantillonnage données)
    - FONCTION : Applique techniques d'échantillonnage pour équilibrage dataset
    - UTILITÉ : Optimise distribution des données pour entraînement

14. _calculate_decay_feature.txt (HybridBaccaratPredictor._calculate_decay_feature - Calcul feature decay)
    - FONCTION : Calcule features avec pondération temporelle décroissante
    - UTILITÉ : Donne plus d'importance aux événements récents

15. _create_temporal_split.txt (HybridBaccaratPredictor._create_temporal_split - Division temporelle)
    - FONCTION : Crée division temporelle des données pour validation
    - UTILITÉ : Assure validation temporellement cohérente

16. _validate_data_shapes.txt (HybridBaccaratPredictor._validate_data_shapes - Validation formes données)
    - FONCTION : Valide cohérence des dimensions des données
    - UTILITÉ : Prévient erreurs de compatibilité entre modèles

17. _get_cumulative_new_data.txt (HybridBaccaratPredictor._get_cumulative_new_data - Données nouvelles cumulatives)
    - FONCTION : Récupère données nouvelles de manière cumulative
    - UTILITÉ : Gestion efficace des mises à jour incrémentales

18. _get_historical_data_for_refit.txt (HybridBaccaratPredictor._get_historical_data_for_refit - Données historiques pour re-fit)
    - FONCTION : Prépare données historiques pour re-entraînement
    - UTILITÉ : Optimise sélection données pour mise à jour modèles

19. _get_recent_session_data.txt (HybridBaccaratPredictor._get_recent_session_data - Données session récente)
    - FONCTION : Extrait données de la session récente pour analyse
    - UTILITÉ : Focus sur performance récente pour ajustements

20. calculate_lstm_sample_weights.txt (HybridBaccaratPredictor.calculate_lstm_sample_weights - Poids échantillons LSTM)
    - FONCTION : Calcule poids spécialisés pour entraînement LSTM
    - UTILITÉ : Optimise apprentissage LSTM avec pondération adaptée

21. calculate_sample_weights_from_metrics.txt (HybridBaccaratPredictor.calculate_sample_weights_from_metrics - Poids depuis métriques)
    - FONCTION : Calcule poids échantillons basés sur métriques de performance
    - UTILITÉ : Pondération intelligente selon qualité des prédictions

22. _extract_features_for_consecutive_calculator.txt (HybridBaccaratPredictor._extract_features_for_consecutive_calculator - Features calculateur consécutif)
    - FONCTION : Extrait features spécialisées pour calculateur de manches consécutives
    - UTILITÉ : Optimise features pour analyse manches 31-60

23. _extract_pattern_key.txt (HybridBaccaratPredictor._extract_pattern_key - Extraction clé pattern)
    - FONCTION : Extrait clé de pattern pour indexation et recherche
    - UTILITÉ : Identification rapide des patterns récurrents

24. _update_pattern_counts.txt (HybridBaccaratPredictor._update_pattern_counts - MAJ compteurs patterns)
    - FONCTION : Met à jour compteurs de patterns détectés
    - UTILITÉ : Maintient statistiques patterns à jour

25. update_recent_data.txt (HybridBaccaratPredictor.update_recent_data - MAJ données récentes)
    - FONCTION : Met à jour cache des données récentes pour optimisation
    - UTILITÉ : Accès rapide aux données les plus pertinentes

26. _get_cached_lgbm_prediction.txt (HybridBaccaratPredictor._get_cached_lgbm_prediction - Prédiction LGBM cachée)
    - FONCTION : Récupère prédiction LGBM depuis cache pour optimisation
    - UTILITÉ : Évite recalculs coûteux via mise en cache

27. prepare_training_data.txt (HybridBaccaratPredictor.prepare_training_data - Préparation données entraînement)
    - FONCTION : Prépare données complètes pour entraînement des modèles
    - UTILITÉ : Pipeline complet de préparation données avec validation robuste
