DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTION DES DONNÉES
================================================================================

Ce fichier contient la description détaillée des méthodes de gestion des données,
chargement, sauvegarde et manipulation des états.

DOMAINE FONCTIONNEL : Gestion données, chargement/sauvegarde, manipulation états

TOTAL : 7 MÉTHODES ANALYSÉES

================================================================================

1. load_state.txt (load_state - FONCTION CHARGEMENT ÉTAT)
   - Lignes 2283-2324 dans utils.py (42 lignes)
   - FONCTION : Charge état système depuis fichier avec gestion erreurs et validation
   - PARAMÈTRES :
     * filepath (str) - Chemin fichier état à charger
     * default_state (dict, optionnel) - État par défaut si chargement échoue
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION FICHIER :** Teste existence fichier avant chargement
     * **CHARGEMENT JSON :** Utilise json.load avec gestion encodage UTF-8
     * **VALIDATION STRUCTURE :** Vérifie clés obligatoires dans état chargé
     * **GESTION ERREURS :** Try/except avec fallback sur default_state
     * **LOGGING :** Journalise succès/échec chargement pour débogage
   - RETOUR : dict - État système chargé ou par défaut
   - UTILITÉ : Persistance robuste état système avec récupération automatique

2. export_state.txt (export_state - FONCTION SAUVEGARDE ÉTAT)
   - Lignes 2326-2367 dans utils.py (42 lignes)
   - FONCTION : Sauvegarde état système vers fichier avec formatage et validation
   - PARAMÈTRES :
     * state (dict) - État système à sauvegarder
     * filepath (str) - Chemin fichier destination
     * backup (bool, défaut=True) - Créer backup avant écrasement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **BACKUP AUTOMATIQUE :** Crée .bak si fichier existe et backup=True
     * **VALIDATION DONNÉES :** Vérifie structure état avant sauvegarde
     * **SAUVEGARDE JSON :** json.dump avec indent=2 pour lisibilité
     * **GESTION ERREURS :** Try/except avec restauration backup si échec
     * **LOGGING :** Journalise opérations sauvegarde pour traçabilité
   - RETOUR : bool - True si sauvegarde réussie, False sinon
   - UTILITÉ : Persistance sécurisée état avec backup automatique et récupération

3. load_params_from_file.txt (load_params_from_file - CHARGEMENT PARAMÈTRES FICHIER)
   - Lignes 2369-2410 dans utils.py (42 lignes)
   - FONCTION : Charge paramètres configuration depuis fichier JSON avec validation
   - PARAMÈTRES :
     * filepath (str) - Chemin fichier paramètres
     * config_object - Objet configuration à mettre à jour
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CHARGEMENT JSON :** Lecture fichier paramètres avec gestion erreurs
     * **VALIDATION TYPES :** Vérifie types paramètres selon schéma attendu
     * **MISE À JOUR CONFIG :** Applique paramètres à config_object via setattr
     * **PARAMÈTRES MANQUANTS :** Conserve valeurs par défaut si clés absentes
     * **LOGGING :** Journalise paramètres chargés pour vérification
   - RETOUR : bool - True si chargement réussi, False sinon
   - UTILITÉ : Configuration flexible système via fichiers externes

4. apply_params_to_config.txt (apply_params_to_config - APPLICATION PARAMÈTRES CONFIG)
   - Lignes 2412-2453 dans utils.py (42 lignes)
   - FONCTION : Applique dictionnaire paramètres à objet configuration avec validation
   - PARAMÈTRES :
     * params (dict) - Dictionnaire paramètres à appliquer
     * config_object - Objet configuration destination
     * validate (bool, défaut=True) - Activer validation paramètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPES :** Vérifie compatibilité types si validate=True
     * **APPLICATION SÉCURISÉE :** setattr avec gestion exceptions par paramètre
     * **PARAMÈTRES INVALIDES :** Skip paramètres incompatibles avec warning
     * **LOGGING DÉTAILLÉ :** Journalise chaque paramètre appliqué/rejeté
     * **ROLLBACK :** Possibilité restauration état précédent si échec critique
   - RETOUR : int - Nombre paramètres appliqués avec succès
   - UTILITÉ : Application robuste paramètres avec validation et traçabilité
