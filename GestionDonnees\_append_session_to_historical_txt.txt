# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12746 à 12806
# Type: Méthode de la classe HybridBaccaratPredictor

    def _append_session_to_historical_txt(self, filepath: str = "historical_data.txt") -> bool:
        """
        Ajoute la séquence de session actuelle comme une nouvelle ligne dans le fichier historique,
        en utilisant le format '0' pour Player et '1' pour Banker.
        CORRIGÉ: Ouvre en mode 'a+' pour vérifier le dernier caractère et éviter les lignes vides.

        Args:
            filepath: Chemin vers le fichier historical_data.txt.

        Returns:
            bool: True si la session a été ajoutée avec succès (ou si elle était vide), False en cas d'erreur.
        """
        with self.sequence_lock:
            session_to_save = self.sequence[:]

        if not session_to_save:
            logger.info("_append_session_to_historical_txt: Séquence de session vide, rien à ajouter.")
            return True

        logger.info(f"Tentative d'ajout de la session actuelle ({len(session_to_save)} coups) à {filepath} au format 0/1 (évitant lignes vides)...")
        try:
            # Convertir en minuscules pour assurer la cohérence
            sequence_str = ",".join(['0' if outcome.lower() == 'player' else '1' for outcome in session_to_save])

            # Utiliser 'a+' pour append AND read/seek
            # encoding='utf-8' est important
            with open(filepath, 'a+', encoding='utf-8') as f:
                # Se placer à la fin du fichier pour voir sa taille et potentiellement lire le dernier caractère
                f.seek(0, os.SEEK_END)
                file_is_not_empty = f.tell() > 0

                # Si le fichier n'est pas vide, vérifier le dernier caractère
                needs_newline = False
                if file_is_not_empty:
                    # Se déplacer juste avant le dernier caractère
                    try: # Gérer le cas d'un fichier avec 1 seul caractère? Rare mais possible.
                        f.seek(f.tell() - 1, os.SEEK_SET)
                        last_char = f.read(1)
                        if last_char != '\n':
                            needs_newline = True
                    except Exception as e_seek_read:
                        # Si seek/read échoue (ex: fichier très petit?), on ajoute par sécurité le newline
                        logger.warning(f"Petit problème lecture dernier caractère (sera géré): {e_seek_read}")
                        needs_newline = True # Préférable d'avoir une ligne vide que des données collées

                # Se replacer à la fin pour écrire (normalement 'a+' le fait, mais soyons explicites)
                f.seek(0, os.SEEK_END)

                # Écrire le saut de ligne si nécessaire, PUIS la séquence
                if needs_newline:
                    f.write('\n')
                f.write(sequence_str)

            logger.info(f"Session ajoutée avec succès à {filepath} au format 0/1.")
            return True
        except (IOError, OSError) as e:
            logger.error(f"Erreur I/O lors de l'ajout de la session à {filepath}: {e}", exc_info=True)
            return False
        except Exception as e:
            logger.error(f"Erreur inattendue lors de l'ajout de la session à {filepath}: {e}", exc_info=True)
            return False