# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13871 à 13911
# Type: Méthode de la classe HybridBaccaratPredictor

    def _calculate_alternates(self, sequence: List[str]) -> Dict[str, float]:
        """Calcule les statistiques d'alternance pour une séquence."""
        if not sequence or len(sequence) < 2:
            return {'alternate_count_2': 0, 'alternate_count_3': 0, 'alternate_ratio': 0.5}

        alternates = 0
        last_outcome = sequence[0]

        for outcome in sequence[1:]:
            if outcome != last_outcome and outcome in ('banker', 'player'):
                alternates += 1
            last_outcome = outcome if outcome in ('banker', 'player') else last_outcome

        # Motifs d'alternance spécifiques
        alt_2_pattern = ['banker', 'player', 'banker', 'player']
        alt_3_pattern = ['banker', 'player', 'banker', 'player', 'banker', 'player']

        alt_count_2 = 0
        alt_count_3 = 0

        # Recherche du motif dans la séquence (sliding window)
        if len(sequence) >= 4:
            for i in range(len(sequence) - 3):
                window = sequence[i:i+4]
                if window == alt_2_pattern:
                    alt_count_2 += 1

        if len(sequence) >= 6:
            for i in range(len(sequence) - 5):
                window = sequence[i:i+6]
                if window == alt_3_pattern:
                    alt_count_3 += 1

        # Ratio d'alternance
        alt_ratio = alternates / (len(sequence) - 1) if len(sequence) > 1 else 0.5

        return {
            'alternate_count_2': alt_count_2,
            'alternate_count_3': alt_count_3,
            'alternate_ratio': alt_ratio
        }