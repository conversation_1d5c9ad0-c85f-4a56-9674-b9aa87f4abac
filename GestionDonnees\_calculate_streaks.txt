# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13803 à 13869
# Type: Méthode de la classe HybridBaccaratPredictor

    def _calculate_streaks(self, sequence: List[str]) -> Dict[str, int]:
        """Calcule les statistiques de streaks pour une séquence."""
        if not sequence:
            return {
                'banker_streaks': 0, 'player_streaks': 0,
                'banker_streak_2': 0, 'banker_streak_3': 0, 'banker_streak_4': 0,
                'banker_streak_5': 0, 'banker_streak_6': 0, 'banker_streak_7': 0,
                'player_streak_2': 0, 'player_streak_3': 0, 'player_streak_4': 0,
                'player_streak_5': 0, 'player_streak_6': 0, 'player_streak_7': 0,
                'max_banker_streak': 0, 'max_player_streak': 0
            }

        streak_counts = {
            'banker_streaks': 0, 'player_streaks': 0,
            'banker_streak_2': 0, 'banker_streak_3': 0, 'banker_streak_4': 0,
            'banker_streak_5': 0, 'banker_streak_6': 0, 'banker_streak_7': 0,
            'player_streak_2': 0, 'player_streak_3': 0, 'player_streak_4': 0,
            'player_streak_5': 0, 'player_streak_6': 0, 'player_streak_7': 0,
            'max_banker_streak': 0, 'max_player_streak': 0
        }

        current_type = None
        current_length = 0

        for outcome in sequence:
            if outcome not in ('banker', 'player'):
                # Réinitialiser sur les outcomes invalides
                current_type = None
                current_length = 0
                continue

            if outcome != current_type:
                # Fin d'un streak, traiter le précédent s'il existe
                if current_type is not None and current_length > 1:
                    streak_counts[f'{current_type}_streaks'] += 1

                    # Enregistrer les streaks de longueurs spécifiques (2-7)
                    for length in range(2, 8):
                        if current_length == length:
                            streak_counts[f'{current_type}_streak_{length}'] += 1

                    # Mise à jour des streaks max
                    if current_type == 'banker':
                        streak_counts['max_banker_streak'] = max(streak_counts['max_banker_streak'], current_length)
                    else:
                        streak_counts['max_player_streak'] = max(streak_counts['max_player_streak'], current_length)

                # Démarrer un nouveau streak
                current_type = outcome
                current_length = 1
            else:
                # Continuer le streak actuel
                current_length += 1

        # Traiter le dernier streak
        if current_type is not None and current_length > 1:
            streak_counts[f'{current_type}_streaks'] += 1
            for length in range(2, 8):
                if current_length == length:
                    streak_counts[f'{current_type}_streak_{length}'] += 1

            if current_type == 'banker':
                streak_counts['max_banker_streak'] = max(streak_counts['max_banker_streak'], current_length)
            else:
                streak_counts['max_player_streak'] = max(streak_counts['max_player_streak'], current_length)

        return streak_counts