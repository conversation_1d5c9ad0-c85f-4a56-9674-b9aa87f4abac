# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10350 à 10441
# Type: Méthode de la classe HybridBaccaratPredictor

    def _extract_features_for_consecutive_calculator(self) -> List[float]:
        """
        Extrait les features pour le calculateur de confiance consécutive.
        Cette méthode crée un vecteur de features qui capture les caractéristiques importantes
        de la séquence actuelle pour prédire la confiance dans les recommandations NON-WAIT.

        Returns:
            List[float]: Vecteur de features pour le calculateur de confiance consécutive
        """
        # Vérifier si la séquence est suffisamment longue
        if len(self.sequence) < 3:
            # Retourner un vecteur de features par défaut si la séquence est trop courte
            default_feature_value = getattr(self.config, 'default_feature_value', 0.5)
            return [default_feature_value] * 10  # Vecteur de features par défaut

        try:
            # 1. Features basées sur la séquence récente
            recent_sequence = self.sequence[-10:] if len(self.sequence) >= 10 else self.sequence

            # Calculer le ratio banker/player dans la séquence récente
            banker_count = sum(1 for outcome in recent_sequence if outcome == 'banker')
            player_count = sum(1 for outcome in recent_sequence if outcome == 'player')
            banker_ratio = banker_count / len(recent_sequence) if len(recent_sequence) > 0 else 0.5

            # Calculer le nombre d'alternances dans la séquence récente
            alternations = 0
            for i in range(1, len(recent_sequence)):
                if recent_sequence[i] != recent_sequence[i-1]:
                    alternations += 1
            alternation_ratio = alternations / (len(recent_sequence) - 1) if len(recent_sequence) > 1 else 0.5

            # 2. Features basées sur les performances récentes
            # Initialiser les compteurs s'ils n'existent pas encore
            if not hasattr(self, 'total_nonwait'):
                self.total_nonwait = 0
                self.total_nonwait_valid = 0
                self.total_wait = 0
                self.consecutive_nonwait_valid = 0
                self.max_consecutive_nonwait_valid = 0

            # Calculer le taux de succès des recommandations NON-WAIT
            nonwait_success_rate = self.total_nonwait_valid / max(1, self.total_nonwait)

            # Calculer le ratio WAIT/NON-WAIT
            total_recommendations = self.total_nonwait + self.total_wait
            wait_ratio = self.total_wait / max(1, total_recommendations)

            # 3. Features basées sur la position dans la partie
            current_round = len(self.sequence)
            target_round_min = getattr(self.config, 'target_round_min', 31)
            target_round_max = getattr(self.config, 'target_round_max', 60)
            is_target_round = 1.0 if target_round_min <= current_round <= target_round_max else 0.0

            # Position relative dans la plage cible (0 au début, 1 à la fin)
            relative_position = (current_round - target_round_min) / (target_round_max - target_round_min) if is_target_round > 0 else 0.0

            # 4. Features basées sur les séquences consécutives
            current_consecutive = self.consecutive_nonwait_valid
            max_consecutive = self.max_consecutive_nonwait_valid

            # 5. Features basées sur les patterns
            # Détecter les patterns dans la séquence récente
            pattern_strength = 0.0
            if len(recent_sequence) >= 6:
                # Chercher des répétitions de patterns de longueur 2 ou 3
                for pattern_length in [2, 3]:
                    for i in range(len(recent_sequence) - 2 * pattern_length):
                        pattern = recent_sequence[i:i+pattern_length]
                        next_segment = recent_sequence[i+pattern_length:i+2*pattern_length]
                        if pattern == next_segment:
                            pattern_strength += 0.2 * pattern_length  # Plus le pattern est long, plus il est significatif

            # 6. Assembler le vecteur de features
            features = [
                banker_ratio,                # Ratio banker/player
                alternation_ratio,           # Ratio d'alternances
                nonwait_success_rate,        # Taux de succès des recommandations NON-WAIT
                wait_ratio,                  # Ratio WAIT/NON-WAIT
                is_target_round,             # Indicateur de manche cible
                relative_position,           # Position relative dans la plage cible
                current_consecutive / getattr(self.config, 'consecutive_normalization_factor', 10.0),  # Nombre actuel de recommandations NON-WAIT valides consécutives (normalisé)
                max_consecutive / getattr(self.config, 'consecutive_normalization_factor', 10.0),      # Nombre maximum de recommandations NON-WAIT valides consécutives (normalisé)
                pattern_strength,            # Force des patterns détectés
                current_round / getattr(self.config, 'round_normalization_factor', 100.0)        # Numéro de manche (normalisé)
            ]

            return features

        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des features pour le calculateur de confiance consécutive: {e}", exc_info=True)
            default_feature_value = getattr(self.config, 'default_feature_value', 0.5)
            return [default_feature_value] * 10  # Vecteur de features par défaut en cas d'erreur