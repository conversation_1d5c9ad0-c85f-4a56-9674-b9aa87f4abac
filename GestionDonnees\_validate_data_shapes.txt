# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4351 à 4384
# Type: Méthode de la classe HybridBaccaratPredictor

    def _validate_data_shapes(self, X_lgbm_all: np.n<PERSON>ray, y_labels_all: np.n<PERSON>ray, X_lstm_all: np.ndarray, sample_weights_all: np.ndarray, list_of_all_prefix_sequences: List[List[str]], list_of_all_origins: List[int], final_num_samples: int) -> Tuple[bool, str]:
        """Extrait la validation des shapes dans une fonction séparée."""
        is_valid = True
        message = ""

        if X_lgbm_all.shape[0] != final_num_samples:
            is_valid = False
            message += f"Incohérence taille X_lgbm (attendu: {final_num_samples}, obtenu: {X_lgbm_all.shape[0]}). "

        if y_labels_all.shape[0] != final_num_samples:
            is_valid = False
            message += f"Incohérence taille y_labels (attendu: {final_num_samples}, obtenu: {y_labels_all.shape[0]}). "

        if X_lstm_all.shape[0] != final_num_samples:
            is_valid = False
            message += f"Incohérence taille X_lstm (attendu: {final_num_samples}, obtenu: {X_lstm_all.shape[0]}). "

        if sample_weights_all.shape[0] != final_num_samples:
            is_valid = False
            message += f"Incohérence taille sample_weights (attendu: {final_num_samples}, obtenu: {sample_weights_all.shape[0]}). "

        if len(list_of_all_prefix_sequences) != final_num_samples:
            is_valid = False
            message += f"Incohérence taille list_of_all_prefix_sequences (attendu: {final_num_samples}, obtenu: {len(list_of_all_prefix_sequences)}). "

        if len(list_of_all_origins) != final_num_samples:
            is_valid = False
            message += f"Incohérence taille list_of_all_origins (attendu: {final_num_samples}, obtenu: {len(list_of_all_origins)}). "

        if not is_valid:
            full_message = f"Erreur de validation des shapes: {message}"
            return False, full_message
        else:
            return True, ""