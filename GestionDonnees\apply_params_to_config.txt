# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3108 à 3321
# Type: Méthode

def apply_params_to_config(config, params: Dict[str, Any]) -> bool:
    """
    Applique les paramètres optimisés à la configuration de manière exhaustive.
    Gère les cas spéciaux et assure la cohérence des paramètres interdépendants.

    Args:
        config: Instance de PredictorConfig
        params (Dict[str, Any]): Dictionnaire des paramètres à appliquer

    Returns:
        bool: True si l'application a réussi, False sinon
    """
    import logging
    import numpy as np
    import json
    logger = logging.getLogger(__name__)

    if not params or not isinstance(params, dict):
        logger.error("Paramètres invalides pour l'application à la configuration")
        return False

    try:
        # Journaliser les paramètres importants avant modification
        logger.info("=" * 80)
        logger.info("PARAMÈTRES IMPORTANTS AVANT MODIFICATION:")
        important_params = [
            'min_confidence_for_recommendation', 'error_pattern_threshold',
            'transition_uncertainty_threshold', 'wait_optimizer_confidence_threshold',
            'lstm_hidden_dim', 'lstm_num_layers', 'lstm_dropout', 'lstm_bidirectional',
            'lgbm_n_estimators', 'lgbm_learning_rate', 'lgbm_max_depth', 'lgbm_num_leaves'
        ]
        for param in important_params:
            if hasattr(config, param):
                logger.info(f"  {param}: {getattr(config, param)}")
        logger.info("=" * 80)

        # Convertir les valeurs de chaîne en types appropriés
        converted_params = {}
        for k, v in params.items():
            # Vérifier si c'est un paramètre booléen
            if k.startswith(('use_', 'lstm_use_', 'lgbm_use_')) or isinstance(v, bool):
                # Les paramètres booléens sont maintenant correctement gérés à la source
                # Traitement simplifié pour les chaînes 'true'/'false'
                if isinstance(v, str) and v.lower() in ('true', 'false'):
                    # Convertir les chaînes 'true'/'false' en booléens
                    converted_params[k] = v.lower() == 'true'
                else:
                    # Pour les autres valeurs, utiliser la conversion standard
                    converted_params[k] = bool(v)
            elif isinstance(v, str):
                # Tenter de convertir les chaînes en types appropriés
                try:
                    # Essayer de charger comme JSON
                    if v.startswith('{') and v.endswith('}') or v.startswith('[') and v.endswith(']'):
                        converted_params[k] = json.loads(v)
                    # Convertir les booléens
                    elif v.lower() in ('true', 'false'):
                        converted_params[k] = v.lower() == 'true'
                    # Convertir les nombres
                    elif v.replace('.', '', 1).isdigit() or (v.startswith('-') and v[1:].replace('.', '', 1).isdigit()):
                        if '.' in v:
                            converted_params[k] = float(v)
                        else:
                            converted_params[k] = int(v)
                    # Convertir None
                    elif v.lower() == 'none':
                        converted_params[k] = None
                    else:
                        converted_params[k] = v
                except (ValueError, json.JSONDecodeError):
                    # En cas d'erreur, conserver la valeur d'origine
                    converted_params[k] = v
            else:
                converted_params[k] = v

        # Traiter les paramètres de poids séparément
        weight_params = {k: v for k, v in converted_params.items() if k.startswith('weight_') or k.startswith('mo_weight_')}
        if weight_params:
            # Construire le dictionnaire initial_weights
            weights_dict = {}
            for k, v in weight_params.items():
                if k.startswith('weight_'):
                    model_name = k[7:]  # Enlever 'weight_'
                elif k.startswith('mo_weight_'):
                    model_name = k[10:]  # Enlever 'mo_weight_'
                else:
                    continue

                # Convertir en float si nécessaire
                if isinstance(v, str):
                    try:
                        v = float(v)
                    except ValueError:
                        logger.warning(f"Impossible de convertir le poids {k} en float: {v}")
                        continue

                weights_dict[model_name] = v

            # Normaliser les poids
            total = sum(weights_dict.values())
            if total > 1e-9:
                weights_dict = {k: v/total for k, v in weights_dict.items()}
                logger.info(f"Poids normalisés: {weights_dict}")
            else:
                logger.warning(f"Somme des poids trop faible ({total}), utilisation de poids égaux")
                weights_dict = {k: 1.0/len(weights_dict) for k in weights_dict}

            # Appliquer les poids
            config.initial_weights = weights_dict
            logger.info(f"Poids appliqués: {weights_dict}")

        # Traiter les paramètres LGBM spéciaux
        lgbm_params = {}
        for k, v in converted_params.items():
            if k.startswith('lgbm_') and k != 'lgbm_params':
                param_name = k[5:]  # Enlever 'lgbm_'
                lgbm_params[param_name] = v

        if lgbm_params:
            # S'assurer que lgbm_params existe
            if not hasattr(config, 'lgbm_params') or config.lgbm_params is None:
                config.lgbm_params = {}

            # Mettre à jour les paramètres LGBM
            for param_name, param_value in lgbm_params.items():
                config.lgbm_params[param_name] = param_value
                # Également mettre à jour l'attribut correspondant
                setattr(config, f'lgbm_{param_name}', param_value)

            logger.info(f"Paramètres LGBM mis à jour: {lgbm_params}")

        # Traiter les paramètres LSTM spéciaux
        lstm_params = {}
        for k, v in converted_params.items():
            if k.startswith('lstm_'):
                param_name = k[5:]  # Enlever 'lstm_'
                lstm_params[param_name] = v

        if lstm_params:
            # Mettre à jour les paramètres LSTM
            for param_name, param_value in lstm_params.items():
                setattr(config, f'lstm_{param_name}', param_value)

            # S'assurer que les dimensions LSTM sont cohérentes
            if 'hidden_dim' in lstm_params and 'hidden_size' in lstm_params:
                if lstm_params['hidden_dim'] != lstm_params['hidden_size']:
                    logger.warning(f"Synchronisation des dimensions LSTM: hidden_dim={lstm_params['hidden_dim']}, hidden_size={lstm_params['hidden_size']}")
                    # Utiliser hidden_dim comme référence
                    config.lstm_hidden_size = config.lstm_hidden_dim

            logger.info(f"Paramètres LSTM mis à jour: {lstm_params}")

        # Traiter les autres paramètres
        other_params = {k: v for k, v in converted_params.items()
                       if not k.startswith('weight_') and not k.startswith('mo_weight_')
                       and not k.startswith('lgbm_') and not k.startswith('lstm_')}

        for param_name, param_value in other_params.items():
            if hasattr(config, param_name):
                # Vérifier si le type est compatible
                current_value = getattr(config, param_name)
                if current_value is not None and not isinstance(param_value, type(current_value)) and not isinstance(current_value, type(param_value)):
                    logger.warning(f"Type incompatible pour {param_name}: attendu {type(current_value)}, reçu {type(param_value)}")
                    # Tenter de convertir
                    if isinstance(current_value, (int, float)) and isinstance(param_value, (int, float)):
                        if isinstance(current_value, int):
                            param_value = int(param_value)
                        else:
                            param_value = float(param_value)

                # Appliquer le paramètre
                setattr(config, param_name, param_value)
            else:
                logger.warning(f"Paramètre {param_name} non trouvé dans la configuration")

        # Vérifier et ajuster les paramètres interdépendants
        # 1. S'assurer que les seuils de confiance sont cohérents
        if hasattr(config, 'min_confidence_for_recommendation') and hasattr(config, 'max_confidence_for_recommendation'):
            if config.min_confidence_for_recommendation > config.max_confidence_for_recommendation:
                logger.warning(f"Ajustement des seuils de confiance: min={config.min_confidence_for_recommendation}, max={config.max_confidence_for_recommendation}")
                # Échanger les valeurs
                config.min_confidence_for_recommendation, config.max_confidence_for_recommendation = config.max_confidence_for_recommendation, config.min_confidence_for_recommendation

        # 2. S'assurer que les ratios WAIT sont cohérents
        if hasattr(config, 'wait_ratio_min_threshold') and hasattr(config, 'wait_ratio_max_threshold'):
            if config.wait_ratio_min_threshold > config.wait_ratio_max_threshold:
                logger.warning(f"Ajustement des ratios WAIT: min={config.wait_ratio_min_threshold}, max={config.wait_ratio_max_threshold}")
                # Échanger les valeurs
                config.wait_ratio_min_threshold, config.wait_ratio_max_threshold = config.wait_ratio_max_threshold, config.wait_ratio_min_threshold

        # 3. S'assurer que les paramètres LGBM sont cohérents
        if hasattr(config, 'lgbm_num_leaves') and hasattr(config, 'lgbm_max_depth'):
            max_leaves = (2 ** config.lgbm_max_depth) - 1
            if config.lgbm_num_leaves > max_leaves:
                logger.warning(f"Ajustement de lgbm_num_leaves: {config.lgbm_num_leaves} -> {max_leaves} (max_depth={config.lgbm_max_depth})")
                config.lgbm_num_leaves = max_leaves
                if hasattr(config, 'lgbm_params'):
                    config.lgbm_params['num_leaves'] = max_leaves

        # Journaliser les paramètres importants après modification
        logger.info("=" * 80)
        logger.info("PARAMÈTRES IMPORTANTS APRÈS MODIFICATION:")
        for param in important_params:
            if hasattr(config, param):
                logger.info(f"  {param}: {getattr(config, param)}")
        logger.info("=" * 80)

        logger.info(f"Paramètres appliqués à la configuration: {len(params)} paramètres")
        return True
    except Exception as e:
        logger.error(f"Erreur lors de l'application des paramètres à la configuration: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False