# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10027 à 10170
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_lstm_sample_weights(self, X_lstm: np.ndarray, y_lstm: np.ndarray, sequence_positions: np.ndarray = None) -> np.ndarray:
        """
        Calcule les poids d'échantillons pour LSTM basés sur les métriques de confiance et d'incertitude,
        en tenant compte des positions de séquence pour donner plus de poids aux manches 31-60.

        Cette méthode utilise une approche différente de celle pour LGBM, adaptée aux séquences temporelles.
        Elle donne plus d'importance aux échantillons récents, à ceux qui sont plus difficiles à prédire,
        et à ceux qui correspondent aux manches cibles (31-60).

        Args:
            X_lstm (np.ndarray): Features LSTM (séquences)
            y_lstm (np.ndarray): Labels correspondants
            sequence_positions (np.ndarray, optional): Positions des échantillons dans la séquence

        Returns:
            np.ndarray: Poids des échantillons pour LSTM
        """
        if X_lstm is None or len(X_lstm) == 0 or y_lstm is None or len(y_lstm) == 0:
            logger.warning("calculate_lstm_sample_weights: Données insuffisantes.")
            return None

        try:
            # Initialiser les poids à 1.0
            sample_weights = np.ones(len(X_lstm), dtype=np.float32)

            # 1. Facteur temporel: donner plus de poids aux échantillons récents
            # Supposons que les échantillons sont ordonnés chronologiquement
            temporal_factor_min = getattr(self.config, 'temporal_factor_min', 0.5)
            temporal_factor_max = getattr(self.config, 'temporal_factor_max', 1.0)
            temporal_factor = np.linspace(temporal_factor_min, temporal_factor_max, len(X_lstm))

            # 2. Facteur de difficulté: donner plus de poids aux échantillons difficiles à prédire
            difficulty_factor = np.ones_like(sample_weights)

            # Vérifier si le modèle LSTM existe et est entraîné
            lstm_model_fitted = False
            if self.lstm is not None:
                try:
                    # Vérifier si le modèle a des poids initialisés
                    if hasattr(self.lstm, 'state_dict') and len(self.lstm.state_dict()) > 0:
                        lstm_model_fitted = True
                except Exception as e_check:
                    logger.warning(f"Erreur lors de la vérification du modèle LSTM: {e_check}")
                    lstm_model_fitted = False

            # Si le modèle LSTM existe et est entraîné, l'utiliser pour identifier les échantillons difficiles
            if lstm_model_fitted:
                try:
                    self.lstm.eval()  # Mettre en mode évaluation
                    with torch.no_grad():
                        # Convertir les features en tensors
                        X_tensor = torch.FloatTensor(X_lstm).to(self.device)
                        # Obtenir les prédictions
                        outputs = self.lstm(X_tensor)
                        probas = torch.softmax(outputs, dim=1).cpu().numpy()

                        # Calculer la confiance pour chaque prédiction
                        # (distance entre la probabilité prédite et 0.5)
                        y_indices = y_lstm.astype(int)
                        pred_probas = np.array([probas[i, y_indices[i]] for i in range(len(y_lstm))])
                        confidence = np.abs(pred_probas - 0.5) * 2.0

                        # Inverser la confiance pour obtenir la difficulté
                        # (échantillons avec faible confiance = plus difficiles)
                        difficulty_factor = 1.0 - 0.5 * confidence

                        logger.info(f"Facteur de difficulté calculé avec le modèle LSTM: min={np.min(difficulty_factor):.4f}, max={np.max(difficulty_factor):.4f}")
                except Exception as e_lstm_pred:
                    logger.error(f"Erreur lors du calcul des difficultés LSTM: {e_lstm_pred}", exc_info=True)
                    # En cas d'erreur, utiliser un facteur de difficulté uniforme
                    difficulty_factor = np.ones_like(sample_weights)
            else:
                logger.info("Modèle LSTM non entraîné, utilisation d'un facteur de difficulté uniforme.")

            # 3. Facteur de position: donner beaucoup plus de poids aux échantillons des manches cibles (31-60)
            position_factor = np.ones_like(sample_weights)

            # Paramètres configurables pour l'objectif N°1 - Augmentés pour donner encore plus d'importance aux manches cibles
            consecutive_focus_factor = getattr(self.config, 'consecutive_focus_factor', 6.0)  # Augmenté de 2.0 à 6.0
            late_game_weight_factor = getattr(self.config, 'late_game_weight_factor', 10.0)  # Augmenté de 5.0 à 10.0
            target_round_min = getattr(self.config, 'target_round_min', 31)
            target_round_max = getattr(self.config, 'target_round_max', 60)
            objective1_weight = getattr(self.config, 'objective1_weight', 0.9)  # Augmenté pour donner plus d'importance à l'objectif 1

            if sequence_positions is not None:
                # Convertir les positions 0-indexées en positions 1-indexées pour correspondre aux numéros de manches
                positions_1_indexed = sequence_positions + 1

                # Créer un masque pour les manches cibles (31-60)
                late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)

                # Appliquer un poids progressif qui augmente à mesure qu'on approche de la manche 60
                # Cela donne plus d'importance aux dernières manches de la plage cible
                if np.any(late_game_mask):
                    target_positions = positions_1_indexed[late_game_mask]
                    normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)

                    # Fonction qui augmente exponentiellement le poids (de 1.0 à late_game_weight_factor)
                    # Utiliser une fonction exponentielle pour donner encore plus de poids aux manches proches de 60
                    exponential_factor = 2.0  # Contrôle la courbure de l'exponentielle
                    progressive_weights = 1.0 + (np.exp(exponential_factor * normalized_positions) - 1) / (np.exp(exponential_factor) - 1) * (late_game_weight_factor - 1.0)
                    position_factor[late_game_mask] = progressive_weights

                    # Appliquer un poids supplémentaire pour les échantillons de classe minoritaire
                    # Calculer la distribution des classes dans les manches cibles
                    target_y = y_lstm[late_game_mask]
                    class_counts = np.bincount(target_y)
                    if len(class_counts) > 1:  # S'assurer qu'il y a au moins 2 classes
                        # Augmenter le poids des classes minoritaires encore plus
                        class_weights = (len(target_y) / (len(class_counts) * class_counts)) ** 1.5  # Exposant pour accentuer l'effet
                        for i, idx in enumerate(np.where(late_game_mask)[0]):
                            position_factor[idx] *= class_weights[target_y[i]]

                    # 4. Facteur de transition: donner plus de poids aux points de transition (changements de tendance)
                    # Cela aide le modèle à mieux apprendre les points critiques où la tendance change
                    if len(target_y) > 1:
                        transitions = np.diff(target_y, prepend=target_y[0])
                        transition_mask = transitions != 0  # Marque les points où la classe change
                        transition_indices = np.where(late_game_mask)[0][transition_mask]
                        if len(transition_indices) > 0:
                            position_factor[transition_indices] *= 1.5  # Bonus pour les points de transition
                else:
                    # Si aucune position n'est dans la plage cible, utiliser le comportement par défaut
                    position_factor[late_game_mask] = late_game_weight_factor

                logger.info(f"Facteur de position appliqué pour les manches {target_round_min}-{target_round_max}: min={np.min(position_factor):.4f}, max={np.max(position_factor):.4f}")

            # Combiner les facteurs
            combined_weights = temporal_factor * difficulty_factor * position_factor

            # Normaliser pour avoir une moyenne de 1.0
            if np.sum(combined_weights) > 0:
                normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))
                # Limiter les poids entre min et max pour éviter les valeurs extrêmes
                min_weight = getattr(self.config, 'min_sample_weight', 0.2)
                max_weight = getattr(self.config, 'max_sample_weight', 5.0)
                sample_weights = np.clip(normalized_weights, min_weight, max_weight)

            logger.info(f"Poids LSTM calculés: min={np.min(sample_weights):.4f}, max={np.max(sample_weights):.4f}, mean={np.mean(sample_weights):.4f}")
            return sample_weights

        except Exception as e:
            logger.error(f"Erreur lors du calcul des poids LSTM: {e}", exc_info=True)
            return None