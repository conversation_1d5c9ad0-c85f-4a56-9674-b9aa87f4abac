# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 5056 à 5105
# Type: Méthode de la classe HybridBaccaratPredictor

    def create_hybrid_features(self, sequence: List[str]) -> Tuple[Optional[List[float]], Optional[np.ndarray]]:
        """
        Fonction centralisée pour la création de features hybrides (LGBM et LSTM).
        Utilise une fenêtre adaptative pour les modèles LGBM et LSTM.

        Pour calculer les probabilités de la manche N, utilise les N-1 manches précédentes.
        Par exemple, pour la manche 21, utilise les 20 manches précédentes.
        Pour la manche 34, utilise les 33 manches précédentes.

        Args:
            sequence (List[str]): Séquence de résultats ('player', 'banker')
                                 Toute la séquence disponible est utilisée (fenêtre adaptative).

        Returns:
            Tuple[Optional[List[float]], Optional[np.ndarray]]: Tuple (features LGBM, features LSTM)
                Si une erreur se produit pour l'un des types, retourne None pour ce type.
        """
        if not sequence or len(sequence) < 2:
            logger.warning("Séquence vide ou trop courte. Retour (None, None).")
            return None, None

        lgbm_features = None
        lstm_features = None

        # 1. Création des features LGBM (utilise déjà toute la séquence)
        try:
            lgbm_features = self._create_lgbm_features(sequence)
        except Exception as e:
            logger.error(f"Erreur lors de la création des features LGBM: {e}", exc_info=True)

        # 2. Création des features LSTM (utilise maintenant toute la séquence)
        try:
            # Utiliser la longueur de séquence LSTM
            # Utiliser toujours lstm_sequence_length pour uniformiser le code
            lstm_sequence_length = self.config.lstm_sequence_length
            # Ne pas afficher de message de débogage pendant l'optimisation Optuna
            if (not hasattr(self, 'is_optuna_running') or not self.is_optuna_running) and hasattr(self, 'first_run') and self.first_run:
                logger.debug(f"Utilisation de lstm_sequence_length ({lstm_sequence_length}) pour tous les modèles")
                # Désactiver les messages futurs
                self.first_run = False

            lstm_features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)
        except Exception as e:
            logger.error(f"Erreur lors de la création des features LSTM: {e}", exc_info=True)

        # Si aucune feature n'est créée, logger une alerte
        if lgbm_features is None and lstm_features is None:
            logger.warning("Aucune feature valide n'a été créée pour LGBM ou LSTM.")

        return lgbm_features, lstm_features