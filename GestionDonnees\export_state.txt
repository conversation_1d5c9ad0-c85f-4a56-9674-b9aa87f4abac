# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1525 à 1577
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def export_state(self) -> Dict[str, Any]:
        """
        Exporte l'état actuel du calculateur de confiance consécutive dans un format sérialisable.

        Returns:
            Dict[str, Any]: Un dictionnaire contenant l'état complet du calculateur
        """
        logger.info("Exportation de l'état du calculateur de confiance consécutive...")

        # Convertir le defaultdict pattern_stats en dictionnaire standard pour la sérialisation
        pattern_stats_export = {}
        for pattern_key, stats in self.pattern_stats.items():
            pattern_stats_export[pattern_key] = dict(stats)

        # Créer le dictionnaire d'exportation
        export_data = {
            'config': {
                'min_occurrences': self.min_occurrences,
                'max_pattern_length': self.max_pattern_length,
                'target_round_min': self.target_round_min,
                'target_round_max': self.target_round_max,
                'late_game_factor': self.late_game_factor,
                'occurrence_factor_divisor': self.occurrence_factor_divisor,
                'consecutive_factor_divisor': self.consecutive_factor_divisor,
                'max_occurrence_factor': self.max_occurrence_factor,
                'max_consecutive_factor': self.max_consecutive_factor,
                'pattern_similarity_threshold': self.pattern_similarity_threshold,
                'max_similar_patterns': self.max_similar_patterns,
                'optimal_wait_ratio': self.optimal_wait_ratio,
                'wait_ratio_tolerance': self.wait_ratio_tolerance,
                'sequence_bonus_threshold': self.sequence_bonus_threshold,
                'sequence_bonus_factor': self.sequence_bonus_factor,
                'success_rate_weight': self.success_rate_weight,
                'consecutive_length_weight': self.consecutive_length_weight,
                'pattern_frequency_weight': self.pattern_frequency_weight,
                'max_recent_history': self.max_recent_history
            },
            'pattern_stats': pattern_stats_export,
            'recent_recommendations': self.recent_recommendations,
            'recent_outcomes': self.recent_outcomes,
            'counters': {
                'total_recommendations': self.total_recommendations,
                'wait_recommendations': self.wait_recommendations,
                'non_wait_recommendations': self.non_wait_recommendations,
                'correct_recommendations': self.correct_recommendations,
                'current_consecutive_valid': self.current_consecutive_valid,
                'max_consecutive_valid': self.max_consecutive_valid
            },
            'round_performance': self.round_performance
        }

        logger.info("Exportation de l'état du calculateur de confiance consécutive terminée.")
        return export_data