# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4831 à 4886
# Type: Méthode de la classe HybridBaccaratPredictor

    def load_historical_data(self) -> None:
        """Charge les données historiques depuis un fichier .txt."""
        if self.is_training or self.is_fast_updating:
             messagebox.showwarning("Action Impossible", "Veuillez arrêter toute tâche ML avant de charger de nouvelles données.")
             return

        filepath = filedialog.askopenfilename(
            title="Sélectionnez le fichier historique Baccarat (.txt)",
            filetypes=(("Fichiers texte", "*.txt"), ("Tous les fichiers", "*.*"))
        )

        if not filepath:
            logger.info("Chargement de l'historique annulé par l'utilisateur.")
            return

        logger.info(f"Tentative de chargement de l'historique depuis: {filepath}")
        self._update_progress(10, f"Chargement: {os.path.basename(filepath)}")

        # Utiliser la logique interne _load_historical_txt
        success = self._load_historical_txt(filepath)

        if success:
            with self.sequence_lock: # Accéder aux données chargées pour stats
                valid_games = len(self.historical_data)
                avg_len = sum(len(g) for g in self.historical_data) / valid_games if valid_games > 0 else 0
                total_rounds = sum(len(g) for g in self.historical_data)

            stats_msg = (
                f"Chargement historique réussi !\n\n"
                f"- Parties valides chargées : {valid_games}\n"
                # _load_historical_txt ne compte pas les skipped_invalid séparément
                # f"- Lignes invalides ignorées : {skipped_invalid}\n"
                f"- Longueur moyenne partie : {avg_len:.1f} coups\n"
                f"- Total coups chargés : {total_rounds}\n\n"
                "Les modèles Markov globaux ont été mis à jour.\n"
                "Vous pouvez maintenant lancer l'entraînement principal si désiré."
            )
            self._update_progress(100, f"{valid_games} parties ({total_rounds} coups) chargées.")
            messagebox.showinfo("Chargement Terminé", stats_msg)

             # Réinitialiser la séquence de session actuelle après chargement historique?
            if self.sequence: # Si une session était en cours
                 if messagebox.askyesno("Session en Cours", "Chargement historique réussi.\nVoulez-vous réinitialiser la séquence de jeu actuelle ?"):
                      logger.info("Réinitialisation de la session en cours après chargement historique.")
                      self.reset_data('soft', confirm=False) # Reset soft pour vider sequence etc.
                 else:
                      logger.info("Session en cours conservée après chargement historique.")

        else:
            # _load_historical_txt a déjà loggué l'erreur
            self._update_progress(0, "Échec chargement historique.")
            # Afficher un message d'erreur générique si l'interne n'a pas pu (peu probable)
            if not os.path.exists(filepath):
                 messagebox.showerror("Erreur Fichier", f"Le fichier spécifié n'a pas été trouvé:\n{filepath}")
            else:
                 messagebox.showerror("Erreur Chargement", f"Impossible de charger ou traiter le fichier historique:\n{filepath}\nVérifiez l'encodage (UTF-8), le format (B,P,P...) et les permissions.")