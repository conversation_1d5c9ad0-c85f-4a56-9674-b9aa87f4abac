# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1579 à 1663
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def load_state(self, data: Dict[str, Any]) -> bool:
        """
        Charge l'état du calculateur de confiance consécutive à partir d'un dictionnaire.

        Args:
            data: Dictionnaire contenant l'état à charger

        Returns:
            bool: True si le chargement a réussi, False sinon
        """
        if not isinstance(data, dict):
            logger.error("load_state: Données invalides fournies (pas un dictionnaire).")
            return False

        try:
            logger.info("Chargement de l'état du calculateur de confiance consécutive...")

            # Charger la configuration
            if 'config' in data and isinstance(data['config'], dict):
                config = data['config']
                self.min_occurrences = config.get('min_occurrences', self.min_occurrences)
                self.max_pattern_length = config.get('max_pattern_length', self.max_pattern_length)
                self.target_round_min = config.get('target_round_min', self.target_round_min)
                self.target_round_max = config.get('target_round_max', self.target_round_max)
                self.late_game_factor = config.get('late_game_factor', self.late_game_factor)
                self.occurrence_factor_divisor = config.get('occurrence_factor_divisor', self.occurrence_factor_divisor)
                self.consecutive_factor_divisor = config.get('consecutive_factor_divisor', self.consecutive_factor_divisor)
                self.max_occurrence_factor = config.get('max_occurrence_factor', self.max_occurrence_factor)
                self.max_consecutive_factor = config.get('max_consecutive_factor', self.max_consecutive_factor)
                self.pattern_similarity_threshold = config.get('pattern_similarity_threshold', self.pattern_similarity_threshold)
                self.max_similar_patterns = config.get('max_similar_patterns', self.max_similar_patterns)
                self.optimal_wait_ratio = config.get('optimal_wait_ratio', self.optimal_wait_ratio)
                self.wait_ratio_tolerance = config.get('wait_ratio_tolerance', self.wait_ratio_tolerance)
                self.sequence_bonus_threshold = config.get('sequence_bonus_threshold', self.sequence_bonus_threshold)
                self.sequence_bonus_factor = config.get('sequence_bonus_factor', self.sequence_bonus_factor)
                self.success_rate_weight = config.get('success_rate_weight', self.success_rate_weight)
                self.consecutive_length_weight = config.get('consecutive_length_weight', self.consecutive_length_weight)
                self.pattern_frequency_weight = config.get('pattern_frequency_weight', self.pattern_frequency_weight)
                self.max_recent_history = config.get('max_recent_history', self.max_recent_history)

                logger.info("Configuration du calculateur de confiance consécutive chargée.")

            # Charger les statistiques de patterns
            if 'pattern_stats' in data and isinstance(data['pattern_stats'], dict):
                # Réinitialiser les statistiques actuelles
                self.pattern_stats = defaultdict(lambda: {"total": 0, "success": 0, "consecutive_lengths": [], "max_consecutive": 0})

                # Charger les nouvelles statistiques
                for pattern_key, stats in data['pattern_stats'].items():
                    self.pattern_stats[pattern_key] = defaultdict(int, stats)

                logger.info(f"Statistiques de patterns chargées ({len(data['pattern_stats'])} patterns).")

            # Charger l'historique récent
            if 'recent_recommendations' in data and isinstance(data['recent_recommendations'], list):
                self.recent_recommendations = data['recent_recommendations']
                logger.info(f"Historique des recommandations récentes chargé ({len(self.recent_recommendations)} éléments).")

            if 'recent_outcomes' in data and isinstance(data['recent_outcomes'], list):
                self.recent_outcomes = data['recent_outcomes']
                logger.info(f"Historique des résultats récents chargé ({len(self.recent_outcomes)} éléments).")

            # Charger les compteurs
            if 'counters' in data and isinstance(data['counters'], dict):
                counters = data['counters']
                self.total_recommendations = counters.get('total_recommendations', self.total_recommendations)
                self.wait_recommendations = counters.get('wait_recommendations', self.wait_recommendations)
                self.non_wait_recommendations = counters.get('non_wait_recommendations', self.non_wait_recommendations)
                self.correct_recommendations = counters.get('correct_recommendations', self.correct_recommendations)
                self.current_consecutive_valid = counters.get('current_consecutive_valid', self.current_consecutive_valid)
                self.max_consecutive_valid = counters.get('max_consecutive_valid', self.max_consecutive_valid)

                logger.info("Compteurs chargés.")

            # Charger les performances par manche
            if 'round_performance' in data and isinstance(data['round_performance'], dict):
                self.round_performance = data['round_performance']
                logger.info(f"Performances par manche chargées ({len(self.round_performance)} manches).")

            logger.info("Chargement de l'état du calculateur de confiance consécutive terminé avec succès.")
            return True

        except Exception as e:
            logger.error(f"Erreur lors du chargement de l'état du calculateur de confiance consécutive: {e}", exc_info=True)
            return False