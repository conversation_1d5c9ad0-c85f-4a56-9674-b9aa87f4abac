# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 882 à 953
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def update_recent_data(self, recommendation: str, outcome: str) -> None:
        """
        Met à jour les données récentes avec une nouvelle recommandation et son résultat.

        Cette méthode est utilisée pour maintenir un historique des recommandations récentes
        et de leurs résultats, afin de calculer des métriques comme le ratio WAIT/NON-WAIT
        et le taux de succès récent.

        Args:
            recommendation: Recommandation faite ('player', 'banker', 'wait')
            outcome: Rés<PERSON>at réel ('player', 'banker')
        """
        # Initialiser les listes si elles n'existent pas encore
        if not hasattr(self, 'recent_recommendations'):
            self.recent_recommendations = []
            self.recent_outcomes = []

        # Normaliser la recommandation pour assurer la cohérence (toujours en minuscules)
        normalized_recommendation = recommendation.lower() if isinstance(recommendation, str) else recommendation
        normalized_outcome = outcome.lower() if isinstance(outcome, str) else outcome

        # Ajouter la nouvelle recommandation et son résultat
        self.recent_recommendations.append(normalized_recommendation)
        self.recent_outcomes.append(normalized_outcome)

        # Mettre à jour les compteurs pour le suivi des performances
        self.total_recommendations += 1

        # Mettre à jour les compteurs de recommandations WAIT/NON-WAIT
        if normalized_recommendation == 'wait':
            self.wait_recommendations += 1
            self.last_recommendation_was_wait = True
        else:
            self.non_wait_recommendations += 1
            self.last_recommendation_was_wait = False

            # Vérifier si la recommandation NON-WAIT était valide
            is_valid = normalized_recommendation == normalized_outcome
            if is_valid:
                self.correct_recommendations += 1
                self.current_consecutive_valid += 1
                self.current_consecutive_errors = 0
                self.last_recommendation_was_valid = True

                # Mettre à jour le maximum de recommandations NON-WAIT valides consécutives
                self.max_consecutive_valid = max(self.max_consecutive_valid, self.current_consecutive_valid)

                # Réduire progressivement l'ajustement de confiance après une recommandation valide
                if self.confidence_adjustment > 0:
                    self.confidence_adjustment = max(0, self.confidence_adjustment - self.consecutive_recovery_rate)
            else:
                # Réinitialiser le compteur de recommandations NON-WAIT valides consécutives
                self.current_consecutive_valid = 0
                self.current_consecutive_errors += 1
                self.last_recommendation_was_valid = False

                # Augmenter l'ajustement de confiance après une erreur pour être plus conservateur
                self.confidence_adjustment += self.consecutive_error_penalty
                self.confidence_adjustment = min(0.3, self.confidence_adjustment)  # Limiter l'ajustement

        # Ajouter un log pour déboguer
        logger.debug(f"ConsecutiveConfidenceCalculator - Nouvelle donnée: recommandation={normalized_recommendation}, outcome={normalized_outcome}")
        logger.debug(f"ConsecutiveConfidenceCalculator - Total recommandations: {len(self.recent_recommendations)}, dont WAIT: {sum(1 for r in self.recent_recommendations if isinstance(r, str) and r.lower() == 'wait')}")
        logger.debug(f"ConsecutiveConfidenceCalculator - Consécutives valides: {self.current_consecutive_valid}, Max: {self.max_consecutive_valid}, Erreurs consécutives: {self.current_consecutive_errors}")
        logger.debug(f"ConsecutiveConfidenceCalculator - Ajustement confiance: {self.confidence_adjustment:.4f}")

        # Limiter la taille des listes
        max_history = self.max_recent_history
        if len(self.recent_recommendations) > max_history:
            self.recent_recommendations = self.recent_recommendations[-max_history:]
        if len(self.recent_outcomes) > max_history:
            self.recent_outcomes = self.recent_outcomes[-max_history:]