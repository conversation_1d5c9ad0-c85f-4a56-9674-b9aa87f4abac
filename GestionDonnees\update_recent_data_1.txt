# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1311 à 1399
# Type: Méthode de la classe ConsecutiveConfidenceCalculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def update_recent_data(self, recommendation: str, outcome: str, config=None) -> None:
        """
        Met à jour les données récentes avec une nouvelle recommandation et son résultat.
        Suit également les recommandations NON-WAIT valides consécutives.

        Args:
            recommendation: Recommandation faite ('player', 'banker', 'wait')
            outcome: Résultat réel ('player', 'banker')
            config: Configuration du prédicteur (optionnel, pour mise à jour des paramètres)
        """
        # Initialiser les listes si elles n'existent pas
        if not hasattr(self, 'recent_recommendations'):
            self.recent_recommendations = []
        if not hasattr(self, 'recent_outcomes'):
            self.recent_outcomes = []

        # Initialiser les compteurs de séquences consécutives si nécessaire
        if not hasattr(self, 'current_consecutive_valid'):
            self.current_consecutive_valid = 0
        if not hasattr(self, 'max_consecutive_valid'):
            self.max_consecutive_valid = 0
        if not hasattr(self, 'current_consecutive_errors'):
            self.current_consecutive_errors = 0
        if not hasattr(self, 'total_recommendations'):
            self.total_recommendations = 0
            self.wait_recommendations = 0
            self.non_wait_recommendations = 0
            self.correct_recommendations = 0

        # Normaliser la recommandation pour assurer la cohérence (toujours en minuscules)
        normalized_recommendation = recommendation.lower() if isinstance(recommendation, str) else recommendation
        normalized_outcome = outcome.lower() if isinstance(outcome, str) else outcome

        # Ajouter les nouvelles données
        self.recent_recommendations.append(normalized_recommendation)
        self.recent_outcomes.append(normalized_outcome)

        # Mettre à jour les compteurs de recommandations
        self.total_recommendations += 1

        # Mettre à jour les compteurs de séquences consécutives
        if normalized_recommendation != 'wait':
            # C'est une recommandation NON-WAIT
            self.non_wait_recommendations += 1

            # Vérifier si la recommandation est correcte
            is_correct = normalized_recommendation == normalized_outcome

            if is_correct:
                # Recommandation NON-WAIT correcte
                self.correct_recommendations += 1
                self.current_consecutive_valid += 1
                self.current_consecutive_errors = 0

                # Mettre à jour le maximum de recommandations consécutives valides
                if self.current_consecutive_valid > self.max_consecutive_valid:
                    self.max_consecutive_valid = self.current_consecutive_valid
                    logger.info(f"Nouveau record de recommandations NON-WAIT valides consécutives: {self.max_consecutive_valid}")
            else:
                # Recommandation NON-WAIT incorrecte - réinitialiser le compteur
                self.current_consecutive_valid = 0
                self.current_consecutive_errors += 1
        else:
            # C'est une recommandation WAIT - ne pas réinitialiser le compteur de séquences consécutives
            self.wait_recommendations += 1

            # Vérifier si la recommandation WAIT était justifiée (si une recommandation NON-WAIT aurait été incorrecte)
            # Cela nécessite une prédiction interne, que nous n'avons pas ici, donc nous ne pouvons pas le faire directement
            # Mais nous pouvons suivre le ratio WAIT/NON-WAIT

        # Ajouter un log pour déboguer
        logger.debug(f"Nouvelle donnée ajoutée: recommandation={normalized_recommendation}, outcome={normalized_outcome}")
        logger.debug(f"Total recommandations récentes: {len(self.recent_recommendations)}, dont WAIT: {sum(1 for r in self.recent_recommendations if isinstance(r, str) and r.lower() == 'wait')}")
        logger.debug(f"Séquence NON-WAIT valide actuelle: {self.current_consecutive_valid}, Maximum: {self.max_consecutive_valid}")

        # Calculer et logger le ratio WAIT actuel
        wait_count = sum(1 for r in self.recent_recommendations if isinstance(r, str) and r.lower() == 'wait')
        total_count = len(self.recent_recommendations)
        current_wait_ratio = wait_count / total_count if total_count > 0 else 0
        logger.debug(f"Ratio WAIT actuel: {current_wait_ratio:.2f} ({wait_count}/{total_count})")

        # Récupérer la taille maximale de l'historique depuis la configuration
        max_history = getattr(config, 'max_recent_history', 50) if config else 50

        # Limiter la taille des listes (garder les dernières entrées)
        if len(self.recent_recommendations) > max_history:
            self.recent_recommendations = self.recent_recommendations[-max_history:]
        if len(self.recent_outcomes) > max_history:
            self.recent_outcomes = self.recent_outcomes[-max_history:]