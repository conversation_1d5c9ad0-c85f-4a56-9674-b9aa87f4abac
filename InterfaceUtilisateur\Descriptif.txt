DESCRIPTIF DÉTAILLÉ DES MÉTHODES - INTERFACE UTILISATEUR
================================================================================

Ce fichier contient la description détaillée des méthodes d'interface utilisateur
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : INTERFACE UTILISATEUR
Méthodes de configuration et gestion de l'interface utilisateur Tkinter,
affichage et interactions.

TOTAL : 29 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES INTERFACE UTILISATEUR
================================================================================

1. setup_ui.txt (HybridBaccaratPredictor.setup_ui - Configuration interface utilisateur principale)
   - Lignes 2587-2769 dans hbp.py (183 lignes)
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter, incluant tous les widgets, styles et layouts
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION STYLE :** Détecte la plateforme et applique le thème TTK approprié (vista/aqua/clam)
     * **GESTION COULEURS :** Récupère les couleurs TTK et définit des couleurs fixes pour Matplotlib
     * **STRUCTURE LAYOUT :** Crée la structure principale avec frames gauche/droite et organisation modulaire
     * **PANNEAU MODÈLES :** Configure les boutons de gestion des modèles et données (charger, sauvegarder, dashboard)
     * **PANNEAU ENTRAÎNEMENT :** Met en place les contrôles d'entraînement et d'optimisation Optuna
     * **PANNEAU CONFIGURATION :** Initialise le panneau de configuration des ressources
     * **CONTRÔLES PRÉDICTION :** Crée les boutons Player/Banker et annulation avec layout en grille
     * **AFFICHAGE TEMPS RÉEL :** Configure les labels de prédiction avec couleurs et polices spécifiques
     * **GRAPHIQUE MATPLOTLIB :** Intègre le graphique de tendance avec couleurs fixes et canvas Tkinter
     * **STATISTIQUES :** Met en place le panneau d'analyse avec métriques détaillées
   - RETOUR : None - Méthode de configuration ne retourne rien
   - UTILITÉ : Point d'entrée pour créer l'interface utilisateur complète avec tous les composants visuels et interactifs

2. update_display.txt (HybridBaccaratPredictor.update_display - Mise à jour affichage complet)
   - Lignes 11454-11527 dans hbp.py (74 lignes)
   - FONCTION : Met à jour tous les éléments de l'interface utilisateur avec adaptation contextuelle aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour accès sécurisé à l'historique des prédictions
     * **MISE À JOUR PRÉDICTIONS :** Affiche manche actuelle, probabilités Player/Banker et recommandation
     * **DÉTECTION PLAGE CIBLE :** Vérifie si dans la plage 31-60 pour adaptation de l'affichage
     * **AJUSTEMENT CONFIANCE :** Applique formule d'ajustement pour ramener vers 50% en affichage
     * **VÉRIFICATION MODÈLES :** Contrôle si les modèles sont entraînés pour adapter le niveau de confiance
     * **CLASSIFICATION CONFIANCE :** Détermine niveau (Faible/Moyenne/Élevée) selon seuils adaptatifs
     * **APPEL STATISTIQUES :** Déclenche update_statistics() pour métriques avancées
     * **GESTION GRAPHIQUE :** Met à jour le graphique de tendance si visible
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Orchestre la mise à jour complète de l'interface avec adaptation intelligente au contexte

3. show_models_dashboard.txt (HybridBaccaratPredictor.show_models_dashboard - Tableau de bord modèles)
   - Lignes 3332-3490 dans hbp.py (159 lignes)
   - FONCTION : Affiche un tableau de bord interactif des modèles entraînés avec métadonnées et performances
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION FENÊTRE :** Ouvre fenêtre Toplevel avec Treeview pour affichage tabulaire
     * **CONFIGURATION COLONNES :** Définit colonnes pour nom, date, précision, hyperparamètres
     * **SCAN RÉPERTOIRE :** Parcourt dossier models pour fichiers .joblib et .pkl
     * **CHARGEMENT MÉTADONNÉES :** Lit fichiers JSON associés pour informations détaillées
     * **EXTRACTION FALLBACK :** Charge modèles directement si métadonnées JSON manquantes
     * **AFFICHAGE PERFORMANCES :** Présente précision, paramètres LSTM/LGBM, ordre Markov
     * **BOUTONS ACTION :** Fournit options pour voir détails et charger modèle sélectionné
     * **GESTION ERREURS :** Traite erreurs de chargement avec logging approprié
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour explorer et gérer les modèles sauvegardés avec métadonnées complètes

4. toggle_graph_visibility.txt (HybridBaccaratPredictor.toggle_graph_visibility - Basculer visibilité graphique)
   - FONCTION : Bascule l'affichage du graphique matplotlib dans l'interface utilisateur
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle état actuel de visibilité du graphique
     * **BASCULEMENT VISIBILITÉ :** Inverse l'état d'affichage du canvas matplotlib
     * **MISE À JOUR LAYOUT :** Réorganise layout interface selon nouvelle visibilité
     * **SAUVEGARDE PRÉFÉRENCE :** Mémorise préférence utilisateur pour sessions futures
     * **REDIMENSIONNEMENT :** Ajuste taille fenêtre selon présence/absence graphique
   - RETOUR : None - Méthode d'interface ne retourne rien
   - UTILITÉ : Permet masquer/afficher le graphique pour optimiser l'espace d'affichage

5. setup_config_panel.txt (HybridBaccaratPredictor.setup_config_panel - Configuration panneau paramètres)
   - FONCTION : Configure le panneau de paramètres avec contrôles pour ajustement configuration
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame (tkinter.Frame) - Frame parent pour intégration
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION WIDGETS :** Crée contrôles pour paramètres configurables
     * **LIAISON VARIABLES :** Lie widgets aux variables de configuration
     * **VALIDATION ENTRÉES :** Configure validation pour entrées numériques
     * **CALLBACKS CHANGEMENT :** Définit callbacks pour changements temps réel
     * **ORGANISATION LAYOUT :** Organise widgets dans layout logique
   - RETOUR : None - Méthode de configuration interface
   - UTILITÉ : Interface pour modification en temps réel des paramètres système

6. _update_progress.txt (HybridBaccaratPredictor._update_progress - Mise à jour progression)
   - Lignes 4683-4694 dans hbp.py (12 lignes)
   - FONCTION : Met à jour barres de progression et indicateurs d'état dans interface avec validation et protection thread-safe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * value (int) - Valeur progression entre 0 et 100
     * message (str) - Message de statut à afficher
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FONCTION INTERNE :** Définit `def update_ui():` comme fonction interne pour mise à jour thread-safe
     * **VALIDATION VALEUR :** Calcule `value_clamped = max(0, min(100, int(value)))` pour borner valeur entre 0-100
     * **MISE À JOUR PROGRESSION :** Si `hasattr(self, 'progress_var') and self.progress_var:`, applique `self.progress_var.set(value_clamped)` pour barre progression
     * **MISE À JOUR MESSAGE :** Si `hasattr(self, 'progress_label_var') and self.progress_label_var:` :
       - Tronque message avec `message_display = message[:100] + '...' if len(message) > 100 else message` pour éviter débordement
       - Applique `self.progress_label_var.set(message_display)` pour affichage
     * **RAFRAÎCHISSEMENT UI :** Si `self.is_ui_available():`, appelle `self.root.update_idletasks()` pour mise à jour immédiate
     * **EXÉCUTION THREAD-SAFE :** Appelle `self.root.after(0, update_ui)` pour exécution dans thread UI principal
     * **PROTECTION ATTRIBUTS :** Vérifie existence attributs avec `hasattr()` avant utilisation pour éviter erreurs
     * **GESTION LONGUEUR :** Limite message à 100 caractères avec ellipse pour préserver layout interface
   - RETOUR : None - Méthode de mise à jour interface ne retourne rien
   - UTILITÉ : Mise à jour progression thread-safe avec validation robuste et protection débordement interface

7. setup_progress_bar.txt (HybridBaccaratPredictor.setup_progress_bar - Configuration barre progression)
   - FONCTION : Configure et initialise barre de progression pour opérations longues
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_widget (tkinter.Widget) - Widget parent pour intégration
     * max_value (int, optionnel) - Valeur maximale (défaut: 100)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION PROGRESSBAR :** Crée widget Progressbar TTK
     * **CONFIGURATION STYLE :** Applique style cohérent avec interface
     * **INITIALISATION VALEURS :** Configure valeurs min/max et position initiale
     * **INTÉGRATION LAYOUT :** Intègre dans layout parent avec positionnement
     * **CONFIGURATION CALLBACKS :** Définit callbacks pour mise à jour
   - RETOUR : tkinter.ttk.Progressbar - Widget barre de progression créé
   - UTILITÉ : Composant réutilisable pour affichage progression opérations

8. _update_weights_display.txt (HybridBaccaratPredictor._update_weights_display - Mise à jour affichage poids)
   - Lignes 2771-2810 dans hbp.py (40 lignes)
   - FONCTION : Met à jour affichage des poids des méthodes dans interface utilisateur avec formatage et validation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
     * **VALIDATION VARIABLES :** Teste `if not hasattr(self, 'weights_vars'):` avec retour si variables poids non initialisées
     * **EXTRACTION POIDS :** Récupère poids actuels depuis `self.weights` avec gestion sécurisée des clés manquantes
     * **FORMATAGE MARKOV :** Met à jour `self.weights_vars['markov'].set(f"Markov: {self.weights.get('markov', 0.0)*100:.1f}%")` avec pourcentage formaté
     * **FORMATAGE LGBM :** Met à jour `self.weights_vars['lgbm'].set(f"LGBM: {self.weights.get('lgbm', 0.0)*100:.1f}%")` avec pourcentage formaté
     * **FORMATAGE LSTM :** Met à jour `self.weights_vars['lstm'].set(f"LSTM: {self.weights.get('lstm', 0.0)*100:.1f}%")` avec pourcentage formaté
     * **VALIDATION COHÉRENCE :** Calcule `total_weight = sum(self.weights.values())` et affiche warning si `abs(total_weight - 1.0) > 0.01`
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la mise à jour de l'affichage des poids: {e}")` pour robustesse
     * **PERFORMANCE OPTIMISÉE :** Évite `update_idletasks()` pour performance si appelé fréquemment
   - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
   - UTILITÉ : Visualisation temps réel des poids adaptatifs des méthodes avec validation et formatage professionnel

9. lightweight_update_display.txt (HybridBaccaratPredictor.lightweight_update_display - Mise à jour légère affichage)
   - Lignes 11343-11452 dans hbp.py (110 lignes)
   - FONCTION : Mise à jour optimisée des éléments essentiels de l'interface après chaque coup avec adaptation manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * pred (Dict[str, Union[float, str, Dict]]) - Dictionnaire de prédiction avec probabilités et métriques
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉREQUIS :** Vérifie `if not hasattr(self, 'pred_vars'): return` pour sécurité si appelé trop tôt
     * **MISE À JOUR PROBABILITÉS :** Met à jour `self.pred_vars['player'].set(f"Player: {pred.get('player', 0.5)*100:.1f}%")` et `self.pred_vars['banker'].set(f"Banker: {pred.get('banker', 0.5)*100:.1f}%")`
     * **MISE À JOUR RECOMMANDATION :** Utilise `rec_text_map = {'player': "Jouer PLAYER", 'banker': "Jouer BANKER", 'wait': "Attendre"}` puis `self.pred_vars['recommendation'].set(f"Recommandation: {rec_text_map.get(rec, 'Attendre')}")`
     * **MISE À JOUR MANCHE :** Avec `self.sequence_lock:`, calcule `round_num = len(self.sequence)` puis `self.pred_vars['round'].set(f"Manche: {round_num}")`
     * **DÉTECTION PLAGE CIBLE :** Calcule `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)`, `is_target_round = target_round_min <= round_num <= target_round_max`
     * **MISE À JOUR CONFIANCE :** Met à jour `self.pred_vars['confidence'].set(f"Confiance: {pred.get('combined_confidence', 0.5)*100:.1f}%")`
     * **MÉTRIQUES CONDITIONNELLES :** Si `'confidence_metrics' in pred:`, extrait métriques et adapte affichage selon `is_target_round`
     * **AFFICHAGE ADAPTATIF INCERTITUDE :** Si pas dans plage cible, affiche `"Incertitude Détaillée: N/A (manche 1-30)"`, sinon calcule `epistemic = metrics.get('epistemic_uncertainty', 0) * 100` etc.
     * **POIDS BAYÉSIENS CONDITIONNELS :** Si `'bayesian_weights' in pred` et `is_target_round`, formate et affiche poids avec `weights_parts.append(f"{method.upper()}({weight*100:.1f}%)")`
     * **OPTIMISATION PERFORMANCE :** Évite `update_idletasks` si appelé via `root.after` pour performance optimale
   - RETOUR : None - Met à jour directement les variables d'interface Tkinter
   - UTILITÉ : Rafraîchissement rapide et optimisé interface avec adaptation contextuelle manches 31-60 sans recalculs coûteux

10. toggle_auto_update.txt (HybridBaccaratPredictor.toggle_auto_update - Basculer mise à jour automatique)
    - Lignes 2812-2835 dans hbp.py (24 lignes)
    - FONCTION : Active/désactive mise à jour automatique de l'interface avec gestion timer et feedback utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **BASCULEMENT ÉTAT :** Inverse `self.auto_update_enabled = not self.auto_update_enabled` pour changer mode
      * **GESTION TIMER :** Si `self.auto_update_enabled:` :
        - Démarre timer avec `self.auto_update_timer = self.root.after(1000, self._auto_update_callback)` pour mise à jour périodique
        - Log `logger.info("Mise à jour automatique activée")` pour traçabilité
      * **ARRÊT TIMER :** Si désactivé et `self.auto_update_timer:` :
        - Annule timer avec `self.root.after_cancel(self.auto_update_timer)` pour arrêt propre
        - Remet `self.auto_update_timer = None` pour nettoyage
        - Log `logger.info("Mise à jour automatique désactivée")` pour traçabilité
      * **MISE À JOUR BOUTON :** Met à jour texte bouton avec `button_text = "Désactiver Auto-Update" if self.auto_update_enabled else "Activer Auto-Update"`
      * **FEEDBACK VISUEL :** Change couleur bouton selon état pour indication visuelle claire
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging erreur pour robustesse
    - RETOUR : None - Méthode de contrôle interface ne retourne rien
    - UTILITÉ : Contrôle utilisateur sur fréquence rafraîchissement interface avec gestion timer robuste et feedback visuel

11. toggle_stats_visibility.txt (HybridBaccaratPredictor.toggle_stats_visibility - Basculer visibilité statistiques)
    - Lignes 2837-2865 dans hbp.py (29 lignes)
    - FONCTION : Bascule affichage du panneau de statistiques détaillées avec animation et sauvegarde préférences
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION PANNEAU :** Teste `if not hasattr(self, 'stats_frame'):` avec retour si panneau statistiques non initialisé
      * **DÉTECTION ÉTAT :** Vérifie visibilité actuelle avec `is_visible = self.stats_frame.winfo_viewable()` pour déterminer action
      * **MASQUAGE PANNEAU :** Si visible, appelle `self.stats_frame.pack_forget()` pour masquer panneau
      * **AFFICHAGE PANNEAU :** Si masqué, appelle `self.stats_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)` pour réafficher
      * **MISE À JOUR BOUTON :** Change texte bouton avec `button_text = "Masquer Stats" if not is_visible else "Afficher Stats"` pour refléter action suivante
      * **SAUVEGARDE PRÉFÉRENCE :** Met à jour `self.config.show_stats_panel = not is_visible` pour persistance préférences utilisateur
      * **AJUSTEMENT LAYOUT :** Appelle `self.root.update_idletasks()` pour recalcul layout après changement visibilité
      * **LOGGING :** Enregistre `logger.debug(f"Panneau statistiques {'affiché' if not is_visible else 'masqué'}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging erreur pour robustesse
    - RETOUR : None - Méthode de contrôle interface ne retourne rien
    - UTILITÉ : Optimise espace interface selon besoins utilisateur avec persistance préférences et feedback visuel

12. toggle_training_controls.txt (HybridBaccaratPredictor.toggle_training_controls - Basculer contrôles entraînement)
    - Lignes 2867-2905 dans hbp.py (39 lignes)
    - FONCTION : Active/désactive contrôles d'entraînement selon état système avec validation et feedback visuel
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * enabled (bool) - État souhaité pour les contrôles (True=activé, False=désactivé)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION BOUTONS :** Teste existence des boutons d'entraînement avec `if not hasattr(self, 'train_button'):` et retour si non initialisés
      * **ÉTAT BOUTON ENTRAÎNEMENT :** Configure `self.train_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour activation/désactivation
      * **ÉTAT BOUTON OPTUNA :** Configure `self.optuna_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour optimisation
      * **ÉTAT BOUTON RESET :** Configure `self.reset_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour réinitialisation
      * **ÉTAT BOUTON SAUVEGARDE :** Configure `self.save_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour sauvegarde
      * **FEEDBACK VISUEL :** Change couleurs boutons selon état :
        - Couleur normale si `enabled` pour indication disponibilité
        - Couleur grisée si `disabled` pour indication indisponibilité
      * **MISE À JOUR TOOLTIPS :** Met à jour tooltips avec messages appropriés selon état
      * **LOGGING :** Enregistre `logger.debug(f"Contrôles d'entraînement {'activés' if enabled else 'désactivés'}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging erreur pour robustesse
    - RETOUR : None - Méthode de contrôle interface ne retourne rien
    - UTILITÉ : Interface adaptative selon disponibilité fonctionnalités avec feedback visuel et validation robuste

13. ui_operations.txt (HybridBaccaratPredictor.ui_operations - Opérations interface utilisateur)
    - Lignes 2907-2950 dans hbp.py (44 lignes)
    - FONCTION : Gestionnaire central pour opérations interface utilisateur avec routage et validation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * operation (str) - Type d'opération à exécuter
      * **kwargs - Arguments additionnels pour l'opération
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION OPÉRATION :** Teste `if not operation or not isinstance(operation, str):` avec logging erreur si paramètre invalide
      * **ROUTAGE OPÉRATIONS :** Utilise dictionnaire de dispatch pour router vers méthodes appropriées :
        - `'update_display'` → `self.update_display()` pour mise à jour affichage complet
        - `'update_weights'` → `self._update_weights_display()` pour mise à jour poids
        - `'toggle_stats'` → `self.toggle_stats_visibility()` pour basculer statistiques
        - `'toggle_training'` → `self.toggle_training_controls(**kwargs)` pour contrôles entraînement
        - `'show_dashboard'` → `self.show_models_dashboard()` pour tableau de bord modèles
      * **VALIDATION MÉTHODE :** Vérifie existence méthode avec `if hasattr(self, method_name) and callable(getattr(self, method_name)):`
      * **EXÉCUTION SÉCURISÉE :** Appelle méthode avec `method = getattr(self, method_name)` puis `method(**kwargs)` avec gestion erreurs
      * **LOGGING OPÉRATIONS :** Enregistre `logger.debug(f"Opération UI exécutée: {operation}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de l'opération UI '{operation}': {e}")` pour robustesse
      * **OPÉRATIONS NON RECONNUES :** Log warning pour opérations non supportées avec liste des opérations disponibles
    - RETOUR : bool - True si opération exécutée avec succès, False sinon
    - UTILITÉ : Coordination centralisée des interactions utilisateur avec routage sécurisé et validation complète

14. update_ui.txt (HybridBaccaratPredictor.update_ui - Mise à jour interface complète)
    - FONCTION : Met à jour complètement tous éléments de l'interface utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_refresh (bool, optionnel) - Force rafraîchissement complet (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION THREAD :** Assure exécution dans thread principal UI
      * **MISE À JOUR PRÉDICTIONS :** Actualise affichage prédictions actuelles
      * **RAFRAÎCHISSEMENT STATISTIQUES :** Met à jour panneau statistiques
      * **MISE À JOUR GRAPHIQUE :** Actualise graphique matplotlib si visible
      * **SYNCHRONISATION ÉTAT :** Synchronise état widgets avec système
      * **OPTIMISATION PERFORMANCE :** Évite mises à jour inutiles si pas de changement
    - RETOUR : None - Méthode de mise à jour interface
    - UTILITÉ : Point d'entrée principal pour rafraîchissement interface complète

15. setup_auto_update.txt (HybridBaccaratPredictor.setup_auto_update - Configuration mise à jour automatique)
    - FONCTION : Configure système de mise à jour automatique de l'interface
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * interval_ms (int, optionnel) - Intervalle en millisecondes (défaut: 1000)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION TIMER :** Configure timer Tkinter pour mises à jour périodiques
      * **GESTION CALLBACKS :** Définit callback pour mise à jour automatique
      * **OPTIMISATION RESSOURCES :** Évite mises à jour si interface non visible
      * **CONTRÔLE FRÉQUENCE :** Adapte fréquence selon charge système
      * **GESTION ERREURS :** Capture erreurs pour éviter blocage interface
    - RETOUR : None - Méthode de configuration
    - UTILITÉ : Automatisation rafraîchissement interface pour expérience fluide

16. update_device_selection.txt (HybridBaccaratPredictor.update_device_selection - MAJ sélection dispositif)
    - Lignes 2952-2995 dans hbp.py (44 lignes)
    - FONCTION : Met à jour sélection dispositif (CPU/GPU) avec déplacement modèles et vidage cache pour cohérence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **SAUVEGARDE INITIAL :** Stocke `initial_device = self.device` pour comparaison changement
      * **LECTURE SÉLECTION :** Récupère `selected_device_type = self.device_choice.get()` depuis interface utilisateur
      * **VALIDATION CUDA :** Si `selected_device_type == "cuda"` :
        - Teste `if torch.cuda.is_available():` pour disponibilité GPU
        - Définit `new_device = torch.device("cuda")` si disponible
        - Sinon log warning et fallback `new_device = torch.device("cpu")` avec mise à jour `self.device_choice.set("cpu")`
      * **SÉLECTION CPU :** Si `selected_device_type == "cpu"`, définit `new_device = torch.device("cpu")` directement
      * **DÉTECTION CHANGEMENT :** Teste `if new_device != initial_device:` pour éviter opérations inutiles
      * **MISE À JOUR DEVICE :** Applique `self.device = new_device` et log `logger.info(f"Device sélectionné mis à jour: {self.device}")`
      * **DÉPLACEMENT LSTM :** Si `self.lstm:` existe :
        - Utilise `with self.model_lock:` pour thread-safety
        - Appelle `self.lstm.to(self.device)` pour déplacement modèle
        - Marque `lstm_moved = True` pour tracking
        - Log succès ou capture `Exception` avec logging erreur
      * **VIDAGE CACHE CONDITIONNEL :** Si `lstm_moved:` :
        - Utilise `with self.model_lock:` pour cohérence
        - Vide `self.lgbm_cache = deque(maxlen=100)` car changement device peut affecter compatibilité cache
        - Log `logger.info(f"Cache LGBM vidé car le device a changé et l'LSTM a été déplacé vers {self.device}")`
      * **LOGGING INCHANGÉ :** Si device identique, log debug "Device sélectionné identique au device actuel. Aucun changement."
    - RETOUR : None - Méthode de configuration ne retourne rien
    - UTILITÉ : Interface pour configuration ressources de calcul avec déplacement automatique modèles et cohérence cache

17. on_close.txt (HybridBaccaratPredictor.on_close - Gestionnaire fermeture)
    - Lignes 10897-10964 dans hbp.py (68 lignes)
    - FONCTION : Gère fermeture propre application avec vérification tâches en cours et nettoyage ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `should_close = True` et `ui_available = self.is_ui_available()` pour contrôle fermeture
      * **VÉRIFICATION TÂCHES :** Utilise `with self.training_lock:` pour vérifier `task_running = self.is_training or self.is_fast_updating`
      * **DÉTECTION TYPE TÂCHE :** Identifie `task_type = "Entraînement complet" if self.is_training else "Mise à jour rapide" if self.is_fast_updating else "Aucune"`
      * **DIALOGUE CONFIRMATION :** Si tâche active et UI disponible, affiche `messagebox.askyesno()` avec message "Une tâche ML est en cours. Voulez-vous vraiment fermer ?"
      * **ARRÊT FORCÉ :** Si utilisateur confirme, active `self.stop_training = True` et `self.stop_fast_update = True` pour arrêt gracieux
      * **ATTENTE ARRÊT :** Boucle `while (self.is_training or self.is_fast_updating) and wait_time < max_wait_time:` avec `time.sleep(0.1)` et `wait_time += 0.1`
      * **TIMEOUT GESTION :** Si `wait_time >= max_wait_time:`, log warning et continue fermeture malgré tâches actives
      * **NETTOYAGE THREADS :** Appelle `self._cleanup_threads()` pour arrêt propre threads secondaires
      * **NETTOYAGE RESSOURCES :** Exécute `self._cleanup_resources()` pour libération mémoire et ressources système
      * **FERMETURE FENÊTRE :** Si `should_close and ui_available:`, appelle `self.root.quit()` puis `self.root.destroy()` pour fermeture Tkinter
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la fermeture: {e}", exc_info=True)` et fermeture forcée
      * **LOGGING FINAL :** Enregistre "Application fermée proprement." ou "Fermeture forcée après erreur."
    - RETOUR : None - Gestionnaire d'événement ne retourne rien
    - UTILITÉ : Fermeture sécurisée avec arrêt gracieux tâches ML, nettoyage complet ressources et gestion erreurs

18. _setup_ui_variables.txt (HybridBaccaratPredictor._setup_ui_variables - Configuration variables UI)
    - Lignes 2021-2058 dans hbp.py (38 lignes)
    - FONCTION : Initialise toutes les variables Tkinter pour interface utilisateur avec configuration ressources système
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VARIABLES PRÉDICTION :** Crée `self.pred_vars` avec dictionnaire contenant :
        - `'round': tk.StringVar(value="Manche: 0")` pour numéro manche actuelle
        - `'player': tk.StringVar(value="Player: 50.0%")` et `'banker': tk.StringVar(value="Banker: 50.0%")` pour probabilités
        - `'confidence': tk.StringVar(value="Confiance: N/A")` et `'recommendation': tk.StringVar(value="Recommandation: En attente...")` pour état
      * **VARIABLES STATISTIQUES :** Initialise `self.stats_vars` avec métriques :
        - `'streak': tk.StringVar(value="Série actuelle: -")` pour série en cours
        - `'accuracy': tk.StringVar(value="Précision (Session): N/A")` pour précision session
        - `'model_weights': tk.StringVar(value="Poids: Chargement...")` pour poids modèles
        - `'uncertainty': tk.StringVar(value="Incertitude Prediction: N/A")` pour incertitude
        - `'method_acc': tk.StringVar(value="Précisions Méthodes: N/A")` pour précisions par méthode
        - `'game_stats': tk.StringVar(value="Partie: P 0 (0.0%) | B 0 (0.0%)")` pour stats partie
      * **CONFIGURATION DEVICE :** Définit `self.use_cpu = tk.BooleanVar(value=(self.device.type == 'cpu'))` et `self.use_gpu = tk.BooleanVar(value=(self.device.type == 'cuda'))` selon device détecté
      * **CONFIGURATION CPU :** Calcule `default_cores_config = getattr(self.config, 'default_cpu_cores', 2)`, `max_logical_cores = psutil.cpu_count(logical=True)` puis `initial_cores_val = max(1, min(default_cores_config, max_logical_cores))` et `self.cpu_cores = tk.IntVar(value=initial_cores_val)`
      * **CONFIGURATION MÉMOIRE :** Détecte `total_sys_mem_gb = max(1, int(psutil.virtual_memory().total / (1024**3)))`, calcule `configured_mem_gb = getattr(self.config, 'default_max_memory_gb', total_sys_mem_gb // 2)` puis `initial_mem_val = max(1, configured_mem_gb)` et `self.max_mem = tk.IntVar(value=initial_mem_val)`
      * **VALIDATION MÉMOIRE :** Vérifie `if initial_mem_val > total_sys_mem_gb:` avec warning si guideline RAM dépasse RAM système
      * **VARIABLES CONTRÔLE :** Initialise `self.auto_update_enabled = tk.BooleanVar(value=False)` pour mises à jour automatiques
      * **VARIABLES PROGRESSION :** Crée `self.progress_var = tk.DoubleVar(value=0)` et `self.progress_label_var = tk.StringVar(value="Prêt")` pour barre progression
      * **LOGGING CONFIGURATION :** Enregistre `logger.info(f"Variables ressources UI: CPU Cores Init={initial_cores_val} (Max Logic={max_logical_cores}), RAM Guideline Init={initial_mem_val} Go (Total Sys={total_sys_mem_gb} Go)")`
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Configuration complète variables UI avec détection automatique ressources système et validation cohérence

19. _setup_ui_variables_1.txt (HybridBaccaratPredictor._setup_ui_variables_1 - Configuration variables UI v1)
    - Lignes 2101-2156 dans hbp.py (56 lignes)
    - FONCTION : Version alternative configuration variables interface avec variables étendues pour métriques avancées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VARIABLES PRÉDICTION :** Identique à version standard avec `self.pred_vars` contenant round, player, banker, confidence, recommendation
      * **VARIABLES STATISTIQUES ÉTENDUES :** Crée `self.stats_vars` avec métriques supplémentaires :
        - Variables standard : streak, accuracy, model_weights, uncertainty, method_acc, game_stats
        - `'method_conf': tk.StringVar(value="Confiance Méthodes: N/A")` pour confiance par méthode
        - `'uncertainty_details': tk.StringVar(value="Incertitude Détaillée: N/A")` pour détails incertitude
        - `'adaptive_threshold': tk.StringVar(value="Seuil Adaptatif: N/A")` pour seuils adaptatifs
        - `'bayesian_weights': tk.StringVar(value="Poids Bayésiens: N/A")` pour poids bayésiens
      * **CONFIGURATION RESSOURCES IDENTIQUE :** Même logique détection CPU/GPU, calcul cores et mémoire avec validation
      * **VARIABLES CONTRÔLE IDENTIQUES :** Même configuration auto_update_enabled, progress_var, progress_label_var
      * **DIFFÉRENCE PRINCIPALE :** Extension variables statistiques pour métriques ML avancées (confiance méthodes, incertitude détaillée, seuils adaptatifs, poids bayésiens)
      * **LOGGING IDENTIQUE :** Même enregistrement configuration ressources système
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Configuration avancée variables UI avec métriques ML étendues pour analyse approfondie performance

20. _update_weights_display_1.txt (HybridBaccaratPredictor._update_weights_display_1 - MAJ affichage poids v1)
    - Lignes 2997-3025 dans hbp.py (29 lignes)
    - FONCTION : Version améliorée mise à jour affichage poids avec protection thread-safe et gestion erreurs Tkinter
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PRÉREQUIS :** Vérifie `if not hasattr(self, 'stats_vars') or not hasattr(self, 'weights'):` avec retour si UI non prête ou poids non initialisés
      * **ACQUISITION POIDS :** Utilise `with self.weights_lock:` puis `weights_to_display = self.weights.copy()` pour copie thread-safe
      * **FORMATAGE POIDS :** Itère `for method in sorted(weights_to_display.keys()):` pour ordre cohérent :
        - Extrait `weight = weights_to_display[method]` pour chaque méthode
        - Formate avec `weights_text_parts.append(f"{method.capitalize()}({weight * 100:.1f}%)")` pour lisibilité
      * **MISE À JOUR SÉCURISÉE :** Dans bloc `try:` :
        - Vérifie `if self.stats_vars.get('model_weights'):` pour existence variable Tkinter
        - Met à jour avec `self.stats_vars['model_weights'].set(f"Poids: {' | '.join(weights_text_parts)}")` pour affichage
      * **GESTION ERREURS TKINTER :** Capture `tk.TclError as e:` avec `logger.warning(f"Erreur Tkinter mise à jour affichage poids (fenêtre fermée?): {e}")` pour fermeture fenêtre
      * **GESTION ERREURS GÉNÉRALES :** Capture `Exception as e:` avec `logger.error(f"Erreur inattendue _update_weights_display: {e}", exc_info=False)` pour autres erreurs
      * **ROBUSTESSE UI :** Évite crash application si interface fermée pendant mise à jour
    - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
    - UTILITÉ : Visualisation optimisée poids avec protection robuste contre erreurs UI et thread-safety

21. _safe_update_progress.txt (HybridBaccaratPredictor._safe_update_progress - MAJ progression sécurisée)
    - Lignes 3027-3055 dans hbp.py (29 lignes)
    - FONCTION : Mise à jour progression avec protection thread-safe et validation UI pour éviter conflits concurrence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * value (int) - Valeur progression entre 0 et 100
      * message (str) - Message de statut à afficher
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION PARAMÈTRES :** Teste `if value is None or message is None:` avec retour si paramètres invalides
      * **NORMALISATION VALEUR :** Calcule `value_clamped = max(0, min(100, int(value)))` pour borner entre 0-100
      * **TRONCATURE MESSAGE :** Applique `message_truncated = str(message)[:100]` pour éviter débordement affichage
      * **FONCTION INTERNE :** Définit `def safe_update():` comme fonction interne pour exécution thread-safe
      * **MISE À JOUR PROGRESSION :** Dans fonction interne, teste `if hasattr(self, 'progress_var') and self.progress_var:` puis `self.progress_var.set(value_clamped)`
      * **MISE À JOUR MESSAGE :** Teste `if hasattr(self, 'progress_label_var') and self.progress_label_var:` puis `self.progress_label_var.set(message_truncated)`
      * **RAFRAÎCHISSEMENT :** Appelle `self.root.update_idletasks()` pour mise à jour immédiate si UI disponible
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la mise à jour sécurisée de la progression: {e}")` pour robustesse
      * **EXÉCUTION THREAD-SAFE :** Appelle `self.root.after(0, safe_update)` pour exécution dans thread UI principal
    - RETOUR : None - Méthode de mise à jour interface ne retourne rien
    - UTILITÉ : Évite conflits thread lors mise à jour progression avec validation complète et gestion d'erreurs robuste

22. _update_prediction_progress.txt (HybridBaccaratPredictor._update_prediction_progress - MAJ progression prédiction)
    - Lignes 11708-11732 dans hbp.py (25 lignes)
    - FONCTION : Met à jour et affiche la progression des prédictions avec calcul de taux et affichage périodique optimisé
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INCRÉMENTATION COMPTEUR :** Exécute `self._total_predictions += 1` pour suivre nombre total de prédictions effectuées
      * **VÉRIFICATION AFFICHAGE :** Contrôle `current_time = time.time()` et conditions d'affichage périodique pour éviter spam logs
      * **CONDITIONS AFFICHAGE :** Vérifie `(self._total_predictions - self._last_progress_update >= self._progress_update_interval or current_time - self._last_progress_time >= 5.0)` pour affichage conditionnel
      * **CALCUL TAUX :** Si conditions remplies, calcule `elapsed_time = current_time - self._last_progress_time` et `predictions_rate = (self._total_predictions - self._last_progress_update) / max(elapsed_time, 0.1)` pour taux prédictions/seconde
      * **MISE À JOUR VARIABLES :** Met à jour `self._last_progress_update = self._total_predictions` et `self._last_progress_time = current_time` pour tracking
      * **AFFICHAGE PROGRESSION :** Appelle `logger.info(f"Progression prédictions: {self._total_predictions} total ({predictions_rate:.1f}/s)")` pour feedback utilisateur
      * **OPTIMISATION PERFORMANCE :** Utilise affichage périodique plutôt que log par prédiction pour éviter surcharge I/O
      * **GESTION DIVISION ZÉRO :** Protège calcul taux avec `max(elapsed_time, 0.1)` pour éviter division par zéro
      * **INITIALISATION VARIABLES :** Assure que `_progress_update_interval` (défaut: 100) et autres variables tracking sont initialisées
    - RETOUR : None - Méthode de progression ne retourne rien
    - UTILITÉ : Monitoring performance prédictions avec affichage optimisé et calcul taux temps réel sans surcharge système

23. _reset_session_display.txt (HybridBaccaratPredictor._reset_session_display - Reset affichage session)
    - Lignes 12128-12212 dans hbp.py (85 lignes)
    - FONCTION : Réinitialise uniquement l'affichage visuel interface à état "Nouvelle Partie" sans modifier données internes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` et retourne si interface indisponible
      * **VALIDATION VARIABLES :** Contrôle existence `pred_vars`, `stats_vars`, `bg_color_mpl`, `fg_color_mpl` avec warning si manquantes
      * **RESET PRÉDICTIONS :** Met à jour variables prédiction :
        - `self.pred_vars['round'].set("Manche: 0")` pour numéro manche
        - `self.pred_vars['player'].set("Player: N/A")` et `self.pred_vars['banker'].set("Banker: N/A")` pour probabilités
        - `self.pred_vars['confidence'].set("Confiance: N/A")` et `self.pred_vars['recommendation'].set("Recommandation: En attente...")` pour état
      * **RESET STATISTIQUES :** Réinitialise variables statistiques session :
        - `self.stats_vars['streak'].set("Série actuelle: -")` pour série
        - `self.stats_vars['accuracy'].set("Précision (Session): N/A")` pour précision
        - `self.stats_vars['game_stats'].set("Partie: P 0 (0.0%) | B 0 (0.0%)")` pour stats partie
        - Variables incertitude, méthodes, seuils adaptatifs et poids bayésiens à "N/A"
      * **RESET GRAPHIQUE :** Si `hasattr(self, 'ax') and hasattr(self, 'canvas')` :
        - Utilise couleurs fixes `bg_color_mpl = self.bg_color_mpl` et `fg_color_mpl = self.fg_color_mpl`
        - Appelle `self.ax.clear()` puis `self.ax.set_facecolor(bg_color_mpl)`
        - Affiche texte centré `'Prêt (Nouvelle Partie)'` avec `self.ax.text(0.5, 0.5, ...)`
        - Efface labels/ticks avec `self.ax.set_xlabel('')`, `self.ax.set_xticks([])`, etc.
        - Configure couleurs spines avec `self.ax.spines['bottom'].set_color(fg_color_mpl)`
        - Appelle `self.canvas.draw_idle()` avec gestion erreur
      * **RESET PROGRESSION :** Met `progress_var` à 100 et `progress_label_var` à "Prêt (Nouvelle Partie)"
      * **RAFRAÎCHISSEMENT :** Appelle `self.root.update_idletasks()` pour mise à jour immédiate
    - RETOUR : None - Méthode d'affichage ne retourne rien
    - UTILITÉ : Nettoyage visuel complet interface pour nouveau démarrage sans impact sur données chargées

24. _auto_update_callback.txt (HybridBaccaratPredictor._auto_update_callback - Callback mise à jour auto)
    - Lignes 11699-11705 dans hbp.py (7 lignes)
    - FONCTION : Callback obsolète pour mise à jour automatique, remplacé par système événementiel lors enregistrement résultats
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LOGGING DEBUG :** Enregistre `logger.debug("_auto_update_callback: Cette méthode n'est plus utilisée pour les mises à jour automatiques.")` pour traçabilité
      * **MÉTHODE OBSOLÈTE :** Note explicite que méthode n'est plus utilisée pour déclencher mises à jour automatiques
      * **NOUVEAU SYSTÈME :** Les mises à jour sont maintenant déclenchées uniquement lors de l'enregistrement de nouveaux résultats via `auto_fast_update_if_needed()`
      * **CONSERVATION CODE :** Méthode conservée pour compatibilité mais sans fonctionnalité active
    - RETOUR : None - Callback obsolète ne retourne rien
    - UTILITÉ : Méthode legacy conservée pour compatibilité, remplacée par système événementiel plus efficace

25. show_model_hyperparameters.txt (HybridBaccaratPredictor.show_model_hyperparameters - Affichage hyperparamètres)
    - Lignes 3140-3291 dans hbp.py (152 lignes)
    - FONCTION : Affiche fenêtre dédiée hyperparamètres modèle avec métadonnées JSON ou extraction directe du fichier modèle
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * model_filepath (str) - Chemin du fichier modèle pour affichage hyperparamètres
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec warning et retour si interface indisponible
      * **RECHERCHE MÉTADONNÉES :** Construit `json_filepath = os.path.splitext(model_filepath)[0] + '.json'` pour fichier métadonnées associé
      * **CHARGEMENT JSON :** Si `os.path.exists(json_filepath)`, charge avec `json.load(f)` et logging succès
      * **FALLBACK MODÈLE :** Si métadonnées absentes, charge directement modèle :
        - Détecte format avec `model_filepath.endswith('.joblib')` pour Joblib vs Pickle
        - Extrait `config_details` depuis package chargé
        - Crée métadonnées minimales avec timestamp, nom fichier, hyperparamètres
      * **CRÉATION FENÊTRE :** Instancie `hyperparams_window = tk.Toplevel(self.root)` avec titre et géométrie 800x600
      * **INTERFACE TEXTE :** Crée `tk.Text` avec scrollbar et configuration styles :
        - Tags "title", "section", "normal", "value" avec polices différenciées
        - Affichage informations générales : fichier, date, version
      * **SECTIONS ORGANISÉES :** Affiche par catégories :
        - **PERFORMANCE :** Métriques globales et par méthode
        - **LSTM :** Paramètres commençant par 'lstm_'
        - **LGBM :** Paramètres commençant par 'lgbm_'
        - **MARKOV :** Paramètres Markov et ordre maximal
        - **SEUILS :** Paramètres threshold, confidence, uncertainty
        - **POIDS :** Paramètres weights et poids modèles
        - **AUTRES :** Paramètres non catégorisés
      * **BOUTONS ACTION :** Frame avec boutons :
        - "Appliquer ces hyperparamètres" → `self._apply_hyperparameters_from_metadata(metadata)`
        - "Charger ce modèle" → `self.load_trained_models(model_filepath)`
        - "Fermer" → `hyperparams_window.destroy()`
      * **GESTION ERREURS :** Capture exceptions avec messagebox d'erreur et logging détaillé
    - RETOUR : None - Affiche fenêtre modale
    - UTILITÉ : Interface complète visualisation et application hyperparamètres avec fallback robuste et organisation catégorielle

26. show_text_report.txt (HybridBaccaratPredictor.show_text_report - Affichage rapport texte)
    - Lignes 1121-1163 dans hbp.py (43 lignes)
    - FONCTION : Affiche rapport textuel dans fenêtre popup avec interface lecture seule et scrollbar pour rapports longs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * report (str) - Contenu du rapport à afficher
      * title (str, optionnel) - Titre de la fenêtre (défaut: "Rapport")
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec warning et retour si interface indisponible
      * **CRÉATION FENÊTRE :** Instancie `report_window = tk.Toplevel(self.root)` avec `report_window.title(title)` et `report_window.geometry("800x600")`
      * **FRAME CONTENEUR :** Crée `frame = ttk.Frame(report_window)` avec `frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)` pour marges
      * **SCROLLBAR VERTICALE :** Ajoute `scrollbar = ttk.Scrollbar(frame)` avec `scrollbar.pack(side=tk.RIGHT, fill=tk.Y)` pour navigation
      * **WIDGET TEXTE :** Instancie `text_widget = tk.Text(frame, wrap=tk.WORD, yscrollcommand=scrollbar.set, font=('Courier New', 10))` avec :
        - `wrap=tk.WORD` pour retour ligne intelligent
        - Police monospace 'Courier New' pour formatage préservé
        - Liaison scrollbar avec `yscrollcommand=scrollbar.set`
      * **CONFIGURATION SCROLLBAR :** Connecte avec `scrollbar.config(command=text_widget.yview)` pour synchronisation
      * **INSERTION CONTENU :** Utilise `text_widget.insert(tk.END, report)` pour affichage rapport complet
      * **MODE LECTURE SEULE :** Configure `text_widget.config(state=tk.DISABLED)` pour empêcher modification tout en permettant copie
      * **BOUTON FERMETURE :** Ajoute `ttk.Button(report_window, text="Fermer", command=report_window.destroy)` pour fermeture propre
    - RETOUR : None - Affiche fenêtre modale
    - UTILITÉ : Interface standardisée pour affichage rapports textuels avec navigation et formatage préservé

27. _show_optuna_results_window.txt (HybridBaccaratPredictor._show_optuna_results_window - Fenêtre résultats Optuna)
    - Lignes 1121-1408 dans hbp.py (288 lignes)
    - FONCTION : Affiche fenêtre dédiée aux résultats d'optimisation Optuna avec interface complète de gestion des paramètres
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * best_params (Dict) - Meilleurs paramètres trouvés par optimisation
      * study_name (str, optionnel) - Nom de l'étude Optuna
      * duration (float, optionnel) - Durée de l'optimisation en secondes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION FENÊTRE :** Instancie `result_window = tk.Toplevel(self.root)` avec configuration `result_window.title("Résultats de l'Optimisation Optuna")`, `result_window.geometry("800x600")`, `result_window.resizable(True, True)`
      * **FRAME PRINCIPAL :** Crée `main_frame = ttk.Frame(result_window)` avec `main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)`
      * **EN-TÊTE INFORMATIF :** Ajoute titre avec `ttk.Label(main_frame, text="Résultats de l'Optimisation", font=('Arial', 16, 'bold')).pack(pady=(0, 10))`
      * **INFORMATIONS ÉTUDE :** Si `study_name:`, affiche `ttk.Label(main_frame, text=f"Étude: {study_name}").pack(anchor=tk.W)`
      * **DURÉE OPTIMISATION :** Si `duration:`, formate et affiche `ttk.Label(main_frame, text=f"Durée: {duration:.2f} secondes").pack(anchor=tk.W)`
      * **SECTION PARAMÈTRES :** Crée `ttk.Label(main_frame, text="Meilleurs Paramètres:", font=('Arial', 12, 'bold')).pack(anchor=tk.W, pady=(20, 5))`
      * **ZONE TEXTE PARAMÈTRES :** Instancie `all_params_text = tk.Text(main_frame, height=15, width=70, wrap=tk.WORD)` avec scrollbar `scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=all_params_text.yview)`
      * **FORMATAGE PARAMÈTRES :** Itère sur `best_params.items()` pour formater avec `params_str += f"{param}: {value}\n"` puis insère avec `all_params_text.insert(tk.END, params_str)`
      * **BOUTONS ACTION :** Crée `button_frame = ttk.Frame(main_frame)` avec bouton "Sauvegarder les Paramètres" qui appelle `self._save_params_to_file(best_params)` et affiche confirmation
      * **BOUTON FERMETURE :** Ajoute `ttk.Button(button_frame, text="Fermer", command=result_window.destroy)`
      * **GESTION ERREURS :** Capture exceptions avec logging et fallback vers messagebox simple
    - RETOUR : None - Affiche fenêtre modale
    - UTILITÉ : Interface complète pour visualisation, sauvegarde et gestion des résultats d'optimisation Optuna

28. _show_selected_model_details.txt (HybridBaccaratPredictor._show_selected_model_details - Détails modèle sélectionné)
    - Lignes 3492-3518 dans hbp.py (27 lignes)
    - FONCTION : Affiche détails complets du modèle sélectionné depuis tableau de bord via fenêtre hyperparamètres dédiée
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * tree (ttk.Treeview) - Widget Treeview contenant la liste des modèles disponibles
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉLECTION :** Vérifie `selected_items = tree.selection()` puis `if not selected_items:` avec message "Veuillez sélectionner un modèle dans la liste."
      * **EXTRACTION DONNÉES :** Récupère `item = selected_items[0]` puis `values = tree.item(item, 'values')` pour obtenir informations ligne sélectionnée
      * **VALIDATION VALUES :** Teste `if not values: return` pour s'assurer que données sont disponibles
      * **RÉCUPÉRATION NOM :** Extrait `filename = values[0]` pour nom fichier modèle depuis première colonne Treeview
      * **CONSTRUCTION CHEMIN :** Forme `model_path = os.path.join(os.getcwd(), "models", filename)` pour chemin complet vers fichier modèle
      * **VALIDATION EXISTENCE :** Vérifie `if not os.path.exists(model_path):` avec message d'erreur "Le fichier {filename} n'existe pas."
      * **DÉLÉGATION AFFICHAGE :** Appelle `self.show_model_hyperparameters(model_path)` pour ouvrir fenêtre détaillée avec hyperparamètres, métriques et configuration
      * **GESTION ERREURS :** Utilise messagebox pour notifications utilisateur en cas d'erreur ou fichier manquant
      * **INTÉGRATION TABLEAU :** Fonction callback utilisée par bouton "Voir les détails" dans `show_models_dashboard`
    - RETOUR : None - Ouvre fenêtre dédiée pour affichage détails
    - UTILITÉ : Interface détaillée pour exploration modèles avec hyperparamètres, métriques performance et métadonnées complètes

29. update_statistics.txt (HybridBaccaratPredictor.update_statistics - Mise à jour statistiques)
    - Lignes 11529-11666 dans hbp.py (138 lignes)
    - FONCTION : Met à jour panneau statistiques avec métriques actuelles et calculs de performance détaillés
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not hasattr(self, 'stats_vars'): return` pour s'assurer que variables statistiques existent
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données de séquence
      * **CALCUL SÉRIE ACTUELLE :** Détermine série en cours avec `current_streak_length = 1`, `current_streak_type = 'none'` puis parcourt séquence pour détecter continuité
      * **CALCUL PRÉCISION SESSION :** Si `self.session_predictions:` :
        - Calcule `correct_predictions = sum(1 for pred in self.session_predictions if pred.get('correct', False))` pour prédictions correctes
        - Détermine `session_accuracy = correct_predictions / len(self.session_predictions)` pour taux réussite
        - Formate `accuracy_text = f"Précision (Session): {session_accuracy*100:.1f}% ({correct_predictions}/{len(self.session_predictions)})"` pour affichage
      * **CALCUL POIDS MODÈLES :** Extrait poids depuis `self.method_weights` avec formatage `weights_text = f"LGBM: {lgbm_weight:.1f}%, LSTM: {lstm_weight:.1f}%, Markov: {markov_weight:.1f}%"`
      * **CALCUL INCERTITUDE :** Si dernière prédiction disponible, extrait métriques incertitude avec `epistemic_uncertainty`, `aleatoric_uncertainty` et formate pour affichage
      * **PRÉCISIONS PAR MÉTHODE :** Calcule précision individuelle pour LGBM, LSTM, Markov depuis historique prédictions avec comptage succès/total
      * **STATISTIQUES PARTIE :** Compte Player/Banker dans séquence actuelle avec `player_count = self.sequence.count('player')`, calcul pourcentages et formatage
      * **MISE À JOUR VARIABLES :** Met à jour toutes variables `self.stats_vars` avec valeurs calculées :
        - `self.stats_vars['streak'].set(f"Série actuelle: {current_streak_type.title()} {current_streak_length}")`
        - `self.stats_vars['accuracy'].set(accuracy_text)` pour précision session
        - `self.stats_vars['model_weights'].set(weights_text)` pour poids modèles
        - Variables incertitude, méthodes, stats partie selon calculs
      * **GESTION ERREURS :** Capture exceptions avec `except Exception as e:` et `logger.error(f"Erreur lors de la mise à jour des statistiques: {e}")` avec valeurs par défaut
    - RETOUR : None - Méthode de mise à jour interface ne retourne rien
    - UTILITÉ : Maintient statistiques complètes à jour pour suivi performance temps réel avec calculs détaillés et gestion erreurs robuste
