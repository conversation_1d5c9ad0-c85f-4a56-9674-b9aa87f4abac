DESCRIPTIF DÉTAILLÉ DES MÉTHODES - INTERFACE UTILISATEUR
================================================================================

Ce fichier contient la description détaillée des méthodes d'interface utilisateur
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : INTERFACE UTILISATEUR
Méthodes de configuration et gestion de l'interface utilisateur Tkinter,
affichage et interactions.

TOTAL : 2 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES INTERFACE UTILISATEUR
================================================================================

1. setup_ui.txt (HybridBaccaratPredictor.setup_ui - Configuration interface utilisateur principale)
   - Lignes 2587-2769 dans hbp.py (183 lignes)
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter, incluant tous les widgets, styles et layouts
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION STYLE :** Détecte la plateforme et applique le thème TTK approprié (vista/aqua/clam)
     * **GESTION COULEURS :** Récupère les couleurs TTK et définit des couleurs fixes pour Matplotlib
     * **STRUCTURE LAYOUT :** Crée la structure principale avec frames gauche/droite et organisation modulaire
     * **PANNEAU MODÈLES :** Configure les boutons de gestion des modèles et données (charger, sauvegarder, dashboard)
     * **PANNEAU ENTRAÎNEMENT :** Met en place les contrôles d'entraînement et d'optimisation Optuna
     * **PANNEAU CONFIGURATION :** Initialise le panneau de configuration des ressources
     * **CONTRÔLES PRÉDICTION :** Crée les boutons Player/Banker et annulation avec layout en grille
     * **AFFICHAGE TEMPS RÉEL :** Configure les labels de prédiction avec couleurs et polices spécifiques
     * **GRAPHIQUE MATPLOTLIB :** Intègre le graphique de tendance avec couleurs fixes et canvas Tkinter
     * **STATISTIQUES :** Met en place le panneau d'analyse avec métriques détaillées
   - RETOUR : None - Méthode de configuration ne retourne rien
   - UTILITÉ : Point d'entrée pour créer l'interface utilisateur complète avec tous les composants visuels et interactifs

2. update_display.txt (HybridBaccaratPredictor.update_display - Mise à jour affichage complet)
   - Lignes 11454-11527 dans hbp.py (74 lignes)
   - FONCTION : Met à jour tous les éléments de l'interface utilisateur avec adaptation contextuelle aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour accès sécurisé à l'historique des prédictions
     * **MISE À JOUR PRÉDICTIONS :** Affiche manche actuelle, probabilités Player/Banker et recommandation
     * **DÉTECTION PLAGE CIBLE :** Vérifie si dans la plage 31-60 pour adaptation de l'affichage
     * **AJUSTEMENT CONFIANCE :** Applique formule d'ajustement pour ramener vers 50% en affichage
     * **VÉRIFICATION MODÈLES :** Contrôle si les modèles sont entraînés pour adapter le niveau de confiance
     * **CLASSIFICATION CONFIANCE :** Détermine niveau (Faible/Moyenne/Élevée) selon seuils adaptatifs
     * **APPEL STATISTIQUES :** Déclenche update_statistics() pour métriques avancées
     * **GESTION GRAPHIQUE :** Met à jour le graphique de tendance si visible
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Orchestre la mise à jour complète de l'interface avec adaptation intelligente au contexte
