DESCRIPTIF DÉTAILLÉ DES MÉTHODES - INTERFACE UTILISATEUR
================================================================================

Ce fichier contient la description détaillée des méthodes d'interface utilisateur
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : INTERFACE UTILISATEUR
Méthodes de configuration et gestion de l'interface utilisateur Tkinter,
affichage et interactions.

TOTAL : 29 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES INTERFACE UTILISATEUR
================================================================================

1. setup_ui.txt (HybridBaccaratPredictor.setup_ui - Configuration interface utilisateur principale)
   - Lignes 2587-2769 dans hbp.py (183 lignes)
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter, incluant tous les widgets, styles et layouts
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION STYLE :** Détecte la plateforme et applique le thème TTK approprié (vista/aqua/clam)
     * **GESTION COULEURS :** Récupère les couleurs TTK et définit des couleurs fixes pour Matplotlib
     * **STRUCTURE LAYOUT :** Crée la structure principale avec frames gauche/droite et organisation modulaire
     * **PANNEAU MODÈLES :** Configure les boutons de gestion des modèles et données (charger, sauvegarder, dashboard)
     * **PANNEAU ENTRAÎNEMENT :** Met en place les contrôles d'entraînement et d'optimisation Optuna
     * **PANNEAU CONFIGURATION :** Initialise le panneau de configuration des ressources
     * **CONTRÔLES PRÉDICTION :** Crée les boutons Player/Banker et annulation avec layout en grille
     * **AFFICHAGE TEMPS RÉEL :** Configure les labels de prédiction avec couleurs et polices spécifiques
     * **GRAPHIQUE MATPLOTLIB :** Intègre le graphique de tendance avec couleurs fixes et canvas Tkinter
     * **STATISTIQUES :** Met en place le panneau d'analyse avec métriques détaillées
   - RETOUR : None - Méthode de configuration ne retourne rien
   - UTILITÉ : Point d'entrée pour créer l'interface utilisateur complète avec tous les composants visuels et interactifs

2. update_display.txt (HybridBaccaratPredictor.update_display - Mise à jour affichage complet)
   - Lignes 11454-11527 dans hbp.py (74 lignes)
   - FONCTION : Met à jour tous les éléments de l'interface utilisateur avec adaptation contextuelle aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour accès sécurisé à l'historique des prédictions
     * **MISE À JOUR PRÉDICTIONS :** Affiche manche actuelle, probabilités Player/Banker et recommandation
     * **DÉTECTION PLAGE CIBLE :** Vérifie si dans la plage 31-60 pour adaptation de l'affichage
     * **AJUSTEMENT CONFIANCE :** Applique formule d'ajustement pour ramener vers 50% en affichage
     * **VÉRIFICATION MODÈLES :** Contrôle si les modèles sont entraînés pour adapter le niveau de confiance
     * **CLASSIFICATION CONFIANCE :** Détermine niveau (Faible/Moyenne/Élevée) selon seuils adaptatifs
     * **APPEL STATISTIQUES :** Déclenche update_statistics() pour métriques avancées
     * **GESTION GRAPHIQUE :** Met à jour le graphique de tendance si visible
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Orchestre la mise à jour complète de l'interface avec adaptation intelligente au contexte

3. show_models_dashboard.txt (HybridBaccaratPredictor.show_models_dashboard - Tableau de bord modèles)
   - Lignes 3332-3490 dans hbp.py (159 lignes)
   - FONCTION : Affiche un tableau de bord interactif des modèles entraînés avec métadonnées et performances
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION FENÊTRE :** Ouvre fenêtre Toplevel avec Treeview pour affichage tabulaire
     * **CONFIGURATION COLONNES :** Définit colonnes pour nom, date, précision, hyperparamètres
     * **SCAN RÉPERTOIRE :** Parcourt dossier models pour fichiers .joblib et .pkl
     * **CHARGEMENT MÉTADONNÉES :** Lit fichiers JSON associés pour informations détaillées
     * **EXTRACTION FALLBACK :** Charge modèles directement si métadonnées JSON manquantes
     * **AFFICHAGE PERFORMANCES :** Présente précision, paramètres LSTM/LGBM, ordre Markov
     * **BOUTONS ACTION :** Fournit options pour voir détails et charger modèle sélectionné
     * **GESTION ERREURS :** Traite erreurs de chargement avec logging approprié
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour explorer et gérer les modèles sauvegardés avec métadonnées complètes

4. toggle_graph_visibility.txt (HybridBaccaratPredictor.toggle_graph_visibility - Basculer visibilité graphique)
   - FONCTION : Bascule l'affichage du graphique matplotlib dans l'interface utilisateur
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle état actuel de visibilité du graphique
     * **BASCULEMENT VISIBILITÉ :** Inverse l'état d'affichage du canvas matplotlib
     * **MISE À JOUR LAYOUT :** Réorganise layout interface selon nouvelle visibilité
     * **SAUVEGARDE PRÉFÉRENCE :** Mémorise préférence utilisateur pour sessions futures
     * **REDIMENSIONNEMENT :** Ajuste taille fenêtre selon présence/absence graphique
   - RETOUR : None - Méthode d'interface ne retourne rien
   - UTILITÉ : Permet masquer/afficher le graphique pour optimiser l'espace d'affichage

5. setup_config_panel.txt (HybridBaccaratPredictor.setup_config_panel - Configuration panneau paramètres)
   - FONCTION : Configure le panneau de paramètres avec contrôles pour ajustement configuration
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame (tkinter.Frame) - Frame parent pour intégration
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION WIDGETS :** Crée contrôles pour paramètres configurables
     * **LIAISON VARIABLES :** Lie widgets aux variables de configuration
     * **VALIDATION ENTRÉES :** Configure validation pour entrées numériques
     * **CALLBACKS CHANGEMENT :** Définit callbacks pour changements temps réel
     * **ORGANISATION LAYOUT :** Organise widgets dans layout logique
   - RETOUR : None - Méthode de configuration interface
   - UTILITÉ : Interface pour modification en temps réel des paramètres système

6. _update_progress.txt (HybridBaccaratPredictor._update_progress - Mise à jour progression)
   - FONCTION : Met à jour barres de progression et indicateurs d'état dans interface
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * progress_value (float) - Valeur progression entre 0 et 1
     * status_text (str, optionnel) - Texte de statut à afficher
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR BARRE :** Actualise valeur de la barre de progression
     * **AFFICHAGE STATUT :** Met à jour texte de statut si fourni
     * **CALCUL POURCENTAGE :** Convertit valeur en pourcentage pour affichage
     * **ESTIMATION TEMPS :** Calcule et affiche temps restant estimé
     * **RAFRAÎCHISSEMENT UI :** Force rafraîchissement interface utilisateur
   - RETOUR : None - Méthode de mise à jour interface
   - UTILITÉ : Feedback visuel temps réel de la progression des opérations

7. setup_progress_bar.txt (HybridBaccaratPredictor.setup_progress_bar - Configuration barre progression)
   - FONCTION : Configure et initialise barre de progression pour opérations longues
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_widget (tkinter.Widget) - Widget parent pour intégration
     * max_value (int, optionnel) - Valeur maximale (défaut: 100)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION PROGRESSBAR :** Crée widget Progressbar TTK
     * **CONFIGURATION STYLE :** Applique style cohérent avec interface
     * **INITIALISATION VALEURS :** Configure valeurs min/max et position initiale
     * **INTÉGRATION LAYOUT :** Intègre dans layout parent avec positionnement
     * **CONFIGURATION CALLBACKS :** Définit callbacks pour mise à jour
   - RETOUR : tkinter.ttk.Progressbar - Widget barre de progression créé
   - UTILITÉ : Composant réutilisable pour affichage progression opérations

8. _update_weights_display.txt (HybridBaccaratPredictor._update_weights_display - Mise à jour affichage poids)
   - FONCTION : Met à jour affichage des poids des méthodes dans interface utilisateur
   - UTILITÉ : Visualisation temps réel des poids adaptatifs des méthodes

9. lightweight_update_display.txt (HybridBaccaratPredictor.lightweight_update_display - Mise à jour légère affichage)
   - Lignes 11343-11452 dans hbp.py (110 lignes)
   - FONCTION : Mise à jour optimisée des éléments essentiels de l'interface après chaque coup avec adaptation manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * pred (Dict[str, Union[float, str, Dict]]) - Dictionnaire de prédiction avec probabilités et métriques
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉREQUIS :** Vérifie `if not hasattr(self, 'pred_vars'): return` pour sécurité si appelé trop tôt
     * **MISE À JOUR PROBABILITÉS :** Met à jour `self.pred_vars['player'].set(f"Player: {pred.get('player', 0.5)*100:.1f}%")` et `self.pred_vars['banker'].set(f"Banker: {pred.get('banker', 0.5)*100:.1f}%")`
     * **MISE À JOUR RECOMMANDATION :** Utilise `rec_text_map = {'player': "Jouer PLAYER", 'banker': "Jouer BANKER", 'wait': "Attendre"}` puis `self.pred_vars['recommendation'].set(f"Recommandation: {rec_text_map.get(rec, 'Attendre')}")`
     * **MISE À JOUR MANCHE :** Avec `self.sequence_lock:`, calcule `round_num = len(self.sequence)` puis `self.pred_vars['round'].set(f"Manche: {round_num}")`
     * **DÉTECTION PLAGE CIBLE :** Calcule `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)`, `is_target_round = target_round_min <= round_num <= target_round_max`
     * **MISE À JOUR CONFIANCE :** Met à jour `self.pred_vars['confidence'].set(f"Confiance: {pred.get('combined_confidence', 0.5)*100:.1f}%")`
     * **MÉTRIQUES CONDITIONNELLES :** Si `'confidence_metrics' in pred:`, extrait métriques et adapte affichage selon `is_target_round`
     * **AFFICHAGE ADAPTATIF INCERTITUDE :** Si pas dans plage cible, affiche `"Incertitude Détaillée: N/A (manche 1-30)"`, sinon calcule `epistemic = metrics.get('epistemic_uncertainty', 0) * 100` etc.
     * **POIDS BAYÉSIENS CONDITIONNELS :** Si `'bayesian_weights' in pred` et `is_target_round`, formate et affiche poids avec `weights_parts.append(f"{method.upper()}({weight*100:.1f}%)")`
     * **OPTIMISATION PERFORMANCE :** Évite `update_idletasks` si appelé via `root.after` pour performance optimale
   - RETOUR : None - Met à jour directement les variables d'interface Tkinter
   - UTILITÉ : Rafraîchissement rapide et optimisé interface avec adaptation contextuelle manches 31-60 sans recalculs coûteux

10. toggle_auto_update.txt (HybridBaccaratPredictor.toggle_auto_update - Basculer mise à jour automatique)
    - FONCTION : Active/désactive mise à jour automatique de l'interface
    - UTILITÉ : Contrôle utilisateur sur fréquence rafraîchissement interface

11. toggle_stats_visibility.txt (HybridBaccaratPredictor.toggle_stats_visibility - Basculer visibilité statistiques)
    - FONCTION : Bascule affichage du panneau de statistiques détaillées
    - UTILITÉ : Optimise espace interface selon besoins utilisateur

12. toggle_training_controls.txt (HybridBaccaratPredictor.toggle_training_controls - Basculer contrôles entraînement)
    - FONCTION : Active/désactive contrôles d'entraînement selon état système
    - UTILITÉ : Interface adaptative selon disponibilité fonctionnalités

13. ui_operations.txt (HybridBaccaratPredictor.ui_operations - Opérations interface utilisateur)
    - FONCTION : Gestionnaire central pour opérations interface utilisateur
    - UTILITÉ : Coordination centralisée des interactions utilisateur

14. update_ui.txt (HybridBaccaratPredictor.update_ui - Mise à jour interface complète)
    - FONCTION : Met à jour complètement tous éléments de l'interface utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_refresh (bool, optionnel) - Force rafraîchissement complet (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION THREAD :** Assure exécution dans thread principal UI
      * **MISE À JOUR PRÉDICTIONS :** Actualise affichage prédictions actuelles
      * **RAFRAÎCHISSEMENT STATISTIQUES :** Met à jour panneau statistiques
      * **MISE À JOUR GRAPHIQUE :** Actualise graphique matplotlib si visible
      * **SYNCHRONISATION ÉTAT :** Synchronise état widgets avec système
      * **OPTIMISATION PERFORMANCE :** Évite mises à jour inutiles si pas de changement
    - RETOUR : None - Méthode de mise à jour interface
    - UTILITÉ : Point d'entrée principal pour rafraîchissement interface complète

15. setup_auto_update.txt (HybridBaccaratPredictor.setup_auto_update - Configuration mise à jour automatique)
    - FONCTION : Configure système de mise à jour automatique de l'interface
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * interval_ms (int, optionnel) - Intervalle en millisecondes (défaut: 1000)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION TIMER :** Configure timer Tkinter pour mises à jour périodiques
      * **GESTION CALLBACKS :** Définit callback pour mise à jour automatique
      * **OPTIMISATION RESSOURCES :** Évite mises à jour si interface non visible
      * **CONTRÔLE FRÉQUENCE :** Adapte fréquence selon charge système
      * **GESTION ERREURS :** Capture erreurs pour éviter blocage interface
    - RETOUR : None - Méthode de configuration
    - UTILITÉ : Automatisation rafraîchissement interface pour expérience fluide

16. update_device_selection.txt (HybridBaccaratPredictor.update_device_selection - MAJ sélection dispositif)
    - FONCTION : Met à jour sélection dispositif (CPU/GPU) dans interface
    - UTILITÉ : Interface pour configuration ressources de calcul

17. on_close.txt (HybridBaccaratPredictor.on_close - Gestionnaire fermeture)
    - FONCTION : Gestionnaire appelé à la fermeture de l'application
    - UTILITÉ : Nettoyage propre ressources avant fermeture

18. _setup_ui_variables.txt (HybridBaccaratPredictor._setup_ui_variables - Configuration variables UI)
    - FONCTION : Initialise variables Tkinter pour interface utilisateur
    - UTILITÉ : Préparation variables pour liaison avec widgets

19. _setup_ui_variables_1.txt (HybridBaccaratPredictor._setup_ui_variables_1 - Configuration variables UI v1)
    - FONCTION : Version alternative configuration variables interface
    - UTILITÉ : Configuration avancée variables avec validation

20. _update_weights_display_1.txt (HybridBaccaratPredictor._update_weights_display_1 - MAJ affichage poids v1)
    - FONCTION : Version améliorée mise à jour affichage poids
    - UTILITÉ : Visualisation optimisée poids avec graphiques

21. _safe_update_progress.txt (HybridBaccaratPredictor._safe_update_progress - MAJ progression sécurisée)
    - FONCTION : Mise à jour progression avec protection thread-safe
    - UTILITÉ : Évite conflits thread lors mise à jour progression

22. _update_prediction_progress.txt (HybridBaccaratPredictor._update_prediction_progress - MAJ progression prédiction)
    - Lignes 11708-11732 dans hbp.py (25 lignes)
    - FONCTION : Met à jour et affiche la progression des prédictions avec calcul de taux et affichage périodique
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INCRÉMENTATION COMPTEUR :** Exécute `self._total_predictions += 1` pour suivre nombre total de prédictions
      * **VÉRIFICATION AFFICHAGE :** Contrôle `current_time = time.time()` et conditions d'affichage périodique
      * **CONDITIONS AFFICHAGE :** Vérifie `(self._total_predictions - self._last_progress_update >= self._progress_update_interval or current_time - self._last_progress_time >= 5.0)`
      * **CALCUL TAUX :** Calcule `elapsed_time = current_time - self._last_progress_time` et `predictions_per_second = (self._total_predictions - self._last_progress_update) / elapsed_time`
      * **AFFICHAGE PROGRESSION :** Enregistre `logger.info(f"Progression des prédictions: {self._total_predictions:,} total ({predictions_per_second:.1f} prédictions/sec)")`
      * **MISE À JOUR COMPTEURS :** Met à jour `self._last_progress_update = self._total_predictions` et `self._last_progress_time = current_time`
    - RETOUR : None - Méthode d'affichage ne retourne rien
    - UTILITÉ : Feedback visuel optimisé pour processus de prédiction avec calcul de performance en temps réel

23. _reset_session_display.txt (HybridBaccaratPredictor._reset_session_display - Reset affichage session)
    - FONCTION : Remet à zéro affichage pour nouvelle session
    - UTILITÉ : Nettoyage interface pour nouveau démarrage

24. _auto_update_callback.txt (HybridBaccaratPredictor._auto_update_callback - Callback mise à jour auto)
    - FONCTION : Callback pour système de mise à jour automatique
    - UTILITÉ : Gestion automatisée rafraîchissement interface

25. show_model_hyperparameters.txt (HybridBaccaratPredictor.show_model_hyperparameters - Affichage hyperparamètres)
    - FONCTION : Affiche hyperparamètres des modèles dans fenêtre dédiée
    - UTILITÉ : Visualisation détaillée configuration modèles

26. show_text_report.txt (HybridBaccaratPredictor.show_text_report - Affichage rapport texte)
    - FONCTION : Affiche rapport textuel détaillé dans fenêtre popup
    - UTILITÉ : Présentation rapports avec formatage texte

27. _show_optuna_results_window.txt (HybridBaccaratPredictor._show_optuna_results_window - Fenêtre résultats Optuna)
    - Lignes 1121-1408 dans hbp.py (288 lignes)
    - FONCTION : Affiche fenêtre dédiée aux résultats d'optimisation Optuna avec interface complète de gestion des paramètres
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * best_params (Dict) - Meilleurs paramètres trouvés par optimisation
      * study_name (str, optionnel) - Nom de l'étude Optuna
      * duration (float, optionnel) - Durée de l'optimisation en secondes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION FENÊTRE :** Instancie `result_window = tk.Toplevel(self.root)` avec configuration `result_window.title("Résultats de l'Optimisation Optuna")`, `result_window.geometry("800x600")`, `result_window.resizable(True, True)`
      * **FRAME PRINCIPAL :** Crée `main_frame = ttk.Frame(result_window)` avec `main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)`
      * **EN-TÊTE INFORMATIF :** Ajoute titre avec `ttk.Label(main_frame, text="Résultats de l'Optimisation", font=('Arial', 16, 'bold')).pack(pady=(0, 10))`
      * **INFORMATIONS ÉTUDE :** Si `study_name:`, affiche `ttk.Label(main_frame, text=f"Étude: {study_name}").pack(anchor=tk.W)`
      * **DURÉE OPTIMISATION :** Si `duration:`, formate et affiche `ttk.Label(main_frame, text=f"Durée: {duration:.2f} secondes").pack(anchor=tk.W)`
      * **SECTION PARAMÈTRES :** Crée `ttk.Label(main_frame, text="Meilleurs Paramètres:", font=('Arial', 12, 'bold')).pack(anchor=tk.W, pady=(20, 5))`
      * **ZONE TEXTE PARAMÈTRES :** Instancie `all_params_text = tk.Text(main_frame, height=15, width=70, wrap=tk.WORD)` avec scrollbar `scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=all_params_text.yview)`
      * **FORMATAGE PARAMÈTRES :** Itère sur `best_params.items()` pour formater avec `params_str += f"{param}: {value}\n"` puis insère avec `all_params_text.insert(tk.END, params_str)`
      * **BOUTONS ACTION :** Crée `button_frame = ttk.Frame(main_frame)` avec bouton "Sauvegarder les Paramètres" qui appelle `self._save_params_to_file(best_params)` et affiche confirmation
      * **BOUTON FERMETURE :** Ajoute `ttk.Button(button_frame, text="Fermer", command=result_window.destroy)`
      * **GESTION ERREURS :** Capture exceptions avec logging et fallback vers messagebox simple
    - RETOUR : None - Affiche fenêtre modale
    - UTILITÉ : Interface complète pour visualisation, sauvegarde et gestion des résultats d'optimisation Optuna

28. _show_selected_model_details.txt (HybridBaccaratPredictor._show_selected_model_details - Détails modèle sélectionné)
    - FONCTION : Affiche détails complets du modèle sélectionné
    - UTILITÉ : Interface détaillée pour exploration modèles

29. update_statistics.txt (HybridBaccaratPredictor.update_statistics - Mise à jour statistiques)
    - FONCTION : Met à jour panneau statistiques avec métriques actuelles
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL MÉTRIQUES :** Calcule statistiques actuelles de performance
      * **MISE À JOUR LABELS :** Actualise labels d'affichage statistiques
      * **FORMATAGE DONNÉES :** Formate valeurs pour présentation claire
      * **GESTION ERREURS :** Traite erreurs calcul avec valeurs par défaut
      * **RAFRAÎCHISSEMENT UI :** Force mise à jour visuelle des statistiques
    - RETOUR : None - Méthode de mise à jour interface
    - UTILITÉ : Maintient statistiques à jour pour suivi performance temps réel
