# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11708 à 11732
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_prediction_progress(self):
        """
        Met à jour et affiche la progression des prédictions.
        Cette méthode est appelée périodiquement pour afficher un indicateur de progression
        au lieu de logs répétitifs pour chaque prédiction.
        """
        self._total_predictions += 1

        # Vérifier si nous devons afficher la progression
        current_time = time.time()
        if (self._total_predictions - self._last_progress_update >= self._progress_update_interval or
            current_time - self._last_progress_time >= 5.0):  # Au moins toutes les 5 secondes

            # Calculer le taux de prédictions par seconde
            elapsed_time = current_time - self._last_progress_time
            if elapsed_time > 0:
                predictions_per_second = (self._total_predictions - self._last_progress_update) / elapsed_time

                # Afficher la progression
                logger.info(f"Progression des prédictions: {self._total_predictions:,} total "
                           f"({predictions_per_second:.1f} prédictions/sec)")

                # Mettre à jour les compteurs
                self._last_progress_update = self._total_predictions
                self._last_progress_time = current_time