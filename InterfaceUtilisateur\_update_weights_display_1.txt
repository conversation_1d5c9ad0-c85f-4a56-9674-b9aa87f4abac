# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10742 à 10767
# Type: Méthode de la classe HybridBaccaratPredictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _update_weights_display(self):
        """ Met à jour l'affichage textuel des poids des modèles dans l'UI. """
        # Pas besoin de verrou ici si appelé via root.after depuis une section critique
        if not hasattr(self, 'stats_vars') or not hasattr(self, 'weights'): # Check existence
            return # UI pas prête ou poids non initialisés

        # Lire les poids ACTUELS (pour affichage)
        # Utiliser un accès direct est généralement OK si appelé depuis root.after,
        # mais plus sûr avec verrou si appelé potentiellement d'ailleurs.
        with self.weights_lock:
            weights_to_display = self.weights.copy()

        weights_text_parts = []
        # Trier par nom de méthode pour affichage cohérent
        for method in sorted(weights_to_display.keys()):
            weight = weights_to_display[method]
            weights_text_parts.append(f"{method.capitalize()}({weight * 100:.1f}%)") # Capitalize pour lisibilité

        try:
            # Vérifier si la variable Tkinter existe toujours
            if self.stats_vars.get('model_weights'):
                self.stats_vars['model_weights'].set(f"Poids: {' | '.join(weights_text_parts)}")
        except tk.TclError as e:
             logger.warning(f"Erreur Tkinter mise à jour affichage poids (fenêtre fermée?): {e}")
        except Exception as e:
             logger.error(f"Erreur inattendue _update_weights_display: {e}", exc_info=False)