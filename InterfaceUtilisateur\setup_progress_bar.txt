# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4651 à 4678
# Type: Méthode de la classe HybridBaccaratPredictor

    def setup_progress_bar(self):
        """Configure la barre de progression dans self.progress_frame."""
        if not hasattr(self, 'progress_frame') or not self.progress_frame:
            logger.error("Tentative de configurer la barre de progression avant son frame.")
            return

        # Vider le frame
        for widget in self.progress_frame.winfo_children():
            widget.destroy()

        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            orient=tk.HORIZONTAL,
            length=180, # Longueur ajustée pour le panneau
            mode='determinate',
            variable=self.progress_var,
            #Ajout
            style = "green.Horizontal.TProgressbar"#permet de passer à la couleur verte après que le green Tprogress a été appelé
            #Fin Ajout
        )
        self.progress_bar.pack(fill=tk.X, padx=5, pady=(5, 0))

        self.progress_label = ttk.Label(
            self.progress_frame,
            textvariable=self.progress_label_var,
            font=('Segoe UI', 8) # Police plus petite pour le label
        )
        self.progress_label.pack(fill=tk.X, padx=5, pady=(0, 5))