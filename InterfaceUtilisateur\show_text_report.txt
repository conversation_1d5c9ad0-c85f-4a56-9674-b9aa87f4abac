# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1121 à 1163
# Type: Méthode de la classe HybridBaccaratPredictor

    def show_text_report(self, report, title="Rapport"):
        """
        Affiche un rapport textuel dans une fenêtre.

        Args:
            report (str): Le rapport à afficher
            title (str, optional): Le titre de la fenêtre
        """
        if not self.is_ui_available():
            logger.warning("Interface utilisateur non disponible pour afficher le rapport")
            return

        # Créer une fenêtre pour afficher le rapport
        report_window = tk.Toplevel(self.root)
        report_window.title(title)
        report_window.geometry("800x600")

        # Créer un widget Text avec scrollbar
        frame = ttk.Frame(report_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        scrollbar = ttk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget = tk.Text(frame, wrap=tk.WORD, yscrollcommand=scrollbar.set, font=('Courier New', 10))
        text_widget.pack(fill=tk.BOTH, expand=True)

        scrollbar.config(command=text_widget.yview)

        # Insérer le rapport
        text_widget.insert(tk.END, report)

        # Rendre le widget en lecture seule mais permettre la copie
        text_widget.config(state=tk.DISABLED)

        # Ajouter des boutons d'action
        button_frame = ttk.Frame(report_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Sauvegarder",
                  command=lambda: self.save_optimization_report(report)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Fermer",
                  command=report_window.destroy).pack(side=tk.RIGHT, padx=5)