# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11828 à 11869
# Type: Méthode de la classe HybridBaccaratPredictor

    def toggle_graph_visibility(self) -> None:
        """Masque ou affiche le cadre contenant le graphique Matplotlib."""
        # Vérifier que les éléments UI nécessaires existent
        graph_frame = getattr(self, 'graph_frame', None)
        toggle_graph_btn = getattr(self, 'toggle_graph_button', None)

        if not graph_frame or not toggle_graph_btn:
            logger.warning("Tentative de basculer la visibilité du graphique avant initialisation complète des widgets.")
            return

        if self.graph_visible:
            # Cacher le graphique
            graph_frame.pack_forget()
            toggle_graph_btn.config(text="Afficher Graphique")
            self.graph_visible = False
            logger.debug("Graphique masqué.")
        else:
            # Afficher le graphique
            toggle_graph_btn.config(text="Masquer Graphique")

            # ***** MODIFICATION : Replacer avant le bouton stats *****
            # Trouver le widget 'ancre' (le bouton pour afficher/masquer les stats)
            anchor_widget = getattr(self, 'toggle_stats_button', None)
            if anchor_widget:
                # Repack le frame du graphique AVANT le bouton des statistiques
                graph_frame.pack(pady=10, fill=tk.BOTH, expand=True, before=anchor_widget)
            else:
                # Fallback: si le bouton stats n'existe pas (ne devrait pas arriver),
                # le pack simplement à la fin (ou selon ses options par défaut)
                logger.warning("toggle_graph_visibility: toggle_stats_button non trouvé, packing graphique à la fin.")
                graph_frame.pack(pady=10, fill=tk.BOTH, expand=True)
            # ***** FIN MODIFICATION *****

            self.graph_visible = True
            logger.debug("Graphique affiché.")
            # Redessiner au cas où les données auraient changé pendant qu'il était caché
            if hasattr(self, 'canvas'):
                try:
                    self.draw_trend_chart() # Utiliser draw_trend_chart qui gère déjà le cas 'pas de données'
                    self.canvas.draw_idle()
                except Exception as e:
                    logger.error(f"Erreur redraw après ré-affichage graphique: {e}")