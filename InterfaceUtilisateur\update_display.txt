# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11454 à 11527
# Type: Méthode de la classe HybridBaccaratPredictor

    def update_display(self) -> None:
        """Met à jour tous les éléments de l'interface utilisateur (statistiques, graphe)."""
        if not hasattr(self, 'pred_vars'): return # Sécurité

        # ... (code de mise à jour des labels de prédiction inchangé) ...
        last_pred = {}
        with self.sequence_lock: # Accès à l'historique
            if self.prediction_history:
                 last_pred = self.prediction_history[-1] # Prédiction pour le *prochain* coup
            round_num = len(self.sequence)

        self.pred_vars['round'].set(f"Manche: {round_num}")
        self.pred_vars['player'].set(f"Player: {last_pred.get('player', 0.5)*100:.1f}%")
        self.pred_vars['banker'].set(f"Banker: {last_pred.get('banker', 0.5)*100:.1f}%")
        rec_text_map = {'player': "Jouer PLAYER", 'banker': "<PERSON>uer BANKER", 'wait': "Attendre"}
        rec = last_pred.get('recommendation', 'wait')
        self.pred_vars['recommendation'].set(f"Recommandation: {rec_text_map.get(rec, 'Attendre')}")
        uncertainty = last_pred.get('uncertainty', 0.5)
        raw_confidence = 1.0 - uncertainty

        # Vérifier si nous sommes dans la plage de manches cibles (31-60)
        target_round_min = getattr(self.config, 'target_round_min', 31)
        target_round_max = getattr(self.config, 'target_round_max', 60)
        is_target_round = target_round_min <= round_num <= target_round_max

        # Ajuster la confiance pour l'affichage uniquement (sans modifier le calcul sous-jacent)
        # Formule d'ajustement pour la session en cours : confiance_affichée = 0.5 + (confiance_réelle - 0.5) / 2
        # Cette formule ramène les valeurs vers 50% sans affecter le calcul sous-jacent
        display_confidence = 0.5 + (raw_confidence - 0.5) / 2.0

        # Déterminer le niveau de confiance affiché
        # Vérifier si les modèles sont entraînés de manière sécurisée
        models_trained = False
        try:
            if hasattr(self, '_models_are_trained'):
                models_trained = self._models_are_trained()
            else:
                # Vérification directe si la méthode n'existe pas encore
                with self.model_lock:
                    lgbm_trained = (hasattr(self, 'calibrated_lgbm') and self.calibrated_lgbm is not None and
                                  hasattr(self.calibrated_lgbm, 'classes_'))
                    lstm_trained = (hasattr(self, 'lstm') and self.lstm is not None)
                    models_trained = lgbm_trained or lstm_trained
        except Exception:
            models_trained = False  # En cas d'erreur, considérer comme non entraîné

        if not is_target_round:
            # Si nous ne sommes pas dans la plage cible (31-60), indiquer que la confiance n'est pas applicable
            conf_level = "N/A (manche 1-30)"
            confidence_text = f"Confiance: {conf_level}"
        elif not models_trained:
            # Si les modèles ne sont pas entraînés, la confiance est toujours faible
            conf_level = "Faible (non entraîné)"
            confidence_text = f"Confiance: {conf_level} ({display_confidence*100:.1f}%)"
        else:
            # Échelle ajustée pour les modèles entraînés dans la plage cible
            if display_confidence > 0.75: conf_level = "Élevée"
            elif display_confidence > 0.6: conf_level = "Moyenne+"
            elif display_confidence > 0.5: conf_level = "Moyenne"
            elif display_confidence > 0.4: conf_level = "Faible+"
            else: conf_level = "Faible"
            confidence_text = f"Confiance: {conf_level} ({display_confidence*100:.1f}%)"

        # Afficher la confiance ajustée
        self.pred_vars['confidence'].set(confidence_text)
        # --- FIN Mise à jour des éléments de prédiction ---

        # --- Mise à jour des statistiques avancées ---
        self.update_statistics()

        # --- Mise à jour du graphique ---
        # ***** MODIFICATION : Ligne DÉCOMMENTÉE *****
        if self.graph_visible: # Vérifier si le graphique doit être dessiné
            self.draw_trend_chart()