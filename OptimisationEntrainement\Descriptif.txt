DESCRIPTIF DÉTAILLÉ DES MÉTHODES - OPTIMISATION ENTRAÎNEMENT
================================================================================

Ce fichier contient la description détaillée des méthodes d'optimisation et
d'entraînement du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : OPTIMISATION ENTRAÎNEMENT
Méthodes d'optimisation des hyperparamètres avec Optuna, entraînement
des modèles et gestion des processus d'apprentissage.

TOTAL : 1 MÉTHODE ANALYSÉE

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES OPTIMISATION ENTRAÎNEMENT
================================================================================

1. run_hyperparameter_optimization.txt (HybridBaccaratPredictor.run_hyperparameter_optimization - Optimisation Optuna multi-niveaux)
   - Lignes 12836-13032 dans hbp.py (197 lignes)
   - FONCTION : Lance l'optimisation des hyperparamètres avec stratégie multi-niveaux adaptée CPU via OptunaThreadManager
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * n_trials (int, optionnel) - Nombre d'essais à effectuer (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATIONS PRÉALABLES :** Contrôle état training, données historiques et suffisance des données (min 10 jeux)
     * **PRÉPARATION DONNÉES :** Appelle _prepare_training_data avec force_use_historical=True pour package complet
     * **VALIDATION ÉCHANTILLONS :** Vérifie qu'au moins 20 échantillons sont générés pour l'entraînement
     * **CONFIGURATION AVANCÉE :** Active multi-level, régularisation adaptative, SWA, méta-apprentissage, CV temporelle
     * **INITIALISATION OPTUNA :** Crée OptunaOptimizer avec réinitialisation des compteurs et transmission des options
     * **GESTIONNAIRE THREAD :** Utilise OptunaThreadManager pour exécution asynchrone avec callbacks
     * **CALLBACKS SYSTÈME :** Configure success_callback, error_callback et progress_callback pour UI
     * **LANCEMENT ASYNCHRONE :** Démarre l'optimisation dans thread séparé avec gestion complète des erreurs
   - RETOUR : None - Méthode asynchrone ne retourne rien
   - UTILITÉ : Point d'entrée principal pour l'optimisation sophistiquée des hyperparamètres avec interface non-bloquante
