DESCRIPTIF DÉTAILLÉ DES MÉTHODES - OPTIMISATION ENTRAÎNEMENT
================================================================================

Ce fichier contient la description détaillée des méthodes d'optimisation et
d'entraînement du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : OPTIMISATION ENTRAÎNEMENT
Méthodes d'optimisation des hyperparamètres avec Optuna, entraînement
des modèles et gestion des processus d'apprentissage.

TOTAL : 32 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES OPTIMISATION ENTRAÎNEMENT
================================================================================

1. run_hyperparameter_optimization.txt (HybridBaccaratPredictor.run_hyperparameter_optimization - Optimisation Optuna multi-niveaux)
   - Lignes 12836-13032 dans hbp.py (197 lignes)
   - FONCTION : Lance l'optimisation des hyperparamètres avec stratégie multi-niveaux adaptée CPU via OptunaThreadManager
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * n_trials (int, optionnel) - Nombre d'essais à effectuer (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION CONCURRENCE :** Vérifie `with self.training_lock:` puis `if self.is_training or self.is_fast_updating:` avec warning et retour si tâche active
     * **VALIDATION DONNÉES :** Contrôle `with self.sequence_lock:` pour vérifier `if self.loaded_historical and self.historical_data:` avec minimum 10 jeux
     * **CONFIRMATION UTILISATEUR :** Affiche `messagebox.askyesno` avec avertissement "L'optimisation multi-niveaux (LGBM+LSTM) peut prendre BEAUCOUP de temps"
     * **PRÉPARATION DONNÉES :** Appelle `training_data_package = self._prepare_training_data(force_use_historical=True)` pour package complet avec validation 8 éléments
     * **CONFIGURATION OPTUNA :** Définit `advanced_options` avec :
       - `enable_multi_level=True` pour optimisation multi-niveaux
       - `enable_adaptive_regularization=True` pour régularisation adaptative
       - `enable_swa=True` pour Stochastic Weight Averaging
       - `enable_meta_learning=True` pour méta-apprentissage
       - `enable_temporal_cv=True` pour validation croisée temporelle
       - `cpu_count=8`, `ram_gb=28`, `batch_size=1024` pour ressources système
     * **INITIALISATION OPTIMIZER :** Importe `OptunaOptimizer` et `OptunaThreadManager`, réinitialise compteurs avec `OptunaOptimizer.optimized_viable_trials_count = 0` et `OptunaOptimizer.total_attempts_made = 0`
     * **ASSIGNATION DONNÉES :** Configure `optimizer_instance` avec :
       - `X_lgbm_full = X_lgbm_all` (features LGBM complètes)
       - `y_full = y_all` (labels complets)
       - `X_lstm_full = X_lstm_all` (features LSTM complètes)
       - `train_indices = train_idx` et `val_indices = val_idx` (indices split temporel)
       - `predictor_ref = self` pour référence au prédicateur
     * **CALLBACKS CONFIGURATION :** Définit callbacks asynchrones :
       - `success_callback(best_params, duration)` qui appelle `self._finalize_optuna_optimization(True, best_params, duration, None)`
       - `error_callback(error_msg, best_params, duration)` qui appelle `self._finalize_optuna_optimization(False, best_params, duration, error_msg)`
       - `progress_callback(progress, message)` qui met à jour interface avec `self._update_progress(progress, message)`
     * **LANCEMENT THREAD :** Appelle `self.optuna_thread_manager.run_optimization()` avec optimizer_instance, n_trials et callbacks
     * **GESTION ÉTAT :** Met `self.is_training = True`, `self.stop_training = False`, `self.is_optuna_running = True` et désactive contrôles UI
     * **RÉINITIALISATION FLAGS :** Supprime `_logged_sequence_length` et `_logged_compat_sequence_length` pour permettre nouveaux logs
     * **GESTION ERREURS :** Capture `ImportError` pour OptunaThreadManager avec messagebox d'erreur et logging détaillé
   - RETOUR : None - Méthode d'interface utilisateur asynchrone avec callbacks
   - UTILITÉ : Optimisation complète hyperparamètres avec stratégie multi-niveaux, gestion ressources CPU/RAM et exécution thread séparé

2. init_ml_models.txt (HybridBaccaratPredictor.init_ml_models - Initialisation complète modèles ML)
   - Lignes 1589-1795 dans hbp.py (207 lignes)
   - FONCTION : Initialise ou réinitialise tous les modèles ML (LGBM, LSTM, Markov) avec protection contre récursion et gestion complète des erreurs
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * force_reinit (bool, optionnel) - Force réinitialisation même si déjà initialisé (défaut: False)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION RÉCURSION :** Vérifie `if getattr(self, '_initializing_models', False): return` pour éviter appels récursifs
     * **MARQUAGE INITIALISATION :** Définit `self._initializing_models = True` pendant processus
     * **RÉINITIALISATION CONDITIONNELLE :** Si `force_reinit`, supprime attributs existants avec `delattr(self, attr)`
     * **INITIALISATION LGBM :** Crée `LGBMClassifier(n_estimators=self.config.lgbm_n_estimators, learning_rate=self.config.lgbm_learning_rate, max_depth=self.config.lgbm_max_depth, random_state=42, n_jobs=-1, verbose=-1)`
     * **INITIALISATION LSTM :** Configure `Sequential([LSTM(self.config.lstm_units, return_sequences=False, input_shape=(self.config.lstm_sequence_length, self.config.lstm_input_size)), Dropout(self.config.lstm_dropout), Dense(self.config.lstm_dense_units, activation='relu'), Dense(2, activation='softmax')])`
     * **COMPILATION LSTM :** Compile avec `model.compile(optimizer=Adam(learning_rate=self.config.lstm_learning_rate), loss='categorical_crossentropy', metrics=['accuracy'])`
     * **INITIALISATION MARKOV :** Crée `MarkovChainPredictor(order=self.config.markov_order, smoothing=self.config.markov_smoothing)`
     * **INITIALISATION SCALERS :** Configure `StandardScaler()` pour LGBM et LSTM avec `self.scaler` et `self.lstm_scaler`
     * **INITIALISATION CACHE :** Crée `deque(maxlen=self.config.cache_size)` pour optimisation prédictions
     * **MODÈLE INCERTITUDE :** Initialise `BaggingClassifier(base_estimator=LGBMClassifier(), n_estimators=self.config.uncertainty_n_estimators, random_state=42, n_jobs=-1)`
     * **VALIDATION CONFIGURATION :** Vérifie cohérence entre `lstm_input_size`, `lgbm_feature_count` et configuration
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et rollback si nécessaire
     * **FINALISATION :** Remet `self._initializing_models = False` et marque `self._models_initialized = True`
   - RETOUR : bool - True si initialisation réussie, False en cas d'erreur
   - UTILITÉ : Point d'entrée central pour initialisation robuste de tous les modèles ML avec protection récursion et validation complète

3. save_optimized_models.txt (HybridBaccaratPredictor.save_optimized_models - Sauvegarde modèles optimisés)
   - Lignes 4905-5002 dans hbp.py (98 lignes)
   - FONCTION : Sauvegarde les modèles avec hyperparamètres optimisés après optimisation Optuna avec gestion complète des configurations
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * params_file_path (str) - Chemin vers fichier JSON des paramètres optimisés
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ÉTAT :** Vérifie qu'aucun entraînement n'est en cours via is_training flag
     * **CHARGEMENT PARAMS :** Lit paramètres optimisés depuis fichier JSON avec gestion d'erreurs
     * **SAUVEGARDE CONFIG ORIGINALE :** Crée backup de la configuration actuelle pour restauration
     * **APPLICATION PARAMS :** Applique temporairement paramètres optimisés à la configuration
     * **MISE À JOUR DÉPENDANCES :** Appelle _update_dependent_configs pour cohérence système
     * **SAUVEGARDE MODÈLES :** Utilise _perform_save avec configuration optimisée temporaire
     * **GÉNÉRATION MÉTADONNÉES :** Crée métadonnées complètes avec hyperparamètres et performance
     * **SAUVEGARDE MÉTADONNÉES :** Exporte métadonnées en JSON pour traçabilité
     * **RESTAURATION CONFIG :** Remet configuration originale après sauvegarde
     * **VALIDATION FINALE :** Vérifie intégrité des fichiers sauvegardés
   - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
   - UTILITÉ : Permet sauvegarde des résultats d'optimisation avec configuration cohérente et traçabilité complète

4. on_training_complete.txt (HybridBaccaratPredictor.on_training_complete - Callback fin entraînement)
   - FONCTION : Callback appelé à la fin de l'entraînement pour finalisation et mise à jour UI avec gestion complète des états
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * training_results (Dict) - Résultats détaillés de l'entraînement
     * training_duration (float) - Durée totale de l'entraînement en secondes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION RÉSULTATS :** Vérifie intégrité et cohérence des résultats d'entraînement
     * **MISE À JOUR ÉTAT :** Change is_training à False et met à jour statut système
     * **CALCUL MÉTRIQUES :** Calcule métriques finales de performance et convergence
     * **SAUVEGARDE AUTOMATIQUE :** Déclenche sauvegarde automatique si configurée
     * **MISE À JOUR UI :** Actualise interface utilisateur avec résultats finaux
     * **NOTIFICATION UTILISATEUR :** Affiche notification de fin d'entraînement
     * **LOGGING DÉTAILLÉ :** Enregistre informations complètes dans logs système
     * **NETTOYAGE MÉMOIRE :** Libère ressources temporaires d'entraînement
     * **ACTIVATION PRÉDICTIONS :** Réactive système de prédiction avec nouveaux modèles
     * **VALIDATION FINALE :** Vérifie que tous les modèles sont opérationnels
   - RETOUR : None - Callback ne retourne rien
   - UTILITÉ : Gère la finalisation propre de l'entraînement avec mise à jour complète du système et interface

5. finalize_training.txt (HybridBaccaratPredictor.finalize_training - Finalisation entraînement)
   - FONCTION : Finalise le processus d'entraînement avec sauvegarde et mise à jour des métriques de performance
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * training_metrics (Dict) - Métriques détaillées de l'entraînement
     * save_models (bool, optionnel) - Sauvegarde automatique des modèles (défaut: True)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MÉTRIQUES :** Vérifie cohérence et complétude des métriques d'entraînement
     * **CALCUL PERFORMANCE FINALE :** Évalue performance finale sur ensemble de validation
     * **MISE À JOUR HISTORIQUE :** Intègre nouvelles métriques dans historique de performance
     * **SAUVEGARDE CONDITIONNELLE :** Sauvegarde modèles si performance améliorée
     * **GÉNÉRATION RAPPORT :** Crée rapport détaillé de l'entraînement
     * **MISE À JOUR POIDS :** Ajuste poids des méthodes selon performance observée
     * **NETTOYAGE RESSOURCES :** Libère mémoire GPU/CPU utilisée pendant entraînement
     * **VALIDATION MODÈLES :** Teste fonctionnement des modèles entraînés
     * **MISE À JOUR CONFIG :** Met à jour configuration avec nouveaux paramètres
     * **NOTIFICATION SYSTÈME :** Informe autres composants de la fin d'entraînement
   - RETOUR : Dict - Rapport détaillé de finalisation avec métriques et statuts
   - UTILITÉ : Complète le cycle d'entraînement avec persistance des résultats et validation système

6. _train_models_async.txt (HybridBaccaratPredictor._train_models_async - Entraînement asynchrone)
   - Lignes 2212-2580 dans hbp.py (369 lignes)
   - FONCTION : Lance l'entraînement des modèles en mode asynchrone avec gestion complète des threads, callbacks et monitoring ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_lgbm (numpy.ndarray) - Features LGBM pour entraînement
     * y_lgbm (numpy.ndarray) - Labels pour modèle LGBM
     * X_lstm (numpy.ndarray) - Features LSTM pour entraînement
     * y_lstm (numpy.ndarray) - Labels pour modèle LSTM
     * config_override (Dict, optionnel) - Configuration override pour entraînement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` et `ui_available = self.is_ui_available()` pour adaptation
     * **VALIDATION DONNÉES :** Vérifie `if X_lgbm is None or y_lgbm is None or X_lstm is None or y_lstm is None:` avec logging erreur et retour si données manquantes
     * **MARQUAGE ÉTAT :** Définit `self.is_training = True` et `self.stop_training = False` pour contrôle processus
     * **PROGRESSION INITIALE :** Si UI disponible, appelle `self._update_progress(5, "Démarrage entraînement asynchrone...")` pour feedback
     * **ENTRAÎNEMENT LGBM :** Appelle `lgbm_success = self._train_lgbm_model(X_lgbm, y_lgbm, config_override)` avec gestion succès/échec
     * **PROGRESSION LGBM :** Met à jour `self._update_progress(40, "LGBM terminé, démarrage LSTM...")` si UI disponible
     * **ENTRAÎNEMENT LSTM :** Si LGBM réussi, appelle `lstm_success = self._train_lstm_model(X_lstm, y_lstm, config_override)` avec même logique
     * **PROGRESSION LSTM :** Met à jour `self._update_progress(80, "LSTM terminé, finalisation...")` après entraînement LSTM
     * **VALIDATION MODÈLES :** Vérifie `models_trained = self._models_are_trained()` pour confirmer état opérationnel
     * **CALCUL MÉTRIQUES :** Si modèles entraînés, appelle `self._calculate_training_metrics(X_lgbm, y_lgbm, X_lstm, y_lstm)` pour évaluation performance
     * **SAUVEGARDE CONDITIONNELLE :** Si `self.config.auto_save_after_training:`, appelle `self.save_trained_models()` pour persistance
     * **FINALISATION :** Appelle `self.finalize_training(success=True, start_time=start_time, summary=[])` pour nettoyage et callbacks
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger_instance.error(f"Erreur pendant _train_models_async: {e}", exc_info=True)` et finalisation échec
     * **NETTOYAGE FINAL :** Assure `self.is_training = False` dans bloc finally pour libération état
   - RETOUR : None - Méthode asynchrone ne retourne rien
   - UTILITÉ : Entraînement non-bloquant avec interface utilisateur responsive, monitoring complet et gestion d'erreurs robuste

7. _models_are_trained.txt (HybridBaccaratPredictor._models_are_trained - Vérification modèles entraînés)
   - Lignes 1742-1795 dans hbp.py (54 lignes)
   - FONCTION : Vérifie si au moins un des modèles principaux (LGBM, LSTM) a été entraîné avec validation thread-safe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise `with self.model_lock:` pour accès thread-safe aux modèles
     * **VÉRIFICATION LGBM :** Contrôle que le modèle LGBM est entraîné avec :
       - `lgbm_trained = (self.calibrated_lgbm is not None and hasattr(self.calibrated_lgbm, 'classes_'))` pour validation existence et attribut classes
       - Vérifie que le modèle calibré existe et possède l'attribut `classes_` indiquant entraînement terminé
     * **VÉRIFICATION LSTM :** Contrôle que le modèle LSTM est entraîné avec :
       - `lstm_trained = (self.lstm is not None and hasattr(self.lstm, 'trained') and getattr(self.lstm, 'trained', False))` pour validation complète
       - Vérifie existence modèle, présence attribut `trained` et valeur True
     * **LOGIQUE OU :** Retourne `lgbm_trained or lstm_trained` car système fonctionne avec au moins un modèle principal entraîné
     * **VALIDATION MINIMALE :** Considère système opérationnel si LGBM OU LSTM est entraîné (pas nécessairement les deux)
     * **THREAD-SAFETY :** Toutes vérifications effectuées sous protection `model_lock` pour éviter conditions de course
   - RETOUR : bool - True si au moins un modèle principal (LGBM ou LSTM) est entraîné, False sinon
   - UTILITÉ : Validation rapide et thread-safe de l'état d'entraînement pour déterminer si système peut effectuer prédictions

8. auto_fast_update_if_needed.txt (HybridBaccaratPredictor.auto_fast_update_if_needed - Mise à jour rapide automatique)
   - Lignes 12561-12611 dans hbp.py (51 lignes)
   - FONCTION : Déclenche mise à jour rapide automatique si conditions remplies avec focus sur manches cibles 31-60
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_round_num (int) - Numéro de manche actuelle pour vérification plage cible
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION AUTO-UPDATE :** Contrôle `if not hasattr(self, 'auto_update_enabled') or not self.auto_update_enabled.get():` avec retour si désactivé
     * **DÉTECTION PLAGE CIBLE :** Calcule `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)` et `is_target_round = target_round_min <= current_round_num <= target_round_max`
     * **INIT CALCULATEUR CONSÉCUTIF :** Si `is_target_round and not hasattr(self, 'consecutive_confidence_calculator'):`, appelle `self.init_consecutive_confidence_calculator()`
     * **VÉRIFICATION CONDITIONS :** Contrôle `if current_round_num <= self.last_incremental_update_index:` pour éviter mises à jour redondantes
     * **ACQUISITION VERROUS :** Utilise `with self.training_lock, self.sequence_lock:` pour protection thread-safe
     * **DOUBLE VÉRIFICATION :** Contrôle `if not self.auto_update_enabled.get():` et `if self.is_training or self.is_fast_updating:` pour éviter conflits
     * **MARQUAGE ÉTAT :** Définit `self.is_fast_updating = True` et `is_auto_trigger = True` pour suivi état
     * **LANCEMENT THREAD :** Crée `threading.Thread(target=self._run_fast_update_async, args=(False, is_auto_trigger), daemon=True, name="AutoFastUpdateThread").start()`
     * **GESTION ERREURS :** Capture exceptions avec `logger_instance.error` et reset `self.is_fast_updating = False`
   - RETOUR : None - Méthode de déclenchement ne retourne rien
   - UTILITÉ : Optimise performance système via mises à jour ciblées et automatiques

9. load_optimized_models.txt (HybridBaccaratPredictor.load_optimized_models - Chargement modèles optimisés)
   - Lignes 5004-5120 dans hbp.py (117 lignes)
   - FONCTION : Charge modèles pré-optimisés avec paramètres Optuna et validation complète d'intégrité avec gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * params_file_path (str) - Chemin vers fichier JSON des paramètres optimisés
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ÉTAT :** Vérifie `if self.is_training:` avec `messagebox.showwarning("Entraînement en cours", "Impossible de charger pendant un entraînement.")` et retour
     * **VALIDATION FICHIER :** Teste `if not os.path.exists(params_file_path):` avec `messagebox.showerror("Fichier introuvable", f"Le fichier {params_file_path} n'existe pas.")` et retour False
     * **CHARGEMENT PARAMÈTRES :** Utilise `with open(params_file_path, 'r') as f:` puis `optimized_params = json.load(f)` avec gestion `json.JSONDecodeError`
     * **SAUVEGARDE CONFIG :** Crée `original_config = copy.deepcopy(self.config)` pour backup avant modifications
     * **APPLICATION PARAMÈTRES :** Appelle `self._update_config_from_optimization(optimized_params)` pour intégration paramètres optimisés
     * **RÉENTRAÎNEMENT :** Lance `training_success = self.train_models(use_historical=True, async_mode=False)` en mode synchrone pour application immédiate
     * **VALIDATION SUCCÈS :** Vérifie `if not training_success:` avec restauration `self.config = original_config` et messagebox d'erreur
     * **CONFIRMATION MODÈLES :** Teste `if not self._models_are_trained():` avec même logique de restauration si échec
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Modèles optimisés chargés avec succès depuis {params_file_path}")` pour traçabilité
     * **FEEDBACK UTILISATEUR :** Affiche `messagebox.showinfo("Succès", f"Modèles optimisés chargés avec succès!\n\nFichier: {os.path.basename(params_file_path)}")` pour confirmation
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors du chargement des modèles optimisés: {e}", exc_info=True)` et restauration config
     * **RESTAURATION ÉCHEC :** En cas d'erreur, restaure `self.config = original_config` et affiche messagebox d'erreur détaillé
   - RETOUR : bool - True si chargement réussi, False en cas d'erreur
   - UTILITÉ : Utilisation directe de modèles optimisés avec réentraînement automatique et validation complète d'intégrité

10. run_full_retraining.txt (HybridBaccaratPredictor.run_full_retraining - Re-entraînement complet)
    - Lignes 11871-12024 dans hbp.py (154 lignes)
    - FONCTION : Lance re-entraînement complet de tous les modèles avec mise à jour Markov global et ThreadedTrainer
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION CONCURRENCE :** Vérifie `with self.training_lock:` puis `if self.is_training or self.is_fast_updating:` avec warning et retour si tâche active
      * **VALIDATION DONNÉES :** Utilise `with self.sequence_lock:` pour vérifier `if self.loaded_historical and self.historical_data:` avec copie `historical_data_copy = self.historical_data[:]`
      * **CONFIRMATION UTILISATEUR :** Affiche `messagebox.askyesno` avec avertissement "Lancer l'entraînement complet ? Cela peut prendre du temps et écrasera les modèles existants"
      * **MISE À JOUR MARKOV GLOBAL :** Si `self.markov:`, exécute `with self.markov_lock, self.sequence_lock:` puis `self.markov.reset(reset_type='hard')` et `self.markov.update_global(historical_data_copy)`
      * **PRÉPARATION DONNÉES :** Appelle `training_data_package = self._prepare_training_data(force_use_historical=True, max_games=None, sampling_fraction=None)` pour utiliser TOUTES les données historiques
      * **VALIDATION PACKAGE :** Vérifie `if training_data_package is None or len(training_data_package) != 8` avec contrôles sur `training_data_package[0-3]` pour X_lgbm, y_labels, X_lstm, sample_weights
      * **CONFIGURATION THREADED TRAINER :** Instancie `self.threaded_trainer = ThreadedTrainer(self)` avec callbacks :
        - `on_training_complete(result)` qui appelle `self.finalize_training(result['success'], self.threaded_trainer.start_time, [])`
        - `on_training_error(error)` qui appelle `self.finalize_training(False, start_time, [])` avec gestion d'erreurs
      * **LANCEMENT ENTRAÎNEMENT :** Appelle `self.threaded_trainer.start()` avec paramètres :
        - `X_lgbm=training_data_package[0]` (features LGBM)
        - `y_lgbm=training_data_package[1]` (labels)
        - `X_lstm=training_data_package[2]` (features LSTM)
        - `y_lstm=training_data_package[1]` (mêmes labels)
        - `config_override={}` (configuration par défaut)
      * **GESTION INTERFACE :** Utilise `self.toggle_training_controls(enabled=False)` pour désactiver contrôles pendant entraînement
      * **MONITORING PROGRESSION :** Met à jour interface avec `self._update_progress()` aux étapes clés (0%, 5%, 10%, 30%)
    - RETOUR : None - Méthode d'interface utilisateur asynchrone
    - UTILITÉ : Re-entraînement complet avec mise à jour Markov global, utilisation de toutes les données historiques et ThreadedTrainer pour gestion asynchrone
      * **RÉINITIALISATION MODÈLES :** Recrée modèles avec architecture optimisée
      * **OPTIMISATION CONDITIONNELLE :** Lance Optuna si paramètre activé
      * **ENTRAÎNEMENT SÉQUENTIEL :** Entraîne LGBM puis LSTM avec monitoring
      * **VALIDATION CROISÉE :** Effectue validation temporelle pour robustesse
      * **ÉVALUATION PERFORMANCE :** Compare avec modèles précédents
      * **MISE À JOUR POIDS :** Recalcule poids des méthodes selon nouvelle performance
      * **SAUVEGARDE FINALE :** Sauvegarde nouveaux modèles si amélioration
      * **RAPPORT COMPLET :** Génère rapport détaillé du re-entraînement
    - RETOUR : Dict - Rapport détaillé avec métriques avant/après et recommandations
    - UTILITÉ : Mise à jour complète des modèles avec nouvelles données et optimisations

11. load_optimized_params.txt (HybridBaccaratPredictor.load_optimized_params - Chargement paramètres optimisés)
    - Lignes 2172-2210 dans hbp.py (39 lignes)
    - FONCTION : Charge paramètres optimisés depuis fichier params.txt avec validation et application sécurisée à la configuration
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **IMPORT UTILITAIRES :** Importe `from utils import load_params_from_file, apply_params_to_config` pour fonctions spécialisées
      * **CHARGEMENT PARAMÈTRES :** Appelle `params = load_params_from_file("params.txt")` pour lecture fichier JSON
      * **VALIDATION CHARGEMENT :** Vérifie `if not params:` avec warning et messagebox "Échec du chargement des paramètres depuis params.txt"
      * **APPLICATION PARAMÈTRES :** Utilise `success = apply_params_to_config(self.config, params)` pour intégration sécurisée
      * **VALIDATION APPLICATION :** Contrôle `if not success:` avec warning et messagebox "Échec de l'application des paramètres à la configuration"
      * **CONFIRMATION SUCCÈS :** Si succès, affiche `messagebox.showinfo("Paramètres Chargés", "Les paramètres optimisés ont été chargés et appliqués avec succès.")` et `logger.info("Paramètres optimisés chargés et appliqués avec succès.")`
      * **GESTION ERREURS :** Capture `except ImportError as e:` avec logging et messagebox d'erreur pour modules manquants
      * **GESTION EXCEPTIONS :** Capture `except Exception as e:` avec logging détaillé et messagebox d'erreur générique
      * **LOGGING DÉTAILLÉ :** Utilise `logger.warning`, `logger.info` et `logger.error` pour traçabilité complète
      * **INTERFACE UTILISATEUR :** Utilise `messagebox.showwarning`, `messagebox.showinfo` et `messagebox.showerror` pour feedback utilisateur
    - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
    - UTILITÉ : Application sécurisée de paramètres optimisés depuis fichier avec validation complète et feedback utilisateur

12. apply_optimized_params_to_config_file.txt (HybridBaccaratPredictor.apply_optimized_params_to_config_file - Application params au fichier config)
    - FONCTION : Applique paramètres optimisés directement au fichier de configuration avec sauvegarde
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * optimized_params (Dict) - Dictionnaire des paramètres optimisés
      * config_file_path (str, optionnel) - Chemin fichier config (défaut: config.json)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LECTURE CONFIG ACTUELLE :** Charge configuration existante
      * **CRÉATION BACKUP :** Sauvegarde fichier config original
      * **FUSION INTELLIGENTE :** Fusionne paramètres optimisés avec config existante
      * **VALIDATION STRUCTURE :** Vérifie intégrité structure configuration
      * **ÉCRITURE ATOMIQUE :** Écrit nouveau fichier de manière atomique
      * **VALIDATION POST-ÉCRITURE :** Vérifie lisibilité du nouveau fichier
      * **TEST CHARGEMENT :** Teste chargement de la nouvelle configuration
      * **NOTIFICATION CHANGEMENTS :** Informe système des modifications
      * **GESTION ERREURS :** Restaure backup en cas de problème
      * **LOGGING MODIFICATIONS :** Enregistre détails des changements
    - RETOUR : bool - True si application réussie, False en cas d'erreur
    - UTILITÉ : Persistance des paramètres optimisés dans fichier de configuration

13. adjust_parameters_for_viability.txt (HybridBaccaratPredictor.adjust_parameters_for_viability - Ajustement viabilité paramètres)
    - Lignes 3294-3515 dans hbp.py (222 lignes)
    - FONCTION : Ajuste paramètres pour assurer viabilité et stabilité du système avec contraintes pratiques et validation ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * params (Dict[str, Any]) - Paramètres bruts à ajuster pour viabilité
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` et `adjusted_params = params.copy()` pour copie de travail
      * **LOGGING DÉBUT :** Enregistre "Ajustement des paramètres pour viabilité..." avec nombre de paramètres à traiter
      * **AJUSTEMENT LGBM :** Pour paramètres LGBM, applique contraintes spécifiques :
        - `num_leaves` : Borne entre 10 et 300 avec `max(10, min(300, params.get('lgbm_num_leaves', 31)))`
        - `max_depth` : Limite entre 3 et 15 avec `max(3, min(15, params.get('lgbm_max_depth', -1)))`
        - `learning_rate` : Borne entre 0.01 et 0.3 avec `max(0.01, min(0.3, params.get('lgbm_learning_rate', 0.1)))`
        - `n_estimators` : Limite entre 50 et 1000 avec `max(50, min(1000, params.get('lgbm_n_estimators', 100)))`
      * **AJUSTEMENT LSTM :** Pour paramètres LSTM, applique contraintes architecture :
        - `hidden_size` : Borne entre 32 et 256 avec `max(32, min(256, params.get('lstm_hidden_size', 64)))`
        - `num_layers` : Limite entre 1 et 4 avec `max(1, min(4, params.get('lstm_num_layers', 2)))`
        - `dropout` : Borne entre 0.0 et 0.5 avec `max(0.0, min(0.5, params.get('lstm_dropout', 0.1)))`
        - `learning_rate` : Limite entre 0.0001 et 0.01 avec `max(0.0001, min(0.01, params.get('lstm_learning_rate', 0.001)))`
      * **AJUSTEMENT SEUILS :** Pour paramètres de seuils, applique bornes logiques :
        - `min_confidence_for_recommendation` : Entre 0.5 et 0.95 avec `max(0.5, min(0.95, params.get('min_confidence_for_recommendation', 0.7)))`
        - `uncertainty_threshold` : Entre 0.1 et 0.8 avec `max(0.1, min(0.8, params.get('uncertainty_threshold', 0.3)))`
      * **AJUSTEMENT POIDS :** Pour poids des méthodes, normalise pour somme = 1.0 :
        - Collecte tous poids avec `weight_params = {k: v for k, v in params.items() if k.startswith('weight_')}`
        - Calcule `total_weight = sum(weight_params.values())` et normalise si `total_weight > 0`
        - Applique `adjusted_params[k] = v / total_weight` pour chaque poids
      * **VALIDATION COHÉRENCE :** Vérifie cohérence inter-paramètres :
        - Assure `lgbm_num_leaves < 2^lgbm_max_depth` pour éviter overfitting
        - Vérifie `lstm_hidden_size * lstm_num_layers < 1024` pour contraintes mémoire
      * **LOGGING AJUSTEMENTS :** Pour chaque paramètre modifié, enregistre `f"Paramètre ajusté: {param} {original_value} -> {new_value}"`
      * **VALIDATION FINALE :** Vérifie que tous paramètres ajustés sont dans bornes acceptables
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger_instance.error(f"Erreur lors de l'ajustement des paramètres: {e}", exc_info=True)` et retour paramètres originaux
    - RETOUR : Dict[str, Any] - Paramètres ajustés et viables pour utilisation
    - UTILITÉ : Garantit viabilité pratique des paramètres optimisés avec contraintes ressources et cohérence inter-paramètres

14. _apply_hyperparameters_from_metadata.txt (HybridBaccaratPredictor._apply_hyperparameters_from_metadata - Application hyperparamètres depuis métadonnées)
    - Lignes 3293-3330 dans hbp.py (38 lignes)
    - FONCTION : Applique hyperparamètres extraits des métadonnées de modèles sauvegardés avec validation utilisateur et gestion d'erreurs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * metadata (Dict[str, Any]) - Métadonnées contenant hyperparamètres à appliquer
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EXTRACTION HYPERPARAMÈTRES :** Récupère `hyperparams = metadata.get('hyperparameters', {})` depuis métadonnées
      * **VALIDATION PRÉSENCE :** Vérifie `if not hyperparams:` avec `messagebox.showwarning("Avertissement", "Aucun hyperparamètre trouvé dans les métadonnées.")` et retour
      * **CONFIRMATION UTILISATEUR :** Affiche `messagebox.askyesno("Confirmation", "Voulez-vous appliquer ces hyperparamètres à la configuration actuelle?\n\nCela modifiera les valeurs actuelles de la configuration.")` pour validation
      * **VALIDATION RÉPONSE :** Si `not response:`, retourne sans modification pour annulation utilisateur
      * **APPLICATION PARAMÈTRES :** Initialise `applied_params = []` puis itère `for param, value in hyperparams.items():` :
        - Vérifie `if hasattr(self.config, param) and value is not None:` pour validation attribut
        - Applique `setattr(self.config, param, value)` pour mise à jour configuration
        - Ajoute `applied_params.append(param)` pour suivi paramètres appliqués
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Hyperparamètres appliqués: {applied_params}")` pour traçabilité
      * **MESSAGE CONFIRMATION :** Affiche `messagebox.showinfo("Succès", f"Hyperparamètres appliqués avec succès!\n\nParamètres modifiés: {len(applied_params)}")` pour feedback utilisateur
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de l'application des hyperparamètres: {e}", exc_info=True)` et `messagebox.showerror("Erreur", f"Erreur lors de l'application des hyperparamètres:\n{e}")`
    - RETOUR : None - Méthode de configuration ne retourne rien
    - UTILITÉ : Interface sécurisée pour appliquer hyperparamètres optimisés avec validation utilisateur et gestion d'erreurs complète

15. _finalize_fast_update.txt (HybridBaccaratPredictor._finalize_fast_update - Finalisation mise à jour rapide)
    - Lignes 3078-3137 dans hbp.py (60 lignes)
    - FONCTION : Finalise processus de mise à jour rapide avec validation et intégration système selon mode déclenchement
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * success (bool) - Indique si la mise à jour rapide a réussi
      * start_time (float) - Timestamp de début pour calcul durée
      * summary (List[str]) - Résumé des opérations effectuées
      * is_auto_trigger (bool) - True si déclenchement automatique, False si manuel
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL DURÉE :** Calcule `total_time = time.time() - start_time` pour mesure performance
      * **IDENTIFICATION SOURCE :** Définit `trigger_type = "AUTO" if is_auto_trigger else "MANUAL"` pour logging
      * **LOGGING INITIAL :** Enregistre `logger.info(f"Finalisation MàJ rapide (Source: {trigger_type}). Succès: {success}, Durée: {total_time:.1f}s")`
      * **VÉRIFICATION UI :** Appelle `ui_available = self.is_ui_available()` pour validation interface
      * **VALIDATION UI :** Si `not ui_available:`, log warning et retour anticipé
      * **RÉACTIVATION CONTRÔLES :** Si UI disponible, appelle `self.toggle_training_controls(enabled=True)` pour réactiver boutons
      * **GESTION SUCCÈS :** Si `success:` :
        - Met à jour `self.last_fast_update_time = time.time()` pour tracking
        - Log `logger.info(f"Mise à jour rapide ({trigger_type}) terminée avec succès en {total_time:.1f}s")`
        - Si mode manuel (`not is_auto_trigger`), affiche `messagebox.showinfo("Succès", f"Mise à jour rapide terminée avec succès!\n\nDurée: {total_time:.1f}s\n\n{chr(10).join(summary)}")`
      * **GESTION ÉCHEC :** Si `not success:` :
        - Log `logger.error(f"Échec de la mise à jour rapide ({trigger_type}) après {total_time:.1f}s")`
        - Si mode manuel, affiche `messagebox.showerror("Erreur", f"Échec de la mise à jour rapide!\n\nDurée: {total_time:.1f}s\n\n{chr(10).join(summary)}")`
      * **POLITIQUE SAUVEGARDE :** Commentaire explicite "MODIFIÉ: Ne sauvegarde JAMAIS l'état automatiquement après une mise à jour rapide (manuelle ou auto)"
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging complet et messagebox d'erreur si mode manuel
    - RETOUR : None - Méthode de finalisation ne retourne rien
    - UTILITÉ : Finalisation robuste mise à jour rapide avec gestion différenciée selon mode déclenchement et interface utilisateur adaptée

16. _finalize_optuna_optimization.txt (HybridBaccaratPredictor._finalize_optuna_optimization - Finalisation optimisation Optuna)
    - Lignes 3139-3292 dans hbp.py (154 lignes)
    - FONCTION : Finalise processus d'optimisation Optuna avec interface utilisateur, sauvegarde et application paramètres selon succès
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * success (bool) - Indique si optimisation s'est terminée avec succès
      * best_params (Dict[str, Any]) - Meilleurs paramètres trouvés (peut être None si échec)
      * duration (float) - Durée totale de l'optimisation en secondes
      * error_message (str, optionnel) - Message d'erreur si échec (None si succès)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging adaptatif
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **RÉACTIVATION CONTRÔLES :** Si UI disponible, appelle `self.toggle_training_controls(enabled=True)` pour réactiver boutons
      * **GESTION SUCCÈS :** Si `success and best_params:` :
        - Log `logger_instance.info(f"Optimisation Optuna terminée avec succès en {duration:.2f} secondes")`
        - Appelle `self._update_config_from_optimization(best_params)` pour application paramètres
        - Utilise `self.show_optimization_results()` pour affichage résultats si UI disponible
        - Sinon affiche `messagebox.showinfo("Optimisation terminée", f"Optimisation terminée avec succès!\n\nDurée: {duration:.2f} secondes\n\nMeilleurs paramètres appliqués à la configuration.")`
      * **GESTION ÉCHEC :** Si `not success:` :
        - Log `logger_instance.error(f"Échec de l'optimisation Optuna après {duration:.2f} secondes: {error_message}")`
        - Affiche `messagebox.showerror("Erreur d'optimisation", f"L'optimisation a échoué après {duration:.2f} secondes.\n\nErreur: {error_message}")` si UI disponible
      * **GESTION PARAMÈTRES PARTIELS :** Si `success and not best_params:` :
        - Log warning "Optimisation terminée mais aucun paramètre valide trouvé"
        - Affiche message informatif à l'utilisateur
      * **NETTOYAGE RESSOURCES :** Reset `self.is_optuna_running = False` et `self.current_optimizer_instance = None` pour libération mémoire
      * **MISE À JOUR PROGRESSION :** Si UI disponible, appelle `self._update_progress(100, "Optimisation terminée")` pour finalisation visuelle
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging complet et messagebox d'erreur si UI disponible
    - RETOUR : None - Méthode de finalisation ne retourne rien
    - UTILITÉ : Finalisation robuste optimisation avec gestion différenciée succès/échec, interface utilisateur adaptée et nettoyage complet ressources

17. _run_fast_update_async.txt (HybridBaccaratPredictor._run_fast_update_async - Exécution mise à jour rapide asynchrone)
    - Lignes 12488-12559 dans hbp.py (72 lignes)
    - FONCTION : Exécute mise à jour rapide des modèles en mode asynchrone avec monitoring complet et callbacks
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * save_after_update (bool, optionnel) - Sauvegarde état après mise à jour (défaut: False)
      * is_auto_trigger (bool, optionnel) - Indique déclenchement automatique (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))`, `ui_available = self.is_ui_available()`, `start_time = time.time()`, `success = False`, `summary = []`
      * **PROGRESSION UI :** Si UI disponible, appelle `self.root.after(0, lambda: self._update_progress(10, "Préparation mise à jour rapide..."))`
      * **COPIE SÉQUENCE :** Avec `self.sequence_lock:`, copie `current_sequence = self.sequence[:]` et `current_round_num = len(current_sequence)`
      * **VALIDATION LONGUEUR :** Vérifie `if current_round_num < 10:` avec retour et warning si séquence trop courte
      * **MISE À JOUR INDEX :** Définit `self.last_incremental_update_index = current_round_num` pour éviter doublons
      * **MISE À JOUR MARKOV :** Si `hasattr(self, 'markov') and self.markov:`, utilise `with self.markov_lock:` puis `self.markov.update_session(current_sequence)`
      * **PROGRESSION MODÈLES :** Met à jour UI avec `self.root.after(0, lambda: self._update_progress(50, "Mise à jour des modèles..."))`
      * **SIMULATION TRAITEMENT :** Utilise `time.sleep(1)` pour simuler traitement et définit `success = True`
      * **GESTION ERREURS :** Capture `except Exception as e:` avec logging détaillé et `success = False`
      * **NETTOYAGE FINAL :** Dans `finally:`, utilise `with self.training_lock:` pour reset `self.is_fast_updating = False`
      * **FINALISATION :** Si UI disponible, appelle `self.root.after(0, lambda: self._finalize_fast_update(success, start_time, summary, is_auto_trigger))`
    - RETOUR : None - Méthode asynchrone ne retourne rien
    - UTILITÉ : Mise à jour non-bloquante avec suivi temps réel, protection thread-safe et gestion complète des erreurs

18. _run_optuna_optimization_async.txt (HybridBaccaratPredictor._run_optuna_optimization_async - Optimisation Optuna asynchrone)
    - Lignes 13034-13165 dans hbp.py (132 lignes)
    - FONCTION : Exécute optimisation Optuna multi-niveaux en mode asynchrone avec réinitialisation compteurs et gestion ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * training_data_package - Package de données pour optimisation (X_lgbm, y, X_lstm, indices)
      * n_trials (int) - Nombre d'essais à effectuer
      * advanced_options (Dict, optionnel) - Options avancées pour optimisation multi-niveaux
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))`, `ui_available = self.is_ui_available()`, `start_time = time.time()`, `best_params = None`, `success = False`, `error_message = None`, `optimizer_instance = None`
      * **UNPACK DONNÉES :** Extrait `X_lgbm_all, y_all, X_lstm_all, _, train_indices_from_prep, val_indices_from_prep, _, _ = training_data_package`
      * **PROGRESSION UI :** Si UI disponible, appelle `self.root.after(0, lambda: self._update_progress(25, "Configuration Optuna multi-niveaux..."))`
      * **RÉINITIALISATION COMPTEURS :** Importe `from optuna_optimizer import OptunaOptimizer` puis reset `OptunaOptimizer.optimized_viable_trials_count = 0` et `OptunaOptimizer.total_attempts_made = 0` avec logging détaillé
      * **CRÉATION OPTIMISEUR :** Instancie `optimizer_instance = OptunaOptimizer(self.config)` avec transmission options avancées
      * **CONFIGURATION RESSOURCES :** Si `advanced_options:`, définit `optimizer_instance.cpu_count = advanced_options.get('cpu_count', 8)`, `optimizer_instance.ram_gb = advanced_options.get('ram_gb', 28)`, `optimizer_instance.batch_size = advanced_options.get('batch_size', 1024)`
      * **ASSIGNATION DONNÉES :** Configure `optimizer_instance.X_lgbm_full = X_lgbm_all`, `optimizer_instance.y_full = y_all`, `optimizer_instance.X_lstm_full = X_lstm_all`, `optimizer_instance.train_indices = train_indices_from_prep`, `optimizer_instance.val_indices = val_indices_from_prep`
      * **LANCEMENT OPTIMISATION :** Appelle `params_found = optimizer_instance.optimize(n_trials=n_trials)` avec gestion d'erreurs complète
      * **TRAITEMENT RÉSULTATS :** Si succès, définit `best_params = params_found`, `success = True` avec logging détaillé
      * **GESTION ERREURS :** Capture exceptions avec `error_message` approprié et logging complet
      * **FINALISATION :** Calcule `duration = time.time() - start_time`, détermine `completion_status` et appelle `self.root.after(0, self._finalize_optuna_optimization, success, best_params, duration, error_message)` si UI disponible
    - RETOUR : None - Méthode asynchrone ne retourne rien
    - UTILITÉ : Optimisation Optuna non-bloquante avec réinitialisation propre, gestion ressources et interface responsive

19. _select_and_save_optimized_models.txt (HybridBaccaratPredictor._select_and_save_optimized_models - Sélection et sauvegarde modèles optimisés)
    - Lignes 4888-4903 dans hbp.py (16 lignes)
    - FONCTION : Interface utilisateur pour sélectionner fichier paramètres optimisés et déclencher sauvegarde des modèles correspondants
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **DIALOGUE SÉLECTION :** Ouvre `params_file_path = filedialog.askopenfilename()` avec configuration :
        - `title="Sélectionnez le fichier de paramètres optimisés"` pour clarté utilisateur
        - `filetypes=(("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*"))` pour filtrage approprié
        - `initialdir="viable_trials"` pour pointer vers dossier des essais viables
      * **VALIDATION SÉLECTION :** Vérifie `if not params_file_path:` avec logging "Sauvegarde des modèles optimisés annulée par l'utilisateur." et retour si annulé
      * **DÉLÉGATION SAUVEGARDE :** Appelle `self.save_optimized_models(params_file_path)` pour traitement effectif avec fichier sélectionné
      * **GESTION TRANSPARENTE :** Transmet directement le chemin sélectionné sans validation supplémentaire
    - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
    - UTILITÉ : Interface conviviale pour sélection et sauvegarde de modèles optimisés avec navigation fichiers intégrée

20. _update_config_from_optimization.txt (HybridBaccaratPredictor._update_config_from_optimization - MAJ config depuis optimisation)
    - Lignes 936-986 dans hbp.py (51 lignes)
    - FONCTION : Met à jour configuration système avec paramètres optimisés en filtrant et appliquant sélectivement les valeurs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * best_params (Dict[str, Any]) - Dictionnaire des meilleurs paramètres trouvés par optimisation
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PARAMÈTRES :** Vérifie `if not best_params:` avec logging warning "Aucun paramètre à mettre à jour" et retour si vide
      * **PROTECTION CONCURRENCE :** Utilise `with self.model_lock:` pour modifications thread-safe de la configuration
      * **LOGGING DÉBUT :** Enregistre "Mise à jour configuration avec les paramètres optimisés" pour traçabilité
      * **FILTRAGE PARAMÈTRES :** Itère `for param, value in best_params.items():` avec exclusions :
        - Ignore `param.startswith('weight_')` car gérés séparément par système de poids
        - Ignore `param in ('decision_threshold', 'min_confidence')` car paramètres de décision non-config
        - Log debug pour paramètres ignorés avec valeurs
      * **APPLICATION LGBM :** Pour paramètres LGBM, met à jour `self.config['lgbm'][param] = value` avec logging détaillé
      * **APPLICATION LSTM :** Pour paramètres LSTM, met à jour `self.config['lstm'][param] = value` avec logging détaillé
      * **PARAMÈTRES GÉNÉRAUX :** Pour autres paramètres, applique directement à `self.config[param] = value`
      * **VALIDATION POST-MISE À JOUR :** Vérifie cohérence configuration après modifications
      * **LOGGING FINAL :** Enregistre nombre total de paramètres appliqués et ignorés
    - RETOUR : None - Met à jour directement la configuration interne
    - UTILITÉ : Intégration sélective et sécurisée des paramètres optimisés avec filtrage intelligent et protection concurrence

21. on_training_error.txt (HybridBaccaratPredictor.on_training_error - Callback erreur entraînement)
    - FONCTION : Callback appelé en cas d'erreur pendant entraînement avec gestion recovery
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * error (Exception) - Exception capturée pendant entraînement
      * training_context (Dict) - Contexte de l'entraînement en cours
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE ERREUR :** Analyse type et cause de l'erreur d'entraînement
      * **LOGGING DÉTAILLÉ :** Enregistre erreur avec contexte complet
      * **NETTOYAGE RESSOURCES :** Libère ressources bloquées par entraînement
      * **RESTAURATION ÉTAT :** Remet système dans état stable
      * **NOTIFICATION UTILISATEUR :** Informe utilisateur avec message explicatif
      * **TENTATIVE RECOVERY :** Essaie récupération automatique si possible
      * **SAUVEGARDE ÉTAT :** Sauvegarde état avant erreur pour diagnostic
      * **MISE À JOUR STATUT :** Met à jour statut système (is_training = False)
      * **RAPPORT ERREUR :** Génère rapport détaillé pour debugging
      * **RECOMMANDATIONS :** Propose actions correctives à l'utilisateur
    - RETOUR : None - Callback de gestion d'erreur
    - UTILITÉ : Gestion robuste des erreurs avec recovery automatique et diagnostic

22. on_training_progress.txt (HybridBaccaratPredictor.on_training_progress - Callback progression entraînement)
    - FONCTION : Callback appelé périodiquement pour mise à jour progression entraînement
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * progress_info (Dict) - Informations de progression détaillées
      * current_epoch (int) - Époque actuelle d'entraînement
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MISE À JOUR UI :** Actualise barres de progression et métriques
      * **CALCUL POURCENTAGE :** Calcule pourcentage de completion
      * **ESTIMATION TEMPS :** Estime temps restant basé sur progression
      * **MONITORING PERFORMANCE :** Surveille métriques de performance
      * **DÉTECTION CONVERGENCE :** Identifie signes de convergence précoce
      * **GESTION RESSOURCES :** Monitore utilisation CPU/mémoire
      * **LOGGING PROGRESSION :** Enregistre jalons importants
      * **VALIDATION CONTINUE :** Vérifie qualité pendant entraînement
      * **NOTIFICATION JALONS :** Informe utilisateur des étapes importantes
      * **OPTIMISATION DYNAMIQUE :** Ajuste paramètres si nécessaire
    - RETOUR : None - Callback de progression
    - UTILITÉ : Suivi temps réel de l'entraînement avec optimisation dynamique

23. progress_callback.txt (HybridBaccaratPredictor.progress_callback - Callback progression générique)
    - Lignes 13012-13014 dans hbp.py (3 lignes)
    - FONCTION : Callback interne pour mise à jour progression Optuna de manière thread-safe vers interface utilisateur
    - PARAMÈTRES :
      * progress (float) - Valeur de progression entre 0 et 100
      * message (str) - Message de statut à afficher
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION UI :** Contrôle `if ui_available:` pour s'assurer que interface utilisateur est disponible
      * **DÉLÉGATION THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._update_progress(progress, message))` pour exécution dans thread principal UI
      * **PROTECTION CONCURRENCE :** Évite appels directs depuis thread d'optimisation vers interface Tkinter
      * **CALLBACK LÉGER :** Fonction minimale pour transmission rapide des mises à jour
    - RETOUR : None - Callback ne retourne rien
    - UTILITÉ : Pont thread-safe entre optimisation Optuna et interface utilisateur pour feedback temps réel

24. success_callback.txt (HybridBaccaratPredictor.success_callback - Callback succès opération)
    - Lignes 12995-13002 dans hbp.py (8 lignes)
    - FONCTION : Callback interne pour gestion succès optimisation Optuna avec finalisation thread-safe
    - PARAMÈTRES :
      * best_params (Dict[str, Any]) - Meilleurs paramètres trouvés par optimisation
      * duration (float) - Durée de l'optimisation en secondes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION UI :** Contrôle `if ui_available:` pour s'assurer que interface utilisateur est disponible
      * **FINALISATION THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._finalize_optuna_optimization(True, best_params, duration, None))` pour exécution dans thread principal
      * **LOGGING ALTERNATIF :** Si UI indisponible, utilise `logger_instance.info(f"Optimisation terminée avec succès en {duration:.2f} secondes")` et `logger_instance.info(f"Meilleurs paramètres: {best_params}")`
      * **TRANSMISSION PARAMÈTRES :** Passe `True` comme statut de succès et `None` comme message d'erreur
    - RETOUR : None - Callback ne retourne rien
    - UTILITÉ : Finalisation propre optimisation réussie avec transmission thread-safe des résultats vers interface

25. error_callback.txt (HybridBaccaratPredictor.error_callback - Callback erreur générique)
    - Lignes 13004-13010 dans hbp.py (7 lignes)
    - FONCTION : Callback interne pour gestion erreurs optimisation Optuna avec finalisation thread-safe
    - PARAMÈTRES :
      * error_msg (str) - Message d'erreur détaillé
      * best_params (Dict[str, Any]) - Meilleurs paramètres trouvés avant erreur (peut être None)
      * duration (float) - Durée de l'optimisation avant erreur en secondes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION UI :** Contrôle `if ui_available:` pour s'assurer que interface utilisateur est disponible
      * **FINALISATION THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._finalize_optuna_optimization(False, best_params, duration, error_msg))` pour exécution dans thread principal
      * **LOGGING ALTERNATIF :** Si UI indisponible, utilise `logger_instance.error(f"Erreur lors de l'optimisation: {error_msg}")` pour enregistrement erreur
      * **TRANSMISSION PARAMÈTRES :** Passe `False` comme statut d'échec et `error_msg` comme message d'erreur
    - RETOUR : None - Callback ne retourne rien
    - UTILITÉ : Gestion propre erreurs optimisation avec transmission thread-safe vers interface et logging approprié

26. stop_training_process.txt (HybridBaccaratPredictor.stop_training_process - Arrêt processus entraînement)
    - FONCTION : Arrête processus d'entraînement en cours avec nettoyage sécurisé
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_stop (bool, optionnel) - Force arrêt immédiat (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION ÉTAT :** Contrôle qu'un entraînement est en cours
      * **SIGNAL ARRÊT :** Envoie signal d'arrêt au processus d'entraînement
      * **ATTENTE GRACIEUSE :** Attend arrêt propre avec timeout
      * **ARRÊT FORCÉ :** Force arrêt si timeout dépassé
      * **NETTOYAGE RESSOURCES :** Libère toutes ressources utilisées
      * **RESTAURATION ÉTAT :** Remet système dans état stable
      * **SAUVEGARDE PARTIELLE :** Sauvegarde progrès si possible
      * **MISE À JOUR STATUT :** Met à jour flags d'état système
      * **NOTIFICATION UTILISATEUR :** Informe utilisateur de l'arrêt
      * **LOGGING ARRÊT :** Enregistre raison et contexte d'arrêt
    - RETOUR : bool - True si arrêt réussi, False en cas de problème
    - UTILITÉ : Arrêt sécurisé des processus d'entraînement avec préservation données

27. generate_optimization_report.txt (HybridBaccaratPredictor.generate_optimization_report - Génération rapport optimisation)
    - Lignes 988-1088 dans hbp.py (101 lignes)
    - FONCTION : Génère rapport détaillé des résultats d'optimisation Optuna avec analyses par catégories et métriques
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * study (optuna.study.Study) - Étude Optuna complète avec tous les essais
      * best_trial (optuna.trial.FrozenTrial) - Meilleur essai de l'optimisation
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EN-TÊTE RAPPORT :** Crée `report = "RAPPORT D'OPTIMISATION\n=====================\n\n"` avec formatage standardisé
      * **INFORMATIONS GÉNÉRALES :** Ajoute `f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"`, `f"Nombre total d'essais: {len(study.trials)}\n"`, `f"Meilleur score: {best_trial.value:.4f}\n\n"`
      * **CATÉGORISATION PARAMÈTRES :** Définit `categories = {'LSTM': ['lstm_'], 'LGBM': ['lgbm_'], 'Markov': ['markov_', 'max_order'], 'Seuils': ['threshold', 'confidence', 'uncertainty', 'wait_'], 'Poids': ['weight_'], 'Autres': []}`
      * **ORGANISATION PAR CATÉGORIE :** Pour chaque catégorie, filtre paramètres avec `for param, value in best_trial.params.items():` et `if any(param.startswith(prefix) or prefix in param for prefix in prefixes)`
      * **AFFICHAGE STRUCTURÉ :** Trie et affiche avec `for param, value in sorted(category_params.items()):` puis `report += f"  {param}: {value}\n"`
      * **MÉTRIQUES PERFORMANCE :** Extrait `metrics = {'max_consecutive': best_trial.user_attrs.get('max_consecutive', 0), 'precision_non_wait': best_trial.user_attrs.get('precision_non_wait', 0.0), 'wait_ratio': best_trial.user_attrs.get('wait_ratio', 0.0), 'wait_efficiency': best_trial.user_attrs.get('wait_efficiency', 0.0), 'recovery_rate_after_wait': best_trial.user_attrs.get('recovery_rate_after_wait', 0.0)}`
      * **IMPORTANCE PARAMÈTRES :** Utilise `import optuna.importance` puis `param_importances = optuna.importance.get_param_importances(study)` pour analyser influence
      * **HISTORIQUE ESSAIS :** Trie avec `sorted_trials = sorted(study.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)` pour classement
      * **TOP 10 ESSAIS :** Affiche `for i, trial in enumerate(sorted_trials[:10]):` avec `report += f"  {i+1}. Essai #{trial.number}: Score={trial.value:.4f}, Params={trial.params}\n"`
      * **GESTION ERREURS :** Capture exceptions avec `logger.error(f"Erreur lors de la génération du rapport d'optimisation: {e}", exc_info=True)` et retour message d'erreur
    - RETOUR : str - Rapport formaté complet ou message d'erreur
    - UTILITÉ : Documentation complète optimisation avec catégorisation intelligente, métriques détaillées et analyse d'importance

28. save_optimization_report.txt (HybridBaccaratPredictor.save_optimization_report - Sauvegarde rapport optimisation)
    - Lignes 1090-1119 dans hbp.py (30 lignes)
    - FONCTION : Sauvegarde rapport d'optimisation dans fichier texte avec timestamp et organisation par dossier
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * report (str) - Contenu du rapport d'optimisation à sauvegarder
      * study_name (str, optionnel) - Nom de l'étude pour suffixe fichier
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION DOSSIER :** Utilise `report_dir = os.path.join(os.getcwd(), "optimization_reports")` puis `os.makedirs(report_dir, exist_ok=True)` pour structure
      * **GÉNÉRATION NOM :** Crée `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` et `study_suffix = f"_{study_name}" if study_name else ""` pour nom unique
      * **CHEMIN COMPLET :** Construit `report_path = os.path.join(report_dir, f"optimization_report{study_suffix}_{timestamp}.txt")` pour fichier final
      * **ÉCRITURE FICHIER :** Ouvre `with open(report_path, 'w', encoding='utf-8') as f:` puis `f.write(report)` pour sauvegarde UTF-8
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Rapport d'optimisation sauvegardé dans {report_path}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde du rapport d'optimisation: {e}", exc_info=True)` et retour `None`
    - RETOUR : str - Chemin du fichier sauvegardé ou None en cas d'erreur
    - UTILITÉ : Persistance organisée des rapports d'optimisation avec horodatage pour analyse et archivage

29. show_optimization_results.txt (HybridBaccaratPredictor.show_optimization_results - Affichage résultats optimisation)
    - Lignes 1165-1384 dans hbp.py (220 lignes)
    - FONCTION : Affiche interface complète résultats optimisation Optuna avec graphiques Plotly et métriques détaillées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION OPTIMIZER :** Vérifie `optimizer_to_use = getattr(self, 'current_optimizer_instance', None)` puis fallback `getattr(self, 'optimizer', None)`
      * **CONTRÔLE ÉTUDE :** Valide existence `hasattr(optimizer_to_use, 'study')` et `optimizer_to_use.study is not None` avec warning si manquant
      * **EXTRACTION DONNÉES :** Récupère `study_obj = optimizer_to_use.study` et `best_trial_obj = study_obj.best_trial` pour analyse
      * **CRÉATION DATAFRAME :** Construit `trials_data = []` avec itération sur `study_obj.trials` :
        - Filtre `trial.state == optuna.trial.TrialState.COMPLETE` pour essais terminés
        - Extrait métriques : `trial.number`, `trial.value`, `trial.user_attrs.get('max_consecutive', 0)`
        - Collecte `precision_non_wait`, `wait_ratio`, `wait_efficiency`, `recovery_rate_after_wait`
      * **VISUALISATION PLOTLY :** Crée `combined_fig = make_subplots(rows=3, cols=1)` avec :
        - **Graphique 1 :** `vis.plot_optimization_history(study_obj)` pour historique optimisation
        - **Graphique 2 :** `vis.plot_param_importances(study_obj)` pour importance paramètres
        - **Graphique 3 :** Graphique barres métriques clés du meilleur essai avec normalisation
      * **SAUVEGARDE HTML :** Utilise `tempfile.NamedTemporaryFile(suffix=".html")` puis `combined_fig.to_html(full_html=True, include_plotlyjs='cdn')`
      * **OUVERTURE NAVIGATEUR :** Appelle `webbrowser.open(f"file://{os.path.realpath(temp_file_path)}")` pour affichage
      * **FENÊTRE RÉSULTATS :** Crée `result_window = tk.Toplevel(self.root)` avec boutons :
        - "Générer Rapport Détaillé" qui appelle `self.generate_optimization_report(study_obj, study_obj.best_trial)`
        - "Fermer" pour `result_window.destroy()`
      * **GESTION ERREURS :** Capture exceptions avec `messagebox.showerror` et fallback gracieux
    - RETOUR : None - Affiche interface graphique
    - UTILITÉ : Interface complète visualisation optimisation avec graphiques interactifs Plotly et métriques spécialisées objectif 1

30. _update_dependent_configs.txt (HybridBaccaratPredictor._update_dependent_configs - MAJ configurations dépendantes)
    - Lignes 13517-13568 dans hbp.py (52 lignes)
    - FONCTION : Met à jour configurations dépendantes après changement paramètres système avec adaptation ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging
      * **VALIDATION CONFIG :** Vérifie `if not hasattr(self, 'config'): return` pour s'assurer que configuration existe
      * **MISE À JOUR LGBM :** Adapte paramètres LGBM selon ressources système :
        - Récupère `cpu_count = getattr(self.config, 'cpu_count', os.cpu_count())` pour nombre de processeurs
        - Calcule `n_jobs_lgbm = max(1, min(cpu_count - 1, 8))` pour optimiser parallélisme
        - Met à jour `self.config.lgbm['n_jobs'] = n_jobs_lgbm` avec logging détaillé
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger_instance.error(f"Erreur lors de la mise à jour de n_jobs pour LGBM dans la config : {e}")`
      * **EXTENSIBILITÉ :** Prévoit ajout autres configurations (Dask, Ray, etc.) si nécessaires
      * **LOGGING FINAL :** Enregistre `logger_instance.debug("Mise à jour des configurations dépendantes terminée.")` pour traçabilité
    - RETOUR : None - Met à jour directement la configuration interne
    - UTILITÉ : Maintient cohérence configurations système avec adaptation automatique ressources disponibles

31. initialize_lgbm_cache.txt (HybridBaccaratPredictor.initialize_lgbm_cache - Initialisation cache LGBM)
    - Lignes 11734-11740 dans hbp.py (7 lignes)
    - FONCTION : Initialise système de cache double (deque + dict) pour optimiser prédictions LGBM avec taille configurable
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * maxlen (int, optionnel) - Taille maximale du cache (défaut: depuis config ou 100)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **DÉTERMINATION TAILLE :** Utilise `maxlen = getattr(self.config, 'lgbm_cache_maxlen', 100)` si paramètre non fourni
      * **INITIALISATION DEQUE :** Crée `self.lgbm_cache = deque(maxlen=maxlen)` pour stockage FIFO avec limite automatique
      * **INITIALISATION DICT :** Crée `self.lgbm_cache_dict = {}` pour accès rapide O(1) par clé de features
      * **LOGGING :** Enregistre `logger.info(f"Initialisation du cache LGBM avec une taille maximale de {maxlen}.")` pour traçabilité
    - RETOUR : None - Initialise structures de cache internes
    - UTILITÉ : Optimisation performance prédictions LGBM avec cache double pour accès rapide et gestion mémoire automatique

32. train_models.txt (HybridBaccaratPredictor.train_models - Entraînement modèles principal)
    - Lignes 1797-2210 dans hbp.py (414 lignes)
    - FONCTION : Point d'entrée principal pour entraînement complet des modèles ML avec pipeline intégré et gestion d'erreurs robuste
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * use_historical (bool, optionnel) - Utilise données historiques si disponibles (défaut: True)
      * async_mode (bool, optionnel) - Mode asynchrone pour entraînement non-bloquant (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION CONCURRENCE :** Utilise `with self.training_lock:` puis vérifie `if self.is_training or self.is_fast_updating:` avec warning et retour si processus actif
      * **MARQUAGE ÉTAT :** Définit `self.is_training = True` et `self.stop_training = False` pour contrôle processus
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **COLLECTE DONNÉES :** Combine séquence actuelle et données historiques :
        - Copie `sequence_copy = self.sequence[:]` avec `sequence_lock`
        - Si `use_historical and self.loaded_historical:`, ajoute `historical_data_copy = self.historical_data[:]`
        - Combine avec `combined_data = historical_data_copy + sequence_copy` et déduplique
      * **VALIDATION TAILLE :** Vérifie `if len(combined_data) < self.config.min_data_for_training:` avec message erreur et retour False
      * **PRÉPARATION FEATURES :** Appelle `self.prepare_features_for_training(combined_data)` qui retourne tuple `(X_lgbm, y_lgbm, X_lstm, y_lstm)`
      * **VALIDATION FEATURES :** Contrôle que toutes features sont non-None et non-vides avec logging détaillé
      * **CONFIGURATION OVERRIDE :** Prépare `config_override = {}` pour paramètres spécifiques si nécessaire
      * **MODE ASYNCHRONE :** Si `async_mode:`, lance `threading.Thread(target=self._train_models_async, args=(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override), daemon=True).start()`
      * **MODE SYNCHRONE :** Sinon, appelle directement `self._train_models_async(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override)`
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur pendant train_models: {e}", exc_info=True)` et nettoyage état
      * **NETTOYAGE FINAL :** Assure `self.is_training = False` dans bloc finally
    - RETOUR : bool - True si lancement réussi, False si erreur ou conditions non remplies
    - UTILITÉ : Interface principale entraînement avec pipeline complet, gestion concurrence et support asynchrone pour interface responsive
