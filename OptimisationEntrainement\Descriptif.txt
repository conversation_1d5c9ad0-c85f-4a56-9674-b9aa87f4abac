DESCRIPTIF DÉTAILLÉ DES MÉTHODES - OPTIMISATION ENTRAÎNEMENT
================================================================================

Ce fichier contient la description détaillée des méthodes d'optimisation et
d'entraînement du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : OPTIMISATION ENTRAÎNEMENT
Méthodes d'optimisation des hyperparamètres avec Optuna, entraînement
des modèles et gestion des processus d'apprentissage.

TOTAL : 32 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES OPTIMISATION ENTRAÎNEMENT
================================================================================

1. run_hyperparameter_optimization.txt (HybridBaccaratPredictor.run_hyperparameter_optimization - Optimisation Optuna multi-niveaux)
   - Lignes 12836-13032 dans hbp.py (197 lignes)
   - FONCTION : Lance l'optimisation des hyperparamètres avec stratégie multi-niveaux adaptée CPU via OptunaThreadManager
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * n_trials (int, optionnel) - Nombre d'essais à effectuer (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATIONS PRÉALABLES :** Contrôle état training, données historiques et suffisance des données (min 10 jeux)
     * **PRÉPARATION DONNÉES :** Appelle _prepare_training_data avec force_use_historical=True pour package complet
     * **VALIDATION ÉCHANTILLONS :** Vérifie qu'au moins 20 échantillons sont générés pour l'entraînement
     * **CONFIGURATION AVANCÉE :** Active multi-level, régularisation adaptative, SWA, méta-apprentissage, CV temporelle
     * **INITIALISATION OPTUNA :** Crée OptunaOptimizer avec réinitialisation des compteurs et transmission des options
     * **GESTIONNAIRE THREAD :** Utilise OptunaThreadManager pour exécution asynchrone avec callbacks
     * **CALLBACKS SYSTÈME :** Configure success_callback, error_callback et progress_callback pour UI
     * **LANCEMENT ASYNCHRONE :** Démarre l'optimisation dans thread séparé avec gestion complète des erreurs
   - RETOUR : None - Méthode asynchrone ne retourne rien
   - UTILITÉ : Point d'entrée principal pour l'optimisation sophistiquée des hyperparamètres avec interface non-bloquante

2. init_ml_models.txt (HybridBaccaratPredictor.init_ml_models - Initialisation complète modèles ML)
   - Lignes 1589-1795 dans hbp.py (207 lignes)
   - FONCTION : Initialise ou réinitialise tous les modèles ML (LGBM, LSTM, Markov) avec protection contre récursion et gestion complète des erreurs
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * force_reinit (bool, optionnel) - Force réinitialisation même si déjà initialisé (défaut: False)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION RÉCURSION :** Vérifie `if getattr(self, '_initializing_models', False): return` pour éviter appels récursifs
     * **MARQUAGE INITIALISATION :** Définit `self._initializing_models = True` pendant processus
     * **RÉINITIALISATION CONDITIONNELLE :** Si `force_reinit`, supprime attributs existants avec `delattr(self, attr)`
     * **INITIALISATION LGBM :** Crée `LGBMClassifier(n_estimators=self.config.lgbm_n_estimators, learning_rate=self.config.lgbm_learning_rate, max_depth=self.config.lgbm_max_depth, random_state=42, n_jobs=-1, verbose=-1)`
     * **INITIALISATION LSTM :** Configure `Sequential([LSTM(self.config.lstm_units, return_sequences=False, input_shape=(self.config.lstm_sequence_length, self.config.lstm_input_size)), Dropout(self.config.lstm_dropout), Dense(self.config.lstm_dense_units, activation='relu'), Dense(2, activation='softmax')])`
     * **COMPILATION LSTM :** Compile avec `model.compile(optimizer=Adam(learning_rate=self.config.lstm_learning_rate), loss='categorical_crossentropy', metrics=['accuracy'])`
     * **INITIALISATION MARKOV :** Crée `MarkovChainPredictor(order=self.config.markov_order, smoothing=self.config.markov_smoothing)`
     * **INITIALISATION SCALERS :** Configure `StandardScaler()` pour LGBM et LSTM avec `self.scaler` et `self.lstm_scaler`
     * **INITIALISATION CACHE :** Crée `deque(maxlen=self.config.cache_size)` pour optimisation prédictions
     * **MODÈLE INCERTITUDE :** Initialise `BaggingClassifier(base_estimator=LGBMClassifier(), n_estimators=self.config.uncertainty_n_estimators, random_state=42, n_jobs=-1)`
     * **VALIDATION CONFIGURATION :** Vérifie cohérence entre `lstm_input_size`, `lgbm_feature_count` et configuration
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et rollback si nécessaire
     * **FINALISATION :** Remet `self._initializing_models = False` et marque `self._models_initialized = True`
   - RETOUR : bool - True si initialisation réussie, False en cas d'erreur
   - UTILITÉ : Point d'entrée central pour initialisation robuste de tous les modèles ML avec protection récursion et validation complète

3. save_optimized_models.txt (HybridBaccaratPredictor.save_optimized_models - Sauvegarde modèles optimisés)
   - Lignes 4905-5002 dans hbp.py (98 lignes)
   - FONCTION : Sauvegarde les modèles avec hyperparamètres optimisés après optimisation Optuna avec gestion complète des configurations
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * params_file_path (str) - Chemin vers fichier JSON des paramètres optimisés
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ÉTAT :** Vérifie qu'aucun entraînement n'est en cours via is_training flag
     * **CHARGEMENT PARAMS :** Lit paramètres optimisés depuis fichier JSON avec gestion d'erreurs
     * **SAUVEGARDE CONFIG ORIGINALE :** Crée backup de la configuration actuelle pour restauration
     * **APPLICATION PARAMS :** Applique temporairement paramètres optimisés à la configuration
     * **MISE À JOUR DÉPENDANCES :** Appelle _update_dependent_configs pour cohérence système
     * **SAUVEGARDE MODÈLES :** Utilise _perform_save avec configuration optimisée temporaire
     * **GÉNÉRATION MÉTADONNÉES :** Crée métadonnées complètes avec hyperparamètres et performance
     * **SAUVEGARDE MÉTADONNÉES :** Exporte métadonnées en JSON pour traçabilité
     * **RESTAURATION CONFIG :** Remet configuration originale après sauvegarde
     * **VALIDATION FINALE :** Vérifie intégrité des fichiers sauvegardés
   - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
   - UTILITÉ : Permet sauvegarde des résultats d'optimisation avec configuration cohérente et traçabilité complète

4. on_training_complete.txt (HybridBaccaratPredictor.on_training_complete - Callback fin entraînement)
   - FONCTION : Callback appelé à la fin de l'entraînement pour finalisation et mise à jour UI avec gestion complète des états
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * training_results (Dict) - Résultats détaillés de l'entraînement
     * training_duration (float) - Durée totale de l'entraînement en secondes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION RÉSULTATS :** Vérifie intégrité et cohérence des résultats d'entraînement
     * **MISE À JOUR ÉTAT :** Change is_training à False et met à jour statut système
     * **CALCUL MÉTRIQUES :** Calcule métriques finales de performance et convergence
     * **SAUVEGARDE AUTOMATIQUE :** Déclenche sauvegarde automatique si configurée
     * **MISE À JOUR UI :** Actualise interface utilisateur avec résultats finaux
     * **NOTIFICATION UTILISATEUR :** Affiche notification de fin d'entraînement
     * **LOGGING DÉTAILLÉ :** Enregistre informations complètes dans logs système
     * **NETTOYAGE MÉMOIRE :** Libère ressources temporaires d'entraînement
     * **ACTIVATION PRÉDICTIONS :** Réactive système de prédiction avec nouveaux modèles
     * **VALIDATION FINALE :** Vérifie que tous les modèles sont opérationnels
   - RETOUR : None - Callback ne retourne rien
   - UTILITÉ : Gère la finalisation propre de l'entraînement avec mise à jour complète du système et interface

5. finalize_training.txt (HybridBaccaratPredictor.finalize_training - Finalisation entraînement)
   - FONCTION : Finalise le processus d'entraînement avec sauvegarde et mise à jour des métriques de performance
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * training_metrics (Dict) - Métriques détaillées de l'entraînement
     * save_models (bool, optionnel) - Sauvegarde automatique des modèles (défaut: True)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MÉTRIQUES :** Vérifie cohérence et complétude des métriques d'entraînement
     * **CALCUL PERFORMANCE FINALE :** Évalue performance finale sur ensemble de validation
     * **MISE À JOUR HISTORIQUE :** Intègre nouvelles métriques dans historique de performance
     * **SAUVEGARDE CONDITIONNELLE :** Sauvegarde modèles si performance améliorée
     * **GÉNÉRATION RAPPORT :** Crée rapport détaillé de l'entraînement
     * **MISE À JOUR POIDS :** Ajuste poids des méthodes selon performance observée
     * **NETTOYAGE RESSOURCES :** Libère mémoire GPU/CPU utilisée pendant entraînement
     * **VALIDATION MODÈLES :** Teste fonctionnement des modèles entraînés
     * **MISE À JOUR CONFIG :** Met à jour configuration avec nouveaux paramètres
     * **NOTIFICATION SYSTÈME :** Informe autres composants de la fin d'entraînement
   - RETOUR : Dict - Rapport détaillé de finalisation avec métriques et statuts
   - UTILITÉ : Complète le cycle d'entraînement avec persistance des résultats et validation système

6. _train_models_async.txt (HybridBaccaratPredictor._train_models_async - Entraînement asynchrone)
   - FONCTION : Lance l'entraînement des modèles en mode asynchrone avec gestion complète des threads et callbacks
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * training_data (Tuple) - Données d'entraînement préparées (X_lgbm, y_labels, X_lstm, etc.)
     * progress_callback (Callable, optionnel) - Fonction de callback pour progression
     * completion_callback (Callable, optionnel) - Fonction de callback pour fin d'entraînement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Vérifie intégrité et format des données d'entraînement
     * **CONFIGURATION THREAD :** Configure thread d'entraînement avec priorité appropriée
     * **INITIALISATION MODÈLES :** Prépare modèles LGBM et LSTM pour entraînement
     * **GESTION CALLBACKS :** Configure système de callbacks pour suivi progression
     * **ENTRAÎNEMENT PARALLÈLE :** Lance entraînement LGBM et LSTM en parallèle si possible
     * **MONITORING RESSOURCES :** Surveille utilisation CPU/mémoire pendant entraînement
     * **GESTION ERREURS :** Capture et gère erreurs d'entraînement avec recovery
     * **MISE À JOUR PROGRESSIVE :** Appelle callbacks de progression régulièrement
     * **VALIDATION CONTINUE :** Vérifie convergence et qualité pendant entraînement
     * **FINALISATION THREAD :** Nettoie ressources et appelle callback de completion
   - RETOUR : threading.Thread - Thread d'entraînement pour contrôle externe
   - UTILITÉ : Entraînement non-bloquant avec interface utilisateur responsive et monitoring complet

7. _models_are_trained.txt (HybridBaccaratPredictor._models_are_trained - Vérification modèles entraînés)
   - FONCTION : Vérifie si tous les modèles sont correctement entraînés et opérationnels avec validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * check_performance (bool, optionnel) - Vérifie aussi performance minimale (défaut: False)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION LGBM :** Contrôle que le modèle LGBM est initialisé et entraîné
     * **VÉRIFICATION LSTM :** Contrôle que le modèle LSTM est initialisé et entraîné
     * **TEST PRÉDICTION :** Effectue prédictions test pour valider fonctionnement
     * **VALIDATION POIDS :** Vérifie que les poids des modèles sont cohérents
     * **CONTRÔLE PERFORMANCE :** Évalue performance minimale si demandé
     * **VÉRIFICATION SCALER :** Contrôle que le scaler est fitté correctement
     * **TEST COMPATIBILITÉ :** Vérifie compatibilité entre modèles et données
     * **VALIDATION MÉTADONNÉES :** Contrôle cohérence des métadonnées de modèles
     * **DIAGNOSTIC COMPLET :** Effectue diagnostic approfondi si problème détecté
     * **RAPPORT ÉTAT :** Génère rapport détaillé de l'état des modèles
   - RETOUR : bool - True si tous modèles opérationnels, False sinon
   - UTILITÉ : Validation robuste de l'état des modèles avant utilisation en production

8. auto_fast_update_if_needed.txt (HybridBaccaratPredictor.auto_fast_update_if_needed - Mise à jour rapide automatique)
   - Lignes 12561-12611 dans hbp.py (51 lignes)
   - FONCTION : Déclenche mise à jour rapide automatique si conditions remplies avec focus sur manches cibles 31-60
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_round_num (int) - Numéro de manche actuelle pour vérification plage cible
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION AUTO-UPDATE :** Contrôle `if not hasattr(self, 'auto_update_enabled') or not self.auto_update_enabled.get():` avec retour si désactivé
     * **DÉTECTION PLAGE CIBLE :** Calcule `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)` et `is_target_round = target_round_min <= current_round_num <= target_round_max`
     * **INIT CALCULATEUR CONSÉCUTIF :** Si `is_target_round and not hasattr(self, 'consecutive_confidence_calculator'):`, appelle `self.init_consecutive_confidence_calculator()`
     * **VÉRIFICATION CONDITIONS :** Contrôle `if current_round_num <= self.last_incremental_update_index:` pour éviter mises à jour redondantes
     * **ACQUISITION VERROUS :** Utilise `with self.training_lock, self.sequence_lock:` pour protection thread-safe
     * **DOUBLE VÉRIFICATION :** Contrôle `if not self.auto_update_enabled.get():` et `if self.is_training or self.is_fast_updating:` pour éviter conflits
     * **MARQUAGE ÉTAT :** Définit `self.is_fast_updating = True` et `is_auto_trigger = True` pour suivi état
     * **LANCEMENT THREAD :** Crée `threading.Thread(target=self._run_fast_update_async, args=(False, is_auto_trigger), daemon=True, name="AutoFastUpdateThread").start()`
     * **GESTION ERREURS :** Capture exceptions avec `logger_instance.error` et reset `self.is_fast_updating = False`
   - RETOUR : None - Méthode de déclenchement ne retourne rien
   - UTILITÉ : Optimise performance système via mises à jour ciblées et automatiques

9. load_optimized_models.txt (HybridBaccaratPredictor.load_optimized_models - Chargement modèles optimisés)
   - FONCTION : Charge modèles pré-optimisés avec paramètres Optuna et validation complète d'intégrité
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * models_path (str) - Chemin vers dossier contenant modèles optimisés
     * validate_performance (bool, optionnel) - Valide performance après chargement (défaut: True)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CHEMIN :** Vérifie existence et accessibilité du dossier modèles
     * **LECTURE MÉTADONNÉES :** Charge métadonnées des modèles pour validation
     * **VÉRIFICATION COMPATIBILITÉ :** Contrôle compatibilité avec version actuelle
     * **CHARGEMENT LGBM :** Charge modèle LGBM avec validation d'intégrité
     * **CHARGEMENT LSTM :** Charge modèle LSTM avec vérification architecture
     * **RESTAURATION SCALER :** Charge et applique scaler de normalisation
     * **VALIDATION FONCTIONNELLE :** Teste fonctionnement avec données test
     * **VÉRIFICATION PERFORMANCE :** Valide performance sur échantillon si demandé
     * **MISE À JOUR CONFIGURATION :** Applique paramètres optimisés à la configuration
     * **FINALISATION CHARGEMENT :** Marque modèles comme opérationnels
   - RETOUR : bool - True si chargement réussi, False en cas d'erreur
   - UTILITÉ : Utilisation directe de modèles optimisés sans re-entraînement complet

10. run_full_retraining.txt (HybridBaccaratPredictor.run_full_retraining - Re-entraînement complet)
    - FONCTION : Lance re-entraînement complet de tous les modèles avec données actualisées et optimisation avancée
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * use_historical_data (bool, optionnel) - Inclut données historiques (défaut: True)
      * optimize_hyperparams (bool, optionnel) - Lance optimisation Optuna (défaut: False)
      * save_backup (bool, optionnel) - Sauvegarde modèles actuels avant re-entraînement (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **SAUVEGARDE PRÉVENTIVE :** Crée backup des modèles actuels si demandé
      * **PRÉPARATION DONNÉES :** Collecte et prépare toutes données disponibles
      * **RÉINITIALISATION MODÈLES :** Recrée modèles avec architecture optimisée
      * **OPTIMISATION CONDITIONNELLE :** Lance Optuna si paramètre activé
      * **ENTRAÎNEMENT SÉQUENTIEL :** Entraîne LGBM puis LSTM avec monitoring
      * **VALIDATION CROISÉE :** Effectue validation temporelle pour robustesse
      * **ÉVALUATION PERFORMANCE :** Compare avec modèles précédents
      * **MISE À JOUR POIDS :** Recalcule poids des méthodes selon nouvelle performance
      * **SAUVEGARDE FINALE :** Sauvegarde nouveaux modèles si amélioration
      * **RAPPORT COMPLET :** Génère rapport détaillé du re-entraînement
    - RETOUR : Dict - Rapport détaillé avec métriques avant/après et recommandations
    - UTILITÉ : Mise à jour complète des modèles avec nouvelles données et optimisations

11. load_optimized_params.txt (HybridBaccaratPredictor.load_optimized_params - Chargement paramètres optimisés)
    - FONCTION : Charge paramètres optimisés depuis fichier JSON avec validation et application sécurisée
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * params_file (str) - Chemin vers fichier JSON des paramètres
      * apply_immediately (bool, optionnel) - Applique immédiatement (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FICHIER :** Vérifie existence et format JSON du fichier
      * **PARSING SÉCURISÉ :** Parse JSON avec gestion d'erreurs robuste
      * **VALIDATION PARAMÈTRES :** Vérifie cohérence et validité des paramètres
      * **SAUVEGARDE CONFIG :** Backup configuration actuelle avant modification
      * **APPLICATION GRADUELLE :** Applique paramètres par sections avec validation
      * **TEST COMPATIBILITÉ :** Vérifie compatibilité avec modèles existants
      * **ROLLBACK AUTOMATIQUE :** Restaure config précédente si erreur
      * **MISE À JOUR DÉPENDANCES :** Synchronise configurations dépendantes
      * **VALIDATION FINALE :** Teste fonctionnement avec nouveaux paramètres
      * **LOGGING CHANGEMENTS :** Enregistre tous changements pour audit
    - RETOUR : bool - True si chargement réussi, False en cas d'erreur
    - UTILITÉ : Application sécurisée de paramètres optimisés avec rollback automatique

12. apply_optimized_params_to_config_file.txt (HybridBaccaratPredictor.apply_optimized_params_to_config_file - Application params au fichier config)
    - FONCTION : Applique paramètres optimisés directement au fichier de configuration avec sauvegarde
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * optimized_params (Dict) - Dictionnaire des paramètres optimisés
      * config_file_path (str, optionnel) - Chemin fichier config (défaut: config.json)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LECTURE CONFIG ACTUELLE :** Charge configuration existante
      * **CRÉATION BACKUP :** Sauvegarde fichier config original
      * **FUSION INTELLIGENTE :** Fusionne paramètres optimisés avec config existante
      * **VALIDATION STRUCTURE :** Vérifie intégrité structure configuration
      * **ÉCRITURE ATOMIQUE :** Écrit nouveau fichier de manière atomique
      * **VALIDATION POST-ÉCRITURE :** Vérifie lisibilité du nouveau fichier
      * **TEST CHARGEMENT :** Teste chargement de la nouvelle configuration
      * **NOTIFICATION CHANGEMENTS :** Informe système des modifications
      * **GESTION ERREURS :** Restaure backup en cas de problème
      * **LOGGING MODIFICATIONS :** Enregistre détails des changements
    - RETOUR : bool - True si application réussie, False en cas d'erreur
    - UTILITÉ : Persistance des paramètres optimisés dans fichier de configuration

13. adjust_parameters_for_viability.txt (HybridBaccaratPredictor.adjust_parameters_for_viability - Ajustement viabilité paramètres)
    - FONCTION : Ajuste paramètres pour assurer viabilité et stabilité du système avec contraintes pratiques
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * raw_params (Dict) - Paramètres bruts à ajuster
      * constraints (Dict, optionnel) - Contraintes spécifiques à respecter
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE CONTRAINTES :** Évalue contraintes système et ressources
      * **VALIDATION BORNES :** Vérifie que paramètres sont dans limites acceptables
      * **AJUSTEMENT MÉMOIRE :** Adapte paramètres selon mémoire disponible
      * **OPTIMISATION CPU :** Ajuste selon nombre de cœurs disponibles
      * **CONTRAINTES TEMPORELLES :** Adapte selon contraintes de temps
      * **COHÉRENCE INTER-PARAMÈTRES :** Assure cohérence entre paramètres liés
      * **STABILITÉ NUMÉRIQUE :** Évite paramètres causant instabilité
      * **VALIDATION PRATIQUE :** Teste viabilité avec données réelles
      * **OPTIMISATION FINALE :** Affine paramètres pour performance optimale
      * **DOCUMENTATION AJUSTEMENTS :** Enregistre tous ajustements effectués
    - RETOUR : Dict - Paramètres ajustés et viables pour utilisation
    - UTILITÉ : Garantit viabilité pratique des paramètres optimisés

14. _apply_hyperparameters_from_metadata.txt (HybridBaccaratPredictor._apply_hyperparameters_from_metadata - Application hyperparamètres depuis métadonnées)
    - FONCTION : Applique hyperparamètres extraits des métadonnées de modèles sauvegardés avec validation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * metadata (Dict) - Métadonnées contenant hyperparamètres
      * target_models (List[str], optionnel) - Modèles cibles (défaut: tous)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EXTRACTION HYPERPARAMÈTRES :** Extrait hyperparamètres depuis métadonnées
      * **VALIDATION MÉTADONNÉES :** Vérifie intégrité et complétude des métadonnées
      * **FILTRAGE MODÈLES :** Sélectionne hyperparamètres pour modèles cibles
      * **APPLICATION SÉQUENTIELLE :** Applique hyperparamètres modèle par modèle
      * **VALIDATION COMPATIBILITÉ :** Vérifie compatibilité avec architecture actuelle
      * **MISE À JOUR CONFIGURATION :** Synchronise configuration système
      * **TEST FONCTIONNEL :** Valide fonctionnement avec nouveaux hyperparamètres
      * **ROLLBACK SÉLECTIF :** Annule changements problématiques
      * **LOGGING DÉTAILLÉ :** Enregistre tous changements appliqués
      * **RAPPORT APPLICATION :** Génère rapport des hyperparamètres appliqués
    - RETOUR : bool - True si application réussie, False en cas d'erreur
    - UTILITÉ : Restauration précise d'hyperparamètres depuis modèles sauvegardés

15. _finalize_fast_update.txt (HybridBaccaratPredictor._finalize_fast_update - Finalisation mise à jour rapide)
    - FONCTION : Finalise processus de mise à jour rapide avec validation et intégration système
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * update_results (Dict) - Résultats de la mise à jour rapide
      * save_updated_models (bool, optionnel) - Sauvegarde modèles mis à jour (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION RÉSULTATS :** Vérifie qualité et cohérence des résultats
      * **ÉVALUATION AMÉLIORATION :** Compare performance avant/après mise à jour
      * **INTÉGRATION MODÈLES :** Intègre modèles mis à jour dans système principal
      * **MISE À JOUR MÉTRIQUES :** Actualise métriques de performance système
      * **SAUVEGARDE CONDITIONNELLE :** Sauvegarde si amélioration significative
      * **NOTIFICATION UTILISATEUR :** Informe utilisateur des résultats
      * **MISE À JOUR HISTORIQUE :** Enregistre mise à jour dans historique
      * **NETTOYAGE RESSOURCES :** Libère ressources temporaires utilisées
      * **VALIDATION FINALE :** Vérifie stabilité du système mis à jour
      * **RAPPORT FINALISATION :** Génère rapport complet de finalisation
    - RETOUR : Dict - Rapport de finalisation avec métriques et recommandations
    - UTILITÉ : Finalisation propre et sécurisée des mises à jour rapides

16. _finalize_optuna_optimization.txt (HybridBaccaratPredictor._finalize_optuna_optimization - Finalisation optimisation Optuna)
    - FONCTION : Finalise processus d'optimisation Optuna avec sélection et application des meilleurs paramètres
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * study (optuna.Study) - Étude Optuna complétée
      * optimization_results (Dict) - Résultats détaillés de l'optimisation
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE ÉTUDE :** Analyse résultats complets de l'étude Optuna
      * **SÉLECTION MEILLEURS PARAMS :** Identifie paramètres optimaux selon critères
      * **VALIDATION ROBUSTESSE :** Vérifie stabilité des paramètres sélectionnés
      * **APPLICATION PARAMÈTRES :** Applique meilleurs paramètres au système
      * **TEST PERFORMANCE :** Valide amélioration avec nouveaux paramètres
      * **GÉNÉRATION RAPPORT :** Crée rapport détaillé d'optimisation
      * **SAUVEGARDE RÉSULTATS :** Sauvegarde paramètres et métadonnées
      * **MISE À JOUR CONFIGURATION :** Intègre paramètres dans configuration
      * **NETTOYAGE OPTUNA :** Nettoie ressources d'optimisation
      * **NOTIFICATION COMPLETION :** Informe utilisateur de la completion
    - RETOUR : Dict - Rapport final avec paramètres optimaux et métriques
    - UTILITÉ : Finalisation complète et application des résultats d'optimisation Optuna

17. _run_fast_update_async.txt (HybridBaccaratPredictor._run_fast_update_async - Exécution mise à jour rapide asynchrone)
    - Lignes 12488-12559 dans hbp.py (72 lignes)
    - FONCTION : Exécute mise à jour rapide des modèles en mode asynchrone avec monitoring complet et callbacks
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * save_after_update (bool, optionnel) - Sauvegarde état après mise à jour (défaut: False)
      * is_auto_trigger (bool, optionnel) - Indique déclenchement automatique (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))`, `ui_available = self.is_ui_available()`, `start_time = time.time()`, `success = False`, `summary = []`
      * **PROGRESSION UI :** Si UI disponible, appelle `self.root.after(0, lambda: self._update_progress(10, "Préparation mise à jour rapide..."))`
      * **COPIE SÉQUENCE :** Avec `self.sequence_lock:`, copie `current_sequence = self.sequence[:]` et `current_round_num = len(current_sequence)`
      * **VALIDATION LONGUEUR :** Vérifie `if current_round_num < 10:` avec retour et warning si séquence trop courte
      * **MISE À JOUR INDEX :** Définit `self.last_incremental_update_index = current_round_num` pour éviter doublons
      * **MISE À JOUR MARKOV :** Si `hasattr(self, 'markov') and self.markov:`, utilise `with self.markov_lock:` puis `self.markov.update_session(current_sequence)`
      * **PROGRESSION MODÈLES :** Met à jour UI avec `self.root.after(0, lambda: self._update_progress(50, "Mise à jour des modèles..."))`
      * **SIMULATION TRAITEMENT :** Utilise `time.sleep(1)` pour simuler traitement et définit `success = True`
      * **GESTION ERREURS :** Capture `except Exception as e:` avec logging détaillé et `success = False`
      * **NETTOYAGE FINAL :** Dans `finally:`, utilise `with self.training_lock:` pour reset `self.is_fast_updating = False`
      * **FINALISATION :** Si UI disponible, appelle `self.root.after(0, lambda: self._finalize_fast_update(success, start_time, summary, is_auto_trigger))`
    - RETOUR : None - Méthode asynchrone ne retourne rien
    - UTILITÉ : Mise à jour non-bloquante avec suivi temps réel, protection thread-safe et gestion complète des erreurs

18. _run_optuna_optimization_async.txt (HybridBaccaratPredictor._run_optuna_optimization_async - Optimisation Optuna asynchrone)
    - Lignes 13034-13165 dans hbp.py (132 lignes)
    - FONCTION : Exécute optimisation Optuna multi-niveaux en mode asynchrone avec réinitialisation compteurs et gestion ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * training_data_package - Package de données pour optimisation (X_lgbm, y, X_lstm, indices)
      * n_trials (int) - Nombre d'essais à effectuer
      * advanced_options (Dict, optionnel) - Options avancées pour optimisation multi-niveaux
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))`, `ui_available = self.is_ui_available()`, `start_time = time.time()`, `best_params = None`, `success = False`, `error_message = None`, `optimizer_instance = None`
      * **UNPACK DONNÉES :** Extrait `X_lgbm_all, y_all, X_lstm_all, _, train_indices_from_prep, val_indices_from_prep, _, _ = training_data_package`
      * **PROGRESSION UI :** Si UI disponible, appelle `self.root.after(0, lambda: self._update_progress(25, "Configuration Optuna multi-niveaux..."))`
      * **RÉINITIALISATION COMPTEURS :** Importe `from optuna_optimizer import OptunaOptimizer` puis reset `OptunaOptimizer.optimized_viable_trials_count = 0` et `OptunaOptimizer.total_attempts_made = 0` avec logging détaillé
      * **CRÉATION OPTIMISEUR :** Instancie `optimizer_instance = OptunaOptimizer(self.config)` avec transmission options avancées
      * **CONFIGURATION RESSOURCES :** Si `advanced_options:`, définit `optimizer_instance.cpu_count = advanced_options.get('cpu_count', 8)`, `optimizer_instance.ram_gb = advanced_options.get('ram_gb', 28)`, `optimizer_instance.batch_size = advanced_options.get('batch_size', 1024)`
      * **ASSIGNATION DONNÉES :** Configure `optimizer_instance.X_lgbm_full = X_lgbm_all`, `optimizer_instance.y_full = y_all`, `optimizer_instance.X_lstm_full = X_lstm_all`, `optimizer_instance.train_indices = train_indices_from_prep`, `optimizer_instance.val_indices = val_indices_from_prep`
      * **LANCEMENT OPTIMISATION :** Appelle `params_found = optimizer_instance.optimize(n_trials=n_trials)` avec gestion d'erreurs complète
      * **TRAITEMENT RÉSULTATS :** Si succès, définit `best_params = params_found`, `success = True` avec logging détaillé
      * **GESTION ERREURS :** Capture exceptions avec `error_message` approprié et logging complet
      * **FINALISATION :** Calcule `duration = time.time() - start_time`, détermine `completion_status` et appelle `self.root.after(0, self._finalize_optuna_optimization, success, best_params, duration, error_message)` si UI disponible
    - RETOUR : None - Méthode asynchrone ne retourne rien
    - UTILITÉ : Optimisation Optuna non-bloquante avec réinitialisation propre, gestion ressources et interface responsive

19. _select_and_save_optimized_models.txt (HybridBaccaratPredictor._select_and_save_optimized_models - Sélection et sauvegarde modèles optimisés)
    - FONCTION : Sélectionne meilleurs modèles optimisés et les sauvegarde avec métadonnées
    - UTILITÉ : Persistance automatique des meilleurs résultats d'optimisation

20. _update_config_from_optimization.txt (HybridBaccaratPredictor._update_config_from_optimization - MAJ config depuis optimisation)
    - FONCTION : Met à jour configuration système avec résultats d'optimisation
    - UTILITÉ : Intégration automatique des paramètres optimisés

21. on_training_error.txt (HybridBaccaratPredictor.on_training_error - Callback erreur entraînement)
    - FONCTION : Callback appelé en cas d'erreur pendant entraînement avec gestion recovery
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * error (Exception) - Exception capturée pendant entraînement
      * training_context (Dict) - Contexte de l'entraînement en cours
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE ERREUR :** Analyse type et cause de l'erreur d'entraînement
      * **LOGGING DÉTAILLÉ :** Enregistre erreur avec contexte complet
      * **NETTOYAGE RESSOURCES :** Libère ressources bloquées par entraînement
      * **RESTAURATION ÉTAT :** Remet système dans état stable
      * **NOTIFICATION UTILISATEUR :** Informe utilisateur avec message explicatif
      * **TENTATIVE RECOVERY :** Essaie récupération automatique si possible
      * **SAUVEGARDE ÉTAT :** Sauvegarde état avant erreur pour diagnostic
      * **MISE À JOUR STATUT :** Met à jour statut système (is_training = False)
      * **RAPPORT ERREUR :** Génère rapport détaillé pour debugging
      * **RECOMMANDATIONS :** Propose actions correctives à l'utilisateur
    - RETOUR : None - Callback de gestion d'erreur
    - UTILITÉ : Gestion robuste des erreurs avec recovery automatique et diagnostic

22. on_training_progress.txt (HybridBaccaratPredictor.on_training_progress - Callback progression entraînement)
    - FONCTION : Callback appelé périodiquement pour mise à jour progression entraînement
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * progress_info (Dict) - Informations de progression détaillées
      * current_epoch (int) - Époque actuelle d'entraînement
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MISE À JOUR UI :** Actualise barres de progression et métriques
      * **CALCUL POURCENTAGE :** Calcule pourcentage de completion
      * **ESTIMATION TEMPS :** Estime temps restant basé sur progression
      * **MONITORING PERFORMANCE :** Surveille métriques de performance
      * **DÉTECTION CONVERGENCE :** Identifie signes de convergence précoce
      * **GESTION RESSOURCES :** Monitore utilisation CPU/mémoire
      * **LOGGING PROGRESSION :** Enregistre jalons importants
      * **VALIDATION CONTINUE :** Vérifie qualité pendant entraînement
      * **NOTIFICATION JALONS :** Informe utilisateur des étapes importantes
      * **OPTIMISATION DYNAMIQUE :** Ajuste paramètres si nécessaire
    - RETOUR : None - Callback de progression
    - UTILITÉ : Suivi temps réel de l'entraînement avec optimisation dynamique

23. progress_callback.txt (HybridBaccaratPredictor.progress_callback - Callback progression générique)
    - FONCTION : Callback générique pour gestion progression avec interface utilisateur
    - UTILITÉ : Gestion unifiée de la progression pour tous processus

24. success_callback.txt (HybridBaccaratPredictor.success_callback - Callback succès opération)
    - FONCTION : Callback appelé en cas de succès d'opération avec notification utilisateur
    - UTILITÉ : Gestion standardisée des succès avec feedback utilisateur

25. error_callback.txt (HybridBaccaratPredictor.error_callback - Callback erreur générique)
    - FONCTION : Callback générique pour gestion erreurs avec recovery et logging
    - UTILITÉ : Gestion unifiée des erreurs pour tous processus

26. stop_training_process.txt (HybridBaccaratPredictor.stop_training_process - Arrêt processus entraînement)
    - FONCTION : Arrête processus d'entraînement en cours avec nettoyage sécurisé
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_stop (bool, optionnel) - Force arrêt immédiat (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION ÉTAT :** Contrôle qu'un entraînement est en cours
      * **SIGNAL ARRÊT :** Envoie signal d'arrêt au processus d'entraînement
      * **ATTENTE GRACIEUSE :** Attend arrêt propre avec timeout
      * **ARRÊT FORCÉ :** Force arrêt si timeout dépassé
      * **NETTOYAGE RESSOURCES :** Libère toutes ressources utilisées
      * **RESTAURATION ÉTAT :** Remet système dans état stable
      * **SAUVEGARDE PARTIELLE :** Sauvegarde progrès si possible
      * **MISE À JOUR STATUT :** Met à jour flags d'état système
      * **NOTIFICATION UTILISATEUR :** Informe utilisateur de l'arrêt
      * **LOGGING ARRÊT :** Enregistre raison et contexte d'arrêt
    - RETOUR : bool - True si arrêt réussi, False en cas de problème
    - UTILITÉ : Arrêt sécurisé des processus d'entraînement avec préservation données

27. generate_optimization_report.txt (HybridBaccaratPredictor.generate_optimization_report - Génération rapport optimisation)
    - FONCTION : Génère rapport détaillé des résultats d'optimisation avec analyses
    - UTILITÉ : Documentation complète des résultats d'optimisation

28. save_optimization_report.txt (HybridBaccaratPredictor.save_optimization_report - Sauvegarde rapport optimisation)
    - FONCTION : Sauvegarde rapport d'optimisation avec métadonnées et graphiques
    - UTILITÉ : Persistance des rapports pour analyse ultérieure

29. show_optimization_results.txt (HybridBaccaratPredictor.show_optimization_results - Affichage résultats optimisation)
    - FONCTION : Affiche résultats d'optimisation dans interface utilisateur
    - UTILITÉ : Présentation visuelle des résultats d'optimisation

30. _update_dependent_configs.txt (HybridBaccaratPredictor._update_dependent_configs - MAJ configurations dépendantes)
    - FONCTION : Met à jour configurations dépendantes après changement paramètres
    - UTILITÉ : Maintient cohérence entre configurations liées

31. initialize_lgbm_cache.txt (HybridBaccaratPredictor.initialize_lgbm_cache - Initialisation cache LGBM)
    - FONCTION : Initialise système de cache pour optimiser prédictions LGBM
    - UTILITÉ : Optimisation performance via mise en cache intelligente

32. train_models.txt (HybridBaccaratPredictor.train_models - Entraînement modèles principal)
    - FONCTION : Point d'entrée principal pour entraînement complet des modèles ML
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * use_historical (bool, optionnel) - Utilise données historiques (défaut: True)
      * async_mode (bool, optionnel) - Mode asynchrone (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PRÉPARATION DONNÉES :** Collecte et prépare données d'entraînement
      * **VALIDATION PRÉREQUIS :** Vérifie conditions nécessaires à l'entraînement
      * **CONFIGURATION MODÈLES :** Configure paramètres des modèles LGBM et LSTM
      * **LANCEMENT ENTRAÎNEMENT :** Démarre processus d'entraînement selon mode
      * **MONITORING PROGRESSION :** Surveille progression avec callbacks
      * **GESTION ERREURS :** Capture et gère erreurs d'entraînement
      * **VALIDATION RÉSULTATS :** Valide qualité des modèles entraînés
      * **SAUVEGARDE AUTOMATIQUE :** Sauvegarde modèles si amélioration
      * **MISE À JOUR SYSTÈME :** Intègre nouveaux modèles dans système
      * **RAPPORT FINAL :** Génère rapport complet d'entraînement
    - RETOUR : bool - True si entraînement réussi, False en cas d'erreur
    - UTILITÉ : Interface principale pour entraînement complet avec gestion robuste
