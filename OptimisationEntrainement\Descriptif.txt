DESCRIPTIF DÉTAILLÉ DES MÉTHODES - OPTIMISATION ENTRAÎNEMENT
================================================================================

Ce fichier contient la description détaillée des méthodes d'optimisation et
d'entraînement du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : OPTIMISATION ENTRAÎNEMENT
Méthodes d'optimisation des hyperparamètres avec Optuna, entraînement
des modèles et gestion des processus d'apprentissage.

TOTAL : 2 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES OPTIMISATION ENTRAÎNEMENT
================================================================================

1. run_hyperparameter_optimization.txt (HybridBaccaratPredictor.run_hyperparameter_optimization - Optimisation Optuna multi-niveaux)
   - Lignes 12836-13032 dans hbp.py (197 lignes)
   - FONCTION : Lance l'optimisation des hyperparamètres avec stratégie multi-niveaux adaptée CPU via OptunaThreadManager
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * n_trials (int, optionnel) - Nombre d'essais à effectuer (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATIONS PRÉALABLES :** Contrôle état training, données historiques et suffisance des données (min 10 jeux)
     * **PRÉPARATION DONNÉES :** Appelle _prepare_training_data avec force_use_historical=True pour package complet
     * **VALIDATION ÉCHANTILLONS :** Vérifie qu'au moins 20 échantillons sont générés pour l'entraînement
     * **CONFIGURATION AVANCÉE :** Active multi-level, régularisation adaptative, SWA, méta-apprentissage, CV temporelle
     * **INITIALISATION OPTUNA :** Crée OptunaOptimizer avec réinitialisation des compteurs et transmission des options
     * **GESTIONNAIRE THREAD :** Utilise OptunaThreadManager pour exécution asynchrone avec callbacks
     * **CALLBACKS SYSTÈME :** Configure success_callback, error_callback et progress_callback pour UI
     * **LANCEMENT ASYNCHRONE :** Démarre l'optimisation dans thread séparé avec gestion complète des erreurs
   - RETOUR : None - Méthode asynchrone ne retourne rien
   - UTILITÉ : Point d'entrée principal pour l'optimisation sophistiquée des hyperparamètres avec interface non-bloquante

2. init_ml_models.txt (HybridBaccaratPredictor.init_ml_models - Initialisation complète modèles ML)
   - Lignes 1589-1795 dans hbp.py (207 lignes)
   - FONCTION : Initialise ou réinitialise tous les modèles ML avec protection contre récursion et gestion complète des erreurs
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * reset_weights (bool, optionnel) - Réinitialise les poids des méthodes (défaut: True)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION RÉCURSION :** Utilise flag _initializing_models pour éviter appels récursifs
     * **ACQUISITION VERROUS :** Prend model_lock et weights_lock pour modifications thread-safe
     * **INITIALISATION CALCULATEURS :** Configure consecutive_confidence_calculator et wait_placement_optimizer
     * **CONFIGURATION FEATURES :** Charge selected_features depuis config pour feature_names
     * **INITIALISATION SCALER :** Crée StandardScaler non-fitté pour normalisation
     * **CONFIGURATION LGBM :** Initialise LGBMClassifier avec paramètres config et n_jobs optimisé
     * **CRÉATION LSTM :** Instancie EnhancedLSTMModel avec optimisation mémoire et initialisation poids
     * **OPTIMISEURS LSTM :** Configure AdamW optimizer et ReduceLROnPlateau scheduler
     * **RÉINITIALISATION POIDS :** Reset poids méthodes et performances si demandé
   - RETOUR : bool - True si initialisation réussie, False en cas d'erreur
   - UTILITÉ : Point d'entrée central pour configuration complète de l'environnement ML avec robustesse maximale
