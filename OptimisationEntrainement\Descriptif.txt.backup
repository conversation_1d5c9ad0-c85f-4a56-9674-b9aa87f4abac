DESCRIPTIF DÉTAILLÉ DES MÉTHODES - OPTIMISATION ET ENTRAÎNEMENT
================================================================================

Ce fichier contient la description détaillée des méthodes d'optimisation,
d'entraînement et de configuration des modèles ML.

DOMAINE FONCTIONNEL : Optimisation, entraînement, schedulers et configuration modèles

TOTAL : 7 MÉTHODES ANALYSÉES

================================================================================

1. get_optimizer.txt (get_optimizer - FONCTION CRÉATION OPTIMISEUR)
   - Lignes 2029-2070 dans utils.py (42 lignes)
   - FONCTION : Crée optimiseur PyTorch configuré selon paramètres spécifiés avec support multiple algorithmes
   - PARAMÈTRES :
     * model (nn.Module) - Modèle PyTorch pour optimisation
     * optimizer_name (str, défaut='adamw') - Type optimiseur ('adam', 'adamw', 'sgd', 'rmsprop')
     * learning_rate (float, défaut=0.001) - Taux d'apprentissage initial
     * weight_decay (float, défaut=0.01) - Régularisation L2
     * **kwargs - Paramètres additionnels spécifiques à l'optimiseur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION NOM :** Convertit optimizer_name en minuscules pour robustesse
     * **ADAM :** torch.optim.Adam avec lr, weight_decay et kwargs (betas, eps)
     * **ADAMW :** torch.optim.AdamW optimisé pour transformers avec meilleure régularisation
     * **SGD :** torch.optim.SGD avec support momentum et nesterov via kwargs
     * **RMSPROP :** torch.optim.RMSprop avec alpha et momentum configurables
     * **GESTION ERREUR :** ValueError si optimiseur non supporté avec liste disponibles
     * **LOGGING :** Journalise création optimiseur avec paramètres pour traçabilité
   - RETOUR : torch.optim.Optimizer - Instance optimiseur configuré
   - UTILITÉ : Factory pattern pour création optimiseurs avec configuration flexible et robuste

2. get_criterion.txt (get_criterion - FONCTION CRÉATION CRITÈRE PERTE)
   - Lignes 2072-2113 dans utils.py (42 lignes)
   - FONCTION : Crée fonction de perte configurée selon type spécifié avec support pertes avancées
   - PARAMÈTRES :
     * criterion_name (str, défaut='crossentropy') - Type critère ('crossentropy', 'focal', 'mse', 'mae')
     * **kwargs - Paramètres spécifiques au critère (gamma, alpha, label_smoothing, etc.)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION :** Convertit criterion_name en minuscules pour robustesse
     * **CROSSENTROPY :** nn.CrossEntropyLoss avec weight et label_smoothing configurables
     * **FOCAL :** FocalLoss personnalisé avec gamma et alpha pour exemples difficiles
     * **MSE :** nn.MSELoss pour régression avec réduction configurable
     * **MAE :** nn.L1Loss pour régression robuste aux outliers
     * **GESTION ERREUR :** ValueError si critère non supporté avec liste disponibles
     * **LOGGING :** Journalise création critère avec paramètres pour débogage
   - RETOUR : nn.Module - Instance critère de perte configuré
   - UTILITÉ : Factory pattern pour création critères avec support pertes spécialisées ML

3. create_scheduler.txt (create_scheduler - FONCTION CRÉATION SCHEDULER)
   - Lignes 2115-2156 dans utils.py (42 lignes)
   - FONCTION : Crée scheduler de taux d'apprentissage configuré pour optimisation adaptative
   - PARAMÈTRES :
     * optimizer (torch.optim.Optimizer) - Optimiseur à scheduler
     * scheduler_name (str, défaut='cosine') - Type scheduler ('cosine', 'step', 'plateau', 'exponential')
     * **kwargs - Paramètres spécifiques au scheduler
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION :** Convertit scheduler_name en minuscules pour robustesse
     * **COSINE :** CosineAnnealingLR avec T_max et eta_min pour cycles d'apprentissage
     * **STEP :** StepLR avec step_size et gamma pour réduction par paliers
     * **PLATEAU :** ReduceLROnPlateau avec patience et factor pour adaptation métrique
     * **EXPONENTIAL :** ExponentialLR avec gamma pour décroissance exponentielle
     * **GESTION ERREUR :** ValueError si scheduler non supporté avec liste disponibles
     * **LOGGING :** Journalise création scheduler avec paramètres pour suivi
   - RETOUR : torch.optim.lr_scheduler - Instance scheduler configuré
   - UTILITÉ : Factory pattern pour création schedulers avec stratégies d'apprentissage optimales
