# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 3293 à 3330
# Type: Méthode de la classe HybridBaccaratPredictor

    def _apply_hyperparameters_from_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        Applique les hyperparamètres à partir des métadonnées.

        Args:
            metadata (Dict[str, Any]): Métadonnées contenant les hyperparamètres
        """
        try:
            hyperparams = metadata.get('hyperparameters', {})
            if not hyperparams:
                messagebox.showwarning("Avertissement", "Aucun hyperparamètre trouvé dans les métadonnées.")
                return

            # Demander confirmation à l'utilisateur
            response = messagebox.askyesno(
                "Confirmation",
                "Voulez-vous appliquer ces hyperparamètres à la configuration actuelle?\n\n"
                "Cela modifiera les valeurs actuelles de la configuration."
            )

            if not response:
                return

            # Appliquer les hyperparamètres à la configuration
            applied_params = []
            for param, value in hyperparams.items():
                if hasattr(self.config, param) and value is not None:
                    setattr(self.config, param, value)
                    applied_params.append(param)

            logger.info(f"Hyperparamètres appliqués à la configuration: {', '.join(applied_params)}")
            messagebox.showinfo(
                "Hyperparamètres Appliqués",
                f"{len(applied_params)} hyperparamètres ont été appliqués à la configuration actuelle."
            )
        except Exception as e:
            logger.error(f"Erreur lors de l'application des hyperparamètres: {e}", exc_info=True)
            messagebox.showerror("Erreur", f"Erreur lors de l'application des hyperparamètres: {e}")