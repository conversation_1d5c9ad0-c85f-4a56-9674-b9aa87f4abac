# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 3078 à 3137
# Type: Méthode de la classe HybridBaccaratPredictor

    def _finalize_fast_update(self, success: bool, start_time: float, summary: List[str], is_auto_trigger: bool):
        """Appelée dans le thread UI pour finaliser la mise à jour rapide.
           NE MONTRE PAS de messagebox si is_auto_trigger est True.
           MODIFIÉ: Ne sauvegarde JAMAIS l'état automatiquement après une mise à jour rapide (manuelle ou auto).
        """
        total_time = time.time() - start_time
        trigger_type = "AUTO" if is_auto_trigger else "MANUAL" # Pour logs
        logger.info(f"Finalisation MàJ rapide (Source: {trigger_type}). Succès: {success}, Durée: {total_time:.1f}s")

        # Vérifier si l'UI existe toujours
        ui_available = self.is_ui_available()
        if not ui_available:
             logger.warning(f"_finalize_fast_update ({trigger_type}): UI non dispo pour la finalisation complète.")
             return

        # --- Toggle controls dans tous les cas où la finalisation est appelée ---
        self.toggle_training_controls(enabled=True) # Réactiver les contrôles

        if success:
            if not summary:
                 # Cas peu probable mais possible si aucun modèle n'a été mis à jour
                 final_message = f"MàJ ({trigger_type}): Aucune MàJ? ({total_time:.1f}s)"
                 logger.warning(f"Finalisation MàJ rapide ({trigger_type}): Summary vide (inattendu).")
                 self._update_progress(100, final_message)
            else:
                # Message de base pour la progression et les logs
                base_message = f"MàJ Rapide ({trigger_type}) OK ({total_time:.1f}s): {', '.join(summary)}"
                logger.info(f"Finalisation MàJ rapide ({trigger_type}) réussie. Détails: {', '.join(summary)}")

                # --- Affichage conditionnel du message popup ---
                if not is_auto_trigger:
                    # Afficher message succès SEULEMENT si déclenchement MANUEL
                    messagebox.showinfo("Mise à Jour Rapide Terminée", base_message + "\n(Calibration et Incertitude inchangées)")
                else:
                    # Loguer qu'on ne montre pas le message pour l'auto
                    logger.info(f"Message popup de succès ignoré (déclenchement AUTO).")

                # --- Sauvegarde automatique SUPPRIMÉE ---
                # === MODIFICATION ICI: Suppression du bloc de sauvegarde ===
                logger.info(f"Sauvegarde automatique ignorée après MàJ rapide (Source: {trigger_type}).")
                final_progress_msg = f"MàJ ({trigger_type}) OK ({total_time:.1f}s)" # Message final sans mention de sauvegarde
                self._update_progress(100, final_progress_msg)
                # === FIN MODIFICATION ===

        else:
            # Échec de la mise à jour rapide (interruption ou erreur)
            final_message = f"Échec MàJ Rapide ({trigger_type}) ({total_time:.1f}s)"
            logger.error(f"Finalisation MàJ rapide ({trigger_type}): ÉCHEC.")
            self._update_progress(0, final_message) # Indiquer échec sur la barre
            # Afficher message d'erreur seulement si MANUEL
            if not is_auto_trigger:
               messagebox.showerror("Erreur Mise à Jour", f"La mise à jour rapide ({trigger_type}) a échoué.\nConsultez les logs.")
            else:
               logger.info(f"Message popup d'échec ignoré (déclenchement AUTO).")

        # Nettoyage mémoire (inchangé)
        gc.collect()
        if torch.cuda.is_available():
             try: torch.cuda.empty_cache()
             except Exception: pass