# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12561 à 12611
# Type: Méthode de la classe HybridBaccaratPredictor

    def auto_fast_update_if_needed(self, current_round_num: int) -> None:
        """Déclenche une mise à jour rapide automatique si les conditions sont remplies."""
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        ui_available = self.is_ui_available()

        try:
            if not hasattr(self, 'auto_update_enabled') or not self.auto_update_enabled.get():
                logger_instance.debug("Auto-update ignoré (désactivé ou variable manquante).")
                return

            # Vérifier si nous sommes dans la plage de manches cibles (31-60)
            target_round_min = getattr(self.config, 'target_round_min', 31)
            target_round_max = getattr(self.config, 'target_round_max', 60)
            is_target_round = target_round_min <= current_round_num <= target_round_max

            # Si nous sommes dans la plage cible, s'assurer que le calculateur de confiance consécutive est initialisé
            if is_target_round and not hasattr(self, 'consecutive_confidence_calculator'):
                logger_instance.info(f"Manche {current_round_num}: Initialisation du calculateur de confiance consécutive manquant (auto_fast_update)")
                self.init_consecutive_confidence_calculator()

            rounds_since_last_update = current_round_num - self.last_incremental_update_index
            update_frequency = 20

            if rounds_since_last_update < update_frequency:
                logger_instance.debug(f"Auto-update ignoré (seulement {rounds_since_last_update} tours depuis la dernière MàJ, min={update_frequency}).")
                return

            with self.training_lock, self.sequence_lock:
                if not self.auto_update_enabled.get():
                    logger_instance.debug("Auto-update ignoré (désactivé - double vérification).")
                    return

                if self.is_training or self.is_fast_updating:
                    logger_instance.warning("Auto-update ignoré (entraînement ML déjà en cours).")
                    return

                logger_instance.info(
                    f"Lancement de la mise à jour rapide automatique (dernier index={self.last_incremental_update_index}, manche actuelle={current_round_num})..."
                )
                self.is_fast_updating = True
                is_auto_trigger = True

                threading.Thread(
                    target=self._run_fast_update_async,
                    args=(False, is_auto_trigger),
                    daemon=True,
                    name="AutoFastUpdateThread"
                ).start()

        except Exception as e:
            logger_instance.error(f"Exception INATTENDUE dans auto_fast_update_if_needed: {e}", exc_info=True)