# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 300 à 381
# Type: Méthode

def get_criterion(config):
    """
    Crée une fonction de perte en fonction de la configuration

    Cette version améliorée prend en compte le label smoothing configuré
    et ajoute une option pour utiliser une perte focale qui donne plus
    de poids aux exemples difficiles.

    Args:
        config: Configuration

    Returns:
        Fonction de perte
    """
    label_smoothing = getattr(config, 'label_smoothing', 0.1)
    use_focal_loss = getattr(config, 'use_focal_loss', False)

    if use_focal_loss:
        # Implémentation de la perte focale (Focal Loss)
        # La perte focale donne plus de poids aux exemples difficiles à classer
        class FocalLoss(nn.Module):
            def __init__(self, gamma=2.0, alpha=None, label_smoothing=0.0):
                super(FocalLoss, self).__init__()
                self.gamma = gamma
                self.label_smoothing = label_smoothing

                # Gestion du paramètre alpha (poids des classes)
                if alpha is not None:
                    if isinstance(alpha, float):
                        # Si alpha est un float, on crée un tensor [1-alpha, alpha] pour les classes 0 et 1
                        self.alpha = torch.tensor([1.0-alpha, alpha])
                    else:
                        # Sinon on utilise directement le tensor fourni
                        self.alpha = alpha
                else:
                    self.alpha = None

                # Créer la perte d'entropie croisée avec les poids appropriés
                self.ce_loss = nn.CrossEntropyLoss(
                    weight=self.alpha,
                    reduction='none',
                    label_smoothing=label_smoothing
                )

            def forward(self, inputs, targets):
                # Calculer la perte d'entropie croisée standard
                ce_loss = self.ce_loss(inputs, targets)

                # Calculer les probabilités prédites pour les classes cibles
                pt = torch.exp(-ce_loss)

                # Appliquer un facteur focal modifié pour réduire l'impact sur la val_loss
                # Utiliser une version plus douce du facteur focal pour les exemples très difficiles
                # Cela aide à réduire la val_loss tout en maintenant l'attention sur les exemples difficiles
                focal_weight = ((1 - pt) ** self.gamma)

                # Limiter l'impact maximal du facteur focal pour éviter des pertes extrêmes
                # qui peuvent déstabiliser l'entraînement et augmenter la val_loss
                max_focal_weight = 4.0  # Limite l'amplification maximale
                focal_weight = torch.min(focal_weight, torch.tensor(max_focal_weight, device=focal_weight.device))

                focal_loss = focal_weight * ce_loss

                # Retourner la moyenne
                return focal_loss.mean()

        # Utiliser la perte focale avec les paramètres configurés
        gamma = getattr(config, 'focal_loss_gamma', 2.0)  # Valeur par défaut réduite pour une pénalisation plus équilibrée
        alpha = getattr(config, 'focal_loss_alpha', 0.52)  # Valeur par défaut ajustée pour un équilibrage plus fin

        # Vérifier si nous sommes en phase d'entraînement ou d'évaluation
        is_training = getattr(config, 'is_training_phase', True)

        # Si nous sommes en phase d'évaluation, utiliser un gamma plus faible pour réduire la val_loss
        if not is_training:
            gamma = min(gamma, 1.5)  # Limiter gamma en évaluation pour réduire la val_loss

        # Créer et retourner la perte focale
        return FocalLoss(gamma=gamma, alpha=alpha, label_smoothing=label_smoothing)
    else:
        # Utiliser la perte d'entropie croisée standard avec label smoothing
        return nn.CrossEntropyLoss(label_smoothing=label_smoothing)