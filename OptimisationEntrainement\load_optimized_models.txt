# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 2212 à 2331
# Type: Méthode de la classe HybridBaccaratPredictor

    def load_optimized_models(self, params_file_path: str = None) -> bool:
        """
        Charge les modèles entraînés avec les hyperparamètres optimisés.
        Cette fonction est spécifiquement conçue pour être utilisée après une optimisation Optuna.
        Si des modèles pré-entraînés sont disponibles, ils seront chargés directement.
        Sinon, les paramètres seront appliqués à la configuration et l'utilisateur devra lancer un entraînement.

        Args:
            params_file_path (str, optional): Chemin vers le fichier JSON contenant les paramètres optimisés.
                Si None, ouvre une boîte de dialogue pour sélectionner le fichier.

        Returns:
            bool: True si le chargement a réussi, False sinon
        """
        import json
        import os
        from utils import apply_params_to_config

        if self.is_training or self.is_fast_updating:
            logger.warning("Impossible de charger les modèles optimisés pendant l'entraînement ou la mise à jour rapide.")
            if self.is_ui_available():
                messagebox.showwarning("Action Impossible", "Veuillez arrêter tout processus ML avant de charger les modèles optimisés.")
            return False

        # Si aucun chemin n'est fourni, ouvrir une boîte de dialogue
        if params_file_path is None:
            params_file_path = filedialog.askopenfilename(
                title="Sélectionnez le fichier de paramètres optimisés",
                filetypes=(("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")),
                initialdir="viable_trials"
            )

            if not params_file_path:
                logger.info("Chargement des modèles optimisés annulé par l'utilisateur.")
                return False

        # Vérifier que le fichier de paramètres existe
        if not os.path.exists(params_file_path):
            logger.error(f"Le fichier de paramètres optimisés n'existe pas: {params_file_path}")
            if self.is_ui_available():
                messagebox.showerror("Erreur", f"Le fichier de paramètres optimisés n'existe pas:\n{params_file_path}")
            return False

        try:
            # Charger les paramètres optimisés
            with open(params_file_path, 'r', encoding='utf-8') as f:
                optimized_params = json.load(f)

            logger.info(f"Paramètres optimisés chargés depuis {params_file_path}")

            # Vérifier si des modèles pré-entraînés sont disponibles
            models_path = optimized_params.get('models_path')

            if models_path and os.path.exists(models_path):
                # Des modèles pré-entraînés sont disponibles, les charger directement
                logger.info(f"Modèles pré-entraînés trouvés: {models_path}")
                if self.is_ui_available():
                    self._update_progress(10, f"Chargement des modèles pré-entraînés: {os.path.basename(models_path)}...")

                # Charger les modèles pré-entraînés
                load_success = self.load_trained_models(models_path)

                if load_success:
                    logger.info(f"Modèles pré-entraînés chargés avec succès depuis {models_path}")
                    if self.is_ui_available():
                        self._update_progress(100, f"Modèles pré-entraînés chargés: {os.path.basename(models_path)}")
                        messagebox.showinfo("Chargement Réussi",
                                           f"Les modèles pré-entraînés ont été chargés avec succès depuis:\n{models_path}\n\n"
                                           "✅ IMPORTANT: Ces modèles sont déjà entraînés avec les paramètres optimisés.\n"
                                           "✅ Aucun entraînement supplémentaire n'est nécessaire.\n"
                                           "✅ Vous pouvez immédiatement utiliser ces modèles pour des prédictions.")
                    return True
                else:
                    logger.error(f"Échec du chargement des modèles pré-entraînés depuis {models_path}")
                    if self.is_ui_available():
                        self._update_progress(0, "Échec du chargement des modèles pré-entraînés.")
                        messagebox.showwarning("Échec du Chargement",
                                              f"Échec du chargement des modèles pré-entraînés depuis:\n{models_path}\n\n"
                                              "⚠️ Les paramètres optimisés seront appliqués à la configuration.\n"
                                              "⚠️ Vous devrez lancer un entraînement complet pour créer les modèles avec ces paramètres.")

                    # Continuer avec l'application des paramètres à la configuration
            else:
                logger.info("Aucun modèle pré-entraîné trouvé, application des paramètres à la configuration.")

            # Créer une copie de la configuration actuelle
            from config import PredictorConfig
            optimized_config = PredictorConfig()

            # Appliquer les paramètres optimisés à la configuration
            if not apply_params_to_config(optimized_config, optimized_params.get('params', {})):
                logger.error("Échec de l'application des paramètres optimisés à la configuration")
                if self.is_ui_available():
                    messagebox.showerror("Erreur", "Échec de l'application des paramètres optimisés à la configuration")
                return False

            # Mettre à jour la configuration actuelle avec les paramètres optimisés
            self.config = optimized_config
            logger.info("Configuration mise à jour avec les paramètres optimisés")

            # Réinitialiser les modèles pour qu'ils soient recréés avec les nouveaux paramètres
            self.reset_data(reset_models=True, reset_history=False)
            logger.info("Modèles réinitialisés pour être recréés avec les paramètres optimisés")

            # Informer l'utilisateur qu'il doit lancer un entraînement complet
            if self.is_ui_available():
                self._update_progress(10, "Paramètres optimisés appliqués à la configuration...")
                messagebox.showinfo("Information",
                                   "Les paramètres optimisés ont été appliqués à la configuration.\n\n"
                                   "⚠️ IMPORTANT: Aucun modèle pré-entraîné n'a été trouvé.\n"
                                   "⚠️ Vous devez lancer un entraînement complet pour créer les modèles avec ces paramètres.\n"
                                   "⚠️ Utilisez le bouton 'Entraînement Complet' pour finaliser le processus.")

            return True

        except Exception as e:
            logger.error(f"Erreur lors du chargement des modèles optimisés: {e}", exc_info=True)
            if self.is_ui_available():
                messagebox.showerror("Erreur", f"Erreur lors du chargement des modèles optimisés:\n{str(e)}")
            return False