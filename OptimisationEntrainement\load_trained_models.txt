# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 3558 à 4076
# Type: Méthode de la classe HybridBaccaratPredictor

    def load_trained_models(self, filepath: Optional[str] = None) -> bool:
        caller_function_name = ""
        try: caller_function_name = sys._getframe(1).f_code.co_name
        except Exception: pass
        ui_available = self.is_ui_available()

        # Définir le flag pour indiquer que nous chargeons un modèle existant
        # Ce flag sera utilisé pour choisir la bonne longueur de séquence LSTM
        self._loading_existing_model = True
        logger.debug("Flag _loading_existing_model défini à True pour le chargement")

        if filepath is None:
            initial_dir = MODEL_SAVE_DIR if os.path.exists(MODEL_SAVE_DIR) else os.getcwd()
            file_extensions = [("Predictor State Files", "*.joblib *.pkl"), ("Tous", "*.*")]
            filepath = filedialog.askopenfilename(initialdir=initial_dir, title="Charger État", filetypes=file_extensions)
            if not filepath:
                logger.info("Chargement manuel annulé.")
                self._loading_existing_model = False  # Réinitialiser le flag si annulé
                return False

        logger.info(f"Tentative chargement état depuis: {filepath}")
        if not os.path.exists(filepath):
            logger.error(f"Fichier non trouvé: {filepath}")
            if caller_function_name != "_load_latest_state" and ui_available:
                messagebox.showerror("Erreur Fichier", f"Fichier non trouvé:\n{filepath}")
            return False
        if not (filepath.lower().endswith(".pkl") or filepath.lower().endswith(".joblib")):
            logger.error(f"Extension non supportée: {filepath}. Attendu: .pkl ou .joblib")
            if caller_function_name != "_load_latest_state" and ui_available:
                messagebox.showerror("Erreur Type Fichier", "Type de fichier non supporté.\nSélectionnez .pkl ou .joblib.")
            return False

        if ui_available:
            self._update_progress(10, f"Chargement: {os.path.basename(filepath)}")
        loaded_package = None
        success_status = False
        locks_acquired = False
        markov_lock_to_use = None
        try:
            logger.debug("load_trained_models: Tentative acquisition verrous...")
            self.sequence_lock.acquire(); self.model_lock.acquire()
            if self.markov and hasattr(self.markov, 'lock'):
                markov_lock_to_use = self.markov.lock
            else:
                 if not hasattr(self, '_fallback_markov_lock'): self._fallback_markov_lock = threading.RLock()
                 markov_lock_to_use = self._fallback_markov_lock
            markov_lock_to_use.acquire(); self.training_lock.acquire(); self.weights_lock.acquire()
            locks_acquired = True; logger.debug("load_trained_models: Verrous acquis.")

            logger.debug(f"Tentative chargement fichier {filepath}...")
            try:
                loaded_package = joblib.load(filepath); logger.info(f"Chargement Joblib OK: {filepath}")
            except Exception as e_joblib:
                logger.warning(f"Échec chargement Joblib ({type(e_joblib).__name__}: {e_joblib}). Tentative Pickle...")
                try:
                    with open(filepath, 'rb') as f: loaded_package = pickle.load(f); logger.info(f"Chargement Pickle OK: {filepath}")
                except (pickle.UnpicklingError, EOFError, Exception) as e_pickle:
                    err_msg = f"Impossible de charger l'état depuis {os.path.basename(filepath)}.\n Raison: Échec Joblib ET Pickle.\n Détails: {e_pickle}"
                    logger.critical(err_msg, exc_info=True)
                    raise IOError(err_msg) from e_pickle

            if loaded_package is None or not isinstance(loaded_package, dict):
                 raise ValueError("Le fichier chargé est vide ou n'est pas un dictionnaire valide.")

            logger.debug(f"Clés du package chargé: {list(loaded_package.keys())}")
            if ui_available:
                self._update_progress(30, "Vérification contenu du package...")

            loaded_feature_names = loaded_package.get('feature_names')
            if not loaded_feature_names or not isinstance(loaded_feature_names, list):
                 config_details_temp = loaded_package.get('config_details', {})
                 loaded_feature_names = config_details_temp.get('feature_names') if isinstance(config_details_temp, dict) else None
                 if loaded_feature_names and isinstance(loaded_feature_names, list):
                      logger.warning("Utilisation des noms de features depuis 'config_details' (fallback).")
                 else:
                      raise ValueError("Clé 'feature_names' manquante ou invalide dans le fichier chargé (et pas de fallback).")
            self.feature_names = loaded_feature_names
            logger.info(f"Noms features ({len(self.feature_names)}) chargés.")
            expected_n_features = len(self.feature_names)

            loaded_scaler = loaded_package.get('feature_scaler')
            if not isinstance(loaded_scaler, StandardScaler):
                 raise ValueError("Clé 'feature_scaler' manquante ou type incorrect.")
            if not hasattr(loaded_scaler, 'n_features_in_'):
                 raise ValueError("Le 'feature_scaler' chargé n'est pas 'fit' (attribut n_features_in_ manquant).")
            # Vérifier si le nombre de features est différent
            if loaded_scaler.n_features_in_ != expected_n_features:
                logger.warning(f"Le 'feature_scaler' chargé attend {loaded_scaler.n_features_in_} features, mais {expected_n_features} étaient attendues (basé sur feature_names).")

                # Adapter le nombre de features attendues au nombre de features du scaler
                logger.info(f"Adaptation du nombre de features attendues de {expected_n_features} à {loaded_scaler.n_features_in_}")

                # Mettre à jour feature_names si nécessaire
                if loaded_scaler.n_features_in_ < expected_n_features:
                    # Si le scaler attend moins de features, tronquer feature_names
                    self.feature_names = self.feature_names[:loaded_scaler.n_features_in_]
                    logger.warning(f"feature_names tronqué à {len(self.feature_names)} éléments pour correspondre au scaler")
                elif loaded_scaler.n_features_in_ > expected_n_features:
                    # Si le scaler attend plus de features, étendre feature_names
                    additional_features = loaded_scaler.n_features_in_ - expected_n_features
                    self.feature_names.extend([f"feature_{i+expected_n_features}" for i in range(additional_features)])
                    logger.warning(f"{additional_features} noms de features génériques ajoutés pour correspondre au scaler")
            self.feature_scaler = loaded_scaler
            logger.info("Scaler chargé et vérifié 'fit'.")

            models_to_load_check = {
                 'lgbm_base': LGBMClassifier,
                 'calibrated_lgbm': CalibratedClassifierCV,
                 'lgbm_uncertainty': BaggingClassifier
            }
            for name, expected_type in models_to_load_check.items():
                 loaded_model = loaded_package.get(name)
                 if not isinstance(loaded_model, expected_type):
                     raise ValueError(f"Clé '{name}' manquante ou type incorrect (attendu {expected_type.__name__}).")
                 try:
                     check_is_fitted(loaded_model)
                     setattr(self, name, loaded_model)
                     logger.info(f"Modèle '{name}' chargé et vérifié 'fit'.")

                     # Synchroniser les paramètres LGBM avec la configuration
                     if name == 'lgbm_base' and hasattr(loaded_model, 'get_params'):
                         lgbm_params = loaded_model.get_params()
                         # Mettre à jour les attributs de configuration correspondants
                         for param_name, param_value in lgbm_params.items():
                             config_param_name = f'lgbm_{param_name}'
                             if hasattr(self.config, config_param_name) and param_name not in ['n_jobs', 'verbose', 'random_state', 'boosting_type', 'objective', 'metric']:
                                 current_value = getattr(self.config, config_param_name)
                                 if current_value != param_value:
                                     logger.warning(f"Synchronisation paramètre LGBM: {config_param_name}={param_value} (était {current_value})")
                                     setattr(self.config, config_param_name, param_value)
                         logger.info("Paramètres LGBM synchronisés avec la configuration")
                 except NotFittedError as nfe:
                     raise ValueError(f"Le modèle '{name}' chargé depuis le fichier n'est pas 'fit'.") from nfe
                 except Exception as e_check_fit:
                      raise RuntimeError(f"Erreur lors de la vérification 'fit' du modèle '{name}': {e_check_fit}") from e_check_fit

            if ui_available:
                 self._update_progress(85, "Chargement LSTM...")
            lstm_state_dict = loaded_package.get('lstm_state')
            config_source = loaded_package.get('config_details', {})

            self.lstm, self.optimizer, self.scheduler = None, None, None
            lstm_created_and_loaded = False

            try:
                lstm_params_from_file = {}
                if isinstance(config_source, dict):
                     lstm_params_from_file['input_size'] = int(config_source.get('lstm_input_size', 6))
                     lstm_params_from_file['hidden_dim'] = int(config_source.get('lstm_hidden_dim', self.config.lstm_hidden_dim))
                     lstm_params_from_file['num_layers'] = int(config_source.get('lstm_num_layers', self.config.lstm_num_layers))
                     lstm_params_from_file['dropout_prob'] = float(config_source.get('dropout_prob', self.config.lstm_dropout))
                     lstm_params_from_file['bidirectional'] = bool(config_source.get('lstm_bidirectional', self.config.lstm_bidirectional))
                     lstm_params_from_file['output_size'] = 2

                     if not (lstm_params_from_file['input_size'] > 0 and lstm_params_from_file['hidden_dim'] > 0 and
                             lstm_params_from_file['num_layers'] > 0 and 0.0 <= lstm_params_from_file['dropout_prob'] < 1.0):
                          raise ValueError(f"Paramètres LSTM invalides extraits/fallback: {lstm_params_from_file}")
                     logger.info(f"Paramètres LSTM OK depuis config_details/fallback: {lstm_params_from_file}")
                else:
                     logger.error("Source config LSTM ('config_details') invalide ou manquante. Impossible de recréer LSTM.")
                     lstm_params_from_file = None

            except (ValueError, TypeError, KeyError, AttributeError) as e_p:
                 logger.error(f"Erreur extraction/validation paramètres LSTM: {e_p}", exc_info=True)
                 lstm_params_from_file = None

            if lstm_params_from_file and lstm_state_dict:
                 try:
                      # S'assurer que les dimensions du modèle LSTM sont cohérentes
                      # Si lstm_hidden_dim et lstm_hidden_size sont différents, les synchroniser
                      if 'hidden_dim' in lstm_params_from_file and hasattr(self.config, 'lstm_hidden_size'):
                          if lstm_params_from_file['hidden_dim'] != self.config.lstm_hidden_size:
                              logger.warning(f"Synchronisation des dimensions LSTM: hidden_dim={lstm_params_from_file['hidden_dim']}, lstm_hidden_size={self.config.lstm_hidden_size}")
                              # Utiliser hidden_dim comme valeur de référence
                              self.config.lstm_hidden_size = lstm_params_from_file['hidden_dim']
                              logger.warning(f"Dimensions LSTM synchronisées: hidden_dim=lstm_hidden_size={lstm_params_from_file['hidden_dim']}")

                      # Créer le modèle LSTM
                      lstm = EnhancedLSTMModel(**lstm_params_from_file)

                      # Optimiser le modèle LSTM pour une meilleure utilisation de la mémoire
                      # Utiliser training_mode=True pour conserver les gradients nécessaires à l'entraînement
                      lstm = optimize_lstm_memory(lstm, training_mode=True)

                      # Vérifier si les dimensions correspondent avant de tenter de charger les poids
                      try:
                          # Vérifier si les dimensions correspondent
                          target_state_dict = lstm.state_dict()
                          can_load = True
                          input_size_mismatch = False

                          # Vérifier spécifiquement les dimensions de la couche d'entrée
                          for key in lstm_state_dict:
                              if key in target_state_dict:
                                  if lstm_state_dict[key].shape != target_state_dict[key].shape:
                                      # Vérifier si c'est la couche d'entrée qui pose problème
                                      if 'lstm.weight_ih_l0' in key:
                                          input_size_mismatch = True
                                          logger.warning(f"Dimensions incompatibles pour la couche d'entrée LSTM: source={lstm_state_dict[key].shape}, cible={target_state_dict[key].shape}")
                                      can_load = False
                                      logger.warning(f"Dimensions incompatibles pour {key}: source={lstm_state_dict[key].shape}, cible={target_state_dict[key].shape}")

                          # Si le problème est lié à la taille d'entrée, adapter la configuration
                          if input_size_mismatch:
                              # Extraire la taille d'entrée du modèle sauvegardé
                              for key in lstm_state_dict:
                                  if 'lstm.weight_ih_l0' in key:
                                      saved_input_size = lstm_state_dict[key].shape[1] // 4  # Diviser par 4 car le poids contient les portes i, f, g, o
                                      logger.warning(f"Taille d'entrée du modèle LSTM sauvegardé: {saved_input_size}, taille actuelle: {lstm_params_from_file['input_size']}")

                                      # Adapter la configuration
                                      if saved_input_size != lstm_params_from_file['input_size']:
                                          logger.info(f"Adaptation de la taille d'entrée LSTM de {lstm_params_from_file['input_size']} à {saved_input_size}")

                                          # Mettre à jour la configuration
                                          self.config.lstm_input_size = saved_input_size

                                          # Recréer le modèle LSTM avec la nouvelle taille d'entrée
                                          lstm_params_from_file['input_size'] = saved_input_size
                                          lstm = EnhancedLSTMModel(**lstm_params_from_file)
                                          lstm = optimize_lstm_memory(lstm, training_mode=True)

                                          # Vérifier à nouveau les dimensions
                                          target_state_dict = lstm.state_dict()
                                          can_load = True

                                          for k in lstm_state_dict:
                                              if k in target_state_dict and lstm_state_dict[k].shape != target_state_dict[k].shape:
                                                  can_load = False
                                                  logger.warning(f"Dimensions toujours incompatibles après adaptation pour {k}: source={lstm_state_dict[k].shape}, cible={target_state_dict[k].shape}")

                                      break

                          if can_load:
                              # Charger l'état du modèle si les dimensions correspondent
                              lstm.load_state_dict(lstm_state_dict)
                              logger.info(f"État LSTM chargé avec succès (dimensions compatibles)")
                          else:
                              # Si les dimensions ne correspondent pas, initialiser avec de nouveaux poids
                              logger.warning("Dimensions du modèle LSTM incompatibles avec l'état sauvegardé. Initialisation avec de nouveaux poids.")
                              # Pas besoin d'appeler load_state_dict, le modèle est déjà initialisé avec des poids aléatoires

                          # Déplacer le modèle sur le device approprié
                          self.lstm = lstm.to(self.device)
                          self.lstm.eval()
                          self.optimizer = optim.AdamW(self.lstm.parameters(), lr=self.config.lstm_learning_rate, weight_decay=self.config.lstm_weight_decay)
                          # Correction précédente: suppression de verbose=False
                          self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', factor=self.config.scheduler_factor, patience=self.config.scheduler_patience)
                          lstm_created_and_loaded = True
                          logger.info(f"Modèle LSTM créé et initialisé (input_size={lstm_params_from_file.get('input_size')}).")
                      except Exception as e_load:
                          # Capturer spécifiquement les erreurs de chargement du state_dict
                          logger.warning(f"Erreur lors du chargement du state_dict LSTM: {e_load}")
                          # Continuer avec le modèle initialisé avec des poids aléatoires
                          self.lstm = lstm.to(self.device)
                          self.lstm.eval()
                          self.optimizer = optim.AdamW(self.lstm.parameters(), lr=self.config.lstm_learning_rate, weight_decay=self.config.lstm_weight_decay)
                          self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', factor=self.config.scheduler_factor, patience=self.config.scheduler_patience)
                          lstm_created_and_loaded = True
                          logger.info(f"Modèle LSTM créé avec de nouveaux poids après erreur de chargement (input_size={lstm_params_from_file.get('input_size')}).")
                 except Exception as e_l:
                      logger.error(f"Erreur CRITIQUE lors du chargement du state_dict LSTM (incompatible?): {e_l}", exc_info=True)
                      self.lstm, self.optimizer, self.scheduler = None, None, None
                      if ui_available:
                           messagebox.showwarning("Erreur Chargement LSTM", f"Impossible de charger l'état LSTM:\n{e_l}\n\nLSTM sera désactivé.")
                      else:
                           logger.error("Erreur Chargement LSTM (UI non disponible): " + str(e_l))
            else:
                 if not lstm_params_from_file: logger.warning("Paramètres LSTM non trouvés/valides.")
                 if not lstm_state_dict: logger.warning("État LSTM (state_dict) non trouvé.")
                 logger.warning("Modèle LSTM non chargé/recréé.")

            if ui_available:
                 self._update_progress(90, "Chargement Markov...")
            markov_data_loaded = loaded_package.get('markov_models')
            markov_loaded = False
            if markov_data_loaded and self.markov:
                try:
                    # Extraire les paramètres Markov du modèle chargé
                    if isinstance(markov_data_loaded, dict) and 'params' in markov_data_loaded:
                        markov_params = markov_data_loaded.get('params', {})
                        # Synchroniser les paramètres Markov avec la configuration
                        if 'max_order' in markov_params and hasattr(self.config, 'max_markov_order'):
                            max_order = markov_params['max_order']
                            if self.config.max_markov_order != max_order:
                                logger.warning(f"Synchronisation paramètre Markov: max_markov_order={max_order} (était {self.config.max_markov_order})")
                                self.config.max_markov_order = max_order
                        if 'smoothing' in markov_params and hasattr(self.config, 'markov_smoothing'):
                            smoothing = markov_params['smoothing']
                            if self.config.markov_smoothing != smoothing:
                                logger.warning(f"Synchronisation paramètre Markov: markov_smoothing={smoothing} (était {self.config.markov_smoothing})")
                                self.config.markov_smoothing = smoothing
                        logger.info("Paramètres Markov synchronisés avec la configuration")

                    self.markov.load_models(markov_data_loaded)
                    markov_loaded = True
                    logger.info("Modèles Markov chargés.")
                except Exception as e_m:
                    logger.error(f"Échec chargement modèles Markov: {e_m}", exc_info=True)
                    if self.markov: self.markov.reset(reset_type='hard')
                    if ui_available:
                         messagebox.showwarning("Erreur Chargement Markov", f"Impossible de charger l'état Markov:\n{e_m}\n\nMarkov sera réinitialisé.")
                    else:
                         logger.error("Erreur Chargement Markov (UI non disponible pour message): " + str(e_m))
            elif self.markov:
                logger.warning("Données Markov ('markov_models') non trouvées. Markov reste initialisé.")
            else:
                 logger.warning("Instance Markov (self.markov) non disponible, chargement ignoré.")

            if ui_available:
                 self._update_progress(95, "Chargement état session...")
            loaded_sequence = loaded_package.get('sequence')
            if isinstance(loaded_sequence, list) and all(isinstance(s, str) for s in loaded_sequence):
                  self.sequence = loaded_sequence
            else: self.sequence = []; logger.warning("Données 'sequence' invalides ou manquantes, réinitialisées.")
            loaded_pred_hist = loaded_package.get('prediction_history')
            if isinstance(loaded_pred_hist, list) and all(isinstance(p, dict) for p in loaded_pred_hist):
                  self.prediction_history = loaded_pred_hist
            else: self.prediction_history = []; logger.warning("Données 'prediction_history' invalides ou manquantes, réinitialisées.")
            loaded_weights = loaded_package.get('weights')
            expected_weight_keys = set(self.config.initial_weights.keys())
            if isinstance(loaded_weights, dict) and \
               set(loaded_weights.keys()) == expected_weight_keys and \
               all(isinstance(v, (float, int)) and np.isfinite(v) for v in loaded_weights.values()):
                  sum_w = sum(loaded_weights.values())
                  self.weights = {k: v / sum_w for k, v in loaded_weights.items()} if sum_w > 1e-9 else self.config.initial_weights.copy()
            else: self.weights = self.config.initial_weights.copy(); logger.warning("Données 'weights' invalides, utilisation poids initiaux.")
            loaded_perf = loaded_package.get('method_performance', {})
            self._initialize_method_performance()
            if isinstance(loaded_perf, dict):
                valid_perf_items = True
                for method, data in loaded_perf.items():
                    if method in self.method_performance and isinstance(data, dict) and \
                       all(k in data for k in ['correct', 'total']) and \
                       isinstance(data.get('correct'), int) and data['correct'] >= 0 and \
                       isinstance(data.get('total'), int) and data['total'] >= data['correct']:
                        acc_hist_data = data.get('accuracy_history')
                        if isinstance(acc_hist_data, (list, deque)) and \
                           all(isinstance(x, (int, float)) and np.isfinite(x) for x in acc_hist_data):
                            if isinstance(self.method_performance[method]['accuracy_history'], deque):
                                 max_len = self.method_performance[method]['accuracy_history'].maxlen
                                 self.method_performance[method]['accuracy_history'] = deque(acc_hist_data, maxlen=max_len)
                            else:
                                 self.method_performance[method]['accuracy_history'] = list(acc_hist_data)
                            self.method_performance[method]['correct'] = data['correct']
                            self.method_performance[method]['total'] = data['total']
                        else:
                             logger.warning(f"Données 'accuracy_history' invalides pour '{method}' dans method_performance, ignorées.")
                             valid_perf_items = False
                    elif method in self.method_performance:
                        logger.warning(f"Données 'method_performance' invalides pour '{method}', ignorées.")
                        valid_perf_items = False
                if valid_perf_items: logger.debug("Chargement 'method_performance' OK (ou partiel).")
            else: logger.warning("'method_performance' n'est pas un dict valide, ignoré.")
            loaded_best_acc = loaded_package.get('best_accuracy', 0.5)
            if isinstance(loaded_best_acc, (int, float)) and np.isfinite(loaded_best_acc):
                 self.best_accuracy = float(loaded_best_acc)
            else:
                 logger.warning("Donnée 'best_accuracy' invalide, utilisation de 5.")
                 self.best_accuracy = 0.5
            loaded_best_weights = loaded_package.get('best_weights')
            if isinstance(loaded_best_weights, dict) and \
               set(loaded_best_weights.keys()) == expected_weight_keys and \
               all(isinstance(v, (float, int)) and np.isfinite(v) for v in loaded_best_weights.values()):
                  sum_bw = sum(loaded_best_weights.values())
                  self.best_weights = {k: v / sum_bw for k, v in loaded_best_weights.items()} if sum_bw > 1e-9 else self.config.initial_weights.copy()
            else: self.best_weights = self.config.initial_weights.copy(); logger.warning("Données 'best_weights' invalides, utilisation poids initiaux.")
            loaded_last_train_time = loaded_package.get('last_train_time')
            if isinstance(loaded_last_train_time, float):
                 self.last_train_time = loaded_last_train_time
            else: self.last_train_time = 0.0; logger.warning("Donnée 'last_train_time' invalide, réinitialisée.")
            loaded_last_save_time = loaded_package.get('last_save_time')
            if isinstance(loaded_last_save_time, float):
                 self.last_save_time = loaded_last_save_time
            else: self.last_save_time = 0.0; logger.warning("Donnée 'last_save_time' invalide, réinitialisée.")
            loaded_config_details = loaded_package.get('config_details')
            if isinstance(loaded_config_details, dict):
                 self.config_details = loaded_config_details
                 logger.info("Détails de configuration chargés.")

                 # Charger la phase d'optimisation si elle existe dans les détails de configuration
                 if 'optimization_phase' in loaded_config_details:
                     optimization_phase = loaded_config_details.get('optimization_phase')
                     if optimization_phase is not None:
                         self.config.optimization_phase = optimization_phase
                         logger.info(f"Phase d'optimisation chargée: {optimization_phase}")
                     else:
                         logger.debug("Phase d'optimisation trouvée mais None, aucune modification apportée.")
                 else:
                     logger.debug("Aucune phase d'optimisation trouvée dans les détails de configuration.")

                 # Vérifier la cohérence entre les hyperparamètres du modèle chargé et la configuration actuelle
                 loaded_params = loaded_config_details.get('hyperparameters', {})
                 if loaded_params and isinstance(loaded_params, dict):
                     # Comparer les hyperparamètres importants
                     important_params = [
                         'min_confidence_for_recommendation', 'error_pattern_threshold',
                         'transition_uncertainty_threshold', 'wait_optimizer_confidence_threshold',
                         'lstm_hidden_dim', 'lstm_num_layers', 'lstm_dropout', 'lstm_bidirectional',
                         'lgbm_n_estimators', 'lgbm_learning_rate', 'lgbm_max_depth', 'lgbm_num_leaves'
                     ]

                     mismatched_params = []
                     for param in important_params:
                         if param in loaded_params and hasattr(self.config, param):
                             loaded_value = loaded_params[param]
                             current_value = getattr(self.config, param)
                             if loaded_value != current_value:
                                 mismatched_params.append((param, loaded_value, current_value))

                     if mismatched_params:
                         mismatch_msg = "⚠️ ATTENTION: Incohérence entre les hyperparamètres du modèle chargé et la configuration actuelle:\n\n"
                         for param, loaded_val, current_val in mismatched_params:
                             mismatch_msg += f"• {param}: Modèle={loaded_val}, Config={current_val}\n"

                         mismatch_msg += "\nCette incohérence peut entraîner des comportements imprévisibles ou des performances dégradées."
                         mismatch_msg += "\n\nOptions recommandées:\n"
                         mismatch_msg += "1. Utiliser 'Charger Modèles Pré-entraînés Optimisés' pour charger à la fois les modèles ET les paramètres\n"
                         mismatch_msg += "2. Appliquer les paramètres du modèle à la configuration actuelle\n"
                         mismatch_msg += "3. Continuer avec cette incohérence (non recommandé)"

                         logger.warning("Incohérence détectée entre les hyperparamètres du modèle et la configuration")

                         if ui_available:
                             # Demander à l'utilisateur ce qu'il souhaite faire
                             response = messagebox.askyesnocancel(
                                 "Incohérence Détectée",
                                 mismatch_msg + "\n\nSouhaitez-vous appliquer les paramètres du modèle à la configuration actuelle?\n\n"
                                 "• Oui: Appliquer les paramètres du modèle\n"
                                 "• Non: Conserver la configuration actuelle (non recommandé)\n"
                                 "• Annuler: Annuler le chargement du modèle",
                                 icon="warning"
                             )

                             if response is None:  # Annuler
                                 logger.info("Chargement du modèle annulé par l'utilisateur en raison d'incohérences")
                                 return False
                             elif response:  # Oui
                                 # Appliquer les paramètres du modèle à la configuration
                                 for param, loaded_val, _ in mismatched_params:
                                     setattr(self.config, param, loaded_val)
                                 logger.info("Paramètres du modèle appliqués à la configuration")
                                 messagebox.showinfo(
                                     "Paramètres Appliqués",
                                     "Les paramètres du modèle ont été appliqués à la configuration actuelle."
                                 )
                             else:  # Non
                                 logger.warning("L'utilisateur a choisi de conserver la configuration actuelle malgré les incohérences")
                                 messagebox.showwarning(
                                     "Configuration Conservée",
                                     "La configuration actuelle a été conservée malgré les incohérences.\n"
                                     "Cela peut entraîner des comportements imprévisibles ou des performances dégradées."
                                 )
                         else:
                             # En mode non-UI, appliquer automatiquement les paramètres du modèle
                             for param, loaded_val, _ in mismatched_params:
                                 setattr(self.config, param, loaded_val)
                             logger.info("Paramètres du modèle automatiquement appliqués à la configuration (mode non-UI)")
                 else:
                     logger.debug("Aucun hyperparamètre trouvé dans les détails de configuration.")
            else:
                 self.config_details = {}
                 logger.warning("Détails de configuration ('config_details') invalides ou manquants.")

            logger.info("Chargement de l'état de session terminé.")
            success_status = True

        except (IOError, ValueError, pickle.UnpicklingError, EOFError, NotFittedError, RuntimeError) as e:
            logger.error(f"Échec critique du chargement depuis {filepath}: {e}", exc_info=True)
            if ui_available:
                messagebox.showerror("Erreur Chargement", f"Impossible de charger l'état:\n{e}\n\nLe prédicteur n'a pas été chargé.")
            success_status = False
        except Exception as e_global:
            logger.critical(f"Erreur INATTENDUE lors du chargement: {e_global}", exc_info=True)
            if ui_available:
                messagebox.showerror("Erreur Inattendue", f"Une erreur imprévue est survenue:\n{e_global}")
            success_status = False
        finally:
            # Réinitialiser le flag _loading_existing_model
            self._loading_existing_model = False
            logger.debug("Flag _loading_existing_model réinitialisé à False")

            if locks_acquired:
                try: self.weights_lock.release()
                except RuntimeError: pass
                try: self.training_lock.release()
                except RuntimeError: pass
                if markov_lock_to_use:
                    try: markov_lock_to_use.release()
                    except RuntimeError: pass
                try: self.model_lock.release()
                except RuntimeError: pass
                try: self.sequence_lock.release()
                except RuntimeError: pass
                logger.debug("load_trained_models: Verrous libérés.")
            if ui_available:
                self._update_progress(100, "Chargement terminé." if success_status else "Échec chargement.")
                self.root.after(1500, lambda: self._update_progress(0, ""))

            if success_status:
                logger.info(f"État chargé avec succès depuis {filepath}.")
                if hasattr(self, 'lgbm_base') and self.lgbm_base and hasattr(self.lgbm_base, '_clear_cached_props'):
                    try: self.lgbm_base._clear_cached_props(); logger.debug("Cache LGBM base vidé.")
                    except Exception as e_cache: logger.warning(f"Erreur vidage cache LGBM base: {e_cache}")
                if hasattr(self, 'lgbm_uncertainty') and self.lgbm_uncertainty and hasattr(self.lgbm_uncertainty, 'estimators_'):
                    for est in self.lgbm_uncertainty.estimators_:
                        if hasattr(est, '_clear_cached_props'):
                            try: est._clear_cached_props()
                            except Exception as e_cache_bag: logger.warning(f"Erreur vidage cache estimateur Bagging: {e_cache_bag}")
                    logger.debug("Cache estimateurs LGBM uncertainty vidé.")

                if ui_available:
                    # Correction: Commenter l'appel à la méthode inexistante
                    # self._update_ui_after_load()
                    logger.info("Mise à jour UI après chargement ignorée (méthode _update_ui_after_load non trouvée).")
            else:
                logger.error("Le chargement de l'état a échoué.")

        return success_status