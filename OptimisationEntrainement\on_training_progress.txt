# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11993 à 11997
# Type: Méthode de la classe HybridBaccaratPredictor

        def on_training_progress(progress, message):
            if ui_available:
                # Utiliser root.after pour mettre à jour la progression dans le thread UI
                # au lieu d'appeler directement self._update_progress
                self.root.after(0, lambda p=progress, m=message: self._update_progress(p, m))