# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13893 à 13952
# Type: Méthode de la classe MetaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, seed=None, consider_prior=True, prior_weight=1.0, consider_magic_clip=True,
                 consider_endpoints=False, n_startup_trials=10, n_ei_candidates=24,
                 gamma=None, weights=None, **kwargs):
        """
        Initialise le méta-optimiseur avec des paramètres avancés.

        Args:
            seed: Graine pour la reproductibilité
            consider_prior: Considérer les distributions a priori
            prior_weight: Poids des distributions a priori
            consider_magic_clip: Utiliser le clipping magique
            consider_endpoints: Considérer les points extrêmes
            n_startup_trials: Nombre d'essais avant d'utiliser TPE
            n_ei_candidates: Nombre de candidats pour l'amélioration espérée
            gamma: Paramètre gamma pour TPE
            weights: Poids pour TPE
            **kwargs: Arguments supplémentaires
        """
        # Extraire search_space des kwargs pour éviter de le passer à TPESampler
        search_space = kwargs.pop('search_space', None)

        super().__init__(
            seed=seed,
            consider_prior=consider_prior,
            prior_weight=prior_weight,
            consider_magic_clip=consider_magic_clip,
            consider_endpoints=consider_endpoints,
            n_startup_trials=n_startup_trials,
            n_ei_candidates=n_ei_candidates,
            gamma=gamma,
            weights=weights,
            **kwargs
        )

        # Initialiser les attributs pour le suivi des essais problématiques
        self.problematic_regions = {}

        # Paramètres de configuration
        self.config = kwargs.get('config', None)

        # Seuils pour identifier les essais problématiques
        self.error_threshold = 0.5  # Seuil d'erreur pour considérer un essai comme problématique
        self.warning_threshold = 0.3  # Seuil d'avertissement

        # Compteurs pour les statistiques
        self.total_trials = 0
        self.problematic_count = 0

        # Extraire les paramètres spécifiques au méta-optimiseur
        self.use_adaptive_sampling = kwargs.pop('use_adaptive_sampling', True)
        self.use_success_history = kwargs.pop('use_success_history', True)
        self.success_history_weight = kwargs.pop('success_history_weight', 0.7)

        # Stockage de l'espace de recherche restreint
        self.restricted_search_space = search_space or {}

        # Stockage des essais problématiques
        self.problematic_trials = []

        logger.warning("MetaOptimizer initialisé avec des fonctionnalités avancées")