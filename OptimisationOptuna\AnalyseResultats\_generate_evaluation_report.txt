# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 1432 à 1730
# Type: Méthode de la classe OptunaOptimizer

    def _generate_evaluation_report(self, config, evaluation_results, robustness_results=None,
                                   include_plots=True, output_format='text', output_file=None):
        """
        Génère un rapport détaillé d'évaluation d'une configuration.
        Cette méthode permet de créer un rapport complet avec des statistiques,
        des visualisations et des recommandations.

        Args:
            config: Configuration évaluée
            evaluation_results: Résultats de l'évaluation (dict)
            robustness_results: Résultats de l'évaluation de robustesse (dict, optionnel)
            include_plots: Inclure des visualisations dans le rapport
            output_format: Format de sortie ('text', 'html', 'json', 'markdown')
            output_file: Chemin du fichier de sortie (si None, retourne le rapport)

        Returns:
            str: Rapport d'évaluation au format spécifié
        """
        import json
        import time
        import os
        import numpy as np
        from datetime import datetime

        # Vérifier que les résultats d'évaluation sont valides
        if not evaluation_results or not isinstance(evaluation_results, dict):
            logger.warning("Résultats d'évaluation invalides ou vides")
            return "Erreur: Résultats d'évaluation invalides ou vides"

        # Initialiser le rapport
        report = {}
        report['timestamp'] = time.time()
        report['datetime'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        report['config'] = config.copy() if config else {}
        report['evaluation_results'] = evaluation_results.copy()

        if robustness_results and isinstance(robustness_results, dict):
            report['robustness_results'] = robustness_results.copy()

        # Ajouter des métadonnées
        report['metadata'] = {
            'optimizer_version': getattr(self, 'version', '1.0.0'),
            'optimizer_name': self.__class__.__name__,
            'report_type': 'configuration_evaluation',
            'include_plots': include_plots
        }

        # Extraire les métriques principales
        main_metrics = {}

        # Score principal
        if 'score' in evaluation_results:
            main_metrics['score'] = evaluation_results['score']
        elif 'mean_score' in evaluation_results:
            main_metrics['score'] = evaluation_results['mean_score']

        # Précision
        for key in ['accuracy', 'precision', 'recall', 'f1_score', 'auc']:
            if key in evaluation_results:
                main_metrics[key] = evaluation_results[key]

        # Métriques de robustesse
        if robustness_results:
            for key in ['robustness_score', 'mean_score', 'std_score', 'cv_score']:
                if key in robustness_results:
                    main_metrics[f'robustness_{key}'] = robustness_results[key]

        report['main_metrics'] = main_metrics

        # Générer le rapport selon le format demandé
        if output_format == 'json':
            # Format JSON
            report_str = json.dumps(report, indent=2)

        elif output_format == 'html':
            # Format HTML
            html_parts = []
            html_parts.append("<!DOCTYPE html>")
            html_parts.append("<html><head>")
            html_parts.append("<title>Rapport d'évaluation de configuration</title>")
            html_parts.append("<style>")
            html_parts.append("body { font-family: Arial, sans-serif; margin: 20px; }")
            html_parts.append("h1, h2, h3 { color: #2c3e50; }")
            html_parts.append("table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }")
            html_parts.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }")
            html_parts.append("th { background-color: #f2f2f2; }")
            html_parts.append("tr:nth-child(even) { background-color: #f9f9f9; }")
            html_parts.append(".metric-good { color: green; }")
            html_parts.append(".metric-bad { color: red; }")
            html_parts.append(".metric-neutral { color: orange; }")
            html_parts.append("</style>")
            html_parts.append("</head><body>")

            # En-tête
            html_parts.append(f"<h1>Rapport d'évaluation de configuration</h1>")
            html_parts.append(f"<p>Date: {report['datetime']}</p>")

            # Métriques principales
            html_parts.append("<h2>Métriques principales</h2>")
            html_parts.append("<table>")
            html_parts.append("<tr><th>Métrique</th><th>Valeur</th></tr>")

            for key, value in main_metrics.items():
                # Déterminer la classe CSS selon la valeur
                css_class = "metric-neutral"
                if key in ['score', 'accuracy', 'precision', 'recall', 'f1_score', 'auc', 'robustness_score']:
                    if value > 0.8:
                        css_class = "metric-good"
                    elif value < 0.5:
                        css_class = "metric-bad"

                html_parts.append(f"<tr><td>{key}</td><td class='{css_class}'>{value:.4f}</td></tr>")

            html_parts.append("</table>")

            # Configuration
            html_parts.append("<h2>Configuration</h2>")
            html_parts.append("<table>")
            html_parts.append("<tr><th>Paramètre</th><th>Valeur</th></tr>")

            for key, value in config.items():
                html_parts.append(f"<tr><td>{key}</td><td>{value}</td></tr>")

            html_parts.append("</table>")

            # Résultats d'évaluation détaillés
            html_parts.append("<h2>Résultats d'évaluation détaillés</h2>")
            html_parts.append("<table>")
            html_parts.append("<tr><th>Métrique</th><th>Valeur</th></tr>")

            for key, value in evaluation_results.items():
                if key not in main_metrics:
                    if isinstance(value, (int, float)):
                        html_parts.append(f"<tr><td>{key}</td><td>{value:.4f}</td></tr>")
                    else:
                        html_parts.append(f"<tr><td>{key}</td><td>{value}</td></tr>")

            html_parts.append("</table>")

            # Résultats de robustesse
            if robustness_results:
                html_parts.append("<h2>Résultats de robustesse</h2>")
                html_parts.append("<table>")
                html_parts.append("<tr><th>Métrique</th><th>Valeur</th></tr>")

                for key, value in robustness_results.items():
                    if key not in main_metrics and isinstance(value, (int, float)):
                        html_parts.append(f"<tr><td>{key}</td><td>{value:.4f}</td></tr>")

                html_parts.append("</table>")

            # Pied de page
            html_parts.append("<hr>")
            html_parts.append(f"<p>Généré par {report['metadata']['optimizer_name']} v{report['metadata']['optimizer_version']}</p>")
            html_parts.append("</body></html>")

            report_str = "\n".join(html_parts)

        elif output_format == 'markdown':
            # Format Markdown
            md_parts = []
            md_parts.append("# Rapport d'évaluation de configuration")
            md_parts.append(f"Date: {report['datetime']}")
            md_parts.append("")

            # Métriques principales
            md_parts.append("## Métriques principales")
            md_parts.append("")
            md_parts.append("| Métrique | Valeur |")
            md_parts.append("| --- | --- |")

            for key, value in main_metrics.items():
                if isinstance(value, (int, float)):
                    md_parts.append(f"| {key} | {value:.4f} |")
                else:
                    md_parts.append(f"| {key} | {value} |")

            md_parts.append("")

            # Configuration
            md_parts.append("## Configuration")
            md_parts.append("")
            md_parts.append("| Paramètre | Valeur |")
            md_parts.append("| --- | --- |")

            for key, value in config.items():
                md_parts.append(f"| {key} | {value} |")

            md_parts.append("")

            # Résultats d'évaluation détaillés
            md_parts.append("## Résultats d'évaluation détaillés")
            md_parts.append("")
            md_parts.append("| Métrique | Valeur |")
            md_parts.append("| --- | --- |")

            for key, value in evaluation_results.items():
                if key not in main_metrics:
                    if isinstance(value, (int, float)):
                        md_parts.append(f"| {key} | {value:.4f} |")
                    else:
                        md_parts.append(f"| {key} | {value} |")

            md_parts.append("")

            # Résultats de robustesse
            if robustness_results:
                md_parts.append("## Résultats de robustesse")
                md_parts.append("")
                md_parts.append("| Métrique | Valeur |")
                md_parts.append("| --- | --- |")

                for key, value in robustness_results.items():
                    if key not in main_metrics and isinstance(value, (int, float)):
                        md_parts.append(f"| {key} | {value:.4f} |")

                md_parts.append("")

            # Pied de page
            md_parts.append("---")
            md_parts.append(f"Généré par {report['metadata']['optimizer_name']} v{report['metadata']['optimizer_version']}")

            report_str = "\n".join(md_parts)

        else:
            # Format texte (par défaut)
            text_parts = []
            text_parts.append("=" * 80)
            text_parts.append(f"RAPPORT D'ÉVALUATION DE CONFIGURATION")
            text_parts.append(f"Date: {report['datetime']}")
            text_parts.append("=" * 80)
            text_parts.append("")

            # Métriques principales
            text_parts.append("MÉTRIQUES PRINCIPALES")
            text_parts.append("-" * 80)

            for key, value in main_metrics.items():
                if isinstance(value, (int, float)):
                    text_parts.append(f"{key:30s}: {value:.4f}")
                else:
                    text_parts.append(f"{key:30s}: {value}")

            text_parts.append("")

            # Configuration
            text_parts.append("CONFIGURATION")
            text_parts.append("-" * 80)

            for key, value in config.items():
                text_parts.append(f"{key:30s}: {value}")

            text_parts.append("")

            # Résultats d'évaluation détaillés
            text_parts.append("RÉSULTATS D'ÉVALUATION DÉTAILLÉS")
            text_parts.append("-" * 80)

            for key, value in evaluation_results.items():
                if key not in main_metrics:
                    if isinstance(value, (int, float)):
                        text_parts.append(f"{key:30s}: {value:.4f}")
                    else:
                        text_parts.append(f"{key:30s}: {value}")

            text_parts.append("")

            # Résultats de robustesse
            if robustness_results:
                text_parts.append("RÉSULTATS DE ROBUSTESSE")
                text_parts.append("-" * 80)

                for key, value in robustness_results.items():
                    if key not in main_metrics and isinstance(value, (int, float)):
                        text_parts.append(f"{key:30s}: {value:.4f}")

                text_parts.append("")

            # Pied de page
            text_parts.append("=" * 80)
            text_parts.append(f"Généré par {report['metadata']['optimizer_name']} v{report['metadata']['optimizer_version']}")

            report_str = "\n".join(text_parts)

        # Sauvegarder le rapport si un fichier de sortie est spécifié
        if output_file:
            try:
                # Créer le répertoire parent si nécessaire
                os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)

                # Écrire le rapport dans le fichier
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_str)

                logger.warning(f"Rapport d'évaluation sauvegardé dans {output_file}")
            except Exception as e:
                logger.warning(f"Erreur lors de la sauvegarde du rapport: {e}")

        return report_str