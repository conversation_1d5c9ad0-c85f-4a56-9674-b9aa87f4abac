# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14326 à 14391
# Type: Méthode de la classe MetaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _identify_problematic_params(self) -> None:
        """
        Identifie les paramètres qui sont probablement responsables des essais problématiques.
        """
        if len(self.problematic_trials) < self.min_problematic_trials:
            return

        # Regrouper les essais par type de problème
        too_few_wait_trials = [t for t in self.problematic_trials if t['too_few_wait']]
        too_many_wait_trials = [t for t in self.problematic_trials if t['too_many_wait']]

        # Analyser les paramètres pour les essais avec trop peu de WAIT
        if len(too_few_wait_trials) >= self.min_problematic_trials:
            for param in self.key_wait_ratio_params:
                values = [t['params'].get(param, None) for t in too_few_wait_trials if param in t['params']]
                if len(values) >= self.min_problematic_trials:
                    # Calculer les statistiques des valeurs
                    values = [v for v in values if v is not None]
                    if not values:
                        continue

                    mean_value = np.mean(values)
                    std_value = np.std(values)

                    # Identifier les plages problématiques
                    if param not in self.problematic_params:
                        self.problematic_params[param] = {'too_few_wait': [], 'too_many_wait': []}

                    # Ajouter la plage problématique
                    self.problematic_params[param]['too_few_wait'].append({
                        'mean': mean_value,
                        'std': std_value,
                        'min': max(0, mean_value - std_value),
                        'max': mean_value + std_value
                    })

                    logger.warning(f"Paramètre '{param}' identifié comme problématique pour trop peu de WAIT")
                    logger.warning(f"  Plage problématique: {mean_value - std_value:.4f} - {mean_value + std_value:.4f}")

        # Analyser les paramètres pour les essais avec trop de WAIT
        if len(too_many_wait_trials) >= self.min_problematic_trials:
            for param in self.key_wait_ratio_params:
                values = [t['params'].get(param, None) for t in too_many_wait_trials if param in t['params']]
                if len(values) >= self.min_problematic_trials:
                    # Calculer les statistiques des valeurs
                    values = [v for v in values if v is not None]
                    if not values:
                        continue

                    mean_value = np.mean(values)
                    std_value = np.std(values)

                    # Identifier les plages problématiques
                    if param not in self.problematic_params:
                        self.problematic_params[param] = {'too_few_wait': [], 'too_many_wait': []}

                    # Ajouter la plage problématique
                    self.problematic_params[param]['too_many_wait'].append({
                        'mean': mean_value,
                        'std': std_value,
                        'min': max(0, mean_value - std_value),
                        'max': mean_value + std_value
                    })

                    logger.warning(f"Paramètre '{param}' identifié comme problématique pour trop de WAIT")
                    logger.warning(f"  Plage problématique: {mean_value - std_value:.4f} - {mean_value + std_value:.4f}")