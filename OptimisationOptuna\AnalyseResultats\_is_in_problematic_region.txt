# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14125 à 14206
# Type: Méthode de la classe MetaOptimizer

    def _is_in_problematic_region(self, params):
        """
        Vérifie si un ensemble de paramètres est dans une région problématique.

        Args:
            params: Dictionnaire de paramètres

        Returns:
            bool: True si les paramètres sont dans une région problématique
        """
        for param_name, param_value in params.items():
            if self._is_param_in_problematic_region(param_name, param_value):
                return True
        return False

        # Critères pour considérer un essai comme réussi
        self.success_criteria = {
            'min_score': 0.65,                # Score minimum pour considérer un essai comme réussi
            'optimal_wait_ratio_min': 0.15,   # Ratio WAIT minimum optimal
            'optimal_wait_ratio_max': 0.35,   # Ratio WAIT maximum optimal
            'min_recommendation_rate': 0.5    # Taux de recommandation minimum
        }

        # Paramètres identifiés comme problématiques
        self.problematic_params = {}

        # Seuils pour considérer un essai comme problématique
        self.wait_ratio_min_threshold = 0.05  # Moins de 5% de WAIT est problématique
        self.wait_ratio_max_threshold = 0.95  # Plus de 95% de WAIT est problématique

        # Nombre minimum d'essais problématiques pour identifier un paramètre comme problématique
        self.min_problematic_trials = 3

        # Marge d'exclusion pour les paramètres problématiques
        self.exclusion_margin = 0.05

        # Compteur d'essais analysés
        self.analyzed_trials_count = 0

        # Historique des performances pour l'adaptation dynamique
        self.performance_history = {
            'scores': [],
            'wait_ratios': [],
            'recommendation_rates': []
        }

        # Paramètres clés qui influencent le ratio WAIT/NON-WAIT
        self.key_wait_ratio_params = [
            'min_confidence_for_recommendation',
            'transition_uncertainty_threshold',
            'global_uncertainty_factor',
            'wait_ratio_tolerance',
            'uncertainty_threshold',
            'error_pattern_threshold',
            'wait_optimizer_confidence_threshold',
            'lstm_learning_rate',
            'lgbm_learning_rate',
            'markov_smoothing'
        ]

        # Paramètres pour l'échantillonnage adaptatif
        self.adaptive_sampling = {
            'exploration_factor': 0.3,        # Facteur d'exploration initial
            'exploitation_factor': 0.7,       # Facteur d'exploitation initial
            'min_exploration': 0.1,           # Exploration minimale
            'adaptation_rate': 0.05           # Taux d'adaptation des facteurs
        }

        logger.warning("=" * 80)
        logger.warning("META-OPTIMISEUR AVANCÉ INITIALISÉ")
        logger.warning("Capacités activées:")
        logger.warning(f"- Échantillonnage adaptatif: {'Activé' if self.use_adaptive_sampling else 'Désactivé'}")
        logger.warning(f"- Utilisation de l'historique des succès: {'Activé' if self.use_success_history else 'Désactivé'}")
        logger.warning("Exclusion dynamique des régions produisant des résultats déséquilibrés:")
        logger.warning(f"- Trop de WAIT (>= {self.wait_ratio_max_threshold*100:.0f}% WAIT, <= {(1-self.wait_ratio_max_threshold)*100:.0f}% NON-WAIT)")
        logger.warning(f"- Trop peu de WAIT (<= {self.wait_ratio_min_threshold*100:.0f}% WAIT, >= {(1-self.wait_ratio_min_threshold)*100:.0f}% NON-WAIT)")
        logger.warning(f"Critères de succès: score >= {self.success_criteria['min_score']}, {self.success_criteria['optimal_wait_ratio_min']*100:.0f}% <= WAIT <= {self.success_criteria['optimal_wait_ratio_max']*100:.0f}%")
        if self.restricted_search_space:
            logger.warning("Espace de recherche restreint fourni:")
            for param, values in self.restricted_search_space.items():
                logger.warning(f"  {param}: {values}")
        logger.warning("=" * 80)