# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13989 à 14021
# Type: Méthode de la classe MetaOptimizer

    def _mark_trial_as_problematic(self, trial, reason):
        """
        Marque un essai comme problématique et met à jour les régions problématiques.

        Args:
            trial: Essai Optuna
            reason: Raison pour laquelle l'essai est problématique
        """
        # Incrémenter le compteur d'essais problématiques
        self.problematic_count += 1

        # Ajouter l'essai à la liste des essais problématiques
        self.problematic_trials.append({
            'trial_number': trial.number,
            'params': trial.params,
            'reason': reason
        })

        # Mettre à jour les régions problématiques
        for param_name, param_value in trial.params.items():
            if param_name not in self.problematic_regions:
                self.problematic_regions[param_name] = []

            # Ajouter la valeur problématique
            self.problematic_regions[param_name].append(param_value)

        # Afficher un avertissement
        logger.warning(f"Essai {trial.number} marqué comme problématique: {reason}")

        # Afficher des statistiques
        if self.total_trials > 0:
            problematic_ratio = self.problematic_count / self.total_trials
            logger.warning(f"Ratio d'essais problématiques: {problematic_ratio:.2f} ({self.problematic_count}/{self.total_trials})")