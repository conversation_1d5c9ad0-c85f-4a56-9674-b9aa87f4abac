# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 1732 à 2019
# Type: Méthode de la classe OptunaOptimizer

    def _plot_optimization_history(self, study, params_to_plot=None, plot_type='history',
                                  output_file=None, show_plot=True, figsize=(12, 8)):
        """
        Génère des visualisations de l'historique d'optimisation.
        Cette méthode permet de créer différents types de graphiques pour analyser
        l'évolution de l'optimisation et les relations entre les paramètres.

        Args:
            study: Étude Optuna terminée
            params_to_plot: Liste des paramètres à inclure dans les graphiques (si None, utilise les plus importants)
            plot_type: Type de graphique ('history', 'contour', 'slice', 'importance', 'parallel', 'all')
            output_file: Chemin du fichier de sortie (si None, affiche le graphique)
            show_plot: Afficher le graphique (True) ou le sauvegarder uniquement (False)
            figsize: Taille du graphique en pouces (largeur, hauteur)

        Returns:
            dict: Informations sur les graphiques générés
        """
        import matplotlib
        matplotlib.use('Agg')  # Utiliser le backend non interactif
        import matplotlib.pyplot as plt
        import numpy as np
        import os
        import optuna
        from optuna.visualization import plot_optimization_history, plot_contour, plot_slice, plot_param_importances, plot_parallel_coordinate

        # Vérifier que l'étude contient des essais
        if len(study.trials) == 0:
            logger.warning("Aucun essai à visualiser")
            return {'error': 'Aucun essai à visualiser'}

        # Filtrer les essais terminés
        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]

        if len(completed_trials) == 0:
            logger.warning("Aucun essai terminé à visualiser")
            return {'error': 'Aucun essai terminé à visualiser'}

        # Déterminer les paramètres à visualiser
        if params_to_plot is None:
            # Utiliser les paramètres les plus importants
            try:
                importances = optuna.importance.get_param_importances(study)
                # Prendre les 5 paramètres les plus importants
                params_to_plot = list(importances.keys())[:5]
            except Exception as e:
                logger.warning(f"Impossible de calculer l'importance des paramètres: {e}")
                # Utiliser tous les paramètres du meilleur essai
                params_to_plot = list(study.best_params.keys())

        # Créer le répertoire de sortie si nécessaire
        if output_file:
            os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)

        # Initialiser le dictionnaire de résultats
        results = {
            'plot_type': plot_type,
            'params_plotted': params_to_plot,
            'n_trials': len(completed_trials),
            'output_file': output_file,
            'plots_generated': []
        }

        # Fonction pour sauvegarder ou afficher un graphique
        def save_or_show_plot(fig, name):
            if output_file:
                # Créer un nom de fichier basé sur le type de graphique
                base, ext = os.path.splitext(output_file)
                if not ext:
                    ext = '.png'
                file_path = f"{base}_{name}{ext}"

                # Sauvegarder le graphique
                fig.savefig(file_path, bbox_inches='tight', dpi=300)
                logger.warning(f"Graphique '{name}' sauvegardé dans {file_path}")

                results['plots_generated'].append({
                    'name': name,
                    'path': file_path
                })

            if show_plot:
                plt.show()
            else:
                plt.close(fig)

        # Générer les graphiques selon le type demandé
        if plot_type in ['history', 'all']:
            try:
                # Graphique de l'historique d'optimisation
                fig = plt.figure(figsize=figsize)
                ax = fig.add_subplot(111)

                # Extraire les valeurs objectives et les numéros d'essai
                values = [t.value for t in completed_trials]
                trial_numbers = [t.number for t in completed_trials]

                # Tracer l'historique des valeurs objectives
                ax.plot(trial_numbers, values, 'o-', alpha=0.6)

                # Tracer la meilleure valeur jusqu'à présent
                best_values = np.minimum.accumulate(values) if study.direction == optuna.study.StudyDirection.MINIMIZE else np.maximum.accumulate(values)
                ax.plot(trial_numbers, best_values, 'r-', linewidth=2, label='Meilleure valeur')

                # Ajouter des étiquettes et une légende
                ax.set_xlabel('Numéro d\'essai')
                ax.set_ylabel('Valeur objective')
                ax.set_title('Historique d\'optimisation')
                ax.legend()
                ax.grid(True, linestyle='--', alpha=0.7)

                # Ajouter une annotation pour la meilleure valeur
                best_trial_idx = values.index(study.best_value)
                best_trial_number = trial_numbers[best_trial_idx]
                ax.annotate(f'Meilleur: {study.best_value:.4f}',
                            xy=(best_trial_number, study.best_value),
                            xytext=(10, 10), textcoords='offset points',
                            arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=.2'))

                save_or_show_plot(fig, 'history')

            except Exception as e:
                logger.warning(f"Erreur lors de la génération du graphique d'historique: {e}")

        if plot_type in ['importance', 'all']:
            try:
                # Graphique de l'importance des paramètres
                importances = optuna.importance.get_param_importances(study)

                fig = plt.figure(figsize=figsize)
                ax = fig.add_subplot(111)

                # Extraire les paramètres et leurs importances
                params = list(importances.keys())
                values = list(importances.values())

                # Limiter le nombre de paramètres affichés
                max_params = 10
                if len(params) > max_params:
                    params = params[:max_params]
                    values = values[:max_params]

                # Tracer l'importance des paramètres
                y_pos = np.arange(len(params))
                ax.barh(y_pos, values, align='center')
                ax.set_yticks(y_pos)
                ax.set_yticklabels(params)
                ax.invert_yaxis()  # Les paramètres les plus importants en haut
                ax.set_xlabel('Importance relative')
                ax.set_title('Importance des paramètres')
                ax.grid(True, linestyle='--', alpha=0.7, axis='x')

                save_or_show_plot(fig, 'importance')

            except Exception as e:
                logger.warning(f"Erreur lors de la génération du graphique d'importance: {e}")

        if plot_type in ['contour', 'all'] and len(params_to_plot) >= 2:
            try:
                # Graphique de contour pour les paires de paramètres les plus importants
                for i in range(min(len(params_to_plot), 3)):
                    for j in range(i+1, min(len(params_to_plot), 4)):
                        param_x = params_to_plot[i]
                        param_y = params_to_plot[j]

                        fig = plt.figure(figsize=figsize)
                        ax = fig.add_subplot(111)

                        # Extraire les valeurs des paramètres et les valeurs objectives
                        x_values = []
                        y_values = []
                        obj_values = []

                        for trial in completed_trials:
                            if param_x in trial.params and param_y in trial.params:
                                x_values.append(trial.params[param_x])
                                y_values.append(trial.params[param_y])
                                obj_values.append(trial.value)

                        # Créer une grille pour le contour
                        if len(x_values) > 5:  # Vérifier qu'il y a assez de points
                            # Normaliser les valeurs objectives pour la couleur
                            norm_values = np.array(obj_values)
                            if study.direction == optuna.study.StudyDirection.MINIMIZE:
                                norm_values = (norm_values - np.min(norm_values)) / (np.max(norm_values) - np.min(norm_values) + 1e-10)
                            else:
                                norm_values = 1 - (norm_values - np.min(norm_values)) / (np.max(norm_values) - np.min(norm_values) + 1e-10)

                            # Tracer les points avec une couleur basée sur la valeur objective
                            scatter = ax.scatter(x_values, y_values, c=norm_values, cmap='viridis',
                                               alpha=0.8, s=50, edgecolors='k', linewidths=0.5)

                            # Ajouter une barre de couleur
                            cbar = plt.colorbar(scatter)
                            cbar.set_label('Valeur objective (normalisée)')

                            # Marquer le meilleur point
                            best_trial = study.best_trial
                            if param_x in best_trial.params and param_y in best_trial.params:
                                ax.plot(best_trial.params[param_x], best_trial.params[param_y], 'r*',
                                       markersize=15, label='Meilleur')
                                ax.legend()

                            # Ajouter des étiquettes
                            ax.set_xlabel(param_x)
                            ax.set_ylabel(param_y)
                            ax.set_title(f'Relation entre {param_x} et {param_y}')
                            ax.grid(True, linestyle='--', alpha=0.7)

                            save_or_show_plot(fig, f'contour_{param_x}_{param_y}')
                        else:
                            plt.close(fig)

            except Exception as e:
                logger.warning(f"Erreur lors de la génération des graphiques de contour: {e}")

        if plot_type in ['parallel', 'all'] and len(params_to_plot) >= 2:
            try:
                # Graphique de coordonnées parallèles
                fig = plt.figure(figsize=figsize)
                ax = fig.add_subplot(111)

                # Extraire les données pour le graphique
                data = []
                for trial in completed_trials:
                    row = [trial.number, trial.value]
                    for param in params_to_plot:
                        if param in trial.params:
                            row.append(trial.params[param])
                        else:
                            row.append(None)
                    data.append(row)

                # Convertir en tableau numpy
                data = np.array(data)

                # Normaliser les valeurs pour chaque axe
                norm_data = data.copy()
                for i in range(2, data.shape[1]):
                    col = data[:, i]
                    valid_mask = ~np.isnan(col)
                    if np.any(valid_mask):
                        col_min = np.min(col[valid_mask])
                        col_max = np.max(col[valid_mask])
                        if col_max > col_min:
                            norm_data[valid_mask, i] = (col[valid_mask] - col_min) / (col_max - col_min)

                # Tracer les lignes pour chaque essai
                for i in range(len(data)):
                    # Couleur basée sur la valeur objective
                    if study.direction == optuna.study.StudyDirection.MINIMIZE:
                        color_val = 1 - (data[i, 1] - np.min(data[:, 1])) / (np.max(data[:, 1]) - np.min(data[:, 1]) + 1e-10)
                    else:
                        color_val = (data[i, 1] - np.min(data[:, 1])) / (np.max(data[:, 1]) - np.min(data[:, 1]) + 1e-10)

                    color = plt.cm.viridis(color_val)
                    alpha = 0.5 + 0.5 * color_val  # Plus opaque pour les meilleurs essais

                    # Tracer la ligne
                    valid_mask = ~np.isnan(norm_data[i, 2:])
                    if np.any(valid_mask):
                        x_vals = np.arange(len(params_to_plot))[valid_mask]
                        y_vals = norm_data[i, 2:][valid_mask]
                        ax.plot(x_vals, y_vals, '-', color=color, alpha=alpha, linewidth=1.5)

                # Marquer le meilleur essai
                best_idx = np.argmin(data[:, 1]) if study.direction == optuna.study.StudyDirection.MINIMIZE else np.argmax(data[:, 1])
                valid_mask = ~np.isnan(norm_data[best_idx, 2:])
                if np.any(valid_mask):
                    x_vals = np.arange(len(params_to_plot))[valid_mask]
                    y_vals = norm_data[best_idx, 2:][valid_mask]
                    ax.plot(x_vals, y_vals, 'r-', linewidth=3, label='Meilleur')

                # Configurer les axes
                ax.set_xticks(np.arange(len(params_to_plot)))
                ax.set_xticklabels(params_to_plot, rotation=45, ha='right')
                ax.set_yticks([0, 0.5, 1])
                ax.set_yticklabels(['Min', 'Moyen', 'Max'])
                ax.set_title('Coordonnées parallèles des paramètres')
                ax.grid(True, linestyle='--', alpha=0.7)
                ax.legend()

                save_or_show_plot(fig, 'parallel')

            except Exception as e:
                logger.warning(f"Erreur lors de la génération du graphique de coordonnées parallèles: {e}")

        return results