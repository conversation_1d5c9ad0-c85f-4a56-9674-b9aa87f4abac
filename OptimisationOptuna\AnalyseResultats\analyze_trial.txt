# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13954 à 13987
# Type: Méthode de la classe MetaOptimizer

    def analyze_trial(self, trial):
        """
        Analyse un essai terminé pour identifier les régions problématiques.

        Args:
            trial: Essai Optuna terminé
        """
        # Ignorer les essais non terminés
        if trial.state != optuna.trial.TrialState.COMPLETE:
            return

        # Incrémenter le compteur d'essais
        self.total_trials += 1

        # Récupérer les paramètres et la valeur de l'essai
        params = trial.params
        value = trial.value

        # Si la valeur est None ou NaN, considérer l'essai comme problématique
        if value is None or (isinstance(value, float) and math.isnan(value)):
            self._mark_trial_as_problematic(trial, "Valeur None ou NaN")
            return

        # Vérifier si l'essai est dans une région problématique
        if self._is_in_problematic_region(params):
            logger.warning(f"Essai {trial.number} dans une région problématique connue")

        # Identifier les paramètres potentiellement problématiques
        problematic_params = self._identify_problematic_params(trial)

        # Si des paramètres problématiques sont identifiés, marquer l'essai
        if problematic_params:
            reason = f"Paramètres problématiques: {', '.join(problematic_params)}"
            self._mark_trial_as_problematic(trial, reason)