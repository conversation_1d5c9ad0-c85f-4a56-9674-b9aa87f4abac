# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 988 à 1088
# Type: Méthode de la classe HybridBaccaratPredictor

    def generate_optimization_report(self, study, best_trial):
        """
        Génère un rapport détaillé d'optimisation.

        Args:
            study (optuna.study.Study): L'étude Optuna
            best_trial (optuna.trial.FrozenTrial): Le meilleur essai de l'étude

        Returns:
            str: Le rapport d'optimisation
        """
        try:
            # Créer un rapport détaillé
            report = "RAPPORT D'OPTIMISATION\n"
            report += "=====================\n\n"

            # Informations générales
            report += f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            report += f"Nombre total d'essais: {len(study.trials)}\n"
            report += f"Meilleur score: {best_trial.value:.4f}\n\n"

            # Meilleurs hyperparamètres
            report += "MEILLEURS HYPERPARAMÈTRES\n"
            report += "------------------------\n\n"

            # Organiser les hyperparamètres par catégorie
            categories = {
                'LSTM': ['lstm_'],
                'LGBM': ['lgbm_'],
                'Markov': ['markov_', 'max_order'],
                'Seuils': ['threshold', 'confidence', 'uncertainty', 'wait_'],
                'Poids': ['weight_'],
                'Autres': []
            }

            for category, prefixes in categories.items():
                report += f"{category}:\n"

                # Filtrer les hyperparamètres pour cette catégorie
                category_params = {}
                for param, value in best_trial.params.items():
                    if any(param.startswith(prefix) or prefix in param for prefix in prefixes) or (category == 'Autres' and not any(param.startswith(prefix) or prefix in param for cat, prefixes_list in categories.items() for prefix in prefixes_list if cat != 'Autres')):
                        category_params[param] = value

                # Afficher les hyperparamètres de cette catégorie
                for param, value in sorted(category_params.items()):
                    report += f"  {param}: {value}\n"

                report += "\n"

            # Métriques de performance
            report += "MÉTRIQUES DE PERFORMANCE\n"
            report += "------------------------\n\n"

            # Extraire les métriques de performance du meilleur essai
            metrics = {
                'max_consecutive': best_trial.user_attrs.get('max_consecutive', 0),
                'precision_non_wait': best_trial.user_attrs.get('precision_non_wait', 0.0),
                'wait_ratio': best_trial.user_attrs.get('wait_ratio', 0.0),
                'wait_efficiency': best_trial.user_attrs.get('wait_efficiency', 0.0),
                'recovery_rate_after_wait': best_trial.user_attrs.get('recovery_rate_after_wait', 0.0)
            }

            for metric, value in metrics.items():
                report += f"  {metric}: {value}\n"

            report += "\n"

            # Graphiques d'importance des hyperparamètres
            report += "IMPORTANCE DES HYPERPARAMÈTRES\n"
            report += "----------------------------\n\n"

            # Ajouter des informations sur l'importance des hyperparamètres
            try:
                import optuna.importance
                param_importances = optuna.importance.get_param_importances(study)
                for param, importance in param_importances.items():
                    report += f"  {param}: {importance:.4f}\n"
            except Exception as e:
                report += f"  Erreur lors du calcul de l'importance des hyperparamètres: {e}\n"

            report += "\n"

            # Historique des essais
            report += "HISTORIQUE DES ESSAIS\n"
            report += "--------------------\n\n"

            # Trier les essais par score décroissant
            sorted_trials = sorted(study.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)

            # Afficher les 10 meilleurs essais
            report += "10 meilleurs essais:\n"
            for i, trial in enumerate(sorted_trials[:10]):
                report += f"  {i+1}. Essai #{trial.number}: Score={trial.value:.4f}, Params={trial.params}\n"

            report += "\n"

            return report
        except Exception as e:
            logger.error(f"Erreur lors de la génération du rapport d'optimisation: {e}", exc_info=True)
            return f"Erreur lors de la génération du rapport d'optimisation: {e}"