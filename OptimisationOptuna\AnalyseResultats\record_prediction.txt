# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13639 à 13695
# Type: Méthode de la classe OptimizationStatsCollector

    def record_prediction(self,
                         phase: int,
                         is_wait: bool,
                         decision_threshold: float,
                         confidence: float,
                         uncertainty: float,
                         wait_ratio: float,
                         consecutive_wait: int,
                         player_prob: float,
                         banker_prob: float,
                         recommendation_score: float):
        """
        Enregistre les statistiques d'une prédiction.

        Args:
            phase: Phase d'optimisation (1 ou 2)
            is_wait: True si c'est une recommandation WAIT, False sinon
            decision_threshold: Seuil de décision utilisé
            confidence: Valeur de confiance
            uncertainty: Valeur d'incertitude
            wait_ratio: Ratio WAIT actuel
            consecutive_wait: Nombre de WAIT consécutifs
            player_prob: Probabilité pour PLAYER
            banker_prob: Probabilité pour BANKER
            recommendation_score: Score de la recommandation
        """
        # Incrémenter les compteurs généraux (seulement si nous n'avons pas déjà généré un rapport)
        # Cela évite le double comptage des prédictions
        if not self.report_generated:
            self.total_predictions += 1
            if is_wait:
                self.wait_predictions += 1
            else:
                self.non_wait_predictions += 1

        # Enregistrer les valeurs détaillées
        self.decision_thresholds.append(decision_threshold)
        self.confidence_values.append(confidence)
        self.uncertainty_values.append(uncertainty)
        self.wait_ratios.append(wait_ratio)
        self.consecutive_wait_counts.append(consecutive_wait)
        self.player_probabilities.append(player_prob)
        self.banker_probabilities.append(banker_prob)
        self.recommendation_scores.append(recommendation_score)

        # Enregistrer les statistiques par phase
        phase_key = f"Phase_{phase}"
        self.phase_stats[phase_key]['count'] += 1
        if is_wait:
            self.phase_stats[phase_key]['wait_count'] += 1
        else:
            self.phase_stats[phase_key]['non_wait_count'] += 1

        self.phase_stats[phase_key]['decision_thresholds'].append(decision_threshold)
        self.phase_stats[phase_key]['confidence_values'].append(confidence)
        self.phase_stats[phase_key]['uncertainty_values'].append(uncertainty)
        self.phase_stats[phase_key]['recommendation_scores'].append(recommendation_score)