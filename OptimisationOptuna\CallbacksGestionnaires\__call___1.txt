# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 11651 à 11653
# Type: Méthode de la classe SimplifiedTrialPrinter
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

                def __call__(self, study, trial):
                    # Ce callback ne fait rien, le filtrage est géré par OptunaMessageFilter
                    pass