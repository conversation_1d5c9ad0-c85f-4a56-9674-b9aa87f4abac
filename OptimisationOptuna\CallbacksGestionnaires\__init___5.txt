# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12776 à 12783
# Type: Méthode de la classe LSTMDynamicCallback
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

            def __init__(self, initial_lr, initial_batch_size):
                self.initial_lr = initial_lr
                self.current_lr = initial_lr
                self.initial_batch_size = initial_batch_size
                self.current_batch_size = initial_batch_size
                self.best_loss = float('inf')
                self.patience = 0
                self.max_patience = 3