# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12819 à 12822
# Type: Méthode de la classe MarkovDynamicAdapter
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

            def __init__(self, initial_smoothing):
                self.initial_smoothing = initial_smoothing
                self.current_smoothing = initial_smoothing
                self.performance_history = []