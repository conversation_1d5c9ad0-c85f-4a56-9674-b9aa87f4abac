# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13463 à 13472
# Type: Méthode de la classe OptunaThreadManager
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self):
        """Initialise le gestionnaire de thread."""
        self.threaded_optimizer = None
        self.result_queue = queue.Queue()
        self.is_running = False
        self.start_time = None
        self.callback = None
        self.error_callback = None
        self.progress_callback = None
        self.stop_requested = False