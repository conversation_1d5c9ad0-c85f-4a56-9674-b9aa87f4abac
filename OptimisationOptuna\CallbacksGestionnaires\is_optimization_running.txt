# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13474 à 13483
# Type: Méthode de la classe OptunaThreadManager

    def is_optimization_running(self):
        """
        Vérifie si une optimisation est en cours.

        Returns:
            bool: True si une optimisation est en cours, False sinon
        """
        if self.threaded_optimizer:
            return self.threaded_optimizer.is_optimization_running()
        return False