# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 12748 à 12770
# Type: Méthode de la classe OptunaOptimizer

        def lgbm_lr_callback(env):
            """Callback pour ajuster le learning rate de LGBM pendant l'entraînement."""
            # Récupérer l'itération actuelle
            iteration = env.iteration

            # Récupérer les métriques d'évaluation
            eval_result = env.evaluation_result_list

            # Récupérer le learning rate actuel
            current_lr = env.params.get('learning_rate', 0.1)

            # Ajuster le learning rate en fonction des performances
            if iteration > 0 and iteration % 10 == 0:
                # Vérifier si les performances stagnent
                if len(env.evaluation_result_list) > 0:
                    current_score = eval_result[0][2]

                    # Réduire le learning rate si les performances stagnent
                    if hasattr(env, 'best_score') and current_score <= env.best_score:
                        env.params['learning_rate'] = current_lr * 0.9
                        print(f"LGBM: Réduction du learning rate à {env.params['learning_rate']}")
                    else:
                        env.best_score = current_score