DESCRIPTIF DÉTAILLÉ DES MÉTHODES - CLASSESPRINCIPALES
==========================================================

Ce fichier contient la description détaillée de toutes les méthodes
présentes dans le sous-dossier ClassesPrincipales.

1. class_DynamicRangeAdjuster.txt (DynamicRangeAdjuster - CLASSE AJUSTEMENT PLAGES DYNAMIQUE)
   - Lignes 236-976 dans optuna_optimizer.py (Classe complète 741 lignes)
   - FONCTION : Classe complète ajustement dynamique plages hyperparamètres avec analyse essais et mise à jour config
   - MÉTHODES PRINCIPALES :
     * __init__() - Initialisation avec config_path et adjustment_interval
     * adjust_ranges_for_study() - Ajustement plages selon meilleurs essais
     * _identify_out_of_range_params() - Identification paramètres hors plage
     * _update_config_ranges() - Mise à jour config.py et espace recherche
     * _reload_search_space_from_config() - Rechargement secours depuis config
   - FONCTIONNALITÉS :
     * **AJUSTEMENT ADAPTATIF :** Analyse top 10 essais pour nouvelles plages optimales
     * **SYNCHRONISATION CONFIG :** Mise à jour fichier config.py et sampler mémoire
     * **GESTION ERREURS :** Fallbacks robustes et reload module automatique
     * **HISTORIQUE :** Tracking ajustements avec timestamps et sauvegarde originale
   - UTILITÉ : Optimisation continue plages hyperparamètres avec adaptation automatique performance


2. class_IsolatedMetricsModule.txt (IsolatedMetricsModule - CLASSE MODULE MÉTRIQUES ISOLÉ)
   - Classe complète module métriques isolé pour évitement problèmes pickling LightGBM
   - UTILITÉ : Module séparé métriques personnalisées avec isolation complète


3. class_LSTMDynamicCallback.txt (LSTMDynamicCallback - CLASSE CALLBACK LSTM DYNAMIQUE)
   - Classe complète callback adaptation dynamique hyperparamètres LSTM pendant entraînement
   - UTILITÉ : Adaptation temps réel learning rate et paramètres LSTM selon performance


4. class_MarkovDynamicAdapter.txt (MarkovDynamicAdapter - CLASSE ADAPTATEUR MARKOV DYNAMIQUE)
   - Classe complète adaptation dynamique paramètres modèle Markov avec historique performance
   - UTILITÉ : Ajustement automatique smoothing et paramètres Markov selon tendances


5. class_MetaOptimizer.txt (MetaOptimizer - CLASSE MÉTA-OPTIMISATION AVANCÉE)
   - Lignes 13882-15302 dans optuna_optimizer.py (1421 lignes)
   - FONCTION : Classe méta-optimisation héritant TPESampler avec apprentissage historique et prédiction paramètres
   - PARAMÈTRES : Hérite paramètres TPESampler avec extensions méta-apprentissage
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HÉRITAGE TPE :** Étend optuna.samplers.TPESampler avec fonctionnalités avancées
     * **MÉTA-APPRENTISSAGE :** Apprend des optimisations passées pour prédire paramètres optimaux
     * **HISTORIQUE ÉTUDES :** Maintient base données études précédentes pour apprentissage
     * **PRÉDICTION INTELLIGENTE :** Prédit paramètres optimaux basés sur caractéristiques dataset
     * **ADAPTATION DYNAMIQUE :** Ajuste stratégie échantillonnage selon performance historique
     * **ANALYSE RÉSULTATS :** Méthodes analyse avancée avec statistiques et tendances
     * **OPTIMISATION MULTI-OBJECTIFS :** Support optimisation avec objectifs multiples
   - RETOUR : Sampler Optuna avancé avec capacités méta-apprentissage
   - UTILITÉ : Optimisation intelligente avec apprentissage historique pour convergence accélérée


6. class_OptimizerAdapter.txt (OptimizerAdapter - CLASSE ADAPTATEUR OPTIMISEUR)
   - Classe adaptateur optimiseur avec gestion callbacks et adaptation paramètres
   - UTILITÉ : Interface unifiée optimiseurs avec adaptation dynamique


7. class_OptunaMessageFilter.txt (OptunaMessageFilter - CLASSE FILTRE MESSAGES OPTUNA)
   - Classe filtre messages logging Optuna pour réduction verbosité
   - UTILITÉ : Contrôle logging avec filtrage messages répétitifs


8. class_OptunaOptimizer.txt (OptunaOptimizer - CLASSE PRINCIPALE OPTIMISATION)
   - Lignes 982-13315 dans optuna_optimizer.py (12334 lignes)
   - FONCTION : Classe principale orchestrant optimisation hyperparamètres avec Optuna pour système ML hybride
   - PARAMÈTRES : Configuration complète système avec modèles LGBM/LSTM/Markov
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ARCHITECTURE HYBRIDE :** Gère 3 modèles (LGBM, LSTM, Markov) avec optimisation coordonnée
     * **PHASES OPTIMISATION :** 4 phases séquentielles (0,1,2,3) + phase Markov spécialisée
     * **CACHE AVANCÉ :** Système cache intelligent avec gestion mémoire adaptative
     * **PARALLÉLISME :** Optimisation multi-thread avec adaptation ressources système
     * **MÉTA-APPRENTISSAGE :** Prédiction paramètres optimaux basée historique
     * **VALIDATION CROISÉE :** CV temporelle et stratifiée pour robustesse
     * **INTÉGRATION MLFLOW :** Suivi expériences et registre modèles
     * **EXPORT ONNX :** Conversion modèles pour déploiement production
     * **ANALYSE RÉSULTATS :** Statistiques détaillées et visualisations
     * **GESTION ERREURS :** Callbacks robustes avec récupération automatique
   - RETOUR : Optimiseur configuré avec toutes fonctionnalités
   - UTILITÉ : Orchestrateur central optimisation ML avec fonctionnalités enterprise


9. class_OptunaThreadManager.txt (OptunaThreadManager - CLASSE GESTIONNAIRE THREADS OPTUNA)
   - Classe gestion threads optimisation Optuna avec synchronisation
   - UTILITÉ : Parallélisation sécurisée optimisation avec gestion ressources


10. class_SimplifiedTrialPrinter.txt (SimplifiedTrialPrinter - CLASSE AFFICHAGE ESSAIS SIMPLIFIÉ)
   - Classe affichage simplifié essais Optuna avec formatage personnalisé
   - UTILITÉ : Interface utilisateur claire avec informations essentielles


11. class_SimplifiedTrialPrinter_1.txt (SimplifiedTrialPrinter - CLASSE AFFICHAGE ESSAIS - DOUBLON 1)
   - Doublon classe affichage simplifié essais avec variante formatage
   - UTILITÉ : Alternative affichage essais avec style différent


12. class_StandardCrossEntropyLoss.txt (StandardCrossEntropyLoss - CLASSE LOSS CROSS-ENTROPY)
   - Classe loss cross-entropy standard pour entraînement modèles
   - UTILITÉ : Fonction perte optimisée classification binaire/multiclasse


TOTAL : 12 méthodes analysées et documentées
- Méthodes trouvées dans descriptif principal: 12
- Méthodes manquantes: 0