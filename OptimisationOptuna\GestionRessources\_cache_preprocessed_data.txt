# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6156 à 6181
# Type: Méthode de la classe OptunaOptimizer

    def _cache_preprocessed_data(self, subset_size, data):
        """
        Met en cache les données prétraitées pour une taille de sous-ensemble donnée.

        Args:
            subset_size: Taille du sous-ensemble
            data: Données prétraitées à mettre en cache

        Returns:
            bool: True si les données ont été mises en cache, False sinon
        """
        if not hasattr(self, '_advanced_data_cache'):
            self._initialize_advanced_data_cache()

        # Vérifier si le cache est activé
        if not getattr(self.config, 'use_advanced_cache', True):
            return False

        # Mettre en cache les données
        self._advanced_data_cache['preprocessed_data'][subset_size] = data
        logger.warning(f"Données prétraitées mises en cache pour subset_size={subset_size}")

        # Nettoyer le cache si nécessaire
        self._cleanup_cache_if_needed()

        return True