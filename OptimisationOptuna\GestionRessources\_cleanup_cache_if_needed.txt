# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6274 à 6441
# Type: Méthode de la classe OptunaOptimizer

    def _cleanup_cache_if_needed(self, force=False):
        """
        Nettoie le cache si nécessaire pour éviter une consommation excessive de mémoire.
        Implémente des stratégies avancées de gestion du cache pour optimiser l'utilisation de la mémoire.

        Args:
            force: Force le nettoyage même si l'intervalle n'est pas atteint
        """
        import time
        import sys
        import gc
        import os

        # Vérifier si le cache est initialisé
        if not hasattr(self, '_advanced_data_cache'):
            return

        # Récupérer la configuration du cache
        cache_config = self._advanced_data_cache.get('cache_config', {})
        cleanup_interval = cache_config.get('cleanup_interval', 300)  # 5 minutes par défaut
        max_cache_size_mb = cache_config.get('max_size_mb', 1024)  # 1 GB par défaut
        max_items_per_category = cache_config.get('max_items_per_category', 100)
        ttl = cache_config.get('ttl', 3600)  # 1 heure par défaut
        priority_phases = cache_config.get('priority_phases', ['phase3', 'markov'])

        # Vérifier si un nettoyage est nécessaire
        current_time = time.time()
        if not force and current_time - self._advanced_data_cache['last_cleanup'] < cleanup_interval:
            return

        # Mettre à jour le timestamp de nettoyage
        self._advanced_data_cache['last_cleanup'] = current_time

        # Estimer la taille du cache avec une méthode plus précise
        def get_size(obj, seen=None):
            """Calculer la taille récursive d'un objet"""
            size = sys.getsizeof(obj)
            if seen is None:
                seen = set()
            obj_id = id(obj)
            if obj_id in seen:
                return 0
            seen.add(obj_id)
            if isinstance(obj, dict):
                size += sum(get_size(k, seen) + get_size(v, seen) for k, v in obj.items())
            elif isinstance(obj, (list, tuple, set, frozenset)):
                size += sum(get_size(i, seen) for i in obj)
            return size

        # Calculer la taille de chaque composant du cache
        memory_usage = {}
        total_size = 0

        # Composants principaux du cache
        cache_components = [
            'preprocessed_data',
            'feature_cache',
            'validation_cache',
            'model_cache',
            'prediction_cache',
            'importance_cache',
            'phase_data'
        ]

        for component in cache_components:
            if component in self._advanced_data_cache:
                component_size = get_size(self._advanced_data_cache[component])
                memory_usage[component] = component_size
                total_size += component_size

        # Convertir en MB
        total_size_mb = total_size / (1024 * 1024)

        # Stocker l'utilisation de la mémoire
        self._advanced_data_cache['memory_usage'] = memory_usage

        # Afficher les statistiques du cache
        logger.warning(f"Statistiques du cache: {self._advanced_data_cache['cache_hits']} hits, {self._advanced_data_cache['cache_misses']} misses")
        logger.warning(f"Taille estimée du cache: {total_size:,} octets ({total_size_mb:.2f} MB)")

        # Afficher la répartition de la mémoire
        logger.warning("Répartition de la mémoire du cache:")
        for component, size in sorted(memory_usage.items(), key=lambda x: x[1], reverse=True):
            component_size_mb = size / (1024 * 1024)
            if component_size_mb > 0.1:  # Afficher seulement les composants significatifs
                logger.warning(f"- {component}: {component_size_mb:.2f} MB ({component_size_mb/total_size_mb*100:.1f}%)")

        # Vérifier la mémoire système disponible
        try:
            import psutil
            memory = psutil.virtual_memory()
            available_mb = memory.available / (1024 * 1024)
            logger.warning(f"Mémoire système disponible: {available_mb:.2f} MB")

            # Ajuster dynamiquement la taille maximale du cache en fonction de la mémoire disponible
            if available_mb < 1024:  # Moins de 1 GB disponible
                # Réduire la taille maximale du cache pour éviter les OOM
                max_cache_size_mb = min(max_cache_size_mb, 512)
                logger.warning(f"Mémoire système faible, taille maximale du cache réduite à {max_cache_size_mb} MB")
        except:
            logger.warning("Impossible de détecter la mémoire système disponible")

        # Nettoyer le cache si nécessaire
        if total_size_mb > max_cache_size_mb or force:
            logger.warning(f"Nettoyage du cache (taille: {total_size_mb:.2f} MB, max: {max_cache_size_mb:.2f} MB)")

            # 1. Supprimer les entrées expirées
            current_time = time.time()
            for component in cache_components:
                if component in self._advanced_data_cache:
                    component_dict = self._advanced_data_cache[component]
                    if isinstance(component_dict, dict):
                        keys_to_delete = []
                        for key, value in component_dict.items():
                            # Vérifier si l'entrée a un timestamp
                            if isinstance(value, dict) and 'timestamp' in value:
                                if current_time - value['timestamp'] > ttl:
                                    keys_to_delete.append(key)

                        # Supprimer les entrées expirées
                        for key in keys_to_delete:
                            del component_dict[key]

                        if keys_to_delete:
                            logger.warning(f"Suppression de {len(keys_to_delete)} entrées expirées dans {component}")

            # 2. Limiter le nombre d'entrées par catégorie
            for component in cache_components:
                if component in self._advanced_data_cache:
                    component_dict = self._advanced_data_cache[component]
                    if isinstance(component_dict, dict) and component != 'phase_data':
                        if len(component_dict) > max_items_per_category:
                            # Trier les clés par timestamp si disponible, sinon par ordre alphabétique
                            if all(isinstance(v, dict) and 'timestamp' in v for v in component_dict.values()):
                                sorted_keys = sorted(component_dict.keys(), key=lambda k: component_dict[k]['timestamp'])
                            else:
                                sorted_keys = sorted(component_dict.keys())

                            # Garder seulement les entrées les plus récentes
                            keys_to_delete = sorted_keys[:-max_items_per_category]
                            for key in keys_to_delete:
                                del component_dict[key]

                            if keys_to_delete:
                                logger.warning(f"Limitation à {max_items_per_category} entrées dans {component}, {len(keys_to_delete)} supprimées")

            # 3. Traitement spécial pour les données de phase
            if 'phase_data' in self._advanced_data_cache:
                phase_data = self._advanced_data_cache['phase_data']
                for phase, data in phase_data.items():
                    # Conserver les données des phases prioritaires
                    if phase in priority_phases:
                        continue

                    # Vider les données des phases non prioritaires si le cache est trop grand
                    if total_size_mb > max_cache_size_mb * 0.9:  # Si on utilise plus de 90% du cache
                        phase_data[phase] = {}
                        logger.warning(f"Vidage des données de la phase {phase} pour libérer de la mémoire")

            # 4. Forcer la collecte des objets non référencés
            gc.collect()

            logger.warning("Cache nettoyé avec succès")

            # Recalculer la taille du cache après nettoyage
            total_size = sum(get_size(self._advanced_data_cache[component]) for component in cache_components if component in self._advanced_data_cache)
            total_size_mb = total_size / (1024 * 1024)
            logger.warning(f"Nouvelle taille du cache: {total_size_mb:.2f} MB")