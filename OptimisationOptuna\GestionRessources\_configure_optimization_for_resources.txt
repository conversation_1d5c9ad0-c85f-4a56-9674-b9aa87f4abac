# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 5655 à 5718
# Type: Méthode de la classe OptunaOptimizer

    def _configure_optimization_for_resources(self, resources):
        """
        Configure les paramètres d'optimisation en fonction des ressources disponibles.

        Args:
            resources: Dictionnaire des ressources disponibles
        """
        # Configurer le nombre de workers pour le parallélisme
        if hasattr(self, 'config'):
            # CPU
            if resources['cpu']['threads'] is not None:
                # Utiliser 75% des threads disponibles pour le parallélisme
                recommended_workers = max(1, int(resources['cpu']['threads'] * 0.75))

                # Limiter en fonction de la mémoire disponible
                if resources['memory']['available_gb'] is not None:
                    # Estimer 2 GB par worker
                    memory_limited_workers = max(1, int(resources['memory']['available_gb'] / 2))
                    recommended_workers = min(recommended_workers, memory_limited_workers)

                # Mettre à jour la configuration
                if hasattr(self.config, 'n_jobs'):
                    current_n_jobs = getattr(self.config, 'n_jobs', 1)
                    if current_n_jobs > recommended_workers:
                        logger.warning(f"Réduction du nombre de workers de {current_n_jobs} à {recommended_workers} "
                                      f"en fonction des ressources disponibles")
                        setattr(self.config, 'n_jobs', recommended_workers)
                    else:
                        logger.warning(f"Nombre actuel de workers ({current_n_jobs}) compatible avec les ressources")

            # GPU
            if hasattr(self.config, 'use_gpu'):
                if resources['gpu']['available']:
                    setattr(self.config, 'use_gpu', True)
                    logger.warning("GPU activé pour l'optimisation")
                else:
                    setattr(self.config, 'use_gpu', False)
                    logger.warning("GPU désactivé (non disponible)")

            # Taille de batch
            if resources['memory']['available_gb'] is not None:
                if resources['memory']['available_gb'] < 4:
                    # Mémoire limitée, utiliser des batchs plus petits
                    if hasattr(self.config, 'batch_size'):
                        current_batch_size = getattr(self.config, 'batch_size', 1000)
                        recommended_batch_size = min(current_batch_size, 256)
                        setattr(self.config, 'batch_size', recommended_batch_size)
                        logger.warning(f"Mémoire limitée, taille de batch réduite à {recommended_batch_size}")

        # Configurer le cache avancé
        if hasattr(self, '_advanced_data_cache') and 'cache_config' in self._advanced_data_cache:
            if resources['memory']['total_gb'] is not None:
                # Ajuster la taille du cache en fonction de la mémoire totale
                if resources['memory']['total_gb'] > 16:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 4096  # 4 GB
                elif resources['memory']['total_gb'] > 8:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 2048  # 2 GB
                elif resources['memory']['total_gb'] > 4:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 1024  # 1 GB
                else:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 512   # 512 MB

                logger.warning(f"Taille du cache ajustée à {self._advanced_data_cache['cache_config']['max_size_mb']} MB "
                              f"en fonction de la mémoire disponible")