# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 10666 à 10699
# Type: Méthode de la classe OptunaOptimizer

    def _decompress_entry(self, cache_dict, key):
        """
        Décompresse une entrée de cache compressée.

        Args:
            cache_dict: Dictionnaire de cache contenant l'entrée
            key: Clé de l'entrée à décompresser

        Returns:
            bool: True si la décompression a réussi, False sinon
        """
        if key not in cache_dict:
            return False

        value = cache_dict[key]
        if not isinstance(value, tuple) or len(value) != 2 or value[0] != '__compressed__':
            return False  # Pas une entrée compressée

        try:
            import zlib
            import pickle

            # Décompresser et désérialiser l'entrée
            compressed = value[1]
            serialized = zlib.decompress(compressed)
            data = pickle.loads(serialized)

            # Remplacer l'entrée compressée par sa version décompressée
            cache_dict[key] = data
            self._advanced_data_cache['cache_stats']['compressed'].remove(key)
            return True
        except Exception as e:
            logger.warning(f"Erreur lors de la décompression de l'entrée {key}: {str(e)}")
            return False