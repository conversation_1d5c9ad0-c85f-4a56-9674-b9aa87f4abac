# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 5554 à 5653
# Type: Méthode de la classe OptunaOptimizer

    def _detect_available_resources(self):
        """
        Détecte les ressources système disponibles et configure l'optimisation en conséquence.
        Analyse CPU, RAM, GPU et stockage pour optimiser l'utilisation des ressources.

        Returns:
            dict: Informations sur les ressources disponibles
        """
        import os
        import sys
        import time
        import platform

        # Initialiser le dictionnaire de ressources
        resources = {
            'cpu': {
                'cores': None,
                'threads': None,
                'architecture': platform.machine(),
                'usage': None
            },
            'memory': {
                'total_gb': None,
                'available_gb': None,
                'usage_percent': None
            },
            'gpu': {
                'available': False,
                'name': None,
                'memory_gb': None,
                'cuda_version': None
            },
            'storage': {
                'free_gb': None,
                'total_gb': None
            },
            'system': {
                'os': platform.system(),
                'version': platform.version(),
                'python_version': platform.python_version()
            },
            'timestamp': time.time()
        }

        # Détecter les ressources CPU
        try:
            import multiprocessing
            resources['cpu']['cores'] = multiprocessing.cpu_count() // 2  # Estimation des cœurs physiques
            resources['cpu']['threads'] = multiprocessing.cpu_count()
            logger.warning(f"CPU détecté: {resources['cpu']['cores']} cœurs, {resources['cpu']['threads']} threads")
        except:
            logger.warning("Impossible de détecter les ressources CPU")

        # Détecter les ressources mémoire
        try:
            import psutil
            memory = psutil.virtual_memory()
            resources['memory']['total_gb'] = memory.total / (1024**3)
            resources['memory']['available_gb'] = memory.available / (1024**3)
            resources['memory']['usage_percent'] = memory.percent

            logger.warning(f"Mémoire détectée: {resources['memory']['total_gb']:.1f} GB total, "
                          f"{resources['memory']['available_gb']:.1f} GB disponible "
                          f"({resources['memory']['usage_percent']}% utilisé)")

            # Détecter l'utilisation CPU
            resources['cpu']['usage'] = psutil.cpu_percent(interval=0.5)
            logger.warning(f"Utilisation CPU: {resources['cpu']['usage']}%")

            # Détecter l'espace disque
            disk = psutil.disk_usage(os.getcwd())
            resources['storage']['total_gb'] = disk.total / (1024**3)
            resources['storage']['free_gb'] = disk.free / (1024**3)
            logger.warning(f"Stockage: {resources['storage']['free_gb']:.1f} GB libre sur {resources['storage']['total_gb']:.1f} GB")

        except:
            logger.warning("Module psutil non disponible, impossible de détecter les ressources mémoire et stockage")

        # Détecter les ressources GPU
        try:
            import torch
            resources['gpu']['available'] = torch.cuda.is_available()

            if resources['gpu']['available']:
                resources['gpu']['name'] = torch.cuda.get_device_name(0)
                resources['gpu']['memory_gb'] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                resources['gpu']['cuda_version'] = torch.version.cuda

                logger.warning(f"GPU détecté: {resources['gpu']['name']}, "
                              f"{resources['gpu']['memory_gb']:.1f} GB, "
                              f"CUDA {resources['gpu']['cuda_version']}")
            else:
                logger.warning("Aucun GPU compatible CUDA détecté")
        except:
            logger.warning("PyTorch non disponible ou erreur lors de la détection GPU")

        # Configurer l'optimisation en fonction des ressources
        self._configure_optimization_for_resources(resources)

        return resources