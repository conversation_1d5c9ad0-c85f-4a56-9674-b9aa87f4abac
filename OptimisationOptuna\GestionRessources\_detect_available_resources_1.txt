# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 5897 à 6019
# Type: Méthode de la classe OptunaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _detect_available_resources(self):
        """
        Détecte les ressources système disponibles (CPU, RAM, GPU) et recommande
        des paramètres optimaux pour l'utilisation des ressources.

        Returns:
            dict: Dictionnaire contenant les informations sur les ressources disponibles
                  et les paramètres recommandés
        """
        import os
        import sys
        import multiprocessing
        import numpy as np
        import torch

        logger.warning("Détection des ressources système disponibles...")

        # Initialiser le dictionnaire de ressources
        resources = {
            'cpu': {},
            'memory': {},
            'gpu': {},
            'optimal_workers': 4,  # Valeur par défaut
            'optimal_batch_size': 1024  # Valeur par défaut
        }

        # 1. Détecter les ressources CPU
        try:
            # Nombre total de cœurs logiques
            cpu_count = multiprocessing.cpu_count()
            resources['cpu']['count'] = cpu_count

            # Nombre de threads recommandé (80% des cœurs disponibles)
            recommended_threads = max(1, int(cpu_count * 0.8))
            resources['cpu']['threads'] = recommended_threads

            logger.warning(f"CPU: {cpu_count} cœurs détectés, {recommended_threads} threads recommandés")

            # Définir le nombre optimal de workers
            resources['optimal_workers'] = recommended_threads
        except:
            logger.warning("Impossible de détecter les ressources CPU")

        # 2. Détecter les ressources mémoire
        try:
            import psutil

            # Mémoire système
            system_memory = psutil.virtual_memory()
            total_memory_gb = system_memory.total / (1024**3)
            available_memory_gb = system_memory.available / (1024**3)

            resources['memory']['total_gb'] = total_memory_gb
            resources['memory']['available_gb'] = available_memory_gb
            resources['memory']['percent_used'] = system_memory.percent

            logger.warning(f"Mémoire: {available_memory_gb:.2f} GB disponible sur {total_memory_gb:.2f} GB total ({system_memory.percent}% utilisé)")

            # Recommander une taille de batch en fonction de la mémoire disponible
            if available_memory_gb < 4:
                recommended_batch_size = 256
            elif available_memory_gb < 8:
                recommended_batch_size = 512
            elif available_memory_gb < 16:
                recommended_batch_size = 1024
            else:
                recommended_batch_size = 2048

            resources['memory']['recommended_batch_size'] = recommended_batch_size
            resources['optimal_batch_size'] = recommended_batch_size

            logger.warning(f"Taille de batch recommandée: {recommended_batch_size}")
        except ImportError:
            logger.warning("Module psutil non disponible, impossible de détecter les ressources mémoire")
        except:
            logger.warning("Erreur lors de la détection des ressources mémoire")

        # 3. Détecter les ressources GPU
        try:
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                resources['gpu']['available'] = True
                resources['gpu']['count'] = gpu_count

                # Informations sur chaque GPU
                gpu_info = []
                for i in range(gpu_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)  # En GB

                    gpu_info.append({
                        'index': i,
                        'name': gpu_name,
                        'memory_gb': gpu_memory
                    })

                    logger.warning(f"GPU {i}: {gpu_name} avec {gpu_memory:.2f} GB de mémoire")

                resources['gpu']['devices'] = gpu_info

                # Ajuster les paramètres en fonction de la présence de GPU
                # Augmenter la taille de batch si un GPU est disponible
                resources['optimal_batch_size'] = min(2048, resources['optimal_batch_size'] * 2)
                logger.warning(f"Taille de batch ajustée pour GPU: {resources['optimal_batch_size']}")
            else:
                resources['gpu']['available'] = False
                logger.warning("Aucun GPU disponible")
        except:
            resources['gpu']['available'] = False
            logger.warning("Erreur lors de la détection des ressources GPU")

        # 4. Ajuster les paramètres en fonction des ressources détectées
        # Limiter le nombre de workers en fonction de la mémoire disponible
        if 'available_gb' in resources.get('memory', {}):
            memory_per_worker = 2.0  # Estimation de la mémoire par worker en GB
            max_workers_by_memory = max(1, int(resources['memory']['available_gb'] / memory_per_worker))

            # Prendre le minimum entre le nombre de threads CPU et la limite de mémoire
            resources['optimal_workers'] = min(resources['optimal_workers'], max_workers_by_memory)
            logger.warning(f"Nombre de workers ajusté en fonction de la mémoire: {resources['optimal_workers']}")

        logger.warning("Détection des ressources système terminée")
        return resources