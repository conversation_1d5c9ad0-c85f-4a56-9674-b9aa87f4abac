# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6679 à 6697
# Type: Méthode de la classe OptunaOptimizer

    def _enable_memory_profiling(self):
        """
        Active le profilage de la mémoire pour identifier les fuites de mémoire.
        """
        try:
            import tracemalloc
            import time

            # Démarrer le profilage de la mémoire
            tracemalloc.start()
            logger.warning("Profilage de la mémoire activé")

            # Stocker le timestamp de démarrage
            self._memory_profiling_start_time = time.time()

            return True
        except ImportError:
            logger.warning("Module tracemalloc non disponible, profilage de la mémoire désactivé")
            return False