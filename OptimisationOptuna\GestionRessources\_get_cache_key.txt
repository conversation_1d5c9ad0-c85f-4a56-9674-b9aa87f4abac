# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6122 à 6154
# Type: Méthode de la classe OptunaOptimizer

    def _get_cache_key(self, indices=None, config=None, phase=None):
        """
        Génère une clé de cache unique basée sur les indices, la configuration et la phase.

        Args:
            indices: Indices des échantillons (peut être None pour utiliser tous les indices)
            config: Configuration à utiliser (peut être None pour utiliser la configuration actuelle)
            phase: Phase d'optimisation (peut être None)

        Returns:
            str: Clé de cache unique
        """
        import hashlib
        import json

        # Utiliser un sous-ensemble des indices pour la clé (pour éviter des clés trop longues)
        if indices is not None and len(indices) > 100:
            # Utiliser les 50 premiers et les 50 derniers indices, triés
            sorted_indices = sorted(indices)
            sample_indices = sorted_indices[:50] + sorted_indices[-50:]
        else:
            sample_indices = sorted(indices) if indices is not None else None

        # Créer un dictionnaire avec les informations de la clé
        key_dict = {
            'indices_hash': hashlib.md5(str(sample_indices).encode()).hexdigest() if sample_indices is not None else None,
            'indices_len': len(indices) if indices is not None else None,
            'config_hash': hashlib.md5(str(sorted(config.__dict__.items())).encode()).hexdigest() if config is not None else None,
            'phase': phase
        }

        # Générer une clé unique
        return hashlib.md5(json.dumps(key_dict, sort_keys=True).encode()).hexdigest()