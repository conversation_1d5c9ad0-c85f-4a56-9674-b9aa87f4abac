# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 11963 à 12244
# Type: Méthode de la classe OptunaOptimizer

    def _optimize_training_batch_sizes(self):
        """
        Détermine les tailles de batch optimales pour l'entraînement des modèles
        en fonction des caractéristiques des données et des ressources disponibles.
        """
        total_samples = len(self.y_full)

        # Détecter les ressources disponibles
        try:
            import psutil
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            cpu_cores = psutil.cpu_count(logical=False)
            has_gpu = torch.cuda.is_available()
        except ImportError:
            available_memory_gb = 8.0  # Valeur par défaut conservative
            cpu_cores = 4
            has_gpu = torch.cuda.is_available() if 'torch' in sys.modules else False

        logger.info(f"Ressources détectées: {available_memory_gb:.1f} GB RAM, {cpu_cores} cœurs CPU, GPU: {'Oui' if has_gpu else 'Non'}")

        # Calculer les tailles de batch optimales pour chaque modèle et phase

        # 1. LGBM: Optimiser tous les paramètres de batch automatiquement
        # Calcul dynamique basé sur le nombre d'échantillons et les ressources disponibles

        # Calculer le ratio échantillons/ressources pour adapter les paramètres
        samples_per_core = total_samples / max(1, cpu_cores)
        memory_per_sample = available_memory_gb / max(1, total_samples) * 1024  # En MB par échantillon

        logger.info(f"Ratio échantillons/cœur CPU: {samples_per_core:.1f}, Mémoire par échantillon: {memory_per_sample:.3f} MB")

        # Ajuster subsample en fonction du nombre d'échantillons et des ressources
        # Formule: plus d'échantillons ou moins de ressources = subsample plus petit
        if samples_per_core < 100 or memory_per_sample > 10:
            # Ressources abondantes ou très petit ensemble: utiliser presque toutes les données
            optimal_subsample = 0.98
        elif samples_per_core < 500 or memory_per_sample > 1:
            # Ressources suffisantes ou petit ensemble: utiliser une grande proportion
            optimal_subsample = 0.95
        elif samples_per_core < 2000 or memory_per_sample > 0.1:
            # Ressources moyennes ou ensemble moyen: équilibre
            optimal_subsample = 0.9
        else:
            # Ressources limitées ou grand ensemble: réduire pour éviter l'overfitting et économiser les ressources
            optimal_subsample = 0.85

        # min_child_samples adapté à la taille des données et aux ressources
        # Formule: plus d'échantillons ou moins de ressources = min_child_samples plus grand
        if samples_per_core < 100 or memory_per_sample > 10:
            # Ressources abondantes ou très petit ensemble: permettre des feuilles très petites
            optimal_min_child_samples = max(3, int(total_samples * 0.0002))
        elif samples_per_core < 500 or memory_per_sample > 1:
            # Ressources suffisantes ou petit ensemble: permettre des feuilles petites
            optimal_min_child_samples = max(5, int(total_samples * 0.0003))
        elif samples_per_core < 2000 or memory_per_sample > 0.1:
            # Ressources moyennes ou ensemble moyen: équilibre
            optimal_min_child_samples = max(10, int(total_samples * 0.0004))
        else:
            # Ressources limitées ou grand ensemble: feuilles plus grandes
            optimal_min_child_samples = max(20, int(total_samples * 0.0005))

        # Calculer le nombre optimal d'itérations en fonction des données et des ressources
        if samples_per_core < 100:
            optimal_num_iterations = max(50, min(200, int(100 + total_samples / 100)))
        elif samples_per_core < 500:
            optimal_num_iterations = max(100, min(300, int(150 + total_samples / 200)))
        else:
            optimal_num_iterations = max(150, min(500, int(200 + total_samples / 300)))

        # Calculer le learning rate optimal (inversement proportionnel au nombre d'itérations)
        optimal_learning_rate = max(0.01, min(0.2, 0.1 * (200 / optimal_num_iterations)))

        # 2. LSTM: Optimiser tous les paramètres de batch automatiquement
        # Calcul dynamique basé sur le nombre d'échantillons, les ressources disponibles et la complexité du modèle

        # Calculer la complexité du modèle LSTM (estimation)
        lstm_hidden_size = getattr(self.config, 'lstm_hidden_size', 128)
        lstm_num_layers = getattr(self.config, 'lstm_num_layers', 2)
        lstm_input_size = getattr(self.config, 'lstm_input_size', 64)

        # Estimer la mémoire requise par échantillon (en MB)
        params_per_sample = lstm_hidden_size * lstm_num_layers * lstm_input_size * 4 / (1024 * 1024)

        # Facteur d'ajustement basé sur la taille des données et la complexité du modèle
        # Formule: plus d'échantillons ou modèle plus complexe = batchs plus petits
        complexity_factor = min(1.0, 0.5 / max(0.01, params_per_sample))

        # Facteur d'ajustement basé sur le ratio échantillons/ressources
        if samples_per_core < 100:
            # Ressources abondantes: batchs plus grands
            resource_factor = 1.2
        elif samples_per_core < 500:
            # Ressources suffisantes: batchs standards
            resource_factor = 1.0
        elif samples_per_core < 2000:
            # Ressources limitées: batchs plus petits
            resource_factor = 0.8
        else:
            # Ressources très limitées: batchs très petits
            resource_factor = 0.6

        # Calculer la taille de batch optimale en fonction de tous les facteurs
        if has_gpu:
            # Sur GPU, ajuster en fonction de la mémoire GPU disponible
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3) if torch.cuda.is_available() else 4
            memory_factor = min(1.0, gpu_memory_gb / 8.0)  # Normaliser par rapport à 8GB

            # Formule: taille de base * facteurs d'ajustement
            base_batch_size = int(32 * memory_factor * complexity_factor * resource_factor)
            optimal_lstm_batch_size = max(8, min(256, base_batch_size))

            # Calculer le nombre d'époques optimal (inversement proportionnel à la taille du batch)
            optimal_lstm_epochs = max(2, min(10, int(6 * (32 / optimal_lstm_batch_size))))
        else:
            # Sur CPU, des batchs plus petits et plus d'époques
            memory_factor = min(1.0, available_memory_gb / 16.0)  # Normaliser par rapport à 16GB

            # Formule: taille de base * facteurs d'ajustement
            base_batch_size = int(16 * memory_factor * complexity_factor * resource_factor)
            optimal_lstm_batch_size = max(4, min(128, base_batch_size))

            # Calculer le nombre d'époques optimal (inversement proportionnel à la taille du batch)
            optimal_lstm_epochs = max(3, min(15, int(8 * (16 / optimal_lstm_batch_size))))

        # Calculer le learning rate optimal pour LSTM
        optimal_lstm_learning_rate = max(0.0005, min(0.005, 0.001 * (16 / optimal_lstm_batch_size)))

        # 3. Markov: Optimiser les paramètres de batch pour le modèle Markov
        # Calculer les paramètres optimaux en fonction des données et des ressources

        # Calculer la profondeur optimale de l'historique Markov
        if total_samples < 1000:
            # Petit ensemble: historique court pour éviter l'overfitting
            optimal_markov_depth = 3
        elif total_samples < 5000:
            # Ensemble moyen: historique moyen
            optimal_markov_depth = 4
        elif total_samples < 20000:
            # Grand ensemble: historique plus long
            optimal_markov_depth = 5
        else:
            # Très grand ensemble: historique maximum
            optimal_markov_depth = 6

        # Calculer le facteur de lissage optimal (plus petit ensemble = plus de lissage)
        optimal_markov_smoothing = max(0.01, min(0.2, 0.1 * (5000 / max(1, total_samples))))

        # Calculer la taille de batch optimale pour les mises à jour Markov
        if samples_per_core < 100:
            # Ressources abondantes: batchs plus grands
            optimal_markov_batch_size = max(50, min(500, int(total_samples / 10)))
        elif samples_per_core < 500:
            # Ressources moyennes: batchs moyens
            optimal_markov_batch_size = max(20, min(200, int(total_samples / 20)))
        else:
            # Ressources limitées: batchs plus petits
            optimal_markov_batch_size = max(10, min(100, int(total_samples / 40)))

        # 4. Évaluation progressive: Optimiser les segments
        # Adapter les segments en fonction de la taille des données et des ressources
        if samples_per_core < 100 or memory_per_sample > 10:
            # Ressources abondantes: évaluation plus granulaire
            self.evaluation_segments = [
                int(0.20 * total_samples),  # 20% des données chargées
                int(0.40 * total_samples),  # 40% des données chargées
                int(0.70 * total_samples),  # 70% des données chargées
                total_samples               # 100% des données chargées
            ]
        else:
            # Ressources limitées: évaluation plus efficace
            self.evaluation_segments = [
                int(0.30 * total_samples),  # 30% des données chargées
                int(0.60 * total_samples),  # 60% des données chargées
                total_samples               # 100% des données chargées
            ]

        # 5. Parallélisation: Optimiser le nombre de jobs en fonction des ressources
        # Adapter le nombre de workers en fonction du nombre de cœurs et de la complexité des modèles

        # Calculer le nombre optimal de jobs en fonction des ressources et de la complexité
        if cpu_cores <= 2:
            # Très peu de cœurs: limiter le parallélisme
            self.optimal_jobs = {
                'phase0': 1,
                'phase1': 1,
                'phase2': 1,
                'phase3': 1
            }
        elif cpu_cores <= 4:
            # Peu de cœurs: parallélisme modéré
            self.optimal_jobs = {
                'phase0': 2,
                'phase1': 2,
                'phase2': 1,
                'phase3': 1
            }
        elif cpu_cores <= 8:
            # Nombre moyen de cœurs: bon parallélisme
            self.optimal_jobs = {
                'phase0': min(4, cpu_cores),
                'phase1': min(2, cpu_cores // 2),
                'phase2': 1,
                'phase3': 1
            }
        else:
            # Beaucoup de cœurs: parallélisme élevé
            self.optimal_jobs = {
                'phase0': min(8, cpu_cores // 2),
                'phase1': min(4, cpu_cores // 4),
                'phase2': min(2, cpu_cores // 8),
                'phase3': 1
            }

        # Stocker tous les paramètres optimaux dans un dictionnaire
        self.optimal_batch_params = {
            # Paramètres LGBM
            'lgbm_subsample': optimal_subsample,
            'lgbm_min_child_samples': optimal_min_child_samples,
            'lgbm_num_iterations': optimal_num_iterations,
            'lgbm_learning_rate': optimal_learning_rate,

            # Paramètres LSTM
            'lstm_batch_size': optimal_lstm_batch_size,
            'lstm_epochs': optimal_lstm_epochs,
            'lstm_learning_rate': optimal_lstm_learning_rate,

            # Paramètres Markov
            'markov_depth': optimal_markov_depth,
            'markov_smoothing': optimal_markov_smoothing,
            'markov_batch_size': optimal_markov_batch_size,
            'markov_global_weight': getattr(self.config, 'markov_global_weight', 0.7),
            'markov_context_weight': getattr(self.config, 'markov_context_weight', 0.8),
            'markov_decay_factor': getattr(self.config, 'markov_decay_factor', 0.95),

            # Paramètres d'évaluation et de parallélisation
            'evaluation_segments': self.evaluation_segments,
            'optimal_jobs': self.optimal_jobs,

            # Métriques de ressources
            'samples_per_core': samples_per_core,
            'memory_per_sample': memory_per_sample,
            'total_samples': total_samples,

            # Paramètres spécifiques par phase
            'phase0': {
                'lgbm_subsample': min(0.98, optimal_subsample * 1.05),  # Légèrement plus élevé pour l'exploration
                'lgbm_min_child_samples': max(3, int(optimal_min_child_samples * 0.8)),  # Plus petit pour l'exploration
                'lgbm_batch_size': max(512, int(optimal_lstm_batch_size * 2)),  # Plus grand pour l'exploration rapide
                'lstm_batch_size': max(128, int(optimal_lstm_batch_size * 1.5))  # Plus grand pour l'exploration rapide
            },
            'phase1': {
                'lgbm_subsample': optimal_subsample,
                'lgbm_min_child_samples': optimal_min_child_samples,
                'lgbm_batch_size': max(256, int(optimal_lstm_batch_size * 1.5)),
                'lstm_batch_size': optimal_lstm_batch_size
            },
            'phase2': {
                'lgbm_subsample': min(0.95, optimal_subsample * 0.98),  # Légèrement plus petit pour l'optimisation fine
                'lgbm_min_child_samples': max(5, int(optimal_min_child_samples * 1.1)),  # Plus grand pour l'optimisation fine
                'lgbm_batch_size': max(128, int(optimal_lstm_batch_size * 1.2)),
                'lstm_batch_size': max(64, int(optimal_lstm_batch_size * 0.8))  # Plus petit pour l'optimisation fine
            },
            'phase3': {
                'lgbm_subsample': min(0.9, optimal_subsample * 0.95),  # Encore plus petit pour l'optimisation très fine
                'lgbm_min_child_samples': max(10, int(optimal_min_child_samples * 1.2)),  # Encore plus grand pour l'optimisation très fine
                'lgbm_batch_size': max(64, int(optimal_lstm_batch_size * 1.0)),
                'lstm_batch_size': max(32, int(optimal_lstm_batch_size * 0.6))  # Beaucoup plus petit pour l'optimisation très fine
            }
        }

        # Journaliser tous les paramètres optimisés
        logger.info(f"Paramètres de batch optimisés automatiquement:")
        logger.info(f"  - LGBM: subsample={optimal_subsample}, min_child_samples={optimal_min_child_samples}, "
                   f"num_iterations={optimal_num_iterations}, learning_rate={optimal_learning_rate:.4f}")
        logger.info(f"  - LSTM: batch_size={optimal_lstm_batch_size}, epochs={optimal_lstm_epochs}, "
                   f"learning_rate={optimal_lstm_learning_rate:.6f}")
        logger.info(f"  - Markov: depth={optimal_markov_depth}, smoothing={optimal_markov_smoothing:.4f}, "
                   f"batch_size={optimal_markov_batch_size}")
        logger.info(f"  - Évaluation progressive: segments={[s for s in self.evaluation_segments]}")
        logger.info(f"  - Jobs par phase: {self.optimal_jobs}")

        return self.optimal_batch_params