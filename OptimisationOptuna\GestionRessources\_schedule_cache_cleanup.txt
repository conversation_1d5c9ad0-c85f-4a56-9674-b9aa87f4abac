# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6094 à 6120
# Type: Méthode de la classe OptunaOptimizer

    def _schedule_cache_cleanup(self):
        """
        Planifie un nettoyage du cache si nécessaire.
        Cette méthode vérifie si un nettoyage est nécessaire en fonction de l'intervalle configuré.
        """
        import time

        # Vérifier si le cache est initialisé
        if not hasattr(self, '_advanced_data_cache'):
            return

        # Récupérer la configuration du cache
        cache_config = self._advanced_data_cache.get('cache_config', {})
        cleanup_interval = cache_config.get('cleanup_interval', 300)  # 5 minutes par défaut

        # Vérifier si un nettoyage est nécessaire
        current_time = time.time()
        last_cleanup = self._advanced_data_cache.get('last_cleanup', 0)

        if current_time - last_cleanup > cleanup_interval:
            # Effectuer le nettoyage
            self._cleanup_cache_if_needed(force=True)

            # Mettre à jour le timestamp du dernier nettoyage
            self._advanced_data_cache['last_cleanup'] = current_time

            logger.debug(f"Nettoyage du cache planifié effectué après {current_time - last_cleanup:.1f} secondes")