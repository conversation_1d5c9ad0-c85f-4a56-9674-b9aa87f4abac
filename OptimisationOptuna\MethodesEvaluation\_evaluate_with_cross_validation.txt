# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 1082 à 1271
# Type: Méthode de la classe OptunaOptimizer

    def _evaluate_with_cross_validation(self, config, subset_indices=None, n_folds=5, cv_type='stratified',
                                       random_state=42, use_parallel=False, **kwargs):
        """
        Évalue une configuration avec validation croisée en utilisant différentes stratégies.
        Cette méthode permet d'obtenir une estimation plus robuste de la performance d'une configuration.

        Args:
            config: Configuration à évaluer
            subset_indices: Indices à utiliser pour la validation croisée (si None, utilise tous les indices)
            n_folds: Nombre de plis pour la validation croisée
            cv_type: Type de validation croisée ('stratified', 'temporal', 'group', 'random')
            random_state: Graine aléatoire pour la reproductibilité
            use_parallel: Utiliser le traitement parallèle pour accélérer l'évaluation
            **kwargs: Arguments supplémentaires pour l'évaluation

        Returns:
            Tuple[float, Dict]: Score moyen et métriques moyennes
        """
        import numpy as np
        import random
        from sklearn.model_selection import KFold, StratifiedKFold, GroupKFold
        import multiprocessing
        from concurrent.futures import ProcessPoolExecutor, as_completed

        # Définir la graine aléatoire pour la reproductibilité
        np.random.seed(random_state)
        random.seed(random_state)

        # Utiliser tous les indices si aucun n'est fourni
        if subset_indices is None:
            if hasattr(self, '_preprocessed_data') and 'all_sequences' in self._preprocessed_data:
                subset_indices = list(range(len(self._preprocessed_data['all_sequences'])))
            else:
                logger.warning("Aucun indice fourni et aucune donnée prétraitée disponible")
                return 0.0, {}

        # Vérifier qu'il y a suffisamment de données
        if len(subset_indices) < n_folds * 2:
            logger.warning(f"Pas assez de données pour {n_folds} plis (minimum {n_folds * 2} requis)")
            n_folds = max(2, len(subset_indices) // 2)
            logger.warning(f"Nombre de plis réduit à {n_folds}")

        # Charger les données nécessaires pour la stratification
        if cv_type == 'stratified' or cv_type == 'group':
            # Charger les séquences pour obtenir les labels ou groupes
            if hasattr(self, '_preprocessed_data') and 'all_sequences' in self._preprocessed_data:
                all_sequences = self._preprocessed_data['all_sequences']
                sequences = [all_sequences[i] for i in subset_indices]
            else:
                logger.warning("Données prétraitées non disponibles pour la stratification")
                cv_type = 'random'
                sequences = None

        # Créer les plis selon le type de validation croisée
        if cv_type == 'temporal':
            # Validation croisée temporelle (utilise l'ordre des indices)
            return self._perform_temporal_cross_validation(config, subset_indices, n_folds, **kwargs)

        elif cv_type == 'stratified':
            # Validation croisée stratifiée (utilise les labels)
            try:
                # Extraire les labels pour la stratification
                labels = []
                for seq in sequences:
                    target_seq = seq['target_sequence']
                    # Utiliser le ratio P/B comme label
                    p_count = target_seq.count('P')
                    ratio = round(p_count / len(target_seq), 1)  # Arrondir à 0.1 près
                    labels.append(ratio)

                # Créer les plis stratifiés
                skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=random_state)
                folds = list(skf.split(subset_indices, labels))
            except Exception as e:
                logger.warning(f"Erreur lors de la création des plis stratifiés: {e}")
                logger.warning("Utilisation de la validation croisée aléatoire comme fallback")
                cv_type = 'random'

        elif cv_type == 'group':
            # Validation croisée par groupe (utilise les groupes)
            try:
                # Extraire les groupes
                groups = []
                for seq in sequences:
                    # Utiliser l'ID de séquence ou une autre propriété comme groupe
                    group = seq.get('sequence_id', hash(str(seq)))
                    groups.append(group)

                # Créer les plis par groupe
                gkf = GroupKFold(n_splits=n_folds)
                folds = list(gkf.split(subset_indices, groups=groups))
            except Exception as e:
                logger.warning(f"Erreur lors de la création des plis par groupe: {e}")
                logger.warning("Utilisation de la validation croisée aléatoire comme fallback")
                cv_type = 'random'

        # Fallback: validation croisée aléatoire
        if cv_type == 'random':
            kf = KFold(n_splits=n_folds, shuffle=True, random_state=random_state)
            folds = list(kf.split(subset_indices))

        # Fonction pour évaluer un pli
        def evaluate_fold(fold_idx, train_idx, val_idx):
            # Convertir les indices numpy en listes Python
            train_indices = [subset_indices[i] for i in train_idx]
            val_indices = [subset_indices[i] for i in val_idx]

            # Évaluer la configuration sur ce pli
            score, metrics = self._evaluate_config(
                config,
                subset_indices=train_indices,
                validation_indices=val_indices,
                **kwargs
            )

            return fold_idx, score, metrics

        # Évaluer chaque pli
        all_scores = []
        all_metrics = []

        if use_parallel and n_folds > 1 and multiprocessing.cpu_count() > 1:
            # Évaluation parallèle des plis
            max_workers = min(n_folds, multiprocessing.cpu_count())
            logger.warning(f"Évaluation parallèle avec {max_workers} workers")

            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # Soumettre les tâches
                futures = []
                for fold_idx, (train_idx, val_idx) in enumerate(folds):
                    future = executor.submit(evaluate_fold, fold_idx, train_idx, val_idx)
                    futures.append(future)

                # Collecter les résultats
                results = []
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.warning(f"Erreur lors de l'évaluation d'un pli: {e}")

                # Trier les résultats par indice de pli
                results.sort(key=lambda x: x[0])

                # Extraire les scores et métriques
                for _, score, metrics in results:
                    all_scores.append(score)
                    all_metrics.append(metrics)
        else:
            # Évaluation séquentielle des plis
            for fold_idx, (train_idx, val_idx) in enumerate(folds):
                _, score, metrics = evaluate_fold(fold_idx, train_idx, val_idx)
                all_scores.append(score)
                all_metrics.append(metrics)

        # Calculer le score moyen et les métriques moyennes
        if not all_scores:
            logger.warning("Aucun score valide obtenu lors de la validation croisée")
            return 0.0, {}

        avg_score = sum(all_scores) / len(all_scores)

        # Calculer l'écart-type des scores pour estimer la robustesse
        score_std = np.std(all_scores) if len(all_scores) > 1 else 0.0

        # Agréger les métriques
        avg_metrics = {}
        if all_metrics:
            # Identifier toutes les clés de métriques
            all_keys = set()
            for metrics in all_metrics:
                all_keys.update(metrics.keys())

            # Calculer la moyenne pour chaque métrique
            for key in all_keys:
                values = [m.get(key, 0.0) for m in all_metrics]
                avg_metrics[key] = sum(values) / len(values)

            # Ajouter des statistiques sur les scores
            avg_metrics['cv_score_mean'] = avg_score
            avg_metrics['cv_score_std'] = score_std
            avg_metrics['cv_score_min'] = min(all_scores)
            avg_metrics['cv_score_max'] = max(all_scores)
            avg_metrics['cv_n_folds'] = len(all_scores)
            avg_metrics['cv_type'] = cv_type

        logger.warning(f"Validation croisée ({cv_type}, {n_folds} plis): score moyen = {avg_score:.4f}, écart-type = {score_std:.4f}")

        return avg_score, avg_metrics