DESCRIPTIF DÉTAILLÉ DES MÉTHODES - METHODESOPTIMISATION
============================================================

Ce fichier contient la description détaillée de toutes les méthodes
présentes dans le sous-dossier MethodesOptimisation.

1. __init___2.txt (OptunaOptimizer.__init__ - HOMONYME des méthodes 21 et 22)
   - Lignes 1008-1080 dans optuna_optimizer.py (73 lignes)
   - FONCTION : Constructeur principal de la classe OptunaOptimizer pour initialiser l'optimiseur avec configuration complète
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * config (PredictorConfig) - Configuration du prédicteur avec tous les paramètres d'optimisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HOMONYME IDENTIFIÉ :** Troisième méthode __init__ homonyme pour classe OptunaOptimizer (classe principale)
     * **CONFIGURATION BASE :** Stocke config et initialise study=None, best_params={}
     * **PARAMÈTRES VIABILITÉ :** Extrait optuna_viable_trials_required, optuna_max_trials avec valeurs par défaut
     * **STOCKAGE DONNÉES :** Initialise attributs pour X_lgbm_full, y_full, X_lstm_full, train/val_indices
     * **MODE DEBUG :** Configure debug_mode, debug_log_dir, debug_log_file pour journalisation avancée
     * **MÉTRIQUES VIABILITÉ :** Initialise viability_metrics{} pour tracker métriques de chaque essai
     * **CRÉATION RÉPERTOIRES :** Crée répertoire debug_log_dir si debug_mode activé
     * **LOGGING PARAMÈTRES :** Affiche paramètres de viabilité avec logger.warning pour débogage
     * **DÉTECTION DEVICE :** Détecte torch.device("cuda" if torch.cuda.is_available() else "cpu")
     * **OPTIONS AVANCÉES :** Configure multi_level, adaptive_regularization, SWA, meta_learning, temporal_cv
     * **RESSOURCES SYSTÈME :** Configure cpu_count=multiprocessing.cpu_count(), ram_gb=28, batch_size=1024
     * **CACHE ÉVALUATIONS :** Initialise evaluation_cache{} pour optimiser les évaluations répétées
     * **AJUSTEUR DYNAMIQUE :** Initialise range_adjuster=DynamicRangeAdjuster() pour ajustement plages
   - RETOUR : None (constructeur sans valeur de retour)
   - UTILITÉ : Initialise l'optimiseur Optuna principal avec configuration complète, gestion ressources et options avancées


2. _apply_hyperparameters_from_metadata.txt (HybridBaccaratPredictor._apply_hyperparameters_from_metadata)
   - Lignes 3293-3330 dans hbp.py
   - FONCTION : Applique les hyperparamètres à partir des métadonnées d'un modèle sauvegardé
   - PARAMÈTRES : metadata (Dict[str, Any]) - métadonnées contenant les hyperparamètres
   - FONCTIONNEMENT :
     * Extrait hyperparams depuis metadata.get('hyperparameters', {})
     * Si aucun hyperparamètre : warning et retour
     * Demande confirmation utilisateur via messagebox.askyesno
     * Si confirmé : applique chaque paramètre via setattr(self.config, param, value)
     * Vérifie hasattr(self.config, param) et value is not None avant application
     * Compte et affiche nombre de paramètres appliqués
     * Gestion d'erreurs avec messagebox.showerror
   - RETOUR : None
   - UTILITÉ : Interface pour appliquer hyperparamètres optimisés depuis modèles sauvegardés


3. _finalize_optuna_optimization.txt (HybridBaccaratPredictor._finalize_optuna_optimization)
   - Lignes 13167-13430 dans hbp.py (263 lignes)
   - FONCTION : Finalise l'optimisation multi-niveaux et applique les meilleurs paramètres
   - PARAMÈTRES : success (bool), best_params (Optional[Dict]), duration (float), error_msg (Optional[str])
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NETTOYAGE FLAGS :** is_training=False, stop_training=False, is_optuna_running=False
     * **RÉACTIVATION UI :** toggle_training_controls(enabled=True)
     * **NETTOYAGE RESSOURCES :** optuna_thread_manager, current_optimizer_instance=None
     * **ANALYSE RÉSULTATS :**
       - Compare best_params avec configuration actuelle
       - Détecte si paramètres différents des valeurs par défaut
       - Normalise et compare poids optimisés avec initial_weights
     * **3 SCÉNARIOS DE FINALISATION :**

       **SCÉNARIO 1 - Amélioration trouvée :**
       - Affiche "Optimisation Terminée/Interrompue (Xs) ! Meilleurs paramètres trouvés"
       - Planifie cleanup_and_show_results() + _show_optuna_results_window()
       - Auto-affichage show_optimization_results()

       **SCÉNARIO 2 - Pas d'amélioration :**
       - Affiche "Optimisation finie (sans amélioration)"
       - Planifie cleanup_and_show_message() avec warning
       - Auto-affichage show_optimization_results()

       **SCÉNARIO 3 - Échec/Interruption :**
       - Affiche "Échec Optimisation" ou "Optimisation Interrompue"
       - Planifie cleanup_and_show_message() avec erreur
     * **NETTOYAGE SYSTÉMATIQUE :** gc.collect(), torch.cuda.empty_cache(), terminaison processus multiprocessing
     * **RESET PHASE :** config.optimization_phase = None (retour mode normal)
   - RETOUR : None
   - UTILITÉ : Orchestration complète de fin d'optimisation avec feedback utilisateur et nettoyage ressources


4. _optimize_with_adaptive_parallelism.txt (OptunaOptimizer._optimize_with_adaptive_parallelism - MÉTHODE PARALLÉLISME ADAPTATIF)
   - Lignes 11478-11798 dans optuna_optimizer.py (321 lignes)
   - FONCTION : Optimisation Optuna avec parallélisme adaptatif intelligent basé sur performances et ressources système
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * study - Étude Optuna à optimiser
     * objective_func - Fonction objectif à maximiser/minimiser
     * n_trials (int) - Nombre total d'essais à effectuer
     * max_jobs (int, défaut=4) - Nombre maximum de jobs en parallèle
     * min_jobs (int, défaut=1) - Nombre minimum de jobs en parallèle
     * adaptive_interval (int, défaut=10) - Intervalle d'adaptation du nombre de jobs
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PARALLÉLISME ADAPTATIF :** Ajuste dynamiquement le nombre de workers selon performances et ressources
     * **DÉTECTION RESSOURCES :** Utilise psutil pour détecter CPU, mémoire et ajuster max_jobs automatiquement
     * **OPTIMISATION PHASE :** Utilise optimal_batch_params si disponible pour adapter workers par phase
     * **HISTORIQUE PERFORMANCES :** Maintient parallelism_history avec durées, succès, ressources par lot
     * **LOTS ADAPTATIFS :** Divise n_trials en lots de taille variable (5→20) pour adaptation progressive
     * **MONITORING RESSOURCES :** Surveille CPU/RAM en temps réel et réduit workers si surcharge (>85% RAM, >90% CPU)
     * **PHASES EXPLORATION/EXPLOITATION :** Transition automatique après 1/3 des essais avec réduction workers
     * **RECHARGEMENT CONFIG :** Recharge module config entre lots pour prendre en compte ajustements plages
     * **CALLBACK SIMPLIFIÉ :** Utilise SimplifiedTrialPrinter pour affichage épuré des essais
     * **ADAPTATION TEMPS RÉEL :** Ajuste workers selon durée moyenne (>120s→réduction, <30s→augmentation)
     * **TAUX RÉUSSITE :** Considère batch_success_rate pour décisions d'augmentation parallélisme
     * **TAILLE LOTS DYNAMIQUE :** Ajuste taille lots suivants selon performances (rapide→+20%, lent→-20%)
     * **DÉTECTION DÉGRADATION :** Analyse tendances sur 3 derniers lots et réduit workers si dégradation
     * **OPTIMISATION MÉMOIRE :** Appelle _optimize_memory_usage() entre lots pour libérer ressources
     * **AJUSTEMENT PLAGES :** Utilise range_adjuster pour ajuster plages Optuna selon meilleurs essais
     * **ARRÊT PROPRE :** Vérifie stop_requested() entre lots pour arrêt propre
     * **FINALISATION INTELLIGENTE :** Finalise ajustements plages et sauvegarde dans config.py
     * **STATISTIQUES GLOBALES :** Enregistre optimization_stats pour futures optimisations
     * **GESTION ERREURS :** Gestion robuste erreurs avec logging détaillé et fallback
   - RETOUR : optuna.study.Study - L'étude optimisée avec parallélisme adaptatif
   - UTILITÉ : Optimisation Optuna intelligente avec adaptation automatique du parallélisme selon ressources et performances


5. _parallel_phase_transition.txt (OptunaOptimizer._parallel_phase_transition - MÉTHODE TRANSITION PHASES PARALLÈLE)
   - Lignes 7269-7494 dans optuna_optimizer.py (226 lignes)
   - FONCTION : Exécution parallèle des transitions entre phases d'optimisation avec accélération multi-thread
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * best_trial - Meilleur essai de la phase précédente
     * phase_from - Phase de départ (0, 1, 2, 'markov')
     * phase_to - Phase d'arrivée (1, 2, 3, 'markov')
     * subset_indices - Indices du sous-ensemble à utiliser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MESURE TEMPS :** Chronomètre temps de transition pour optimisation performance
     * **ESPACE RECHERCHE RESTREINT :** Crée plages ±10% autour meilleur essai pour exploration locale
     * **VALIDATION PARALLÈLE :** Valide meilleur essai sur sous-ensemble plus grand avec CV
     * **ACTIVATION CONDITIONNELLE :** Force LSTM/Markov selon transitions de phases
     * **PRÉPARATION DONNÉES PARALLÈLE :** ThreadPoolExecutor 3 workers pour features LGBM/LSTM/targets
     * **CACHE FEATURES :** Met en cache features préparées pour réutilisation
     * **APPRENTISSAGE CURRICULUM :** Calcule difficulté moyenne si difficulty_scores disponible
     * **CRÉATION VARIATIONS :** Génère variations ±5% du meilleur essai avec gestion booléens
     * **PROTECTION BOOLÉENS :** Préserve paramètres booléens sans modification
     * **EXÉCUTION 8 WORKERS :** ThreadPoolExecutor 8 workers pour tâches principales parallèles
     * **SYNCHRONISATION :** concurrent.futures.wait() pour synchronisation complète
     * **LOGGING DÉTAILLÉ :** Journalise résultats validation, formes données, variations
     * **RETOUR STRUCTURÉ :** Dictionnaire avec best_params, variations, validation_result, data_shapes
   - RETOUR : dict - Paramètres optimisés avec variations et résultats validation
   - UTILITÉ : Accélération transitions entre phases avec parallélisme et préparation optimisée


6. _predict_optimal_params_with_meta_learning.txt (OptunaOptimizer._predict_optimal_params_with_meta_learning - MÉTHODE PRÉDICTION PARAMÈTRES MÉTA-APPRENTISSAGE)
   - Lignes 12605-12723 dans optuna_optimizer.py (119 lignes)
   - FONCTION : Prédit hyperparamètres optimaux selon caractéristiques dataset avec règles méta-apprentissage sophistiquées
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * dataset_features - Caractéristiques jeu de données
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTA-CARACTÉRISTIQUES :** complexity = 0.5 + 0.5*(pattern_diversity + alternation_rate + entropy)
     * **DÉSÉQUILIBRE CLASSES :** class_imbalance = abs(p_frequency - b_frequency)
     * **PRÉVISIBILITÉ :** predictability = 1.0 - min(1.0, entropy)
     * **TENDANCE STREAKS :** streak_tendency = min(1.0, avg_streak_length / 5.0)
     * **LGBM ADAPTATIF :** subsample = 0.9-0.3*complexity-0.2*imbalance, min_child_samples selon total_lines*0.0005
     * **LSTM ADAPTATIF :** batch_size réduit si complexe, epochs augmentés si imprévisible, hidden_size selon patterns
     * **MARKOV ADAPTATIF :** max_order selon streaks+complexity, smoothing selon predictability, weights selon patterns
     * **LIMITES SÉCURISÉES :** max/min pour tous paramètres évitant valeurs extrêmes
   - RETOUR : Dict - Hyperparamètres prédits adaptés aux caractéristiques dataset
   - UTILITÉ : Prédiction intelligente hyperparamètres avec méta-apprentissage pour optimisation ciblée selon données


7. _run_optuna_optimization_async.txt
   - FONCTION : [DESCRIPTION NON TROUVÉE DANS LE DESCRIPTIF PRINCIPAL]
   - PARAMÈTRES : [À COMPLÉTER]
   - FONCTIONNEMENT DÉTAILLÉ : [À COMPLÉTER]
   - RETOUR : [À COMPLÉTER]
   - UTILITÉ : [À COMPLÉTER]

8. _select_and_save_optimized_models.txt
   - FONCTION : [DESCRIPTION NON TROUVÉE DANS LE DESCRIPTIF PRINCIPAL]
   - PARAMÈTRES : [À COMPLÉTER]
   - FONCTIONNEMENT DÉTAILLÉ : [À COMPLÉTER]
   - RETOUR : [À COMPLÉTER]
   - UTILITÉ : [À COMPLÉTER]

9. _suggest_params_with_constraints.txt (MetaOptimizer._suggest_params_with_constraints - MÉTHODE SUGGESTION PARAMÈTRES AVEC CONTRAINTES - 2ème MÉTHODE LA PLUS LONGUE)
   - Lignes 14920-15101 dans optuna_optimizer.py (182 lignes)
   - FONCTION : Suggère paramètres essai Optuna respectant contraintes pour éviter combinaisons invalides ou sous-optimales
   - PARAMÈTRES :
     * self - Instance de la classe MetaOptimizer
     * trial - Essai Optuna en cours
     * search_space - Espace recherche paramètres
     * constraints (optionnel) - Liste contraintes (fonctions ou dictionnaires)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ESPACE :** Vérifie search_space dict valide avec fallback params vide
     * **CONTRAINTES MULTIPLES :** range_dependency (>, >=, <, <=, ==, !=), conditional (if_param→then_param), sum_constraint (somme cible±tolérance)
     * **FONCTION VÉRIFICATION :** check_constraints() avec callable(constraint) ou dict constraint types
     * **ORDRE PARAMÈTRES :** Réorganise param_order selon contraintes conditionnelles (if_param avant then_param)
     * **SUGGESTION TYPES :** trial.suggest_int/float/categorical avec log_scale optionnel pour float
     * **TENTATIVES MULTIPLES :** max_attempts=100 pour éviter boucles infinies avec check_constraints()
     * **FALLBACK ALÉATOIRE :** random.randint/uniform/choice si max_attempts atteint avec trial._suggest() forcé
     * **VALIDATION FINALE :** check_constraints(params) avec warning si contraintes non respectées
   - RETOUR : dict - Paramètres suggérés respectant contraintes
   - UTILITÉ : Suggestion intelligente paramètres avec contraintes complexes pour optimisation robuste et évitement combinaisons invalides


10. adapt_parameters_for_full_training.txt (OptunaOptimizer.adapt_parameters_for_full_training - MÉTHODE ADAPTATION PARAMÈTRES ENTRAÎNEMENT COMPLET - MÉTHODE LA PLUS LONGUE)
   - Lignes 12850-13125 dans optuna_optimizer.py (276 lignes)
   - FONCTION : Adapte hyperparamètres optimisés 10% données pour entraînement complet 100% avec formules non-linéaires et méta-apprentissage
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * optimized_params - Dictionnaire paramètres optimisés par Optuna
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FACTEUR ÉCHELLE :** _calculate_dynamic_scale_factor() pour adaptation taille dataset
     * **COMPLEXITÉ DONNÉES :** _estimate_data_complexity() pour analyse patterns et diversité
     * **CONTRAINTES RESSOURCES :** _estimate_resource_constraints() pour CPU/RAM/GPU disponibles
     * **MÉTA-APPRENTISSAGE :** _predict_optimal_params_with_meta_learning() avec dataset_features
     * **COMBINAISON PONDÉRÉE :** 70% Optuna + 30% méta-learning pour paramètres numériques
     * **FORMULES NON-LINÉAIRES :** _apply_nonlinear_formula() pour LGBM/LSTM/Markov avec scale_factor
     * **ADAPTATION LGBM :** subsample, min_child_samples, num_iterations, learning_rate
     * **ADAPTATION LSTM :** batch_size, epochs, learning_rate avec contraintes ressources
     * **ADAPTATION MARKOV :** max_order, smoothing, batch_size avec formules sophistiquées
     * **SEGMENTS ÉVALUATION :** Adaptation selon ressources (limitées→3 segments, suffisantes→5 segments)
     * **PARALLÉLISATION :** Ajustement optimal_jobs selon contraintes (limitées→jobs//4, moyennes→jobs//2)
     * **MÉTA-INFORMATIONS :** Collecte system_info (OS, CPU, RAM, GPU), data_info (patterns, entropie)
     * **CALLBACKS DYNAMIQUES :** create_dynamic_adaptation_callbacks() pour adaptation temps réel
     * **STOCKAGE COMPLET :** _meta_info et _dynamic_callbacks dans adapted_params
   - RETOUR : Dict - Paramètres adaptés avec méta-informations et callbacks dynamiques
   - UTILITÉ : Adaptation sophistiquée paramètres avec méta-apprentissage, formules non-linéaires et adaptation dynamique pour entraînement complet optimal


11. adjust_parameters_for_viability.txt (HybridBaccaratPredictor.adjust_parameters_for_viability)
   - Lignes 13432-13515 dans hbp.py (83 lignes)
   - FONCTION : Ajuste les paramètres pour garantir recommandations WAIT et NON-WAIT dans manches 31-60
   - PARAMÈTRES : has_wait_in_target (bool), has_non_wait_in_target (bool)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OBJECTIF PRINCIPAL :** Maximiser recommandations NON-WAIT valides avec WAIT stratégiques
     * **SCÉNARIO 1 - Manque de WAIT :**
       - Augmente min_confidence_for_recommendation (+0.03, max 0.85)
       - Augmente wait_ratio_min_threshold (+0.05, max 0.3)
       - Réduit error_pattern_threshold (-0.05, min 0.4)
       - Réduit transition_uncertainty_threshold (-0.05, min 0.4)
       - Augmente wait_optimizer_confidence_threshold (+0.05, max 0.85)
       - Logging détaillé des ajustements avec objectif principal
     * **SCÉNARIO 2 - Manque de NON-WAIT :**
       - Réduit significativement min_confidence_for_recommendation (-0.1, min 0.3)
       - Réduit agressivement wait_ratio_max_threshold (-0.15, min 0.2)
       - Augmente uncertainty_threshold (+0.15, max 0.8)
       - Réduit wait_optimizer_confidence_threshold (-0.15, min 0.4)
       - Logging avec focus sur favoriser NON-WAIT
     * **PHILOSOPHIE :** WAIT stratégiques augmentent taux validité NON-WAIT
     * Tous ajustements respectent bornes min/max pour éviter valeurs extrêmes
   - RETOUR : bool - True si ajustements effectués, False sinon
   - UTILITÉ : Auto-ajustement paramètres pour équilibrer WAIT/NON-WAIT selon objectif principal


12. apply_optimized_params_to_config_file.txt (HybridBaccaratPredictor.apply_optimized_params_to_config_file)
   - Lignes 2463-2581 dans hbp.py (118 lignes)
   - FONCTION : Applique les paramètres optimisés depuis params.txt directement dans config.py
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CHARGEMENT :** utilise load_params_from_file("params.txt")
     * **SAUVEGARDE :** crée backup config_backup_YYYYMMDD_HHMMSS.py
     * **LECTURE :** lit contenu config.py avec encoding UTF-8
     * **HEADER :** ajoute commentaire "Valeurs optimisées appliquées le YYYY-MM-DD"
     * **POIDS SPÉCIAUX :**
       - Extrait weight_* params et construit weights_dict
       - Normalise poids (somme = 1.0) si total > 1e-9
       - Pattern regex pour initial_weights avec fonction replace_weights
       - Remplace dictionnaire complet avec apostrophes normalisées
     * **AUTRES PARAMÈTRES :**
       - Pattern regex : (self\.param_name\s*:\s*type\s*=\s*)(valeur)
       - Formatage selon type : bool/int/float → str, str → 'str'
       - Fonction replace_value pour éviter problèmes références groupe
     * **ÉCRITURE :** sauvegarde modified_content dans config.py
     * **FEEDBACK :** messagebox succès avec liste paramètres modifiés et backup
     * **GESTION ERREURS :** try/except à chaque étape avec messagebox.showerror
   - RETOUR : None
   - UTILITÉ : Persistance automatique des paramètres optimisés dans fichier configuration


13. check_constraints.txt (MetaOptimizer.check_constraints - FONCTION VÉRIFICATION CONTRAINTES)
   - Lignes 14949-15009 dans optuna_optimizer.py (61 lignes)
   - FONCTION : Vérifie respect contraintes paramètres avec types range_dependency, conditional, sum_constraint
   - PARAMÈTRES :
     * current_params - Dictionnaire paramètres actuels à vérifier
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION VIDE :** if not current_params return True pour paramètres non définis
     * **CONTRAINTES CALLABLE :** constraint(current_params) pour fonctions personnalisées
     * **CONTRAINTES DICT :** Vérification constraint['type'] pour types spécialisés
     * **RANGE_DEPENDENCY :** Relations >, >=, <, <=, ==, != entre param1 et param2
     * **CONDITIONAL :** if_param == if_value alors then_param == then_value
     * **SUM_CONSTRAINT :** abs(sum(params) - target_sum) <= tolerance
     * **VALIDATION EXISTENCE :** param in current_params avant accès valeurs
     * **RETOUR IMMÉDIAT :** return False dès première contrainte violée
   - RETOUR : bool - True si toutes contraintes respectées, False sinon
   - UTILITÉ : Validation robuste contraintes complexes pour suggestion paramètres valides Optuna


14. create_dynamic_adaptation_callbacks.txt (OptunaOptimizer.create_dynamic_adaptation_callbacks - MÉTHODE CRÉATION CALLBACKS ADAPTATION DYNAMIQUE)
   - Lignes 12725-12848 dans optuna_optimizer.py (124 lignes)
   - FONCTION : Crée callbacks adaptation dynamique hyperparamètres pendant entraînement selon performances observées
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * adapted_params - Paramètres adaptés initiaux
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STRUCTURE CALLBACKS :** Dict avec lgbm, lstm, markov pour organisation modulaire
     * **LGBM CALLBACK :** lgbm_lr_callback() avec ajustement learning_rate *= 0.9 si stagnation toutes 10 itérations
     * **LSTM CALLBACK :** LSTMDynamicCallback classe avec on_epoch_end(), patience=3, lr *= 0.5 si val_loss stagne
     * **MARKOV ADAPTER :** MarkovDynamicAdapter avec update() et performance_history pour tendance
     * **TENDANCE MARKOV :** np.mean(np.diff(history[-3:])) pour ajustement smoothing (baisse→*1.2, hausse→*0.8)
     * **INTÉGRATION TF :** tf.keras.backend.set_value(optimizer.lr) pour mise à jour temps réel
     * **LIMITES SÉCURISÉES :** min(0.5, smoothing*1.2) et max(0.001, smoothing*0.8) pour éviter valeurs extrêmes
   - RETOUR : Dict - Callbacks structurés par modèle avec instances configurées
   - UTILITÉ : Adaptation temps réel hyperparamètres avec callbacks spécialisés pour optimisation continue performance


15. fit.txt (fit - MÉTHODE ENTRAÎNEMENT PRINCIPAL)
   - Méthode entraînement principal modèles avec optimisation hyperparamètres
   - UTILITÉ : Interface unifiée entraînement avec optimisation intégrée


16. fit_1.txt (fit - MÉTHODE ENTRAÎNEMENT - DOUBLON 1)
   - Doublon méthode entraînement avec variante implémentation
   - UTILITÉ : Alternative entraînement avec approche différente


17. get_optimized_params_for_full_training.txt (OptunaOptimizer.get_optimized_params_for_full_training - MÉTHODE RÉCUPÉRATION PARAMÈTRES OPTIMISÉS COMPLETS)
   - Lignes 3881-3935 dans optuna_optimizer.py (55 lignes)
   - FONCTION : Retourne paramètres optimisés adaptés pour entraînement complet 100% données avec validation progressive
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * use_progressive_validation (bool, défaut=True) - Utilise validation progressive pour affinage
     * use_empirical_validation (bool, défaut=True) - Valide empiriquement efficacité adaptation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PARAMÈTRES :** Vérifie existence optimal_batch_params, retourne None si absent
     * **SAUVEGARDE ORIGINAUX :** Copie original_params pour référence et comparaison
     * **ADAPTATION COMPLÈTE :** Utilise adapt_parameters_for_full_training() pour adaptation 10%→100%
     * **VALIDATION PROGRESSIVE :** validate_progressive_adaptation() si use_progressive_validation=True
     * **VALIDATION EMPIRIQUE :** validate_adaptation_empirically() si use_empirical_validation=True
     * **CLONAGE CONFIG :** Crée adapted_config depuis self.config.clone()
     * **APPLICATION PARAMÈTRES :** Applique paramètres adaptés via setattr() si hasattr()
     * **ACTIVATION MARKOV :** Force use_markov_model=True pour configuration finale
     * **PARAMÈTRES MARKOV SPÉCIALISÉS :** Applique phase_markov params si disponibles
     * **RETOUR DICTIONNAIRE :** Filtre attributs privés (_) pour retour propre
   - RETOUR : Dict - Paramètres optimisés adaptés pour entraînement complet ou None si indisponible
   - UTILITÉ : Interface principale récupération paramètres optimisés avec validation et adaptation complète


18. init_wait_placement_optimizer.txt (HybridBaccaratPredictor.init_wait_placement_optimizer)
   - Lignes 1386-1408 dans hbp.py (22 lignes)
   - FONCTION : Initialise l'optimiseur de placement des recommandations WAIT
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Vérification config :** use_wait_placement_optimizer (défaut True)
     * **Si désactivé :** self.wait_placement_optimizer = None, retourne False
     * **Si activé :** crée instance WaitPlacementOptimizer(self.config)
     * **Objectif :** apprendre où placer recommandations WAIT pour maximiser séquences NON-WAIT valides
     * **Gestion erreurs :** try/except avec logging détaillé, fallback None
     * **Logging :** info initialisation et succès/échec
   - RETOUR : bool - True si succès, False si échec ou désactivé
   - UTILITÉ : Configuration optimiseur stratégique pour placement intelligent des WAIT dans manches cibles (31-60)


19. launch_sequential_optimization.txt (OptunaOptimizer.launch_sequential_optimization - MÉTHODE ORCHESTRATION COMPLEXE)
   - Lignes 3937-4621 dans optuna_optimizer.py (685 lignes)
   - FONCTION : Méthode d'orchestration de l'optimisation séquentielle multi-niveaux avec stratégie progressive adaptée
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * n_trials_level0 (int, défaut=100) - Nombre d'essais pour exploration préliminaire (15-20% du temps)
     * n_trials_level1 (int, défaut=5) - Nombre d'essais pour exploration ciblée LGBM (25-30% du temps)
     * n_trials_markov (int, défaut=5) - Nombre d'essais pour phase Markov spécialisée
     * n_trials_level2 (int, défaut=5) - Nombre d'essais pour optimisation LSTM progressive (35-40% du temps)
     * n_trials_level3 (int, défaut=5) - Nombre d'essais pour optimisation fine complète (15-20% du temps)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ORCHESTRATION MULTI-PHASES :** Coordonne 5 phases d'optimisation séquentielle avec allocation temporelle optimisée
     * **DÉTECTION RESSOURCES :** Analyse ressources système (CPU, mémoire) pour optimiser parallélisme et batch size
     * **PRÉTRAITEMENT UNIQUE :** Prétraite données une seule fois pour toutes les phases avec cache avancé
     * **SOUS-ENSEMBLES ADAPTATIFS :** Utilise sous-ensembles de données de tailles croissantes par phase
     * **PHASE 0 - EXPLORATION LHS :** Latin Hypercube Sampling pour exploration uniforme avec scikit-optimize
     * **ESPACE RECHERCHE DYNAMIQUE :** Crée espaces de recherche adaptatifs basés sur meilleurs essais précédents
     * **PARALLÉLISME ADAPTATIF :** Ajuste nombre de workers selon ressources et phase d'optimisation
     * **TRANSITIONS PARALLÈLES :** Exécute transitions entre phases avec génération de variations paramétriques
     * **PHASE 1 - LGBM COMPLET :** Exploration ciblée avec LGBM complet et validation croisée
     * **PHASE MARKOV SPÉCIALISÉE :** Phase dédiée Markov pour compatibilité avec stratégie prog
     * **PHASE 2 - LSTM PROGRESSIF :** Introduction LSTM avec 1 époque pour optimisation progressive
     * **PHASE 3 - OPTIMISATION FINE :** Optimisation finale avec modèles complets et paramètres affinés
     * **GESTION ARRÊT :** Vérifications stop_requested() à chaque phase pour arrêt propre
     * **OPTIMISATION MÉMOIRE :** Optimise usage mémoire entre phases avec snapshots et profilage
     * **SÉLECTION ÉLITE :** Sélectionne top essais de chaque phase pour alimenter phase suivante
     * **VALIDATION CROISÉE :** Active validation croisée progressive selon complexité de la phase
     * **EARLY STOPPING :** Implémente arrêt précoce adaptatif pour éviter surentraînement
     * **LOGGING STRUCTURÉ :** Journalisation détaillée avec séparateurs visuels et métriques de progression
     * **SAUVEGARDE ÉTAT :** Sauvegarde paramètres originaux et restauration en cas d'interruption
     * **STRATÉGIES ÉCHANTILLONNAGE :** Utilise différents samplers (Random, TPE) selon phase d'optimisation
     * **ALLOCATION RESSOURCES :** Allocation dynamique workers et batch size selon phase et ressources
     * **MÉTRIQUES TRANSITION :** Mesure temps de transition et efficacité entre phases
     * **ROBUSTESSE ERREURS :** Gestion robuste des erreurs avec fallback vers phases précédentes
   - RETOUR : Dict - Meilleurs paramètres trouvés, adaptés pour entraînement complet sur 100% des données
   - UTILITÉ : Orchestrateur principal d'optimisation séquentielle multi-niveaux avec allocation optimale des ressources et progression adaptative


20. optimize.txt (OptunaOptimizer.optimize - MÉTHODE POINT ENTRÉE PRINCIPAL OPTIMISATION)
   - Lignes 3459-3526 dans optuna_optimizer.py (68 lignes)
   - FONCTION : Point entrée principal optimisation séquentielle avec stratégie phases et allocation ressources
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * n_trials (optionnel) - Nombre essais à effectuer (si None, utilise max_trials config)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION DONNÉES :** Vérifie X_lgbm_full, y_full, X_lstm_full disponibles
     * **CALCUL ESSAIS TOTAL :** total_trials = n_trials ou self.max_trials
     * **LOGGING STRATÉGIE :** Affiche ressources CPU/RAM et stratégie optimisation
     * **SAUVEGARDE LSTM :** Stocke original_lstm_epochs pour restauration
     * **ALLOCATION PHASES :** Phase0=100 essais, Phase1/Markov/2/3=2 essais chacune
     * **ALLOCATION WORKERS :** level0_jobs=min(6,cpu_count), autres=2 workers
     * **LOGGING RÉPARTITION :** Détaille essais et workers par phase
     * **FONCTION ARRÊT :** Vérifie stop_requested callable ou définit lambda: False
     * **DÉLÉGATION SÉQUENTIELLE :** Appelle launch_sequential_optimization() avec paramètres
   - RETOUR : Dict - Meilleurs paramètres trouvés par optimisation séquentielle
   - UTILITÉ : Interface principale optimisation avec stratégie phases et gestion ressources automatique


21. optimize_1.txt (optimize - MÉTHODE OPTIMISATION - DOUBLON 1)
   - Doublon méthode optimisation avec variante algorithme
   - UTILITÉ : Alternative optimisation avec stratégie différente


22. predict.txt (predict - MÉTHODE PRÉDICTION PRINCIPALE)
   - Méthode prédiction principale avec modèles optimisés
   - UTILITÉ : Interface unifiée prédiction avec modèles entraînés


23. predict_1.txt (predict - MÉTHODE PRÉDICTION - DOUBLON 1)
   - Doublon méthode prédiction avec variante implémentation
   - UTILITÉ : Alternative prédiction avec approche différente


24. predict_proba.txt (predict_proba - MÉTHODE PRÉDICTION PROBABILITÉS)
   - Méthode prédiction probabilités avec confiance
   - UTILITÉ : Prédiction probabilités détaillées avec métriques confiance


25. real_fit_model.txt (real_fit_model - ENTRAÎNEMENT RÉEL MODÈLE)
   - Entraînement réel modèle avec paramètres optimisés
   - UTILITÉ : Entraînement final modèle avec hyperparamètres optimaux


26. run_hyperparameter_optimization.txt (HybridBaccaratPredictor.run_hyperparameter_optimization)
   - Lignes 12836-13032 dans hbp.py (196 lignes)
   - FONCTION : Lance l'optimisation des hyperparamètres avec stratégie multi-niveaux adaptée CPU
   - PARAMÈTRES : n_trials (int, défaut 20) - nombre d'essais à effectuer
   - FONCTIONNEMENT DÉTAILLÉ :
     * Vérifications préliminaires :
       - Vérifie qu'aucune tâche ML n'est en cours (training_lock)
       - Contrôle disponibilité données historiques (loaded_historical)
       - Vérifie seuil minimum 10 jeux historiques pour optimisation
       - Demande confirmation utilisateur (interface bloquée pendant optimisation)
     * Préparation données :
       - Appelle _prepare_training_data(force_use_historical=True)
       - Valide package complet (8 éléments) et données suffisantes (≥20 échantillons)
       - Extrait X_lgbm_all, y_all, X_lstm_all, train_idx, val_idx
     * Configuration optimisation multi-niveaux :
       - Active flags : is_training=True, is_optuna_running=True
       - Réinitialise flags de log pour éviter spam
       - Options avancées : multi_level, adaptive_regularization, SWA, meta_learning, temporal_cv
       - Configuration ressources : cpu_count=8, ram_gb=28, batch_size=1024
     * Initialisation OptunaOptimizer :
       - Réinitialise compteurs globaux (optimized_viable_trials_count, total_attempts_made)
       - Transmet options avancées et ressources disponibles
       - Assigne données complètes et indices train/validation
     * Gestionnaire de thread OptunaThreadManager :
       - Callbacks : success_callback, error_callback, progress_callback
       - Exécution asynchrone avec finalisation via _finalize_optuna_optimization
       - Gestion d'erreurs ImportError pour modules optuna_optimizer
   - RETOUR : None (exécution asynchrone)
   - UTILITÉ : Point d'entrée principal pour optimisation hyperparamètres avec gestion complète ressources et UI


TOTAL : 26 méthodes analysées et documentées
- Méthodes trouvées dans descriptif principal: 24
- Méthodes manquantes: 2