# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 11478 à 11798
# Type: Méthode de la classe OptunaOptimizer

    def _optimize_with_adaptive_parallelism(self, study, objective_func, n_trials, max_jobs=4, min_jobs=1, adaptive_interval=10):
        """
        Optimise une étude Optuna avec un niveau de parallélisme adaptatif.
        Ajuste dynamiquement le nombre de jobs en fonction des performances.

        Args:
            study: Étude Optuna à optimiser
            objective_func: Fonction objectif à maximiser/minimiser
            n_trials: Nombre total d'essais à effectuer
            max_jobs: Nombre maximum de jobs en parallèle
            min_jobs: Nombre minimum de jobs en parallèle
            adaptive_interval: Intervalle d'adaptation du nombre de jobs

        Returns:
            optuna.study.Study: L'étude optimisée
        """
        # Utiliser les ressources détectées si disponibles
        if hasattr(self, 'optimal_batch_params') and 'optimal_jobs' in self.optimal_batch_params:
            # Déterminer la phase actuelle en fonction du nom de l'étude
            study_name = study.study_name.lower() if study.study_name else ""

            if "phase0" in study_name or "level0" in study_name:
                phase_key = 'phase0'
            elif "phase1" in study_name or "level1" in study_name:
                phase_key = 'phase1'
            elif "markov" in study_name:
                phase_key = 'phase_markov'
            elif "phase2" in study_name or "level2" in study_name:
                phase_key = 'phase2'
            elif "phase3" in study_name or "level3" in study_name:
                phase_key = 'phase3'
            else:
                phase_key = 'phase0'  # Par défaut

            # Utiliser le nombre optimal de jobs pour cette phase
            optimal_jobs = self.optimal_batch_params['optimal_jobs'].get(phase_key, max_jobs)
            logger.warning(f"Utilisation du nombre optimal de jobs pour {phase_key}: {optimal_jobs}")

            # Limiter entre min_jobs et max_jobs
            max_jobs = min(max_jobs, optimal_jobs)
            max_jobs = max(min_jobs, max_jobs)

        # Initialiser l'historique des performances si nécessaire
        if not hasattr(self, 'parallelism_history'):
            self.parallelism_history = {
                'batch_durations': [],
                'avg_trial_durations': [],
                'worker_counts': [],
                'memory_usages': [],
                'cpu_usages': [],
                'batch_success_rates': []
            }

        # Détecter les ressources système disponibles
        try:
            import psutil
            cpu_count = psutil.cpu_count(logical=False)
            total_memory_gb = psutil.virtual_memory().total / (1024**3)
            logger.warning(f"Ressources système détectées: {cpu_count} cœurs CPU, {total_memory_gb:.1f} GB RAM")
        except ImportError:
            cpu_count = os.cpu_count() or 4
            total_memory_gb = 8.0  # Valeur par défaut conservative
            logger.warning(f"psutil non disponible, utilisation de valeurs par défaut: {cpu_count} cœurs CPU, {total_memory_gb:.1f} GB RAM")

        # Ajuster le nombre maximum de workers en fonction des ressources système
        system_max_jobs = max(1, min(cpu_count, int(total_memory_gb / 2)))
        adjusted_max_jobs = min(max_jobs, system_max_jobs)

        if adjusted_max_jobs < max_jobs:
            logger.warning(f"Nombre maximum de workers ajusté à {adjusted_max_jobs} en fonction des ressources système")
            max_jobs = adjusted_max_jobs

        # Commencer avec un nombre modéré de workers pour l'exploration initiale
        # Utiliser 75% des workers disponibles pour éviter de surcharger le système au début
        current_jobs = max(1, int(max_jobs * 0.75))

        # Calculer la taille de lot optimale en fonction du nombre d'essais et de workers
        # Plus petit au début pour permettre des ajustements rapides, plus grand ensuite
        initial_batch_size = min(5, n_trials)
        max_batch_size = min(20, n_trials)
        current_batch_size = initial_batch_size

        # Calculer le nombre de lots initial (sera ajusté dynamiquement)
        remaining_trials = n_trials
        batches = []

        while remaining_trials > 0:
            batch_size = min(current_batch_size, remaining_trials)
            batches.append(batch_size)
            remaining_trials -= batch_size
            # Augmenter progressivement la taille des lots
            current_batch_size = min(max_batch_size, current_batch_size + 2)

        num_batches = len(batches)
        logger.warning(f"Optimisation adaptative avec {num_batches} lots (taille variable) pour {n_trials} essais")

        # Variables pour suivre les performances
        total_trials_completed = 0
        exploration_phase = True  # Commencer en phase d'exploration

        for batch_idx, batch_size in enumerate(batches):
            # Mesurer l'utilisation des ressources système avant le lot
            try:
                current_memory_usage = psutil.virtual_memory().percent
                current_cpu_usage = psutil.cpu_percent(interval=0.5)
                logger.info(f"Utilisation des ressources: CPU {current_cpu_usage:.1f}%, RAM {current_memory_usage:.1f}%")

                # Ajuster le nombre de workers en fonction de l'utilisation des ressources
                if current_memory_usage > 85:
                    # Mémoire presque pleine, réduire le nombre de workers
                    resource_adjusted_jobs = max(1, current_jobs // 2)
                    if resource_adjusted_jobs < current_jobs:
                        logger.warning(f"Mémoire élevée ({current_memory_usage:.1f}%), réduction du parallélisme à {resource_adjusted_jobs} workers")
                        current_jobs = resource_adjusted_jobs
                elif current_cpu_usage > 90:
                    # CPU très chargé, réduire légèrement le nombre de workers
                    resource_adjusted_jobs = max(1, int(current_jobs * 0.8))
                    if resource_adjusted_jobs < current_jobs:
                        logger.warning(f"CPU très chargé ({current_cpu_usage:.1f}%), réduction du parallélisme à {resource_adjusted_jobs} workers")
                        current_jobs = resource_adjusted_jobs

                # Enregistrer l'utilisation des ressources
                self.parallelism_history['memory_usages'].append(current_memory_usage)
                self.parallelism_history['cpu_usages'].append(current_cpu_usage)
            except:
                logger.warning("Impossible de mesurer l'utilisation des ressources système")

            # Déterminer si nous sommes en phase d'exploration ou d'exploitation
            # Transition vers l'exploitation après avoir complété environ 1/3 des essais
            if total_trials_completed > n_trials / 3 and exploration_phase:
                exploration_phase = False
                logger.warning(f"Transition vers la phase d'exploitation après {total_trials_completed}/{n_trials} essais")

                # En phase d'exploitation, nous pouvons réduire le parallélisme pour une meilleure utilisation des ressources
                # car nous voulons des évaluations plus précises et moins d'exploration parallèle
                if current_jobs > 1:
                    exploitation_jobs = max(1, int(current_jobs * 0.7))
                    logger.warning(f"Réduction du parallélisme pour la phase d'exploitation: {current_jobs} -> {exploitation_jobs} workers")
                    current_jobs = exploitation_jobs

            logger.warning(f"Lot {batch_idx+1}/{num_batches}: {batch_size} essais avec {current_jobs} workers")

            # Mesurer le temps de début
            batch_start_time = time.time()

            # Nombre d'essais complétés avant ce lot
            pre_batch_completed = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])

            # Optimiser ce lot
            # Recharger le module config pour prendre en compte les modifications des plages
            if batch_idx > 0:  # Ne pas recharger pour le premier lot
                try:
                    import sys
                    import importlib

                    # Recharger le module config pour prendre en compte les modifications
                    if 'config' in sys.modules:
                        importlib.reload(sys.modules['config'])
                        logger.info("Module config rechargé avant l'optimisation du lot")
                except Exception as e:
                    logger.error(f"Erreur lors du rechargement du module config: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

            # Créer un callback personnalisé pour simplifier l'affichage des essais
            class SimplifiedTrialPrinter:
                """
                Callback personnalisé pour simplifier l'affichage des essais Optuna.
                Supprime l'affichage des paramètres dans les messages de fin d'essai.
                """
                def __init__(self):
                    pass

                def __call__(self, study, trial):
                    # Ce callback ne fait rien, le filtrage est géré par OptunaMessageFilter
                    pass

                def restore_print(self):
                    # Méthode vide pour compatibilité
                    pass

            # Créer le callback pour simplifier l'affichage des essais
            simplified_printer = SimplifiedTrialPrinter()

            try:
                study.optimize(
                    objective_func,
                    n_trials=batch_size,
                    n_jobs=current_jobs,
                    callbacks=[simplified_printer]
                )
            finally:
                # Restaurer la fonction print originale
                simplified_printer.restore_print()

            # Mesurer le temps de fin
            batch_end_time = time.time()
            batch_duration = batch_end_time - batch_start_time

            # Enregistrer la durée du lot
            self.parallelism_history['batch_durations'].append(batch_duration)
            self.parallelism_history['worker_counts'].append(current_jobs)

            # Analyser les résultats du lot
            all_completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
            post_batch_completed = len(all_completed_trials)
            batch_completed = post_batch_completed - pre_batch_completed

            # Calculer le taux de réussite du lot
            batch_success_rate = batch_completed / batch_size if batch_size > 0 else 0
            self.parallelism_history['batch_success_rates'].append(batch_success_rate)

            # Mettre à jour le nombre total d'essais complétés
            total_trials_completed = post_batch_completed

            if batch_completed > 0:
                # Calculer le temps moyen par essai
                avg_duration = batch_duration / batch_completed
                self.parallelism_history['avg_trial_durations'].append(avg_duration)

                # Ajuster le nombre de workers en fonction du temps moyen et du taux de réussite
                if avg_duration > 120:  # Si les essais prennent plus de 2 minutes
                    # Réduction plus agressive pour les essais très longs
                    reduction_factor = 0.5 if avg_duration > 300 else 0.7
                    new_jobs = max(1, int(current_jobs * reduction_factor))
                    if new_jobs != current_jobs:
                        logger.warning(f"Essais longs détectés (moy. {avg_duration:.1f}s). Réduction du parallélisme à {new_jobs} workers.")
                        current_jobs = new_jobs
                elif avg_duration < 30 and current_jobs < max_jobs and batch_success_rate > 0.8:
                    # Augmentation plus prudente, seulement si le taux de réussite est bon
                    increase_factor = 1.5  # Plus prudent que doubler
                    new_jobs = min(max_jobs, int(current_jobs * increase_factor))
                    if new_jobs != current_jobs:
                        logger.warning(f"Essais rapides et fiables détectés (moy. {avg_duration:.1f}s, succès {batch_success_rate:.1%}). Augmentation du parallélisme à {new_jobs} workers.")
                        current_jobs = new_jobs

                # Ajuster également la taille du prochain lot en fonction des performances
                if batch_idx < len(batches) - 1:  # S'il reste des lots à traiter
                    if avg_duration < 30 and batch_success_rate > 0.9:
                        # Essais rapides et fiables: augmenter la taille du prochain lot
                        batches[batch_idx + 1] = min(max_batch_size, int(batches[batch_idx + 1] * 1.2))
                        logger.info(f"Taille du prochain lot augmentée à {batches[batch_idx + 1]}")
                    elif avg_duration > 120 or batch_success_rate < 0.5:
                        # Essais longs ou peu fiables: réduire la taille du prochain lot
                        batches[batch_idx + 1] = max(1, int(batches[batch_idx + 1] * 0.8))
                        logger.info(f"Taille du prochain lot réduite à {batches[batch_idx + 1]}")
            else:
                # Aucun essai complété dans ce lot, réduire drastiquement le parallélisme
                logger.warning("Aucun essai complété dans ce lot. Réduction drastique du parallélisme.")
                current_jobs = max(1, current_jobs // 3)

            # Analyser les tendances des performances sur les derniers lots
            if len(self.parallelism_history['batch_durations']) >= 3:
                recent_durations = self.parallelism_history['batch_durations'][-3:]
                recent_success_rates = self.parallelism_history['batch_success_rates'][-3:]

                # Détecter une tendance à la dégradation des performances
                if all(d > 1.2 * recent_durations[0] for d in recent_durations[1:]) or all(r < 0.7 for r in recent_success_rates):
                    logger.warning("Dégradation des performances détectée sur les derniers lots. Ajustement du parallélisme.")
                    current_jobs = max(1, int(current_jobs * 0.7))

            # Libérer la mémoire entre les lots
            self._optimize_memory_usage()

            # Ajuster les plages Optuna en fonction des meilleurs essais
            if hasattr(self, 'range_adjuster') and batch_idx > 0:  # Ne pas ajuster après le premier lot
                try:
                    # Vérifier s'il y a des essais complétés
                    completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
                    if completed_trials:
                        # Ajuster les plages en fonction des meilleurs essais
                        logger.warning(f"Ajustement des plages Optuna après le lot {batch_idx+1}/{num_batches}")
                        self.range_adjuster.adjust_ranges_for_study(study)
                except Exception as e:
                    logger.error(f"Erreur lors de l'ajustement des plages Optuna: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

            # Vérifier si l'arrêt a été demandé
            if hasattr(self, 'stop_requested') and callable(self.stop_requested) and self.stop_requested():
                logger.warning(f"Arrêt demandé après le lot {batch_idx+1}/{num_batches}")
                break

            # Pause courte entre les lots pour permettre au système de se stabiliser
            time.sleep(1)

        # Analyser les performances globales de l'optimisation
        if len(self.parallelism_history['batch_durations']) > 0:
            total_duration = sum(self.parallelism_history['batch_durations'])
            avg_workers = sum(self.parallelism_history['worker_counts']) / len(self.parallelism_history['worker_counts'])
            avg_success_rate = sum(self.parallelism_history['batch_success_rates']) / len(self.parallelism_history['batch_success_rates'])

            logger.warning(f"Optimisation terminée en {total_duration:.1f}s avec {total_trials_completed}/{n_trials} essais complétés")
            logger.warning(f"Nombre moyen de workers: {avg_workers:.1f}, taux de réussite moyen: {avg_success_rate:.1%}")

            # Finaliser les ajustements des plages pour inclure toutes les valeurs hors plage
            if hasattr(self, 'range_adjuster'):
                try:
                    logger.warning("Finalisation des ajustements des plages à la fin de l'optimisation")
                    # Effectuer une dernière vérification des paramètres hors plage
                    self.range_adjuster.adjust_ranges_for_study(study)
                    # Finaliser les ajustements dans config.py
                    self.range_adjuster.finalize_adjustments(save_to_config=True)
                    logger.warning("Plages finalisées avec succès dans config.py")
                except Exception as e:
                    logger.error(f"Erreur lors de la finalisation des ajustements des plages: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

            # Enregistrer les statistiques pour les futures optimisations
            if not hasattr(self, 'optimization_stats'):
                self.optimization_stats = {}

            self.optimization_stats['last_optimization'] = {
                'total_duration': total_duration,
                'total_trials': n_trials,
                'completed_trials': total_trials_completed,
                'avg_workers': avg_workers,
                'avg_success_rate': avg_success_rate,
                'optimal_workers': current_jobs  # Le nombre final de workers est considéré comme optimal
            }