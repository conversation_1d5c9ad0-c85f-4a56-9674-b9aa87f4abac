# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12605 à 12723
# Type: Méthode de la classe OptunaOptimizer

    def _predict_optimal_params_with_meta_learning(self, dataset_features):
        """
        Prédit les hyperparamètres optimaux en fonction des caractéristiques du jeu de données.

        Cette méthode utilise des règles de méta-apprentissage pour prédire les hyperparamètres
        optimaux en fonction des caractéristiques du jeu de données.

        Args:
            dataset_features: Caractéristiques du jeu de données

        Returns:
            Dict: Hyperparamètres prédits
        """
        import math

        # Paramètres par défaut
        predicted_params = {
            'lgbm_subsample': 0.8,
            'lgbm_min_child_samples': 20,
            'lgbm_num_iterations': 200,
            'lgbm_learning_rate': 0.1,
            'lstm_batch_size': 32,
            'lstm_epochs': 5,
            'lstm_hidden_size': 128,
            'lstm_num_layers': 2,
            'lstm_learning_rate': 0.001,
            'max_markov_order': 4,
            'markov_smoothing': 0.1,
            'markov_global_weight': 0.6,
            'markov_context_weight': 0.7,
            'markov_decay_factor': 0.98,
            'markov_batch_size': 100,
            'use_markov_model': True
        }

        try:
            # Extraire les caractéristiques
            total_lines = dataset_features.get('total_lines', 0)
            pattern_diversity = dataset_features.get('pattern_diversity', 0.0)
            alternation_rate = dataset_features.get('alternation_rate', 0.0)
            p_frequency = dataset_features.get('p_frequency', 0.5)
            b_frequency = dataset_features.get('b_frequency', 0.5)
            longest_streak = dataset_features.get('longest_streak', 0)
            avg_streak_length = dataset_features.get('avg_streak_length', 0.0)
            entropy = dataset_features.get('entropy', 0.0)

            # Calculer la complexité globale du jeu de données
            complexity = 0.5 + 0.5 * (pattern_diversity + alternation_rate + entropy)

            # Calculer le déséquilibre des classes
            class_imbalance = abs(p_frequency - b_frequency)

            # Calculer la prévisibilité (inverse de l'entropie)
            predictability = 1.0 - min(1.0, entropy)

            # Calculer la tendance aux streaks
            streak_tendency = min(1.0, avg_streak_length / 5.0)

            logger.info(f"Méta-caractéristiques calculées: complexité={complexity:.2f}, "
                       f"déséquilibre={class_imbalance:.2f}, prévisibilité={predictability:.2f}, "
                       f"tendance aux streaks={streak_tendency:.2f}")

            # Ajuster les paramètres LGBM en fonction des caractéristiques
            # 1. subsample: réduire pour les données complexes ou déséquilibrées
            predicted_params['lgbm_subsample'] = max(0.3, min(0.9, 0.9 - 0.3 * complexity - 0.2 * class_imbalance))

            # 2. min_child_samples: augmenter pour les données complexes ou avec beaucoup de streaks
            base_min_child = 20
            if total_lines > 0:
                base_min_child = max(5, min(100, int(total_lines * 0.0005)))
            predicted_params['lgbm_min_child_samples'] = int(base_min_child * (1 + complexity + streak_tendency))

            # 3. num_iterations: augmenter pour les données complexes ou imprévisibles
            predicted_params['lgbm_num_iterations'] = int(200 * (1 + 0.5 * complexity + 0.3 * (1 - predictability)))

            # 4. learning_rate: réduire pour les données complexes ou imprévisibles
            predicted_params['lgbm_learning_rate'] = max(0.01, 0.1 * (1 - 0.5 * complexity - 0.3 * (1 - predictability)))

            # Ajuster les paramètres LSTM en fonction des caractéristiques
            # 1. batch_size: réduire pour les données complexes ou avec beaucoup de streaks
            predicted_params['lstm_batch_size'] = max(8, int(32 * (1 - 0.3 * complexity - 0.2 * streak_tendency)))

            # 2. epochs: augmenter pour les données complexes ou imprévisibles
            predicted_params['lstm_epochs'] = max(3, int(5 * (1 + 0.4 * complexity + 0.3 * (1 - predictability))))

            # 3. hidden_size: augmenter pour les données complexes ou avec beaucoup de patterns
            predicted_params['lstm_hidden_size'] = max(64, int(128 * (1 + 0.3 * complexity + 0.2 * pattern_diversity)))

            # 4. num_layers: augmenter pour les données complexes
            predicted_params['lstm_num_layers'] = max(1, min(3, int(2 + complexity)))

            # 5. learning_rate: réduire pour les données complexes ou imprévisibles
            predicted_params['lstm_learning_rate'] = max(0.0001, 0.001 * (1 - 0.4 * complexity - 0.3 * (1 - predictability)))

            # Ajuster les paramètres Markov en fonction des caractéristiques
            # 1. max_markov_order: augmenter pour les données avec des streaks longs ou complexes
            predicted_params['max_markov_order'] = max(3, min(8, int(4 + streak_tendency * 3 + complexity * 2)))

            # 2. smoothing: augmenter pour les données imprévisibles, réduire pour les données prévisibles
            predicted_params['markov_smoothing'] = max(0.01, min(0.5, 0.1 * (1 + 2 * (1 - predictability) - streak_tendency)))

            # 3. batch_size: réduire pour les données complexes
            predicted_params['markov_batch_size'] = max(20, int(100 * (1 - 0.3 * complexity)))

            # 4. markov_global_weight: augmenter pour les données prévisibles avec des streaks
            predicted_params['markov_global_weight'] = max(0.3, min(0.9, 0.6 + 0.2 * predictability + 0.1 * streak_tendency))

            # 5. markov_context_weight: augmenter pour les données avec des patterns complexes
            predicted_params['markov_context_weight'] = max(0.3, min(0.9, 0.7 + 0.2 * pattern_diversity))

            # 6. markov_decay_factor: augmenter pour les données stables, réduire pour les données changeantes
            predicted_params['markov_decay_factor'] = max(0.9, min(0.999, 0.98 + 0.01 * predictability - 0.01 * alternation_rate))

            logger.info(f"Hyperparamètres prédits par méta-apprentissage: {predicted_params}")
            return predicted_params

        except Exception as e:
            logger.error(f"Erreur lors de la prédiction des hyperparamètres par méta-apprentissage: {e}")
            return predicted_params