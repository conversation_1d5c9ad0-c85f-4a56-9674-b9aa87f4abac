# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12850 à 13125
# Type: Méthode de la classe OptunaOptimizer

    def adapt_parameters_for_full_training(self, optimized_params):
        """
        Adapte les hyperparamètres optimisés sur 10% des données pour un entraînement complet sur 100% des données.

        Cette méthode utilise des formules non linéaires sophistiquées, le méta-apprentissage et prend en compte
        la complexité des données et les contraintes de ressources pour adapter les hyperparamètres de manière optimale.
        Elle crée également des callbacks pour l'adaptation dynamique pendant l'entraînement.

        Args:
            optimized_params: Dictionnaire des paramètres optimisés par Optuna

        Returns:
            Dict: Paramètres adaptés pour l'entraînement complet
        """
        # Copier les paramètres pour ne pas modifier l'original
        adapted_params = optimized_params.copy()

        # Calculer le facteur d'échelle dynamique
        scale_factor = self._calculate_dynamic_scale_factor()
        logger.warning("=" * 80)
        logger.warning(f"ADAPTATION DES PARAMÈTRES POUR L'ENTRAÎNEMENT COMPLET (facteur d'échelle: {scale_factor:.2f})")
        logger.warning("=" * 80)

        # Estimer la complexité des données
        data_complexity = self._estimate_data_complexity()

        # Estimer les contraintes de ressources
        resource_constraints = self._estimate_resource_constraints()

        # Extraire les caractéristiques du jeu de données pour le méta-apprentissage
        dataset_features = self._extract_dataset_features()

        # Prédire les hyperparamètres optimaux avec le méta-apprentissage
        meta_learning_params = self._predict_optimal_params_with_meta_learning(dataset_features)

        logger.warning("=" * 80)
        logger.warning("PARAMÈTRES PRÉDITS PAR MÉTA-APPRENTISSAGE")
        logger.warning("=" * 80)
        for param_name, param_value in meta_learning_params.items():
            logger.warning(f"  - {param_name}: {param_value}")

        # Combiner les paramètres optimisés par Optuna avec les prédictions du méta-apprentissage
        # Utiliser une pondération pour favoriser les paramètres optimisés par Optuna
        # tout en tenant compte des prédictions du méta-apprentissage
        optuna_weight = 0.7  # 70% de poids pour les paramètres optimisés par Optuna
        meta_weight = 0.3    # 30% de poids pour les prédictions du méta-apprentissage

        # Paramètres numériques à combiner
        numeric_params = [
            'lgbm_subsample', 'lgbm_min_child_samples', 'lgbm_num_iterations', 'lgbm_learning_rate',
            'lstm_batch_size', 'lstm_epochs', 'lstm_hidden_size', 'lstm_num_layers', 'lstm_learning_rate',
            'max_markov_order', 'markov_smoothing', 'markov_batch_size', 'markov_global_weight',
            'markov_context_weight', 'markov_decay_factor'
        ]

        # Combiner les paramètres numériques
        for param in numeric_params:
            if param in adapted_params and param in meta_learning_params:
                optuna_value = adapted_params[param]
                meta_value = meta_learning_params[param]

                # Combiner les valeurs avec pondération
                if isinstance(optuna_value, (int, float)) and isinstance(meta_value, (int, float)):
                    combined_value = optuna_value * optuna_weight + meta_value * meta_weight

                    # Arrondir à l'entier le plus proche pour les paramètres entiers
                    if isinstance(optuna_value, int):
                        combined_value = int(round(combined_value))

                    logger.warning(f"Combinaison de {param}: Optuna={optuna_value}, Méta={meta_value}, Combiné={combined_value}")
                    adapted_params[param] = combined_value
            elif param in meta_learning_params and param not in adapted_params:
                # Si le paramètre n'est pas dans les paramètres optimisés, utiliser la prédiction du méta-apprentissage
                adapted_params[param] = meta_learning_params[param]
                logger.warning(f"Ajout de {param} depuis le méta-apprentissage: {meta_learning_params[param]}")

        logger.warning("=" * 80)
        logger.warning("ADAPTATION AVEC FORMULES NON LINÉAIRES")
        logger.warning("=" * 80)

        # 1. Adapter les paramètres LGBM avec des formules non linéaires
        if 'lgbm_subsample' in adapted_params:
            original_subsample = adapted_params['lgbm_subsample']
            adapted_params['lgbm_subsample'] = self._apply_nonlinear_formula(
                'lgbm_subsample', original_subsample, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"LGBM subsample: {original_subsample} -> {adapted_params['lgbm_subsample']}")

        if 'lgbm_min_child_samples' in adapted_params:
            original_min_child = adapted_params['lgbm_min_child_samples']
            adapted_params['lgbm_min_child_samples'] = self._apply_nonlinear_formula(
                'lgbm_min_child_samples', original_min_child, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"LGBM min_child_samples: {original_min_child} -> {adapted_params['lgbm_min_child_samples']}")

        if 'lgbm_num_iterations' in adapted_params:
            original_iterations = adapted_params['lgbm_num_iterations']
            adapted_params['lgbm_num_iterations'] = self._apply_nonlinear_formula(
                'lgbm_num_iterations', original_iterations, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"LGBM num_iterations: {original_iterations} -> {adapted_params['lgbm_num_iterations']}")

        if 'lgbm_learning_rate' in adapted_params:
            original_lr = adapted_params['lgbm_learning_rate']
            adapted_params['lgbm_learning_rate'] = self._apply_nonlinear_formula(
                'lgbm_learning_rate', original_lr, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"LGBM learning_rate: {original_lr} -> {adapted_params['lgbm_learning_rate']}")

        # 2. Adapter les paramètres LSTM avec des formules non linéaires
        if 'lstm_batch_size' in adapted_params:
            original_batch = adapted_params['lstm_batch_size']
            adapted_params['lstm_batch_size'] = self._apply_nonlinear_formula(
                'lstm_batch_size', original_batch, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"LSTM batch_size: {original_batch} -> {adapted_params['lstm_batch_size']}")

        if 'lstm_epochs' in adapted_params:
            original_epochs = adapted_params['lstm_epochs']
            adapted_params['lstm_epochs'] = self._apply_nonlinear_formula(
                'lstm_epochs', original_epochs, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"LSTM epochs: {original_epochs} -> {adapted_params['lstm_epochs']}")

        if 'lstm_learning_rate' in adapted_params:
            original_lr = adapted_params['lstm_learning_rate']
            adapted_params['lstm_learning_rate'] = self._apply_nonlinear_formula(
                'lstm_learning_rate', original_lr, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"LSTM learning_rate: {original_lr} -> {adapted_params['lstm_learning_rate']}")

        # 3. Adapter les paramètres Markov avec des formules non linéaires
        if 'max_markov_order' in adapted_params:
            original_depth = adapted_params['max_markov_order']
            adapted_params['max_markov_order'] = self._apply_nonlinear_formula(
                'max_markov_order', original_depth, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"Markov order: {original_depth} -> {adapted_params['max_markov_order']}")

        if 'markov_smoothing' in adapted_params:
            original_smoothing = adapted_params['markov_smoothing']
            adapted_params['markov_smoothing'] = self._apply_nonlinear_formula(
                'markov_smoothing', original_smoothing, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"Markov smoothing: {original_smoothing} -> {adapted_params['markov_smoothing']}")

        if 'markov_batch_size' in adapted_params:
            original_batch = adapted_params['markov_batch_size']
            adapted_params['markov_batch_size'] = self._apply_nonlinear_formula(
                'markov_batch_size', original_batch, scale_factor, data_complexity, resource_constraints
            )
            logger.info(f"Markov batch_size: {original_batch} -> {adapted_params['markov_batch_size']}")

        # 4. Adapter les paramètres d'évaluation en fonction du facteur d'échelle et des ressources
        if 'evaluation_segments' in adapted_params:
            # Utiliser des segments plus petits pour un jeu de données plus grand
            total_samples = adapted_params.get('total_samples', 1000) * scale_factor

            # Ajuster les segments en fonction des contraintes de ressources
            if resource_constraints > 1.5:  # Ressources très limitées
                segments = [
                    int(0.10 * total_samples),  # 10% des données complètes
                    int(0.25 * total_samples),  # 25% des données complètes
                    total_samples               # 100% des données complètes
                ]
            elif resource_constraints > 1.0:  # Ressources limitées
                segments = [
                    int(0.05 * total_samples),  # 5% des données complètes
                    int(0.15 * total_samples),  # 15% des données complètes
                    int(0.50 * total_samples),  # 50% des données complètes
                    total_samples               # 100% des données complètes
                ]
            else:  # Ressources suffisantes
                segments = [
                    int(0.05 * total_samples),  # 5% des données complètes
                    int(0.10 * total_samples),  # 10% des données complètes
                    int(0.25 * total_samples),  # 25% des données complètes
                    int(0.50 * total_samples),  # 50% des données complètes
                    total_samples               # 100% des données complètes
                ]

            adapted_params['evaluation_segments'] = segments
            logger.info(f"Segments d'évaluation adaptés pour l'entraînement complet: {segments}")

        # 5. Adapter les paramètres de parallélisation en fonction des ressources
        if 'optimal_jobs' in adapted_params:
            original_jobs = adapted_params['optimal_jobs']
            adapted_jobs = {}

            # Ajuster le nombre de jobs en fonction des contraintes de ressources
            for phase, jobs in original_jobs.items():
                if resource_constraints > 1.5:  # Ressources très limitées
                    # Réduire drastiquement le parallélisme
                    adapted_jobs[phase] = max(1, jobs // 4)
                elif resource_constraints > 1.0:  # Ressources limitées
                    # Réduire modérément le parallélisme
                    adapted_jobs[phase] = max(1, jobs // 2)
                else:  # Ressources suffisantes
                    # Maintenir le parallélisme pour les phases initiales, réduire pour les phases avancées
                    if phase in ['phase2', 'phase3']:
                        adapted_jobs[phase] = max(1, jobs // 2)
                    else:
                        adapted_jobs[phase] = jobs

            adapted_params['optimal_jobs'] = adapted_jobs
            logger.info(f"Jobs adaptés pour l'entraînement complet: {adapted_jobs}")

        # Stocker les méta-informations pour référence future
        import time
        import platform
        import psutil
        import torch
        from datetime import datetime

        # Collecter des informations sur l'environnement
        system_info = {
            'os': platform.system(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(logical=False),
            'logical_cpu_count': psutil.cpu_count(),
            'total_memory_gb': psutil.virtual_memory().total / (1024**3),
            'available_memory_gb': psutil.virtual_memory().available / (1024**3),
            'has_gpu': torch.cuda.is_available(),
            'gpu_info': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A'
        }

        # Collecter des informations sur les données
        data_info = {
            'total_lines': dataset_features.get('total_lines', 0),
            'pattern_diversity': dataset_features.get('pattern_diversity', 0.0),
            'alternation_rate': dataset_features.get('alternation_rate', 0.0),
            'p_frequency': dataset_features.get('p_frequency', 0.5),
            'b_frequency': dataset_features.get('b_frequency', 0.5),
            'entropy': dataset_features.get('entropy', 0.0)
        }

        # Collecter des informations sur l'adaptation
        adaptation_info = {
            'scale_factor': scale_factor,
            'data_complexity': data_complexity,
            'resource_constraints': resource_constraints,
            'optuna_weight': optuna_weight,
            'meta_learning_weight': meta_weight,
            'adaptation_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'adaptation_methods': ['dynamic_scale_factor', 'nonlinear_formulas', 'meta_learning', 'resource_aware']
        }

        # Créer les callbacks pour l'adaptation dynamique
        dynamic_callbacks = self.create_dynamic_adaptation_callbacks(adapted_params)

        # Stocker toutes les méta-informations
        adapted_params['_meta_info'] = {
            'system': system_info,
            'data': data_info,
            'adaptation': adaptation_info
        }

        # Stocker les callbacks pour l'adaptation dynamique
        adapted_params['_dynamic_callbacks'] = dynamic_callbacks

        logger.warning("=" * 80)
        logger.warning("ADAPTATION DES PARAMÈTRES TERMINÉE")
        logger.warning("=" * 80)
        logger.warning(f"Facteur d'échelle: {scale_factor:.2f}")
        logger.warning(f"Complexité des données: {data_complexity:.2f}")
        logger.warning(f"Contraintes de ressources: {resource_constraints:.2f}")
        logger.warning(f"Méthodes d'adaptation: formules non linéaires, méta-apprentissage, validation progressive, adaptation dynamique")
        logger.warning("=" * 80)

        # Journaliser les callbacks créés
        logger.warning("Callbacks pour l'adaptation dynamique créés:")
        logger.warning(f"  - LGBM: {list(dynamic_callbacks['lgbm'].keys())}")
        logger.warning(f"  - LSTM: {list(dynamic_callbacks['lstm'].keys())}")
        logger.warning(f"  - Markov: {list(dynamic_callbacks['markov'].keys())}")

        return adapted_params