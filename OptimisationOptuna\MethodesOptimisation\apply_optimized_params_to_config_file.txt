# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2463 à 2581
# Type: Méthode de la classe HybridBaccaratPredictor

    def apply_optimized_params_to_config_file(self):
        """Applique les paramètres optimisés depuis params.txt directement dans le fichier config.py."""
        try:
            from utils import load_params_from_file
            import re
            import datetime

            # Charger les paramètres optimisés
            params = load_params_from_file("params.txt")
            if not params:
                logger.warning("Échec du chargement des paramètres depuis params.txt.")
                messagebox.showwarning("Échec du Chargement", "Échec du chargement des paramètres depuis params.txt.")
                return

            # Lire le contenu du fichier config.py
            try:
                with open("config.py", "r", encoding="utf-8") as f:
                    config_content = f.read()
            except Exception as e:
                logger.error(f"Erreur lors de la lecture du fichier config.py: {e}", exc_info=True)
                messagebox.showerror("Erreur", f"Erreur lors de la lecture du fichier config.py: {e}")
                return

            # Créer une sauvegarde du fichier config.py
            backup_filename = f"config_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            try:
                with open(backup_filename, "w", encoding="utf-8") as f:
                    f.write(config_content)
                logger.info(f"Sauvegarde du fichier config.py créée: {backup_filename}")
            except Exception as e:
                logger.error(f"Erreur lors de la création de la sauvegarde de config.py: {e}", exc_info=True)
                messagebox.showerror("Erreur", f"Erreur lors de la création de la sauvegarde de config.py: {e}")
                return

            # Modifier le contenu du fichier config.py pour chaque paramètre
            modified_content = config_content
            modified_params = []

            # Date de l'optimisation
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")

            # Ajouter un commentaire pour indiquer que les valeurs ont été optimisées
            header_pattern = r"(class PredictorConfig:.*?\n\s*def __init__\(self, config_overrides=None\):)"
            header_replacement = r"\1\n        # Valeurs optimisées appliquées le " + current_date
            modified_content = re.sub(header_pattern, header_replacement, modified_content, flags=re.DOTALL)

            # Traiter les paramètres de poids séparément
            weight_params = {k: v for k, v in params.items() if k.startswith('weight_')}
            if weight_params:
                # Construire le dictionnaire initial_weights
                weights_dict = {}
                for k, v in weight_params.items():
                    model_name = k[7:]  # Enlever 'weight_'
                    weights_dict[model_name] = v

                # Normaliser les poids
                total = sum(weights_dict.values())
                if total > 1e-9:
                    weights_dict = {k: v/total for k, v in weights_dict.items()}

                # Remplacer la définition de initial_weights
                weights_pattern = r"(self\.initial_weights:\s*Dict\[str,\s*float\]\s*=\s*){.*?}"

                # Utiliser une fonction de remplacement pour éviter les problèmes de référence de groupe
                def replace_weights(match):
                    return match.group(1) + str(weights_dict).replace("'", "'")

                modified_content = re.sub(weights_pattern, replace_weights, modified_content, flags=re.DOTALL)
                modified_params.extend(weight_params.keys())

            # Traiter les autres paramètres
            other_params = {k: v for k, v in params.items() if not k.startswith('weight_')}
            for param_name, param_value in other_params.items():
                # Construire le pattern pour trouver la définition du paramètre
                param_pattern = rf"(self\.{param_name}\s*:\s*(?:int|float|str|bool)\s*=\s*)([^#\n]+)"

                # Formater la valeur selon son type
                if isinstance(param_value, bool):
                    formatted_value = str(param_value)
                elif isinstance(param_value, int):
                    formatted_value = str(param_value)
                elif isinstance(param_value, float):
                    formatted_value = str(param_value)
                elif isinstance(param_value, str):
                    formatted_value = f"'{param_value}'"
                else:
                    formatted_value = str(param_value)

                # Remplacer la valeur du paramètre
                if re.search(param_pattern, modified_content):
                    # Utiliser une fonction de remplacement pour éviter les problèmes de référence de groupe
                    def replace_value(match):
                        return match.group(1) + formatted_value

                    modified_content = re.sub(param_pattern, replace_value, modified_content)
                    modified_params.append(param_name)

            # Écrire le contenu modifié dans le fichier config.py
            try:
                with open("config.py", "w", encoding="utf-8") as f:
                    f.write(modified_content)
                logger.info(f"Paramètres optimisés appliqués au fichier config.py: {', '.join(modified_params)}")
            except Exception as e:
                logger.error(f"Erreur lors de l'écriture dans le fichier config.py: {e}", exc_info=True)
                messagebox.showerror("Erreur", f"Erreur lors de l'écriture dans le fichier config.py: {e}")
                return

            # Afficher un message de succès
            success_msg = (
                f"Les paramètres optimisés ont été appliqués avec succès au fichier config.py.\n\n"
                f"Une sauvegarde a été créée: {backup_filename}\n\n"
                f"Paramètres modifiés ({len(modified_params)}):\n{', '.join(modified_params)}\n\n"
                f"Ces modifications seront prises en compte au prochain démarrage de l'application."
            )
            messagebox.showinfo("Paramètres Appliqués", success_msg)

        except Exception as e:
            logger.error(f"Erreur lors de l'application des paramètres optimisés au fichier config.py: {e}", exc_info=True)
            messagebox.showerror("Erreur", f"Erreur lors de l'application des paramètres optimisés au fichier config.py: {e}")