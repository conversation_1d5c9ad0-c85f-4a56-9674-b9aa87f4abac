# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14949 à 15009
# Type: Méthode de la classe MetaOptimizer

        def check_constraints(current_params):
            # Si aucun paramètre n'est encore défini, les contraintes sont respectées
            if not current_params:
                return True

            for constraint in constraints:
                if callable(constraint):
                    # Contrainte sous forme de fonction
                    if not constraint(current_params):
                        return False
                elif isinstance(constraint, dict):
                    # Contrainte sous forme de dictionnaire
                    if 'type' not in constraint:
                        continue

                    if constraint['type'] == 'range_dependency':
                        # Contrainte de dépendance de plage
                        param1 = constraint.get('param1')
                        param2 = constraint.get('param2')
                        relation = constraint.get('relation', '>')

                        if param1 in current_params and param2 in current_params:
                            val1 = current_params[param1]
                            val2 = current_params[param2]

                            if relation == '>' and not (val1 > val2):
                                return False
                            elif relation == '>=' and not (val1 >= val2):
                                return False
                            elif relation == '<' and not (val1 < val2):
                                return False
                            elif relation == '<=' and not (val1 <= val2):
                                return False
                            elif relation == '==' and not (val1 == val2):
                                return False
                            elif relation == '!=' and not (val1 != val2):
                                return False

                    elif constraint['type'] == 'conditional':
                        # Contrainte conditionnelle
                        condition_param = constraint.get('if_param')
                        condition_value = constraint.get('if_value')
                        then_param = constraint.get('then_param')
                        then_value = constraint.get('then_value')

                        if condition_param in current_params and current_params[condition_param] == condition_value:
                            if then_param in current_params and current_params[then_param] != then_value:
                                return False

                    elif constraint['type'] == 'sum_constraint':
                        # Contrainte de somme
                        params_to_sum = constraint.get('params', [])
                        target_sum = constraint.get('target_sum')
                        tolerance = constraint.get('tolerance', 0.0)

                        if all(p in current_params for p in params_to_sum):
                            current_sum = sum(current_params[p] for p in params_to_sum)
                            if abs(current_sum - target_sum) > tolerance:
                                return False

            return True