# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 3937 à 4621
# Type: Méthode de la classe OptunaOptimizer

    def launch_sequential_optimization(self, n_trials_level0=100, n_trials_level1=5, n_trials_markov=5, n_trials_level2=5, n_trials_level3=5):
        """
        Lance l'optimisation séquentielle multi-niveaux avec la stratégie adaptée de progx.
        Cette méthode implémente une stratégie d'optimisation progressive à quatre niveaux (plus une phase Markov),
        où chaque niveau affine les résultats du niveau précédent.

        Répartition du temps total d'optimisation:
        - Niveau 0: 15-20% (Exploration préliminaire rapide avec LHS)
        - Niveau 1: 25-30% (Exploration ciblée avec LGBM complet)
        - Phase Markov: Spécifique à prog (Conservation de la compatibilité)
        - Niveau 2: 35-40% (Optimisation progressive avec LSTM léger)
        - Niveau 3: 15-20% (Optimisation fine avec modèles complets)

        Args:
            n_trials_level0: Nombre d'essais pour la phase 0 (exploration préliminaire)
            n_trials_level1: Nombre d'essais pour la phase 1 (LGBM complet)
            n_trials_markov: Nombre d'essais pour la phase Markov (Markov activé)
            n_trials_level2: Nombre d'essais pour la phase 2 (LSTM activé avec 2 époques)
            n_trials_level3: Nombre d'essais pour la phase 3 (LSTM complet)

        Returns:
            Dict: Meilleurs paramètres trouvés, adaptés pour un entraînement complet sur 100% des données
        """
        # Sauvegarder le nombre d'époques LSTM original
        original_lstm_epochs = self.config.lstm_epochs

        # Détecter les ressources disponibles pour optimiser l'utilisation du CPU et de la mémoire
        resources = self._detect_available_resources()

        # Ajuster le nombre de workers en fonction des ressources disponibles
        level0_jobs = resources['optimal_workers'] if 'optimal_workers' in resources else 4
        level1_jobs = max(1, level0_jobs - 1)
        markov_jobs = max(1, level0_jobs - 1)
        level2_jobs = max(1, level0_jobs - 1)
        level3_jobs = max(1, level0_jobs - 2)

        # Ajuster la taille des batchs en fonction de la mémoire disponible
        batch_size = resources['optimal_batch_size'] if 'optimal_batch_size' in resources else 1000
        logger.warning(f"Taille de batch optimale: {batch_size}")

        # Prétraiter les données une seule fois pour toutes les phases
        preprocessed_data = self._preprocess_data_once(force_reload=True)

        # Initialiser le cache avancé
        self._initialize_advanced_data_cache()

        # Activer le profilage de la mémoire si demandé
        if getattr(self.config, 'enable_memory_profiling', False):
            self._enable_memory_profiling()

        # Utiliser des sous-ensembles de tailles différentes pour chaque phase
        phase0_indices = preprocessed_data['phase0_indices']
        phase1_indices = preprocessed_data['phase1_indices']
        phase2_indices = preprocessed_data['phase2_indices']
        phase3_indices = preprocessed_data['phase3_indices']
        markov_indices = preprocessed_data['markov_indices']

        logger.warning(f"Utilisation de sous-ensembles de données pour les phases initiales:")
        logger.warning(f"- Phase 0: {len(phase0_indices)} séquences ({len(phase0_indices)/preprocessed_data['total_sequences']*100:.1f}%)")
        logger.warning(f"- Phase 1: {len(phase1_indices)} séquences ({len(phase1_indices)/preprocessed_data['total_sequences']*100:.1f}%)")
        logger.warning(f"- Phase 2: {len(phase2_indices)} séquences ({len(phase2_indices)/preprocessed_data['total_sequences']*100:.1f}%)")
        logger.warning(f"- Phase 3 et Markov: {len(phase3_indices)} séquences (100%)")

        # Niveau 0: Exploration préliminaire rapide (15-20% du temps total)
        logger.warning("=" * 80)
        logger.warning("NIVEAU 0: EXPLORATION PRÉLIMINAIRE RAPIDE AVEC LHS (15-20% DU TEMPS)")
        logger.warning("=" * 80)

        # Vérifier si l'arrêt a été demandé
        if hasattr(self, 'stop_requested') and callable(self.stop_requested) and self.stop_requested():
            logger.warning("Arrêt demandé avant le début de la phase 0")
            return self.config.__dict__

        # Désactiver le LSTM pour cette phase
        self.config.lstm_epochs = 0
        logger.warning(f"LSTM désactivé pour l'exploration rapide (lstm_epochs={self.config.lstm_epochs})")

        # Appliquer les paramètres de batch optimaux pour la phase 0
        optimized_config = self._apply_optimal_batch_parameters(self.config, 'phase0')

        # Récupérer l'espace de recherche
        search_space = getattr(self.config, 'optuna_search_space', {})

        # Utiliser Latin Hypercube Sampling pour la phase 0
        try:
            # Essayer d'utiliser scikit-optimize pour LHS si disponible
            from skopt.sampler import Lhs
            from skopt.space import Real, Integer

            # Créer un espace de recherche pour Latin Hypercube Sampling
            dimensions = []
            param_names = []

            # Ajouter les paramètres clés qui influencent le ratio WAIT/NON-WAIT
            if hasattr(self.config, 'min_confidence_for_recommendation'):
                dimensions.append(Real(0.3, 0.7, name='min_confidence_for_recommendation'))
                param_names.append('min_confidence_for_recommendation')

            if hasattr(self.config, 'transition_uncertainty_threshold'):
                dimensions.append(Real(0.3, 0.7, name='transition_uncertainty_threshold'))
                param_names.append('transition_uncertainty_threshold')

            if hasattr(self.config, 'error_pattern_threshold'):
                dimensions.append(Real(0.3, 0.7, name='error_pattern_threshold'))
                param_names.append('error_pattern_threshold')

            # Ajouter d'autres paramètres LGBM importants
            if hasattr(self.config, 'lgbm_num_leaves'):
                dimensions.append(Integer(20, 100, name='lgbm_num_leaves'))
                param_names.append('lgbm_num_leaves')

            if hasattr(self.config, 'lgbm_max_depth'):
                dimensions.append(Integer(3, 10, name='lgbm_max_depth'))
                param_names.append('lgbm_max_depth')

            if hasattr(self.config, 'lgbm_learning_rate'):
                dimensions.append(Real(0.01, 0.2, name='lgbm_learning_rate'))
                param_names.append('lgbm_learning_rate')

            # Générer des points avec Latin Hypercube Sampling
            lhs = Lhs(lhs_type="classic", criterion="maximin")
            points = lhs.generate(dimensions, n_trials_level0)

            # Créer une étude Optuna
            sampler = optuna.samplers.RandomSampler(seed=42)
            study_level0 = self._create_study(direction="maximize", study_name="level0_lhs", sampler=sampler)

            # Enqueuer les points générés par LHS
            for i, point in enumerate(points):
                params = {param_names[j]: point[j] for j in range(len(param_names))}
                study_level0.enqueue_trial(params)

            logger.warning(f"Latin Hypercube Sampling: {n_trials_level0} points générés pour l'exploration uniforme")

        except ImportError:
            # Si scikit-optimize n'est pas disponible, utiliser notre implémentation LHS intégrée
            logger.warning("scikit-optimize non disponible, utilisation de l'implémentation LHS intégrée")
            sampler = optuna.samplers.RandomSampler(seed=42)
            study_level0 = self._create_study(
                direction="maximize",
                study_name="level0",
                sampler=sampler,
                use_lhs=True,
                n_lhs_points=min(50, n_trials_level0),
                search_space=search_space
            )
            logger.warning(f"Latin Hypercube Sampling activé pour une exploration uniforme de l'espace des hyperparamètres")

        # Optimiser avec parallélisme adaptatif
        level0_jobs = self.optimal_batch_params['optimal_jobs']['phase0'] if hasattr(self, 'optimal_batch_params') else 4
        logger.warning(f"Début de l'optimisation de phase 0 avec {n_trials_level0} essais et {level0_jobs} workers")

        self._optimize_with_adaptive_parallelism(
            study_level0,
            lambda trial: objective_precision(
                trial,
                optimized_config,
                lambda config, **kwargs: self._evaluate_config_with_early_stopping(
                    config,
                    is_viability_check=True,
                    force_lstm_training=False,
                    subset_indices=phase0_indices,
                    **kwargs
                ),
                logger
            ),
            n_trials=n_trials_level0,
            max_jobs=level0_jobs
        )

        # Sélectionner les meilleurs essais de la phase 0
        # Sélectionner le meilleur essai et les 9 suivants pour la phase 1
        top_trials_level0 = sorted(study_level0.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)[:10]
        best_trial_level0 = top_trials_level0[0]  # Le meilleur essai

        logger.warning(f"Phase 0 terminée. Top 10 essais sélectionnés pour la phase 1.")
        for i, trial in enumerate(top_trials_level0[:5]):  # Afficher les 5 premiers pour limiter les logs
            logger.warning(f"Top {i+1}: Score={trial.value:.4f}, Params={trial.params}")

        # Optimiser la mémoire avant la phase 1
        self._optimize_memory_usage()

        # Prendre un instantané de la mémoire après la phase 0
        if getattr(self.config, 'enable_memory_profiling', False):
            self._take_memory_snapshot("après phase 0")

        # Niveau 1: Exploration ciblée avec LGBM complet (25-30% du temps total)
        logger.warning("=" * 80)
        logger.warning("NIVEAU 1: EXPLORATION CIBLÉE AVEC LGBM COMPLET (25-30% DU TEMPS)")
        logger.warning("=" * 80)

        # Vérifier si l'arrêt a été demandé
        if hasattr(self, 'stop_requested') and callable(self.stop_requested) and self.stop_requested():
            logger.warning("Arrêt demandé avant le début de la phase 1")
            return best_trial_level0.params if 'best_trial_level0' in locals() else self.config.__dict__

        # Toujours pas de LSTM, mais LGBM complet
        self.config.lstm_epochs = 0
        logger.warning(f"LSTM toujours désactivé pour la phase 1 (lstm_epochs={self.config.lstm_epochs})")

        # Appliquer les paramètres de batch optimaux pour la phase 1
        optimized_config = self._apply_optimal_batch_parameters(self.config, 'phase1')

        # Créer un espace de recherche restreint basé sur les meilleurs essais du niveau 0
        search_space = {}
        for param_name in self.config.__dict__:
            if param_name.startswith('_'):
                continue

            values = [t.params.get(param_name, None) for t in top_trials_level0 if param_name in t.params]
            values = [v for v in values if v is not None]

            if not values:
                continue

            if isinstance(values[0], (int, float)):
                min_val = min(values)
                max_val = max(values)
                # Élargir légèrement la plage pour l'exploration
                range_val = max_val - min_val
                param_type = 'float' if isinstance(values[0], float) else 'int'
                search_space[param_name] = [
                    param_type,
                    max(0, min_val - 0.2 * range_val),
                    max_val + 0.2 * range_val
                ]

        # Créer une étude Optuna pour la phase 1 avec TPE
        sampler = optuna.samplers.TPESampler(seed=43)
        study_level1 = self._create_study(direction="maximize", study_name="level1", sampler=sampler)

        # Exécuter la transition parallèle entre les phases 0 et 1
        logger.warning("Exécution de la transition parallèle entre les phases 0 et 1...")
        transition_start_time = time.time()

        # Exécuter la transition parallèle
        transition_results = self._parallel_phase_transition(
            best_trial_level0,
            phase_from=0,
            phase_to=1,
            subset_indices=phase1_indices
        )

        # Calculer le temps de transition
        transition_elapsed_time = time.time() - transition_start_time
        logger.warning(f"Transition parallèle terminée en {transition_elapsed_time:.2f} secondes")

        # Enqueuer les variations du meilleur essai
        for variation in transition_results['variations']:
            study_level1.enqueue_trial(variation)

        # Optimiser avec LGBM complet mais sans LSTM
        level1_jobs = self.optimal_batch_params['optimal_jobs']['phase1'] if hasattr(self, 'optimal_batch_params') else 3
        logger.warning(f"Début de l'optimisation de phase 1 avec {n_trials_level1} essais et {level1_jobs} workers")

        # Utiliser l'optimisation avec parallélisme adaptatif et arrêt précoce
        self._optimize_with_adaptive_parallelism(
            study_level1,
            lambda trial: objective_precision(
                trial,
                optimized_config,
                lambda config, **kwargs: self._evaluate_config_with_early_stopping(
                    config,
                    is_viability_check=True,
                    force_lstm_training=False,
                    subset_indices=phase1_indices,
                    enable_cv=True,  # Activer la validation croisée rapide
                    n_folds=3,
                    **kwargs
                ),
                logger
            ),
            n_trials=n_trials_level1,
            max_jobs=level1_jobs
        )

        # Sélectionner les meilleurs essais de la phase 1
        top_trials_level1 = sorted(study_level1.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)[:5]
        best_trial_level1 = top_trials_level1[0]  # Le meilleur essai

        logger.warning(f"Phase 1 terminée. Top 5 essais sélectionnés pour la phase Markov.")
        for i, trial in enumerate(top_trials_level1):
            logger.warning(f"Top {i+1}: Score={trial.value:.4f}, Params={trial.params}")

        # Optimiser la mémoire avant la phase Markov
        self._optimize_memory_usage()

        # Prendre un instantané de la mémoire après la phase 1
        if getattr(self.config, 'enable_memory_profiling', False):
            self._take_memory_snapshot("après phase 1")

        # Phase Markov: Spécifique à prog (Conservation de la compatibilité)
        logger.warning("=" * 80)
        logger.warning("PHASE MARKOV: SPÉCIFIQUE À PROG (CONSERVATION DE LA COMPATIBILITÉ)")
        logger.warning("=" * 80)

        # Vérifier si l'arrêt a été demandé
        if hasattr(self, 'stop_requested') and callable(self.stop_requested) and self.stop_requested():
            logger.warning("Arrêt demandé avant le début de la phase Markov")
            return best_trial_level1.params if 'best_trial_level1' in locals() else (
                best_trial_level0.params if 'best_trial_level0' in locals() else self.config.__dict__
            )

        # Activer Markov mais garder LSTM désactivé
        self.config.lstm_epochs = 0
        self.config.use_markov_model = True
        logger.warning(f"Markov activé pour la phase Markov (use_markov_model={self.config.use_markov_model})")
        logger.warning(f"LSTM toujours désactivé pour la phase Markov (lstm_epochs={self.config.lstm_epochs})")

        # Appliquer les paramètres de batch optimaux pour la phase Markov
        # Utiliser les mêmes paramètres que la phase 1 pour le moment
        optimized_config = self._apply_optimal_batch_parameters(self.config, 'phase1')

        # Créer un espace de recherche encore plus restreint basé sur les meilleurs essais de la phase 1
        search_space_markov = {}
        for param_name in self.config.__dict__:
            if param_name.startswith('_'):
                continue

            values = [t.params.get(param_name, None) for t in top_trials_level1 if param_name in t.params]
            values = [v for v in values if v is not None]

            if not values:
                continue

            if isinstance(values[0], (int, float)):
                min_val = min(values)
                max_val = max(values)
                # Espace plus restreint pour l'exploitation
                range_val = max_val - min_val
                param_type = 'float' if isinstance(values[0], float) else 'int'
                search_space_markov[param_name] = [
                    param_type,
                    max(0, min_val - 0.1 * range_val),
                    max_val + 0.1 * range_val
                ]

        # Créer une étude Optuna pour la phase Markov
        sampler = optuna.samplers.TPESampler(seed=43)
        study_markov = self._create_study(direction="maximize", study_name="markov", sampler=sampler)

        # Exécuter la transition parallèle entre les phases 1 et Markov
        logger.warning("Exécution de la transition parallèle entre les phases 1 et Markov...")
        transition_start_time = time.time()

        # Exécuter la transition parallèle
        transition_results = self._parallel_phase_transition(
            best_trial_level1,
            phase_from=1,
            phase_to='markov',
            subset_indices=markov_indices
        )

        # Calculer le temps de transition
        transition_elapsed_time = time.time() - transition_start_time
        logger.warning(f"Transition parallèle terminée en {transition_elapsed_time:.2f} secondes")

        # Enqueuer les variations du meilleur essai
        for variation in transition_results['variations']:
            study_markov.enqueue_trial(variation)

        # Optimiser avec Markov activé
        markov_jobs = self.optimal_batch_params['optimal_jobs']['phase1'] if hasattr(self, 'optimal_batch_params') else 3
        logger.warning(f"Début de l'optimisation de phase Markov avec {n_trials_markov} essais et {markov_jobs} workers")

        # Utiliser l'optimisation avec parallélisme adaptatif et arrêt précoce
        self._optimize_with_adaptive_parallelism(
            study_markov,
            lambda trial: objective_precision(
                trial,
                optimized_config,
                lambda config, **kwargs: self._evaluate_config_with_early_stopping(
                    config,
                    is_viability_check=True,
                    force_lstm_training=False,
                    force_markov_training=True,
                    subset_indices=markov_indices,
                    enable_cv=True,
                    n_folds=3,
                    **kwargs
                ),
                logger
            ),
            n_trials=n_trials_markov,
            max_jobs=markov_jobs
        )

        # Sélectionner les meilleurs essais de la phase Markov
        top_trials_markov = sorted(study_markov.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)[:3]
        best_trial_markov = top_trials_markov[0]  # Le meilleur essai

        logger.warning(f"Phase Markov terminée. Top 3 essais sélectionnés pour la phase 2.")
        for i, trial in enumerate(top_trials_markov):
            logger.warning(f"Top {i+1}: Score={trial.value:.4f}, Params={trial.params}")

        # Optimiser la mémoire avant la phase 2
        self._optimize_memory_usage()

        # Prendre un instantané de la mémoire après la phase Markov
        if getattr(self.config, 'enable_memory_profiling', False):
            self._take_memory_snapshot("après phase Markov")

        # Niveau 2: Optimisation progressive avec LSTM (35-40% du temps total)
        logger.warning("=" * 80)
        logger.warning("NIVEAU 2: OPTIMISATION PROGRESSIVE AVEC LSTM (35-40% DU TEMPS)")
        logger.warning("=" * 80)

        # Vérifier si l'arrêt a été demandé
        if hasattr(self, 'stop_requested') and callable(self.stop_requested) and self.stop_requested():
            logger.warning("Arrêt demandé avant le début de la phase 2")
            return best_trial_markov.params if 'best_trial_markov' in locals() else (
                best_trial_level1.params if 'best_trial_level1' in locals() else (
                    best_trial_level0.params if 'best_trial_level0' in locals() else self.config.__dict__
                )
            )

        # Activer le LSTM avec 1 époque pour la phase 2 (comme dans launch_multi_level_optimization)
        self.config.lstm_epochs = 1
        # Garder Markov activé
        self.config.use_markov_model = True
        logger.warning(f"LSTM activé avec 1 époque pour la phase 2 (lstm_epochs={self.config.lstm_epochs})")
        logger.warning(f"Markov toujours activé pour la phase 2 (use_markov_model={self.config.use_markov_model})")

        # Appliquer les paramètres de batch optimaux pour la phase 2
        optimized_config = self._apply_optimal_batch_parameters(self.config, 'phase2')

        # Créer un espace de recherche encore plus restreint basé sur les meilleurs essais de la phase Markov
        search_space_level2 = {}
        for param_name in self.config.__dict__:
            if param_name.startswith('_'):
                continue

            values = [t.params.get(param_name, None) for t in top_trials_markov if param_name in t.params]
            values = [v for v in values if v is not None]

            if not values:
                continue

            if isinstance(values[0], (int, float)):
                min_val = min(values)
                max_val = max(values)
                # Espace plus restreint pour l'exploitation
                range_val = max_val - min_val
                param_type = 'float' if isinstance(values[0], float) else 'int'
                search_space_level2[param_name] = [
                    param_type,
                    max(0, min_val - 0.1 * range_val),
                    max_val + 0.1 * range_val
                ]

        # Créer une étude Optuna pour la phase 2
        sampler = optuna.samplers.TPESampler(seed=44)
        study_level2 = self._create_study(direction="maximize", study_name="level2", sampler=sampler)

        # Exécuter la transition parallèle entre les phases Markov et 2
        logger.warning("Exécution de la transition parallèle entre les phases Markov et 2...")
        transition_start_time = time.time()

        # Exécuter la transition parallèle
        transition_results = self._parallel_phase_transition(
            best_trial_markov,
            phase_from='markov',
            phase_to=2,
            subset_indices=phase2_indices
        )

        # Calculer le temps de transition
        transition_elapsed_time = time.time() - transition_start_time
        logger.warning(f"Transition parallèle terminée en {transition_elapsed_time:.2f} secondes")

        # Enqueuer les variations du meilleur essai
        for variation in transition_results['variations']:
            study_level2.enqueue_trial(variation)

        # Optimiser avec LSTM activé (2 époques)
        level2_jobs = self.optimal_batch_params['optimal_jobs']['phase2'] if hasattr(self, 'optimal_batch_params') else 3
        logger.warning(f"Début de l'optimisation de phase 2 avec {n_trials_level2} essais et {level2_jobs} workers")

        # Utiliser l'optimisation avec parallélisme adaptatif et arrêt précoce
        self._optimize_with_adaptive_parallelism(
            study_level2,
            lambda trial: objective_precision(
                trial,
                optimized_config,
                lambda config, **kwargs: self._evaluate_config_with_early_stopping(
                    config,
                    is_viability_check=False,
                    force_lstm_training=True,
                    force_markov_training=True,
                    subset_indices=phase2_indices,
                    enable_cv=True,
                    n_folds=3,
                    **kwargs
                ),
                logger
            ),
            n_trials=n_trials_level2,
            max_jobs=level2_jobs
        )

        # Sélectionner les meilleurs essais de la phase 2
        top_trials_level2 = sorted(study_level2.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)[:3]
        best_trial_level2 = top_trials_level2[0]  # Le meilleur essai

        logger.warning(f"Phase 2 terminée. Top 3 essais sélectionnés pour la phase 3.")
        for i, trial in enumerate(top_trials_level2):
            logger.warning(f"Top {i+1}: Score={trial.value:.4f}, Params={trial.params}")

        # Optimiser la mémoire avant la phase 3
        self._optimize_memory_usage()

        # Prendre un instantané de la mémoire après la phase 2
        if getattr(self.config, 'enable_memory_profiling', False):
            self._take_memory_snapshot("après phase 2")

        # Niveau 3: Optimisation fine avec modèles complets (15-20% du temps total)
        logger.warning("=" * 80)
        logger.warning("NIVEAU 3: OPTIMISATION FINE AVEC MODÈLES COMPLETS (15-20% DU TEMPS)")
        logger.warning("=" * 80)

        # Vérifier si l'arrêt a été demandé
        if hasattr(self, 'stop_requested') and callable(self.stop_requested) and self.stop_requested():
            logger.warning("Arrêt demandé avant le début de la phase 3")
            return best_trial_level2.params if 'best_trial_level2' in locals() else (
                best_trial_markov.params if 'best_trial_markov' in locals() else (
                    best_trial_level1.params if 'best_trial_level1' in locals() else (
                        best_trial_level0.params if 'best_trial_level0' in locals() else self.config.__dict__
                    )
                )
            )

        # Activer le LSTM avec 2-4 époques pour la phase 3 (comme dans launch_multi_level_optimization)
        self.config.lstm_epochs = min(4, original_lstm_epochs)
        # Garder Markov activé
        self.config.use_markov_model = True
        logger.warning(f"LSTM activé avec {self.config.lstm_epochs} époques pour la phase 3")
        logger.warning(f"Markov toujours activé pour la phase 3 (use_markov_model={self.config.use_markov_model})")

        # Appliquer les paramètres de batch optimaux pour la phase 3
        optimized_config = self._apply_optimal_batch_parameters(self.config, 'phase3')

        # Créer un espace de recherche très restreint basé sur les meilleurs essais de la phase 2
        search_space_level3 = {}
        for param_name in self.config.__dict__:
            if param_name.startswith('_'):
                continue

            values = [t.params.get(param_name, None) for t in top_trials_level2 if param_name in t.params]
            values = [v for v in values if v is not None]

            if not values:
                continue

            if isinstance(values[0], (int, float)):
                min_val = min(values)
                max_val = max(values)
                # Espace très restreint pour l'optimisation fine
                range_val = max_val - min_val
                param_type = 'float' if isinstance(values[0], float) else 'int'
                search_space_level3[param_name] = [
                    param_type,
                    max(0, min_val - 0.05 * range_val),
                    max_val + 0.05 * range_val
                ]

        # Créer une étude Optuna pour la phase 3
        sampler = optuna.samplers.TPESampler(seed=45)
        study_level3 = self._create_study(direction="maximize", study_name="level3", sampler=sampler)

        # Exécuter la transition parallèle entre les phases 2 et 3
        logger.warning("Exécution de la transition parallèle entre les phases 2 et 3...")
        transition_start_time = time.time()

        # Exécuter la transition parallèle
        transition_results = self._parallel_phase_transition(
            best_trial_level2,
            phase_from=2,
            phase_to=3,
            subset_indices=phase3_indices
        )

        # Calculer le temps de transition
        transition_elapsed_time = time.time() - transition_start_time
        logger.warning(f"Transition parallèle terminée en {transition_elapsed_time:.2f} secondes")

        # Enqueuer les variations du meilleur essai
        for variation in transition_results['variations']:
            study_level3.enqueue_trial(variation)

        # Optimiser avec LSTM complet et Markov activé
        level3_jobs = self.optimal_batch_params['optimal_jobs']['phase3'] if hasattr(self, 'optimal_batch_params') else 2
        logger.warning(f"Début de l'optimisation de phase 3 avec {n_trials_level3} essais et {level3_jobs} workers")

        # Utiliser l'optimisation avec parallélisme adaptatif et arrêt précoce
        self._optimize_with_adaptive_parallelism(
            study_level3,
            lambda trial: objective_precision(
                trial,
                optimized_config,
                lambda config, **kwargs: self._evaluate_config_with_early_stopping(
                    config,
                    is_viability_check=False,
                    force_lstm_training=True,
                    force_markov_training=True,
                    subset_indices=phase3_indices,
                    enable_cv=True,
                    n_folds=5,
                    **kwargs
                ),
                logger
            ),
            n_trials=n_trials_level3,
            max_jobs=level3_jobs
        )

        # Sélectionner le meilleur essai
        best_trial = study_level3.best_trial

        logger.warning(f"Optimisation multi-niveaux terminée. Meilleur essai: Score={best_trial.value:.4f}")
        logger.warning(f"Paramètres optimaux: {best_trial.params}")

        # Appliquer les paramètres du meilleur essai à la configuration
        for param_name, param_value in best_trial.params.items():
            setattr(self.config, param_name, param_value)

        # Restaurer le nombre d'époques LSTM original
        self.config.lstm_epochs = original_lstm_epochs
        logger.warning(f"Nombre d'époques LSTM restauré à {self.config.lstm_epochs}")

        # Adapter les paramètres pour l'entraînement complet
        logger.warning("=" * 80)
        logger.warning("ADAPTATION DES PARAMÈTRES POUR L'ENTRAÎNEMENT COMPLET (100% DES DONNÉES)")
        logger.warning("=" * 80)

        # Stocker les paramètres optimaux pour chaque phase
        self.optimal_batch_params = {
            'phase0': best_trial_level0.params if 'best_trial_level0' in locals() else {},
            'phase1': best_trial_level1.params if 'best_trial_level1' in locals() else {},
            'phase_markov': best_trial_markov.params if 'best_trial_markov' in locals() else {},
            'phase2': best_trial_level2.params if 'best_trial_level2' in locals() else {},
            'phase3': best_trial.params,
            'optimal_jobs': {
                'phase0': level0_jobs,
                'phase1': level1_jobs,
                'phase_markov': markov_jobs,
                'phase2': level2_jobs,
                'phase3': level3_jobs
            }
        }

        # Obtenir les paramètres adaptés pour l'entraînement complet
        adapted_params = self.get_optimized_params_for_full_training()

        # Journaliser les paramètres adaptés
        logger.warning("Paramètres optimisés adaptés pour l'entraînement complet:")
        for param_name, param_value in adapted_params.items():
            if not param_name.startswith('_') and not isinstance(param_value, (list, dict)):
                logger.warning(f"  - {param_name}: {param_value}")

        # Finaliser les ajustements de plages
        try:
            self.range_adjuster.finalize_adjustments(save_to_config=True)
            logger.warning("Ajustements de plages finalisés et sauvegardés dans config.py")
        except Exception as e:
            logger.error(f"Erreur lors de la finalisation des ajustements de plages: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # Générer et afficher le résumé final
        logger.warning("Génération du résumé final avec les résultats de l'optimisation multi-niveaux...")
        trial_info = {
            'trial_number': best_trial.number,
            'params': best_trial.params,
            'precision_non_wait': best_trial.user_attrs.get('metrics', {}).get('precision_diff_metric_late_game', 0.0),
            'precision_wait': best_trial.user_attrs.get('metrics', {}).get('precision_wait_metric_late_game', 0.0),
            'wait_ratio': best_trial.user_attrs.get('metrics', {}).get('wait_ratio', 0.0),
            'recommendation_rate': best_trial.user_attrs.get('metrics', {}).get('recommendation_rate_late_game', 0.0),
            'wait_count': best_trial.user_attrs.get('metrics', {}).get('wait_count', 0),
            'non_wait_count': best_trial.user_attrs.get('metrics', {}).get('non_wait_count', 0),
            'weighted_score': best_trial.value
        }
        self._generate_optimization_summary(trial_info, adapted_params)

        # Retourner les paramètres adaptés pour l'entraînement complet
        return adapted_params