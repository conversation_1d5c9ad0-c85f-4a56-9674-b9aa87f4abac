# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12246 à 12283
# Type: Méthode de la classe OptunaOptimizer

    def _calculate_dynamic_scale_factor(self):
        """
        Calcule dynamiquement le facteur d'échelle en fonction du nombre réel de lignes
        dans le fichier historical_data.txt.

        Returns:
            float: Facteur d'échelle dynamique
        """
        import os

        # Chemin du fichier historical_data.txt
        historical_data_path = os.path.join(os.getcwd(), "historical_data.txt")

        # Vérifier si le fichier existe
        if not os.path.exists(historical_data_path):
            logger.warning(f"Fichier {historical_data_path} non trouvé. Utilisation du facteur d'échelle par défaut (10.0).")
            return 10.0

        try:
            # Compter le nombre total de lignes dans le fichier
            with open(historical_data_path, 'r') as f:
                total_lines = sum(1 for _ in f)

            # Calculer le nombre de lignes utilisées pour l'optimisation (10%)
            optimization_lines = int(total_lines * 0.1)

            # Calculer le facteur d'échelle réel
            if optimization_lines > 0:
                scale_factor = total_lines / optimization_lines
            else:
                scale_factor = 10.0

            logger.info(f"Facteur d'échelle dynamique calculé: {scale_factor:.2f} (basé sur {total_lines} lignes totales)")
            return scale_factor
        except Exception as e:
            logger.error(f"Erreur lors du calcul du facteur d'échelle dynamique: {e}")
            logger.warning("Utilisation du facteur d'échelle par défaut (10.0).")
            return 10.0