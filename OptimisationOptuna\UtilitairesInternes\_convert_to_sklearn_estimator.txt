# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 2537 à 2730
# Type: Méthode de la classe OptunaOptimizer

    def _convert_to_sklearn_estimator(self, best_params=None, estimator_type='classifier',
                                     include_preprocessing=True, random_state=42):
        """
        Convertit les meilleurs paramètres en un estimateur scikit-learn.
        Cette méthode permet d'intégrer facilement les résultats d'optimisation
        dans un pipeline scikit-learn.

        Args:
            best_params: Meilleurs paramètres (si None, utilise les meilleurs paramètres de la dernière étude)
            estimator_type: Type d'estimateur ('classifier' ou 'regressor')
            include_preprocessing: Inclure les étapes de prétraitement dans l'estimateur
            random_state: Graine aléatoire pour la reproductibilité

        Returns:
            sklearn.base.BaseEstimator: Estimateur scikit-learn configuré avec les meilleurs paramètres
        """
        from sklearn.base import BaseEstimator, ClassifierMixin, RegressorMixin
        from sklearn.pipeline import Pipeline
        from sklearn.compose import ColumnTransformer
        from sklearn.preprocessing import StandardScaler, OneHotEncoder
        import numpy as np
        import inspect

        # Vérifier que les paramètres sont valides
        if best_params is None:
            if hasattr(self, 'best_params') and self.best_params:
                best_params = self.best_params
            else:
                logger.warning("Aucun paramètre fourni et aucun meilleur paramètre disponible")
                return None

        # Vérifier que le type d'estimateur est valide
        if estimator_type not in ['classifier', 'regressor']:
            logger.warning(f"Type d'estimateur non valide: {estimator_type}")
            return None

        # Créer une classe d'estimateur qui encapsule notre modèle optimisé
        class_name = f"OptunaOptimized{'Classifier' if estimator_type == 'classifier' else 'Regressor'}"
        parent_class = ClassifierMixin if estimator_type == 'classifier' else RegressorMixin

        # Définir la classe d'estimateur
        class_dict = {
            '__init__': lambda self, params=None, random_state=42: setattr(self, 'params', params) or setattr(self, 'random_state', random_state),
            'get_params': lambda self, deep=True: self.params.copy(),
            'set_params': lambda self, **params: setattr(self, 'params', {**self.params, **params}) or self,
            '_more_tags': lambda self: {'requires_fit': True}
        }

        # Ajouter les méthodes spécifiques au type d'estimateur
        if estimator_type == 'classifier':
            # Méthodes pour les classificateurs
            def fit(self, X, y):
                self.classes_ = np.unique(y)
                self.n_classes_ = len(self.classes_)
                self._fit_model(X, y)
                return self

            def predict(self, X):
                return self._predict_model(X)

            def predict_proba(self, X):
                return self._predict_proba_model(X)

            class_dict['fit'] = fit
            class_dict['predict'] = predict
            class_dict['predict_proba'] = predict_proba
        else:
            # Méthodes pour les régresseurs
            def fit(self, X, y):
                self._fit_model(X, y)
                return self

            def predict(self, X):
                return self._predict_model(X)

            class_dict['fit'] = fit
            class_dict['predict'] = predict

        # Ajouter les méthodes communes
        def _fit_model(self, X, y):
            # Implémenter la logique d'entraînement spécifique à notre modèle
            # Cette méthode sera remplacée par l'implémentation réelle
            pass

        def _predict_model(self, X):
            # Implémenter la logique de prédiction spécifique à notre modèle
            # Cette méthode sera remplacée par l'implémentation réelle
            pass

        def _predict_proba_model(self, X):
            # Implémenter la logique de prédiction de probabilités spécifique à notre modèle
            # Cette méthode sera remplacée par l'implémentation réelle
            pass

        class_dict['_fit_model'] = _fit_model
        class_dict['_predict_model'] = _predict_model
        class_dict['_predict_proba_model'] = _predict_proba_model

        # Créer la classe d'estimateur
        estimator_class = type(class_name, (BaseEstimator, parent_class), class_dict)

        # Créer une instance de l'estimateur avec les meilleurs paramètres
        estimator = estimator_class(params=best_params, random_state=random_state)

        # Remplacer les méthodes par les implémentations réelles
        def real_fit_model(self, X, y):
            # Stocker X et y pour les prédictions
            self.X_train_ = X
            self.y_train_ = y

            # Créer et entraîner le modèle selon les paramètres
            # Cette implémentation dépend du type de modèle optimisé
            # et doit être adaptée selon les besoins spécifiques
            try:
                # Exemple d'implémentation pour un modèle simple
                from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor

                # Extraire les paramètres pertinents pour le modèle
                model_params = {}
                for key, value in self.params.items():
                    if key.startswith('model_'):
                        # Supprimer le préfixe 'model_'
                        model_params[key[6:]] = value

                # Ajouter la graine aléatoire
                model_params['random_state'] = self.random_state

                # Créer le modèle selon le type d'estimateur
                if hasattr(self, 'n_classes_') and self.n_classes_ > 2:
                    # Classification multi-classe
                    self.model_ = RandomForestClassifier(**model_params)
                elif hasattr(self, 'n_classes_'):
                    # Classification binaire
                    self.model_ = RandomForestClassifier(**model_params)
                else:
                    # Régression
                    self.model_ = RandomForestRegressor(**model_params)

                # Entraîner le modèle
                self.model_.fit(X, y)

                # Stocker des informations supplémentaires
                self.feature_importances_ = getattr(self.model_, 'feature_importances_', None)

                return True
            except Exception as e:
                logger.warning(f"Erreur lors de l'entraînement du modèle: {e}")
                return False

        def real_predict_model(self, X):
            # Vérifier que le modèle a été entraîné
            if not hasattr(self, 'model_'):
                raise RuntimeError("Le modèle n'a pas été entraîné. Appelez fit() d'abord.")

            # Faire des prédictions
            return self.model_.predict(X)

        def real_predict_proba_model(self, X):
            # Vérifier que le modèle a été entraîné
            if not hasattr(self, 'model_'):
                raise RuntimeError("Le modèle n'a pas été entraîné. Appelez fit() d'abord.")

            # Vérifier que le modèle supporte predict_proba
            if not hasattr(self.model_, 'predict_proba'):
                raise RuntimeError("Le modèle ne supporte pas predict_proba.")

            # Faire des prédictions de probabilités
            return self.model_.predict_proba(X)

        # Remplacer les méthodes
        estimator._fit_model = lambda X, y: real_fit_model(estimator, X, y)
        estimator._predict_model = lambda X: real_predict_model(estimator, X)
        estimator._predict_proba_model = lambda X: real_predict_proba_model(estimator, X)

        # Créer un pipeline avec prétraitement si demandé
        if include_preprocessing:
            # Créer un transformateur de colonnes pour le prétraitement
            preprocessor = ColumnTransformer(
                transformers=[
                    ('num', StandardScaler(), [i for i in range(10)]),  # Colonnes numériques (à adapter)
                    ('cat', OneHotEncoder(handle_unknown='ignore'), [])  # Colonnes catégorielles (à adapter)
                ],
                remainder='passthrough'
            )

            # Créer le pipeline
            pipeline = Pipeline([
                ('preprocessor', preprocessor),
                ('estimator', estimator)
            ])

            return pipeline
        else:
            return estimator