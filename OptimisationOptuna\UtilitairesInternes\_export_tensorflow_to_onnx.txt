# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 2937 à 3013
# Type: Méthode de la classe OptunaOptimizer

    def _export_tensorflow_to_onnx(self, model, output_path, input_shape=None, input_names=None, output_names=None, opset_version=12):
        """
        Exporte un modèle TensorFlow/Keras au format ONNX.

        Args:
            model: Modèle TensorFlow/Keras à exporter
            output_path: Chemin du fichier ONNX de sortie
            input_shape: Forme des données d'entrée
            input_names: Noms des entrées du modèle
            output_names: Noms des sorties du modèle
            opset_version: Version de l'ensemble d'opérations ONNX

        Returns:
            str: Chemin du fichier ONNX généré
        """
        try:
            import tf2onnx
            import tensorflow as tf
            import onnxruntime as rt

            # Convertir le modèle en ONNX
            if hasattr(model, 'save'):
                # Sauvegarder le modèle Keras dans un fichier temporaire
                import tempfile
                import os

                temp_dir = tempfile.mkdtemp()
                temp_model_path = os.path.join(temp_dir, 'temp_model')

                model.save(temp_model_path)

                # Convertir le modèle sauvegardé en ONNX
                model_proto, _ = tf2onnx.convert.from_keras(model, opset=opset_version)

                # Nettoyer le répertoire temporaire
                import shutil
                shutil.rmtree(temp_dir)
            else:
                # Convertir directement le modèle TensorFlow en ONNX
                model_proto, _ = tf2onnx.convert.from_tensorflow(
                    frozen_graph=model,
                    input_names=input_names,
                    output_names=output_names,
                    opset=opset_version
                )

            # Sauvegarder le modèle ONNX
            with open(output_path, "wb") as f:
                f.write(model_proto.SerializeToString())

            logger.warning(f"Modèle TensorFlow/Keras exporté au format ONNX: {output_path}")

            # Vérifier que le modèle ONNX est valide
            try:
                sess = rt.InferenceSession(output_path)
                input_name = sess.get_inputs()[0].name
                input_shape = sess.get_inputs()[0].shape

                # Créer des données d'entrée factices pour tester le modèle
                X_test = np.random.rand(*[dim if dim else 1 for dim in input_shape]).astype(np.float32)

                # Exécuter une inférence de test
                _ = sess.run(None, {input_name: X_test})

                logger.warning("Validation du modèle ONNX réussie")
            except Exception as e:
                logger.warning(f"Erreur lors de la validation du modèle ONNX: {e}")

            return output_path

        except ImportError as e:
            logger.warning(f"Impossible d'exporter le modèle TensorFlow/Keras en ONNX: {e}")
            logger.warning("Installez les packages requis: pip install tf2onnx tensorflow onnxruntime")
            return None
        except Exception as e:
            logger.warning(f"Erreur lors de l'exportation du modèle TensorFlow/Keras en ONNX: {e}")
            return None