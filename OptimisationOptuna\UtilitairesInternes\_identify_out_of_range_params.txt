# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 408 à 481
# Type: Méthode de la classe DynamicRangeAdjuster

    def _identify_out_of_range_params(self, trials: List[optuna.trial.FrozenTrial]) -> Dict[str, Tuple[str, float, float]]:
        """
        Identifie les paramètres dont les valeurs sont hors des plages définies.

        Args:
            trials: Liste des essais Optuna à analyser

        Returns:
            Dict: Les paramètres hors plage avec leurs nouvelles plages {param_name: (param_type, new_low, new_high)}
        """
        out_of_range_params = {}

        # Charger les plages actuelles depuis config.py
        current_ranges = self._get_current_ranges()

        # Liste des paramètres booléens connus
        boolean_params = [
            'lstm_use_adaptive_window', 'use_markov_model', 'lstm_use_attention',
            'lstm_use_residual', 'use_advanced_features', 'use_advanced_lstm',
            'use_ensemble', 'use_mixup', 'use_focal_loss', 'adaptive_confidence_threshold',
            'lstm_bidirectional', 'force_markov_training'
        ]

        for trial in trials:
            for param_name, value in trial.params.items():
                if param_name in current_ranges:
                    param_range = current_ranges[param_name]

                    # Vérifier si la valeur est hors plage
                    if isinstance(param_range, tuple) and len(param_range) >= 3:
                        param_type, low, high = param_range[0], param_range[1], param_range[2]

                        # Traitement spécial pour les paramètres booléens
                        is_boolean_param = param_type == 'categorical' and param_name in boolean_params or param_name.startswith(('use_', 'lstm_use_', 'lgbm_use_'))

                        # Traitement pour les paramètres numériques (float ou int)
                        if (param_type == 'float' or param_type == 'int') and (value < low or value > high):
                            # Calculer la nouvelle plage
                            margin = 0.05  # 5% de marge

                            if param_type == 'float':
                                # Pour les flottants, ajouter une marge relative
                                new_low = min(low, value * (1 - margin) if value > 0 else value * (1 + margin))
                                new_high = max(high, value * (1 + margin) if value > 0 else value * (1 - margin))
                            else:  # param_type == 'int'
                                # Pour les entiers, ajouter une marge absolue
                                new_low = min(low, int(value - max(1, abs(value) * margin)))
                                new_high = max(high, int(value + max(1, abs(value) * margin)))

                            # Stocker la nouvelle plage
                            if param_name not in out_of_range_params:
                                out_of_range_params[param_name] = (param_type, new_low, new_high)
                                # Suppression du message INFO pour les paramètres hors plage
                                # logger.info(f"Paramètre hors plage: {param_name}={value} (plage actuelle: [{low}, {high}], nouvelle plage: [{new_low}, {new_high}])")
                            else:
                                # Élargir davantage si nécessaire
                                _, curr_low, curr_high = out_of_range_params[param_name]
                                out_of_range_params[param_name] = (param_type, min(curr_low, new_low), max(curr_high, new_high))
                                # Suppression du message INFO pour les plages élargies
                                # logger.info(f"Plage élargie pour {param_name}: [{curr_low}, {curr_high}] -> [{min(curr_low, new_low)}, {max(curr_high, new_high)}]")

                        # Traitement pour les paramètres catégoriels
                        elif param_type == 'categorical' and not is_boolean_param:
                            # Vérifier si la valeur est dans la liste des catégories
                            if isinstance(low, list) and value not in low:
                                # Ajouter la valeur à la liste des catégories
                                new_categories = low.copy()
                                if value not in new_categories:
                                    new_categories.append(value)
                                    out_of_range_params[param_name] = (param_type, new_categories)
                                    # Suppression du message INFO pour les paramètres catégoriels hors plage
                                    # logger.info(f"Paramètre catégoriel hors plage: {param_name}={value} (catégories actuelles: {low}, nouvelles catégories: {new_categories})")

        return out_of_range_params