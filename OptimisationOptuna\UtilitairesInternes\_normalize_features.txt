# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 5405 à 5443
# Type: Méthode de la classe OptunaOptimizer

    def _normalize_features(self, features):
        """
        Normalise les features pour éviter les problèmes numériques.

        Args:
            features: Tableau numpy de caractéristiques (n_samples, n_features)

        Returns:
            numpy.ndarray: Features normalisées
        """
        import numpy as np

        # Vérifier si les features sont valides
        if features is None or features.size == 0:
            return features

        # Vérifier s'il y a des valeurs non finies
        if not np.all(np.isfinite(features)):
            # Remplacer les valeurs non finies par 0
            features_clean = np.copy(features)
            features_clean[~np.isfinite(features_clean)] = 0
        else:
            features_clean = features

        # Calculer les statistiques
        feature_min = np.min(features_clean, axis=0)
        feature_max = np.max(features_clean, axis=0)
        feature_range = feature_max - feature_min

        # Éviter la division par zéro
        feature_range[feature_range == 0] = 1.0

        # Normaliser les features dans [0, 1]
        normalized = (features_clean - feature_min) / feature_range

        # Vérifier à nouveau les valeurs non finies (au cas où)
        normalized[~np.isfinite(normalized)] = 0.5

        return normalized