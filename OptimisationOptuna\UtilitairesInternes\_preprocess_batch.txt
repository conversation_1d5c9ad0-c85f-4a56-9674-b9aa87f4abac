# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13223 à 13315
# Type: Méthode de la classe OptunaOptimizer

    def _preprocess_batch(self, batch_lines):
        """
        Prétraite un batch de lignes du fichier historical_data.txt.
        Format attendu: séquence de 0 et 1 séparés par des virgules.

        Args:
            batch_lines: Liste de lignes à prétraiter

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: Features LGBM, features LSTM et cibles
        """
        X_lgbm_batch = []
        X_lstm_batch = []
        y_batch = []

        # Longueur minimale de séquence requise
        min_sequence_length = getattr(self.config, 'min_sequence_length', 30)
        # Longueur de séquence pour LSTM
        lstm_sequence_length = getattr(self.config, 'lstm_sequence_length', 20)
        # Longueur de séquence pour LGBM
        lgbm_sequence_length = getattr(self.config, 'lgbm_sequence_length', 20)

        for line in batch_lines:
            try:
                # Nettoyer et diviser la ligne
                line = line.strip()
                if not line:
                    continue

                # Diviser la ligne en éléments (0 et 1 séparés par des virgules)
                elements = line.split(',')

                # Vérifier que la séquence est assez longue
                if len(elements) < min_sequence_length:
                    continue

                # Convertir les éléments en valeurs numériques
                numeric_sequence = []
                for element in elements:
                    element = element.strip()
                    if element == '0':
                        numeric_sequence.append(0)
                    elif element == '1':
                        numeric_sequence.append(1)
                    else:
                        # Ignorer les éléments non reconnus
                        continue

                # Vérifier que la séquence numérique est assez longue
                if len(numeric_sequence) < min_sequence_length:
                    continue

                # Créer des exemples d'entraînement à partir de la séquence
                for i in range(min_sequence_length - 1, len(numeric_sequence)):
                    # Séquence pour LSTM (les n derniers éléments)
                    lstm_seq = numeric_sequence[max(0, i - lstm_sequence_length + 1):i + 1]

                    # Si la séquence LSTM est trop courte, la compléter avec des zéros au début
                    if len(lstm_seq) < lstm_sequence_length:
                        lstm_seq = [0] * (lstm_sequence_length - len(lstm_seq)) + lstm_seq

                    # Séquence pour LGBM (les n derniers éléments)
                    lgbm_seq = numeric_sequence[max(0, i - lgbm_sequence_length + 1):i + 1]

                    # Si la séquence LGBM est trop courte, la compléter avec des zéros au début
                    if len(lgbm_seq) < lgbm_sequence_length:
                        lgbm_seq = [0] * (lgbm_sequence_length - len(lgbm_seq)) + lgbm_seq

                    # Cible: l'élément suivant dans la séquence
                    if i + 1 < len(numeric_sequence):
                        target = numeric_sequence[i + 1]  # 0 ou 1

                        # Ajouter aux batchs
                        X_lstm_batch.append(lstm_seq)
                        X_lgbm_batch.append(lgbm_seq)
                        y_batch.append(target)
            except Exception as e:
                logger.error(f"Erreur lors du prétraitement d'une ligne: {e}")
                continue

        # Vérifier que nous avons des données
        if not X_lgbm_batch or not X_lstm_batch or not y_batch:
            logger.warning("Aucune donnée valide dans ce batch")
            # Retourner des tableaux vides mais avec la bonne forme
            return np.array([]).reshape(0, lgbm_sequence_length), np.array([]).reshape(0, lstm_sequence_length), np.array([])

        # Convertir en arrays numpy
        X_lgbm_array = np.array(X_lgbm_batch)
        X_lstm_array = np.array(X_lstm_batch)
        y_array = np.array(y_batch)

        logger.info(f"Batch prétraité: {len(y_array)} échantillons")
        return X_lgbm_array, X_lstm_array, y_array