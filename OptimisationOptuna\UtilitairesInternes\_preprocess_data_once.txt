# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6443 à 6628
# Type: Méthode de la classe OptunaOptimizer

    def _preprocess_data_once(self, force_reload=False):
        """
        Prétraite les données une seule fois et crée des sous-ensembles pour chaque phase.
        Utilise Latin Hypercube Sampling (LHS) pour une meilleure couverture de l'espace des données.

        Args:
            force_reload: Force le rechargement des données même si elles sont déjà en cache

        Returns:
            dict: Données prétraitées
        """
        import time
        import numpy as np
        import random
        import os
        import gc

        # Mesurer le temps de prétraitement
        start_time = time.time()

        # Vérifier si les données sont déjà en cache et si elles sont dans le cache avancé
        if not force_reload:
            # Vérifier d'abord dans le cache avancé
            if hasattr(self, '_advanced_data_cache') and 'preprocessed_data' in self._advanced_data_cache:
                cache_key = 'full_dataset'
                if cache_key in self._advanced_data_cache['preprocessed_data']:
                    logger.warning("Utilisation des données prétraitées du cache avancé")
                    self._advanced_data_cache['cache_hits'] += 1
                    return self._advanced_data_cache['preprocessed_data'][cache_key]

            # Vérifier ensuite dans le cache simple
            if hasattr(self, '_preprocessed_data_cache'):
                logger.warning("Utilisation des données prétraitées du cache simple")
                return self._preprocessed_data_cache

        # Incrémenter le compteur de cache miss
        if hasattr(self, '_advanced_data_cache'):
            self._advanced_data_cache['cache_misses'] += 1

        logger.warning(f"Prétraitement des données pour toutes les phases d'optimisation avec LHS...")

        # Utiliser la méthode optimisée de chargement et prétraitement
        historical_data_path = os.path.join(os.getcwd(), "historical_data.txt")

        # Charger et prétraiter les données avec taille de batch adaptative
        try:
            X_lgbm, X_lstm, y = self.load_and_preprocess_data(historical_data_path)
        except Exception as e:
            logger.error(f"Erreur lors du chargement des données: {e}")
            return None

        # Créer des séquences à partir des données complètes
        all_sequences = [(X_lgbm[i], X_lstm[i], y[i]) for i in range(len(y))]

        if not all_sequences:
            logger.error("Aucune séquence valide trouvée dans historical_data.txt")
            return None

        logger.warning(f"Prétraitement de {len(all_sequences)} séquences...")

        # Créer des sous-ensembles de différentes tailles pour les différentes phases
        total_sequences = len(all_sequences)

        # Définir les tailles des sous-ensembles pour chaque phase
        # Ajuster les tailles en fonction de la taille totale des données
        if total_sequences > 10000:
            # Pour les très grands ensembles de données, utiliser des pourcentages plus petits
            phase0_size = int(0.05 * total_sequences)  # 5%
            phase1_size = int(0.15 * total_sequences)  # 15%
            phase2_size = int(0.3 * total_sequences)   # 30%
            logger.warning("Grand ensemble de données détecté, utilisation de sous-ensembles plus petits")
        elif total_sequences > 5000:
            # Pour les grands ensembles de données
            phase0_size = int(0.1 * total_sequences)   # 10%
            phase1_size = int(0.25 * total_sequences)  # 25%
            phase2_size = int(0.5 * total_sequences)   # 50%
            logger.warning("Ensemble de données moyen détecté, utilisation de sous-ensembles standards")
        else:
            # Pour les petits ensembles de données
            phase0_size = int(0.15 * total_sequences)  # 15%
            phase1_size = int(0.4 * total_sequences)   # 40%
            phase2_size = int(0.6 * total_sequences)   # 60%
            logger.warning("Petit ensemble de données détecté, utilisation de sous-ensembles plus grands")

        # Utiliser une graine fixe pour la reproductibilité
        random.seed(42)
        np.random.seed(42)

        # Créer des indices pour chaque phase en utilisant différentes méthodes d'échantillonnage
        logger.warning("Utilisation de Latin Hypercube Sampling (LHS) pour la phase 0...")
        phase0_indices = self._stratified_sampling(all_sequences, phase0_size, method='lhs')

        logger.warning("Utilisation d'échantillonnage stratifié pour la phase 1...")
        phase1_indices = self._stratified_sampling(all_sequences, phase1_size, method='stratified')

        logger.warning("Utilisation d'échantillonnage hybride pour la phase 2...")
        # Pour la phase 2, combiner LHS et stratifié pour une meilleure couverture
        lhs_size = int(phase2_size * 0.5)
        stratified_size = phase2_size - lhs_size
        lhs_indices = self._stratified_sampling(all_sequences, lhs_size, method='lhs')
        stratified_indices = self._stratified_sampling(all_sequences, stratified_size, method='stratified')
        # Combiner les indices en évitant les doublons
        phase2_indices = np.unique(np.concatenate([lhs_indices, stratified_indices]))
        # Si nous avons perdu des indices à cause des doublons, compléter avec des indices aléatoires
        if len(phase2_indices) < phase2_size:
            remaining = phase2_size - len(phase2_indices)
            all_indices = set(range(total_sequences))
            used_indices = set(phase2_indices)
            remaining_indices = list(all_indices - used_indices)
            if remaining_indices:
                additional = np.array(random.sample(remaining_indices, min(remaining, len(remaining_indices))))
                phase2_indices = np.concatenate([phase2_indices, additional])

        # Utiliser toutes les données pour la phase 3 et la phase Markov
        phase3_indices = np.arange(total_sequences)
        markov_indices = np.arange(total_sequences)

        logger.warning(f"Utilisation de sous-ensembles de données pour les phases initiales:")
        logger.warning(f"- Phase 0 (LHS): {len(phase0_indices)} séquences ({len(phase0_indices)/total_sequences*100:.1f}%)")
        logger.warning(f"- Phase 1 (Stratifié): {len(phase1_indices)} séquences ({len(phase1_indices)/total_sequences*100:.1f}%)")
        logger.warning(f"- Phase 2 (Hybride): {len(phase2_indices)} séquences ({len(phase2_indices)/total_sequences*100:.1f}%)")
        logger.warning(f"- Phase 3 et Markov: {len(phase3_indices)} séquences (100%)")

        # Optimiser la mémoire pour l'utilisation de 100% des données
        self._optimize_memory_for_full_dataset()

        # Calculer le temps de prétraitement
        elapsed_time = time.time() - start_time
        logger.warning(f"Prétraitement terminé en {elapsed_time:.2f} secondes")

        # Créer un dictionnaire avec les données prétraitées
        preprocessed_data = {
            'all_sequences': all_sequences,
            'phase0_indices': phase0_indices,
            'phase1_indices': phase1_indices,
            'phase2_indices': phase2_indices,
            'phase3_indices': phase3_indices,
            'markov_indices': markov_indices,
            'total_sequences': total_sequences,
            'timestamp': time.time(),
            'X_lgbm_shape': X_lgbm.shape if hasattr(X_lgbm, 'shape') else None,
            'X_lstm_shape': X_lstm.shape if hasattr(X_lstm, 'shape') else None,
            'y_shape': len(y) if hasattr(y, '__len__') else None
        }

        # Mettre en cache les données prétraitées
        self._preprocessed_data_cache = preprocessed_data

        # Mettre également en cache dans le cache avancé
        if hasattr(self, '_advanced_data_cache'):
            cache_key = 'full_dataset'
            self._advanced_data_cache['preprocessed_data'][cache_key] = preprocessed_data

            # Stocker également les données spécifiques à chaque phase
            self._advanced_data_cache['phase_data']['phase0'] = {
                'indices': phase0_indices,
                'size': len(phase0_indices),
                'timestamp': time.time()
            }
            self._advanced_data_cache['phase_data']['phase1'] = {
                'indices': phase1_indices,
                'size': len(phase1_indices),
                'timestamp': time.time()
            }
            self._advanced_data_cache['phase_data']['phase2'] = {
                'indices': phase2_indices,
                'size': len(phase2_indices),
                'timestamp': time.time()
            }
            self._advanced_data_cache['phase_data']['phase3'] = {
                'indices': phase3_indices,
                'size': len(phase3_indices),
                'timestamp': time.time()
            }
            self._advanced_data_cache['phase_data']['markov'] = {
                'indices': markov_indices,
                'size': len(markov_indices),
                'timestamp': time.time()
            }

            logger.warning("Données prétraitées stockées dans le cache avancé")

        # Forcer la collecte des objets non référencés
        gc.collect()

        return preprocessed_data