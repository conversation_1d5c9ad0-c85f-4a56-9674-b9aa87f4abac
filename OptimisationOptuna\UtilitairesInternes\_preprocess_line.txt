# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 5066 à 5140
# Type: Méthode de la classe OptunaOptimizer

    def _preprocess_line(self, line):
        """
        Prétraite une ligne de données pour l'extraction de features.
        Cette méthode est utilisée lors de la validation empirique.

        Args:
            line (str): Ligne de données à prétraiter

        Returns:
            tuple: (X_lgbm, X_lstm, y) - Features LGBM, features LSTM et cible
        """
        # Vérifier si la ligne est valide
        if not line or len(line.strip()) == 0:
            return None, None, None

        # Extraire les données de la ligne
        try:
            # Supprimer les espaces et diviser par les virgules
            parts = line.strip().split(',')

            # Vérifier si la ligne a le bon format
            if len(parts) < 2:
                return None, None, None

            # Extraire la séquence et le résultat
            sequence = parts[:-1]  # Tous les éléments sauf le dernier
            result = parts[-1]     # Dernier élément

            # Vérifier si le résultat est valide
            if result not in ['player', 'banker']:
                return None, None, None

            # Obtenir une instance de HybridBaccaratPredictor pour accéder à ses méthodes
            hbp_instance = get_hbp_instance() if 'get_hbp_instance' in globals() else None

            # Créer les features LGBM
            if hbp_instance is not None and hasattr(hbp_instance, '_create_lgbm_features'):
                X_lgbm = hbp_instance._create_lgbm_features(sequence)
            else:
                # Fallback si l'instance HBP n'est pas disponible
                logger.warning("Instance HBP non disponible pour créer les features LGBM")
                X_lgbm = []

            # Créer les features LSTM
            if hbp_instance is not None and hasattr(hbp_instance, 'create_lstm_sequence_features'):
                X_lstm = hbp_instance.create_lstm_sequence_features(sequence)

                # Vérifier si X_lstm est None et utiliser un fallback si nécessaire
                if X_lstm is None:
                    logger.warning("create_lstm_sequence_features a retourné None")
                    # Récupérer les paramètres de configuration LSTM
                    if hasattr(hbp_instance, 'config'):
                        lstm_sequence_length = getattr(hbp_instance.config, 'lstm_sequence_length', 20)
                        lstm_input_size = getattr(hbp_instance.config, 'lstm_input_size', 15)
                    else:
                        lstm_sequence_length = 20  # Valeur par défaut
                        lstm_input_size = 15       # Valeur par défaut

                    X_lstm = np.zeros((lstm_sequence_length, lstm_input_size), dtype=np.float32)
            else:
                # Fallback si l'instance HBP n'est pas disponible
                logger.warning("Instance HBP non disponible pour créer les features LSTM")
                # Utiliser les valeurs par défaut pour la forme des données LSTM
                lstm_sequence_length = 20  # Valeur par défaut
                lstm_input_size = 15       # Valeur par défaut
                X_lstm = np.zeros((lstm_sequence_length, lstm_input_size), dtype=np.float32)

            # Convertir le résultat en valeur numérique (0 pour player, 1 pour banker)
            y = 0 if result == 'player' else 1

            return X_lgbm, X_lstm, y

        except Exception as e:
            logger.error(f"Erreur lors du prétraitement de la ligne: {e}")
            return None, None, None