# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 895 à 953
# Type: Méthode de la classe DynamicRangeAdjuster

    def _reload_search_space_from_config(self, study):
        """
        Recharge l'espace de recherche à partir du fichier config.py.
        Cette méthode est utilisée comme solution de secours lorsque la mise à jour directe n'est pas possible.

        Args:
            study: L'étude Optuna à mettre à jour
        """
        try:
            # Importer le module config
            import sys
            import importlib

            # Recharger le module config pour prendre en compte les modifications
            if 'config' in sys.modules:
                importlib.reload(sys.modules['config'])
                import config

                # Vérifier si optuna_search_space existe dans le module config
                if hasattr(config, 'optuna_search_space'):
                    # Créer un nouvel espace de recherche
                    new_search_space = {}

                    for param_name, param_config in config.optuna_search_space.items():
                        param_type = param_config[0]

                        if param_type == 'float':
                            low, high = param_config[1], param_config[2]
                            new_search_space[param_name] = optuna.distributions.FloatDistribution(low=low, high=high)
                        elif param_type == 'int':
                            low, high = param_config[1], param_config[2]
                            new_search_space[param_name] = optuna.distributions.IntDistribution(low=int(low), high=int(high))
                        elif param_type == 'categorical':
                            choices = param_config[1]
                            new_search_space[param_name] = optuna.distributions.CategoricalDistribution(choices=choices)

                    # Créer une nouvelle étude avec le nouvel espace de recherche
                    try:
                        # Tenter de mettre à jour l'espace de recherche du sampler existant
                        if hasattr(study.sampler, '_search_space'):
                            study.sampler._search_space = new_search_space
                            logger.info(f"Espace de recherche du sampler mis à jour avec {len(new_search_space)} paramètres")
                        else:
                            logger.warning("Impossible de mettre à jour l'espace de recherche du sampler existant")
                    except Exception as e:
                        logger.error(f"Erreur lors de la mise à jour de l'espace de recherche du sampler: {e}")

                    logger.info(f"Espace de recherche rechargé à partir de config.py avec {len(new_search_space)} paramètres")
                    return True
                else:
                    logger.warning("optuna_search_space n'existe pas dans le module config")
            else:
                logger.warning("Module config non trouvé dans sys.modules")
        except Exception as e:
            logger.error(f"Erreur lors du rechargement de l'espace de recherche: {e}")
            import traceback
            logger.error(traceback.format_exc())

        return False