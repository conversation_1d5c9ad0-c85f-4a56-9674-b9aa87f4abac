# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 831 à 861
# Type: Méthode de la classe DynamicRangeAdjuster

    def _update_group_search_space(self, search_space, new_ranges):
        """
        Met à jour un espace de recherche de type GroupSearchSpace.

        Args:
            search_space: L'espace de recherche à mettre à jour
            new_ranges: Les nouvelles plages à appliquer
        """
        try:
            # Récupérer les groupes d'espaces de recherche
            if hasattr(search_space, 'groups'):
                for group_name, group_space in search_space.groups.items():
                    # Mettre à jour chaque groupe individuellement
                    if hasattr(group_space, 'get') and callable(group_space.get):
                        for param_name, (param_type, new_low, new_high) in new_ranges.items():
                            if param_name in group_space:
                                # Mettre à jour la distribution pour ce paramètre
                                if param_type == 'float':
                                    group_space[param_name] = optuna.distributions.FloatDistribution(low=new_low, high=new_high)
                                elif param_type == 'int':
                                    group_space[param_name] = optuna.distributions.IntDistribution(low=int(new_low), high=int(new_high))
                                elif param_type == 'categorical' and isinstance(new_low, list):
                                    group_space[param_name] = optuna.distributions.CategoricalDistribution(choices=new_low)

                logger.info(f"Espace de recherche GroupSearchSpace mis à jour avec {len(new_ranges)} paramètres")
            else:
                logger.warning("Structure interne du GroupSearchSpace non reconnue")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour du GroupSearchSpace: {e}")
            import traceback
            logger.error(traceback.format_exc())