# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13351 à 13370
# Type: Méthode

def class_to_label(class_index):
    """
    Convertit un indice de classe en étiquette lisible.

    Args:
        class_index: Indice de classe (0 ou 1)

    Returns:
        str: Étiquette correspondante ('PLAYER' ou 'BANKER')
    """
    # Vérifier que l'indice de classe est bien un indice 0-based
    if class_index not in [0, 1]:
        logger.warning(
            f"ATTENTION: Indice de classe invalide: {class_index}. "
            f"Doit être 0 (Player) ou 1 (Banker)."
        )
        # Corriger l'indice si possible
        class_index = max(0, min(1, class_index))

    return "PLAYER" if class_index == 0 else "BANKER"