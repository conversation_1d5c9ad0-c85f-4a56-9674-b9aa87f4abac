# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 7410 à 7461
# Type: Méthode de la classe OptunaOptimizer

        def create_variations():
            logger.warning(f"Création des variations du meilleur essai de la phase {phase_from}")

            # Créer plusieurs variations du meilleur essai
            variations = []

            # Liste des paramètres booléens qui doivent être traités spécialement
            boolean_params = [
                'lstm_use_adaptive_window', 'use_markov_model', 'lstm_use_attention',
                'lstm_use_residual', 'use_advanced_features', 'use_advanced_lstm',
                'use_ensemble', 'use_mixup', 'use_focal_loss', 'adaptive_confidence_threshold',
                'lstm_bidirectional', 'force_markov_training'
            ]

            # Variation 1: Légère variation positive
            variation1 = {}
            for param_name, param_value in best_trial.params.items():
                # Vérifier si c'est un paramètre booléen
                if param_name in boolean_params or param_name.startswith(('use_', 'lstm_use_', 'lgbm_use_')) or param_value is True or param_value is False:
                    # Ne pas modifier les paramètres booléens
                    logger.warning(f"Paramètre booléen '{param_name}' conservé sans variation: {param_value}")
                    variation1[param_name] = param_value
                elif isinstance(param_value, int):
                    variation = max(1, int(param_value * 0.05))
                    variation1[param_name] = param_value + variation
                elif isinstance(param_value, float):
                    variation = param_value * 0.05
                    variation1[param_name] = param_value + variation
                else:
                    variation1[param_name] = param_value

            # Variation 2: Légère variation négative
            variation2 = {}
            for param_name, param_value in best_trial.params.items():
                # Vérifier si c'est un paramètre booléen
                if param_name in boolean_params or param_name.startswith(('use_', 'lstm_use_', 'lgbm_use_')) or param_value is True or param_value is False:
                    # Ne pas modifier les paramètres booléens
                    logger.warning(f"Paramètre booléen '{param_name}' conservé sans variation: {param_value}")
                    variation2[param_name] = param_value
                elif isinstance(param_value, int):
                    variation = max(1, int(param_value * 0.05))
                    variation2[param_name] = max(1, param_value - variation)
                elif isinstance(param_value, float):
                    variation = param_value * 0.05
                    variation2[param_name] = max(0.0, param_value - variation)
                else:
                    variation2[param_name] = param_value

            variations.append(variation1)
            variations.append(variation2)

            return variations