# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 1184 à 1197
# Type: Méthode de la classe OptunaOptimizer

        def evaluate_fold(fold_idx, train_idx, val_idx):
            # Convertir les indices numpy en listes Python
            train_indices = [subset_indices[i] for i in train_idx]
            val_indices = [subset_indices[i] for i in val_idx]

            # Évaluer la configuration sur ce pli
            score, metrics = self._evaluate_config(
                config,
                subset_indices=train_indices,
                validation_indices=val_indices,
                **kwargs
            )

            return fold_idx, score, metrics