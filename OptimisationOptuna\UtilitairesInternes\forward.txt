# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13330 à 13349
# Type: Méthode de la classe StandardCrossEntropyLoss

    def forward(self, inputs, targets):
        """
        Calcule la perte en travaillant directement avec des indices 0-based.

        Args:
            inputs: Sorties du modèle (logits)
            targets: Cibles avec indices 0 et 1

        Returns:
            torch.Tensor: Valeur de la perte
        """
        # Vérifier que les cibles sont bien des indices 0-based
        min_target = torch.min(targets).item()
        max_target = torch.max(targets).item()

        if min_target < 0 or max_target > 1:
            logger.warning(f"Attention: indices invalides détectés dans les cibles: min={min_target}, max={max_target}")

        # Appliquer directement la fonction de perte standard de PyTorch
        return self.standard_loss(inputs, targets)