# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 11800 à 11961
# Type: Méthode de la classe OptunaOptimizer

    def load_and_preprocess_data(self, historical_data_path):
        """
        Charge et prétraite les données d'entraînement avec une taille de batch adaptative.
        Limite les données à 10% du fichier historical_data.txt pour toutes les phases.
        """
        # Analyser le fichier pour déterminer sa taille
        with open(historical_data_path, 'r') as f:
            total_lines = sum(1 for _ in f)

        # Calculer le nombre de lignes à charger (10% du total)
        lines_to_load = int(total_lines * 0.10)

        # Déterminer la taille de batch de chargement optimale
        # Adapter la taille du batch en fonction du nombre total de lignes
        if lines_to_load < 1000:
            # Pour les petits fichiers, utiliser des batchs plus petits
            load_batch_size = max(50, min(int(lines_to_load * 0.2), 200))
        elif lines_to_load < 10000:
            # Pour les fichiers moyens
            load_batch_size = max(100, min(int(lines_to_load * 0.1), 500))
        else:
            # Pour les grands fichiers
            load_batch_size = max(200, min(int(lines_to_load * 0.05), 2000))

        logger.info(f"Fichier historical_data.txt: {total_lines} lignes")
        logger.info(f"Utilisation de 10% des données: {lines_to_load} lignes")
        logger.info(f"Taille de batch de chargement: {load_batch_size} lignes ({load_batch_size/lines_to_load*100:.2f}% des lignes à charger)")

        # Sélectionner aléatoirement 10% des lignes
        import random
        selected_indices = set(random.sample(range(total_lines), lines_to_load))

        # Initialiser les conteneurs pour les données prétraitées
        all_X_lgbm = []
        all_X_lstm = []
        all_y = []

        # Charger et prétraiter par batchs uniquement les lignes sélectionnées
        with open(historical_data_path, 'r') as f:
            batch_lines = []
            lines_processed = 0

            for i, line in enumerate(f):
                # Ne traiter que les lignes sélectionnées (10% du total)
                if i in selected_indices:
                    batch_lines.append(line)
                    lines_processed += 1

                    if len(batch_lines) >= load_batch_size or lines_processed == lines_to_load:
                        # Prétraiter ce batch
                        X_lgbm_batch, X_lstm_batch, y_batch = self._preprocess_batch(batch_lines)

                        # Accumuler les données prétraitées
                        all_X_lgbm.append(X_lgbm_batch)
                        all_X_lstm.append(X_lstm_batch)
                        all_y.append(y_batch)

                        # Réinitialiser pour le prochain batch
                        batch_lines = []

                        # Afficher la progression
                        progress = min(100, int(lines_processed / lines_to_load * 100))
                        logger.info(f"Prétraitement: {progress}% ({lines_processed}/{lines_to_load} lignes)")

        # Vérifier que nous avons des données à concaténer
        if not all_X_lgbm or not all_X_lstm or not all_y:
            logger.error("Aucune donnée n'a été chargée dans load_and_preprocess_data")

            # Afficher un échantillon du fichier pour le débogage
            with open(historical_data_path, 'r') as f:
                sample_lines = [next(f).strip() for _ in range(min(5, total_lines))]

            logger.error(f"Échantillon des premières lignes du fichier:")
            for i, line in enumerate(sample_lines):
                logger.error(f"  Ligne {i+1}: {line[:100]}{'...' if len(line) > 100 else ''}")

            # Initialiser avec des tableaux vides mais valides
            self.X_lgbm_full = np.array([]).reshape(0, 20)  # 20 features pour LGBM par défaut
            self.X_lstm_full = np.array([]).reshape(0, 20)  # 20 timesteps pour LSTM par défaut
            self.y_full = np.array([], dtype=np.int32)
            self.train_indices = np.array([], dtype=np.int32)

            logger.warning("Initialisation avec des tableaux vides")
            return self.X_lgbm_full, self.X_lstm_full, self.y_full

        # Concaténer tous les batchs pour avoir l'ensemble complet des données d'entraînement
        try:
            # Vérifier les dimensions des tableaux avant concaténation
            shapes_lgbm = [arr.shape for arr in all_X_lgbm]
            shapes_lstm = [arr.shape for arr in all_X_lstm]
            shapes_y = [arr.shape for arr in all_y]

            logger.info(f"Formes des tableaux LGBM avant concaténation: {shapes_lgbm}")
            logger.info(f"Formes des tableaux LSTM avant concaténation: {shapes_lstm}")
            logger.info(f"Formes des tableaux y avant concaténation: {shapes_y}")

            # Vérifier si tous les tableaux ont la même dimension
            if len(set(arr.shape[1] for arr in all_X_lgbm)) > 1:
                raise ValueError(f"Les tableaux LGBM n'ont pas tous la même dimension: {shapes_lgbm}")
            if len(set(arr.shape[1] for arr in all_X_lstm)) > 1:
                raise ValueError(f"Les tableaux LSTM n'ont pas tous la même dimension: {shapes_lstm}")

            # Concaténer les tableaux
            self.X_lgbm_full = np.concatenate(all_X_lgbm)
            self.X_lstm_full = np.concatenate(all_X_lstm)
            self.y_full = np.concatenate(all_y)

            logger.info(f"Formes après concaténation: LGBM={self.X_lgbm_full.shape}, LSTM={self.X_lstm_full.shape}, y={self.y_full.shape}")

            # Vérifier que nous avons des données après concaténation
            if len(self.y_full) == 0:
                logger.error("Aucune donnée après concaténation dans load_and_preprocess_data")

                # Afficher un échantillon du fichier pour le débogage
                with open(historical_data_path, 'r') as f:
                    sample_lines = [next(f).strip() for _ in range(min(5, total_lines))]

                logger.error(f"Échantillon des premières lignes du fichier:")
                for i, line in enumerate(sample_lines):
                    logger.error(f"  Ligne {i+1}: {line[:100]}{'...' if len(line) > 100 else ''}")

                # Initialiser avec des tableaux vides mais valides
                self.X_lgbm_full = np.array([]).reshape(0, 20)  # 20 features pour LGBM par défaut
                self.X_lstm_full = np.array([]).reshape(0, 20)  # 20 timesteps pour LSTM par défaut
                self.y_full = np.array([], dtype=np.int32)
                self.train_indices = np.array([], dtype=np.int32)

                logger.warning("Initialisation avec des tableaux vides après concaténation")
                return self.X_lgbm_full, self.X_lstm_full, self.y_full

            # Initialiser les indices d'entraînement
            self.train_indices = np.arange(len(self.y_full))

            # Analyser les caractéristiques des données pour optimiser les batchs d'entraînement
            self._optimize_training_batch_sizes()

            logger.info(f"Données d'entraînement préparées: {len(self.y_full)} échantillons au total")
            logger.info(f"Distribution des classes: {np.bincount(self.y_full)}")

        except Exception as e:
            logger.error(f"Erreur lors de la concaténation des données: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Afficher des informations supplémentaires pour le débogage
            logger.error(f"Nombre de batchs: LGBM={len(all_X_lgbm)}, LSTM={len(all_X_lstm)}, y={len(all_y)}")
            if all_X_lgbm:
                logger.error(f"Premier batch LGBM: forme={all_X_lgbm[0].shape}, type={type(all_X_lgbm[0])}")
            if all_X_lstm:
                logger.error(f"Premier batch LSTM: forme={all_X_lstm[0].shape}, type={type(all_X_lstm[0])}")
            if all_y:
                logger.error(f"Premier batch y: forme={all_y[0].shape}, type={type(all_y[0])}")

            # Initialiser avec des tableaux vides mais valides
            self.X_lgbm_full = np.array([]).reshape(0, 20)  # 20 features pour LGBM par défaut
            self.X_lstm_full = np.array([]).reshape(0, 20)  # 20 timesteps pour LSTM par défaut
            self.y_full = np.array([], dtype=np.int32)
            self.train_indices = np.array([], dtype=np.int32)

            logger.warning("Initialisation avec des tableaux vides suite à une erreur")
            return self.X_lgbm_full, self.X_lstm_full, self.y_full
        return self.X_lgbm_full, self.X_lstm_full, self.y_full