# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12785 à 12810
# Type: Méthode de la classe LSTMDynamicCallback

            def on_epoch_end(self, epoch, logs=None):
                """Appelé à la fin de chaque époque."""
                if logs is None:
                    logs = {}

                current_loss = logs.get('val_loss', logs.get('loss', float('inf')))

                # Vérifier si les performances s'améliorent
                if current_loss < self.best_loss:
                    self.best_loss = current_loss
                    self.patience = 0
                else:
                    self.patience += 1

                # Ajuster les hyperparamètres si nécessaire
                if self.patience >= self.max_patience:
                    # Réduire le learning rate
                    self.current_lr *= 0.5
                    print(f"LSTM: Réduction du learning rate à {self.current_lr}")

                    # Réinitialiser la patience
                    self.patience = 0

                    # Mettre à jour le learning rate du modèle
                    import tensorflow as tf
                    tf.keras.backend.set_value(self.model.optimizer.lr, self.current_lr)