# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 2686 à 2692
# Type: Méthode de la classe OptunaOptimizer

        def real_predict_model(self, X):
            # Vérifier que le modèle a été entraîné
            if not hasattr(self, 'model_'):
                raise RuntimeError("Le modèle n'a pas été entraîné. Appelez fit() d'abord.")

            # Faire des prédictions
            return self.model_.predict(X)