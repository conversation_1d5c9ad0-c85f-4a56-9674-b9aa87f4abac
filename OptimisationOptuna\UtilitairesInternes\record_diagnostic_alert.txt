# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13697 à 13704
# Type: Méthode de la classe OptimizationStatsCollector

    def record_diagnostic_alert(self, alert_type: str):
        """
        Enregistre une alerte de diagnostic.

        Args:
            alert_type: Type d'alerte (par exemple, "confiance_basse", "incertitude_elevee", etc.)
        """
        self.diagnostic_alerts[alert_type] += 1