# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14620 à 14782
# Type: Méthode de la classe MetaOptimizer

    def sample_relative(self, study, trial, search_space):
        """
        Échantillonne des valeurs relatives à l'espace de recherche, en évitant les régions problématiques
        et en tenant compte de l'espace de recherche restreint.
        Utilise l'historique des essais réussis et l'échantillonnage adaptatif pour guider la recherche.

        Args:
            study: Étude Optuna
            trial: Essai Optuna
            search_space: Espace de recherche

        Returns:
            dict: Valeurs échantillonnées
        """
        # Analyser les essais terminés
        for completed_trial in study.trials:
            if completed_trial.state == optuna.trial.TrialState.COMPLETE and completed_trial.number not in [t.get('trial_number', -1) for t in self.problematic_trials]:
                self.analyze_trial(completed_trial)

        # Décider si on utilise l'échantillonnage adaptatif
        use_adaptive_sampling = (
            hasattr(self, 'use_adaptive_sampling') and
            self.use_adaptive_sampling and
            hasattr(self, 'adaptive_sampling')
        )

        # Décider si on utilise l'échantillonnage basé sur les essais réussis
        use_success_based_sampling = (
            hasattr(self, 'use_success_history') and
            self.use_success_history and
            hasattr(self, 'successful_trials') and
            len(self.successful_trials) >= 3
        )

        # Décider si on utilise l'échantillonnage exploratoire ou exploitatif
        use_exploitative_sampling = False
        if use_adaptive_sampling:
            # Décider aléatoirement en fonction des facteurs d'exploration/exploitation
            use_exploitative_sampling = np.random.random() < self.adaptive_sampling.get('exploitation_factor', 0.5)

            if use_exploitative_sampling:
                logger.info(f"Utilisation de l'échantillonnage exploitatif pour l'essai {trial.number}")
            else:
                logger.info(f"Utilisation de l'échantillonnage exploratoire pour l'essai {trial.number}")

        # Ajuster l'espace de recherche pour éviter les régions problématiques
        adjusted_search_space = {}
        for name, distribution in search_space.items():
            # Si le paramètre est dans l'espace de recherche restreint, restreindre la distribution
            if name in self.restricted_search_space:
                param_space = self.restricted_search_space[name]

                # Récupérer les limites restreintes
                low = param_space.get('low')
                high = param_space.get('high')
                current = param_space.get('current')

                # Créer une nouvelle distribution restreinte
                if isinstance(distribution, optuna.distributions.UniformDistribution) and low is not None and high is not None:
                    # Pour les distributions uniformes (float)
                    distribution = optuna.distributions.UniformDistribution(low, high)
                    logger.debug(f"Distribution restreinte pour '{name}': {low:.4f}-{high:.4f}")
                elif isinstance(distribution, optuna.distributions.IntUniformDistribution) and low is not None and high is not None:
                    # Pour les distributions uniformes entières
                    distribution = optuna.distributions.IntUniformDistribution(int(low), int(high))
                    logger.debug(f"Distribution restreinte pour '{name}': {int(low)}-{int(high)}")
                elif isinstance(distribution, optuna.distributions.LogUniformDistribution) and low is not None and high is not None:
                    # Pour les distributions log-uniformes
                    distribution = optuna.distributions.LogUniformDistribution(low, high)
                    logger.debug(f"Distribution restreinte pour '{name}': {low:.4f}-{high:.4f} (log)")

                # Utiliser la valeur actuelle pour le premier essai si disponible
                if trial.number == 0 and current is not None:
                    # Pour le premier essai, utiliser la valeur actuelle
                    # Nous ne pouvons pas retourner directement cette valeur ici,
                    # mais nous la stockerons pour l'utiliser plus tard
                    self.current_values = self.current_values if hasattr(self, 'current_values') else {}
                    self.current_values[name] = current

            # Si nous utilisons l'échantillonnage basé sur les essais réussis et que nous sommes en mode exploitatif
            if use_success_based_sampling and use_exploitative_sampling:
                # Vérifier si nous avons des valeurs réussies pour ce paramètre
                successful_values = []
                for s_trial in self.successful_trials:
                    if 'params' in s_trial and name in s_trial['params']:
                        successful_values.append(s_trial['params'][name])

                if len(successful_values) >= 3:
                    # Calculer la moyenne et l'écart-type des valeurs réussies
                    mean_value = np.mean(successful_values)
                    std_value = max(0.01, np.std(successful_values))  # Éviter un écart-type trop petit

                    # Créer une distribution centrée autour de la moyenne des valeurs réussies
                    if isinstance(distribution, optuna.distributions.UniformDistribution):
                        orig_low = distribution.low
                        orig_high = distribution.high
                        range_width = orig_high - orig_low

                        # Calculer une plage autour de la moyenne des valeurs réussies
                        # avec une largeur proportionnelle à l'écart-type
                        std_factor = 1.5  # Facteur pour contrôler la largeur de la plage

                        new_low = max(orig_low, mean_value - std_value * std_factor)
                        new_high = min(orig_high, mean_value + std_value * std_factor)

                        # S'assurer que la plage n'est pas trop étroite
                        min_width = range_width * 0.1
                        if new_high - new_low < min_width:
                            center = (new_high + new_low) / 2
                            new_low = max(orig_low, center - min_width / 2)
                            new_high = min(orig_high, center + min_width / 2)

                        distribution = optuna.distributions.UniformDistribution(new_low, new_high)
                        logger.info(f"Distribution ajustée pour '{name}' (basée sur {len(successful_values)} essais réussis): {new_low:.4f}-{new_high:.4f}")

                    elif isinstance(distribution, optuna.distributions.IntUniformDistribution):
                        orig_low = distribution.low
                        orig_high = distribution.high

                        # Calculer une plage autour de la moyenne des valeurs réussies
                        std_factor = 1.5

                        new_low = max(orig_low, int(mean_value - std_value * std_factor))
                        new_high = min(orig_high, int(mean_value + std_value * std_factor))

                        # S'assurer que la plage n'est pas trop étroite
                        if new_high - new_low < 2:
                            new_low = max(orig_low, int(mean_value) - 1)
                            new_high = min(orig_high, int(mean_value) + 1)

                        distribution = optuna.distributions.IntUniformDistribution(new_low, new_high)
                        logger.info(f"Distribution ajustée pour '{name}' (basée sur {len(successful_values)} essais réussis): {new_low}-{new_high}")

                    elif isinstance(distribution, optuna.distributions.CategoricalDistribution):
                        # Pour les distributions catégorielles, favoriser les valeurs qui ont bien fonctionné
                        choices = distribution.choices

                        # Compter la fréquence de chaque valeur dans les essais réussis
                        value_counts = {}
                        for value in choices:
                            value_counts[value] = successful_values.count(value)

                        # Trouver la valeur la plus fréquente
                        most_frequent_value = max(value_counts.items(), key=lambda x: x[1])[0]

                        # Créer une nouvelle distribution avec seulement la valeur la plus fréquente
                        distribution = optuna.distributions.CategoricalDistribution([most_frequent_value])
                        logger.info(f"Distribution ajustée pour '{name}' (basée sur {len(successful_values)} essais réussis): {most_frequent_value}")

            # Appliquer l'ajustement pour éviter les régions problématiques
            adjusted_search_space[name] = self._adjust_distribution(name, distribution)

        # Échantillonner avec l'espace de recherche ajusté
        params = super().sample_relative(study, trial, adjusted_search_space)

        # Utiliser les valeurs actuelles pour le premier essai si disponibles
        if trial.number == 0 and hasattr(self, 'current_values'):
            for name, value in self.current_values.items():
                if name in params:
                    logger.warning(f"Utilisation de la valeur actuelle pour '{name}': {value}")
                    params[name] = value

        return params