# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12824 à 12842
# Type: Méthode de la classe MarkovDynamicAdapter

            def update(self, performance):
                """Met à jour le lissage en fonction des performances."""
                self.performance_history.append(performance)

                # Calculer la tendance des performances
                if len(self.performance_history) >= 3:
                    recent_trend = np.mean(np.diff(self.performance_history[-3:]))

                    # Ajuster le lissage en fonction de la tendance
                    if recent_trend < 0:  # Performances en baisse
                        # Augmenter le lissage pour plus de stabilité
                        self.current_smoothing = min(0.5, self.current_smoothing * 1.2)
                        print(f"Markov: Augmentation du lissage à {self.current_smoothing}")
                    elif recent_trend > 0:  # Performances en hausse
                        # Réduire le lissage pour plus de réactivité
                        self.current_smoothing = max(0.001, self.current_smoothing * 0.8)
                        print(f"Markov: Réduction du lissage à {self.current_smoothing}")

                return self.current_smoothing