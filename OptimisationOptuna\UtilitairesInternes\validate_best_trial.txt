# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 7318 à 7359
# Type: Méthode de la classe OptunaOptimizer

        def validate_best_trial():
            logger.warning(f"Validation du meilleur essai de la phase {phase_from} sur un sous-ensemble plus grand")

            # Créer une configuration temporaire avec les paramètres du meilleur essai
            temp_config = self.config.clone()
            for param_name, param_value in best_trial.params.items():
                setattr(temp_config, param_name, param_value)

            # Déterminer si nous devons forcer l'entraînement LSTM ou Markov
            force_lstm = False
            force_markov = False

            # Si nous passons à la phase 2 ou 3, activer LSTM
            if phase_to == 2 or phase_to == 3:
                force_lstm = True
                logger.warning(f"Activation forcée de LSTM pour la transition vers la phase {phase_to}")

            # Si nous passons à la phase Markov ou si nous venons de la phase Markov, activer Markov
            if phase_to == 'markov' or phase_from == 'markov':
                force_markov = True
                logger.warning(f"Activation forcée de Markov pour la transition de/vers la phase Markov")

            # Si nous passons à la phase 2 ou 3 après la phase Markov, activer à la fois LSTM et Markov
            if (phase_to == 2 or phase_to == 3) and phase_from == 'markov':
                force_lstm = True
                force_markov = True
                logger.warning(f"Activation forcée de LSTM et Markov pour la transition de la phase Markov vers la phase {phase_to}")

            # Évaluer la configuration sur un sous-ensemble plus grand
            result = self._evaluate_config(
                temp_config,
                is_viability_check=True,
                force_lstm_training=force_lstm,
                force_markov_training=force_markov,
                subset_indices=subset_indices,
                enable_cv=True,
                n_folds=3,
                phase_from=phase_from,
                phase_to=phase_to
            )

            return result