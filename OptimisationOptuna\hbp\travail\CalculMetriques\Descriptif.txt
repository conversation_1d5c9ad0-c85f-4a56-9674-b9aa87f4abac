DESCRIPTIF DES MÉTHODES - DOSSIER CALCULMETRIQUES
=================================================

Ce dossier contient toutes les méthodes liées au calcul de métriques, de confiance, d'incertitude et de performance des modèles de prédiction.

MÉTHODES ANALYSÉES :

1. __init___1.txt (ConsecutiveConfidenceCalculator.__init__)
   - Lignes 1423-1427 dans hbp.py
   - FONCTION : Initialise le calculateur de confiance consécutive
   - FONCTIONNEMENT :
     * Initialise pattern_stats avec un defaultdict pour stocker les statistiques de patterns
     * Crée des listes vides pour recent_recommendations et recent_outcomes
     * Définit max_recent_history (par défaut 50) pour limiter l'historique récent
   - UTILITÉ : Prépare la structure de données pour analyser les patterns de recommandations consécutives

2. _calculate_alternates.txt (HybridBaccaratPredictor._calculate_alternates)
   - Lignes 13871-13911 dans hbp.py
   - FONCTION : Calcule les statistiques d'alternance pour une séquence de résultats
   - FONCTIONNEMENT :
     * Compte les alternances entre 'banker' et 'player' dans la séquence
     * Recherche des motifs d'alternance spécifiques (2 et 3 alternances consécutives)
     * Utilise une fenêtre glissante pour détecter les patterns alt_2_pattern et alt_3_pattern
     * Calcule le ratio d'alternance global (alternances / longueur-1)
   - RETOUR : Dictionnaire avec alternate_count_2, alternate_count_3, alternate_ratio
   - UTILITÉ : Analyse les patterns d'alternance pour améliorer les prédictions

3. _calculate_decay_feature.txt (HybridBaccaratPredictor._calculate_decay_feature)
   - Lignes 13913-13939 dans hbp.py
   - FONCTION : Calcule une feature avec decay pour donner plus de poids aux résultats récents
   - PARAMÈTRES : sequence (List[str]), target_outcome (str - 'banker' ou 'player')
   - FONCTIONNEMENT :
     * Utilise un facteur de decay (self.config.decay_factor) pour pondérer les résultats
     * Les résultats plus récents ont un poids plus élevé (decay_factor^(len-i-1))
     * Calcule la somme pondérée des occurrences du target_outcome
     * Normalise par le poids total pour obtenir une valeur entre 0 et 1
   - RETOUR : float - valeur de la feature avec decay
   - UTILITÉ : Donne plus d'importance aux tendances récentes dans les prédictions

4. _calculate_streaks.txt (HybridBaccaratPredictor._calculate_streaks)
   - Lignes 13803-13869 dans hbp.py
   - FONCTION : Calcule les statistiques de streaks (séries consécutives) pour une séquence
   - FONCTIONNEMENT :
     * Parcourt la séquence pour identifier les streaks de 'banker' et 'player'
     * Compte les streaks totaux et par longueur spécifique (2 à 7)
     * Ignore les outcomes invalides (ni 'banker' ni 'player')
     * Suit le streak actuel et traite le précédent quand il change
     * Enregistre les streaks maximum pour chaque type
   - RETOUR : Dictionnaire avec compteurs de streaks par type et longueur
   - UTILITÉ : Analyse les patterns de séries consécutives pour les prédictions

5. _extract_features_for_consecutive_calculator.txt (HybridBaccaratPredictor._extract_features_for_consecutive_calculator)
   - Lignes 10350-10441 dans hbp.py
   - FONCTION : Extrait les features pour le calculateur de confiance consécutive
   - FONCTIONNEMENT :
     * Crée un vecteur de 10 features capturant les caractéristiques de la séquence
     * Features basées sur séquence récente : ratio banker/player, alternations
     * Features de performance : taux succès NON-WAIT, ratio WAIT/NON-WAIT
     * Features de position : indicateur manche cible, position relative
     * Features consécutives : nombre actuel/max de NON-WAIT valides consécutives
     * Features de patterns : détection de répétitions de motifs
     * Normalisation des valeurs pour stabilité
   - RETOUR : List[float] - vecteur de 10 features normalisées
   - UTILITÉ : Fournit les données d'entrée pour prédire la confiance NON-WAIT

6. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence)
   - Lignes 1486-1579 dans hbp.py
   - FONCTION : Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle
   - PARAMÈTRES : features_vector, current_round, config
   - FONCTIONNEMENT :
     * Vérifie si la manche est dans la plage cible (target_round_min à target_round_max)
     * Extrait la clé de pattern du vecteur de features
     * Calcule le taux de succès et la longueur moyenne des séquences consécutives
     * Applique des bonus : bell_curve (milieu de plage), sequence, late_game, occurrence, consecutive
     * Calcule les forces de recommandation WAIT et NON-WAIT
     * Limite les valeurs entre 0 et 1
   - RETOUR : Dict avec confidence, expected_consecutive, success_rate, forces de recommandation, facteurs
   - UTILITÉ : Détermine la confiance pour les recommandations NON-WAIT basée sur les patterns historiques

7. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty)
   - Lignes 4386-4547 dans hbp.py
   - FONCTION : Calcule un score d'incertitude basé sur la variance des prédictions des estimateurs LGBM
   - PARAMÈTRES : features (List[float]), predicted_class (Optional[int])
   - FONCTIONNEMENT :
     * Vérifie la validité des features et l'initialisation du modèle lgbm_uncertainty
     * Met à l'échelle les features avec feature_scaler
     * Obtient les prédictions de probabilité de chaque estimateur du BaggingClassifier
     * Calcule la variance des probabilités pour la classe 'Banker'
     * Normalise et clippe le score d'incertitude entre min et max configurés
     * Gestion robuste des erreurs avec valeurs par défaut
   - RETOUR : float - score d'incertitude entre 0 et 1
   - UTILITÉ : Mesure l'incertitude des prédictions pour améliorer la fiabilité des recommandations

8. get_confidence_adjustment.txt (ConsecutiveConfidenceCalculator.get_confidence_adjustment)
   - Lignes 1460-1484 dans hbp.py
   - FONCTION : Calcule l'ajustement de confiance basé sur les performances récentes
   - FONCTIONNEMENT :
     * Analyse les recommandations et outcomes récents
     * Calcule le taux de succès des recommandations NON-WAIT récentes
     * Ajuste la confiance en fonction du taux de succès
     * Utilise des seuils configurables (confidence_adjustment_min/max)
   - RETOUR : float - ajustement de confiance (plage -1.0 à 1.0)
   - UTILITÉ : Adapte dynamiquement la confiance selon les performances récentes

9. consecutive_focused_metric.txt (HybridBaccaratPredictor.consecutive_focused_metric)
   - Lignes 296-365 dans hbp.py
   - FONCTION : Métrique personnalisée pour LGBM focalisée sur les recommandations NON-WAIT valides consécutives
   - PARAMÈTRES : y_true, y_pred, round_indices (manches 31-60)
   - FONCTIONNEMENT :
     * Filtre les données pour les manches cibles (31-60)
     * Simule des recommandations basées sur la confiance minimale
     * Calcule les séquences consécutives de prédictions correctes NON-WAIT
     * Score final pondéré : max_consecutive²*0.8 + weighted_mean*0.15 + accuracy*0.05
   - RETOUR : tuple (score, nom_métrique)
   - UTILITÉ : Optimise spécifiquement pour maximiser les séquences consécutives valides

AUTRES MÉTHODES IMPORTANTES :

10. _extract_pattern_key.txt (ConsecutiveConfidenceCalculator._extract_pattern_key)
   - Lignes 1429-1440 dans hbp.py
   - FONCTION : Extrait une clé de pattern à partir du vecteur de features
   - PARAMÈTRES : features_vector (List[float])
   - FONCTIONNEMENT :
     * Utilise les 5 premières features les plus importantes pour créer une clé
     * Arrondit les valeurs pour regrouper des patterns similaires
     * Features 0-3 (ratios) : arrondies à 1 décimale
     * Autres features : arrondies à 0 décimale
     * Crée une clé formatée "index:valeur" séparée par "|"
   - RETOUR : str - clé de pattern formatée
   - UTILITÉ : Permet de regrouper et identifier des patterns similaires dans l'historique

11. calculate_aleatoric_uncertainty.txt (HybridBaccaratPredictor.calculate_aleatoric_uncertainty)
   - Lignes 8646-8667 dans hbp.py
   - FONCTION : Calcule l'incertitude aléatoire (incertitude inhérente) basée sur l'entropie
   - PARAMÈTRES : prob (float) - probabilité prédite pour une classe
   - FONCTIONNEMENT :
     * Utilise un epsilon depuis la configuration pour éviter les divisions par zéro
     * Assure que prob est dans [epsilon, 1-epsilon] pour éviter log(0)
     * Calcule l'entropie binaire normalisée : -(p*log2(p) + (1-p)*log2(1-p))
     * L'entropie binaire max est 1.0, donc pas de normalisation supplémentaire
   - RETOUR : float - score d'incertitude aléatoire entre 0 et 1
   - UTILITÉ : Mesure l'incertitude inhérente d'une prédiction basée sur sa distribution

12. calculate_bayesian_weights.txt (HybridBaccaratPredictor.calculate_bayesian_weights)
   - Lignes 8589-8622 dans hbp.py
   - FONCTION : Calcule les poids bayésiens des modèles en fonction de leur confiance
   - PARAMÈTRES : current_weights (Dict[str, float]), method_confidences (Dict[str, float])
   - FONCTIONNEMENT :
     * Utilise un epsilon depuis la configuration pour éviter les divisions par zéro
     * Calcule le produit des poids actuels et des confidences (P(M) * P(D|M))
     * Normalise pour obtenir P(M|D) selon le théorème de Bayes
     * Si la somme totale est trop petite, utilise les poids originaux comme fallback
   - RETOUR : Dict[str, float] - poids bayésiens ajustés
   - UTILITÉ : Ajuste dynamiquement les poids des modèles selon leur performance récente

13. calculate_consecutive_focused_weights.txt (HybridBaccaratPredictor.calculate_consecutive_focused_weights)
   - Lignes 172-294 dans hbp.py
   - FONCTION : Calcule des poids d'échantillons favorisant les recommandations NON-WAIT valides consécutives
   - PARAMÈTRES : X_features, y, sequence_positions (positions dans la séquence)
   - FONCTIONNEMENT :
     * Initialise les poids à 1.0 pour tous les échantillons
     * Donne plus de poids aux échantillons des manches cibles (31-60)
     * Applique un facteur progressif selon la position dans la plage cible
     * Si LGBM entraîné : utilise ses prédictions pour pondérer davantage
     * Calcule bonus exponentiel pour séquences consécutives valides
     * Pénalise les recommandations NON-WAIT incorrectes
     * Si LGBM non entraîné : stratégie simplifiée avec poids aléatoires
   - RETOUR : np.ndarray - poids des échantillons
   - UTILITÉ : Optimise l'entraînement pour maximiser les séquences consécutives valides

14. calculate_epistemic_uncertainty.txt (HybridBaccaratPredictor.calculate_epistemic_uncertainty)
   - Lignes 8624-8644 dans hbp.py
   - FONCTION : Calcule l'incertitude épistémique (incertitude du modèle) basée sur la variance
   - PARAMÈTRES : prob_list (List[float]) - liste des probabilités prédites par différents modèles
   - FONCTIONNEMENT :
     * Mesure l'incertitude épistémique par la variance des prédictions entre modèles
     * Utilise un facteur de normalisation configurable (défaut 4.0)
     * La variance max pour des valeurs dans [0,1] est 0.25
     * Clippe la variance normalisée entre min et max configurables
   - RETOUR : float - score d'incertitude épistémique entre 0 et 1
   - UTILITÉ : Mesure le désaccord entre modèles pour évaluer la fiabilité des prédictions

15. calculate_lstm_sample_weights.txt (HybridBaccaratPredictor.calculate_lstm_sample_weights)
   - Lignes 10027-10170 dans hbp.py
   - FONCTION : Calcule les poids d'échantillons pour LSTM basés sur métriques de confiance et incertitude
   - PARAMÈTRES : X_lstm, y_lstm, sequence_positions (positions dans la séquence)
   - FONCTIONNEMENT :
     * Facteur temporel : plus de poids aux échantillons récents (linéaire)
     * Facteur de difficulté : utilise LSTM entraîné pour identifier échantillons difficiles
     * Facteur de position : poids exponentiels pour manches cibles (31-60)
     * Bonus pour classes minoritaires et points de transition
     * Combine tous les facteurs et normalise avec moyenne 1.0
     * Clippe entre min_weight et max_weight configurables
   - RETOUR : np.ndarray - poids des échantillons pour LSTM
   - UTILITÉ : Optimise l'entraînement LSTM pour les séquences temporelles et manches cibles

16. calculate_model_confidence.txt (HybridBaccaratPredictor.calculate_model_confidence)
   - Lignes 8426-8489 dans hbp.py
   - FONCTION : Calcule la confiance d'un modèle en utilisant plusieurs méthodes avancées
   - PARAMÈTRES : prob_banker (float), method (str - 'markov'/'lgbm'/'lstm')
   - FONCTIONNEMENT :
     * Confiance de base : distance à 0.5 normalisée et clippée
     * Ajustement historique : utilise la précision récente du modèle (accuracy_history)
     * Ajustement contextuel : détection de streaks, longueur de séquence
     * Facteurs spécifiques : Markov bon pour patterns, LSTM pour séquences longues
     * Combine tous les facteurs et clippe le résultat final entre 0 et 1
   - RETOUR : float - score de confiance entre 0 et 1
   - UTILITÉ : Évalue dynamiquement la fiabilité de chaque modèle selon le contexte

17. calculate_sample_weights_from_metrics.txt (HybridBaccaratPredictor.calculate_sample_weights_from_metrics)
   - Lignes 9939-10025 dans hbp.py
   - FONCTION : Calcule les poids d'échantillons basés sur les métriques de confiance et d'incertitude
   - PARAMÈTRES : X_features (np.ndarray) - features pour lesquelles calculer les poids
   - FONCTIONNEMENT :
     * Vérifie que le modèle LGBM et le scaler sont disponibles et entraînés
     * Calcule les prédictions LGBM pour obtenir les scores de confiance
     * Utilise le modèle d'incertitude (BaggingClassifier) si disponible
     * Calcule variance des probabilités entre estimateurs (incertitude épistémique)
     * Formule : poids = confiance * (1 - incertitude)
     * Normalise avec moyenne 1.0 et clippe entre min/max configurables
   - RETOUR : np.ndarray - poids des échantillons
   - UTILITÉ : Pondère l'entraînement selon la confiance et l'incertitude prédites

18. class_ConsecutiveConfidenceCalculator.txt (ConsecutiveConfidenceCalculator)
   - Lignes 1422-1579 dans hbp.py
   - FONCTION : Définition complète de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT :
     * Classe spécialisée pour calculer la confiance des recommandations NON-WAIT consécutives
     * Hérite de HybridBaccaratPredictor pour accéder aux méthodes de base
     * Contient toutes les méthodes d'analyse de patterns et de calcul de confiance
     * Gère l'historique des recommandations et outcomes récents
     * Implémente les algorithmes de bonus et d'ajustement de confiance
   - UTILITÉ : Classe centrale pour l'analyse de confiance des séquences consécutives

19. consecutive_valid_recommendations_loss.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss)
   - Lignes 84-170 dans hbp.py
   - FONCTION : Fonction de perte personnalisée optimisant les recommandations NON-WAIT valides consécutives
   - PARAMÈTRES : outputs, targets, weights, sequence_positions
   - FONCTIONNEMENT :
     * Calcule probabilités et confiance à partir des logits
     * Détermine recommandations NON-WAIT vs WAIT selon seuil de confiance
     * Applique poids progressifs pour manches cibles (31-60)
     * Bonus pour recommandations NON-WAIT correctes (consecutive_focus_factor)
     * Pénalise davantage les NON-WAIT incorrectes (facteur * 1.5)
     * Calcule CrossEntropy pondérée par échantillon
   - RETOUR : torch.Tensor - valeur de perte pondérée moyenne
   - UTILITÉ : Optimise l'entraînement pour maximiser les séquences consécutives valides

20. get_current_wait_ratio.txt (ConsecutiveConfidenceCalculator.get_current_wait_ratio)
   - Lignes 1452-1458 dans hbp.py
   - FONCTION : Calcule le ratio WAIT/NON-WAIT actuel
   - FONCTIONNEMENT :
     * Vérifie si des recommandations récentes existent
     * Compte les recommandations 'wait' dans l'historique récent
     * Retourne le ratio wait_count / total_recommendations
     * Utilise default_wait_ratio (0.4) si aucune donnée récente
   - RETOUR : float - ratio WAIT/NON-WAIT entre 0 et 1
   - UTILITÉ : Surveille l'équilibre des recommandations pour ajustements

21. init_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.init_consecutive_confidence_calculator)
   - Lignes 1410-1587 dans hbp.py
   - FONCTION : Initialise le calculateur de confiance consécutive pour recommandations NON-WAIT
   - FONCTIONNEMENT :
     * Crée une classe interne ConsecutiveConfidenceCalculator complète
     * Initialise pattern_stats, recent_recommendations, recent_outcomes
     * Implémente toutes les méthodes : _extract_pattern_key, update_recent_data, etc.
     * Méthode calculate_confidence avec bonus multiples (bell_curve, sequence, late_game)
     * Gère l'historique récent avec limite configurable (max_recent_history)
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Point d'entrée pour créer et configurer le système de confiance consécutive

22. consecutive_valid_recommendations_loss_1.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss - version 2)
   - Lignes 10278-10348 dans hbp.py
   - FONCTION : Fonction de perte personnalisée focalisée sur recommandations consécutives valides (version alternative)
   - PARAMÈTRES : outputs, targets, weights, sequence_positions
   - FONCTIONNEMENT :
     * Utilise uncertainty_weighted_loss comme base
     * Ajoute pénalité supplémentaire pour erreurs dans manches cibles (31-60)
     * Pénalité progressive selon position relative dans plage cible
     * Formule : base_loss + mean(penalty) où penalty = error * consecutive_penalty_factor * (1.0 + relative_pos)
     * Système 0-based standard : 0=Player, 1=Banker
   - RETOUR : torch.Tensor - valeur de perte avec pénalité progressive
   - UTILITÉ : Version alternative optimisant spécifiquement les manches cibles avec pénalité progressive

SOUS-DOSSIERS :
- LGBM/ : Vide (métriques LGBM intégrées dans le dossier principal)
- LSTM/ : Vide (métriques LSTM intégrées dans le dossier principal)
- Markov/ : Vide (métriques Markov intégrées dans le dossier principal)

RÉSUMÉ : Ce dossier centralise tous les calculs de métriques, confiance et incertitude nécessaires pour évaluer et optimiser les performances des modèles de prédiction, avec un focus particulier sur les séquences consécutives de recommandations valides.
