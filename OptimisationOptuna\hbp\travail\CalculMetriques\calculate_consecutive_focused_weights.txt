# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 172 à 294
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_consecutive_focused_weights(self, X_features, y, sequence_positions=None):
        """
        Calcule des poids d'échantillons qui favorisent les recommandations NON-WAIT valides consécutives
        pour les manches 31-60, en utilisant les paramètres configurables pour l'objectif N°1.

        Args:
            X_features (np.ndarray): Features d'entrée
            y (np.ndarray): Labels cibles
            sequence_positions (np.ndarray, optional): Positions des échantillons dans la séquence

        Returns:
            np.ndarray: Poids des échantillons
        """
        # Importer NotFittedError ici pour éviter les problèmes d'importation circulaire
        from sklearn.exceptions import NotFittedError

        # Initialiser les poids à 1.0
        sample_weights = np.ones(len(X_features), dtype=np.float32)

        if sequence_positions is None:
            logger.warning("calculate_consecutive_focused_weights: Positions de séquence non fournies.")
            return sample_weights

        # Paramètres configurables pour l'objectif N°1
        consecutive_focus_factor = self.config.consecutive_focus_factor
        late_game_weight_factor = self.config.late_game_weight_factor
        target_round_min = self.config.target_round_min
        target_round_max = self.config.target_round_max
        objective1_weight = self.config.objective1_weight

        # Convertir les positions 0-indexées en positions 1-indexées pour correspondre aux numéros de manches
        positions_1_indexed = sequence_positions + 1

        # Donner plus de poids aux échantillons des manches cibles (31-60)
        late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)
        sample_weights[late_game_mask] *= late_game_weight_factor

        # Donner un poids progressif en fonction de la position dans la plage cible
        # Plus on avance dans la plage cible, plus le poids est important
        for i, pos in enumerate(positions_1_indexed):
            if target_round_min <= pos <= target_round_max:
                # Calculer la position relative dans la plage cible (0 à 1)
                relative_pos = (pos - target_round_min) / (target_round_max - target_round_min)
                # Appliquer un facteur progressif (1.0 à 1.5)
                progressive_factor = 1.0 + 0.5 * relative_pos
                sample_weights[i] *= progressive_factor

        # Si le modèle LGBM est disponible et entraîné, utiliser ses prédictions pour pondérer davantage
        lgbm_model_fitted = False
        if self.lgbm_base is not None:
            try:
                # Vérifier si le modèle LGBM est entraîné
                try:
                    from sklearn.utils.validation import check_is_fitted
                    check_is_fitted(self.lgbm_base)
                    lgbm_model_fitted = True
                except Exception as e:
                    logger.warning(f"Modèle LGBM non entraîné, utilisation de poids simplifiés: {e}")
                    lgbm_model_fitted = False

                if lgbm_model_fitted:
                    # Calculer les prédictions pour chaque échantillon
                    y_pred_proba = self.lgbm_base.predict_proba(X_features)

                    # Calculer la confiance pour chaque échantillon
                    confidence = np.max(y_pred_proba, axis=1)

                    # Déterminer si c'est une recommandation NON-WAIT ou WAIT
                    min_confidence = self.config.min_confidence_for_recommendation
                    non_wait_mask = confidence >= min_confidence

                    # Déterminer si la prédiction est correcte
                    predicted_classes = np.argmax(y_pred_proba, axis=1)
                    correct_predictions = (predicted_classes == y)

                    # Donner plus de poids aux recommandations NON-WAIT correctes
                    valid_non_wait = non_wait_mask & correct_predictions
                    sample_weights[valid_non_wait] *= consecutive_focus_factor

                    # Pénaliser davantage les recommandations NON-WAIT incorrectes
                    invalid_non_wait = non_wait_mask & ~correct_predictions
                    sample_weights[invalid_non_wait] *= consecutive_focus_factor * 1.5

                    # Simuler des séquences consécutives pour donner encore plus de poids
                    # aux échantillons qui contribuent à des séquences longues
                    consecutive_bonus = np.zeros_like(sample_weights)
                    current_consecutive = 0

                    for i in range(len(sample_weights)):
                        if non_wait_mask[i]:
                            if correct_predictions[i]:
                                current_consecutive += 1
                                # Bonus exponentiel basé sur la longueur de la séquence et le facteur de focus
                                consecutive_bonus[i] = np.exp(min(current_consecutive, 10) / 5) * consecutive_focus_factor - 1
                            else:
                                current_consecutive = 0
                        # Pour WAIT, ne pas réinitialiser le compteur

                    # Appliquer le bonus de consécutivité
                    sample_weights += consecutive_bonus * objective1_weight

                    # Appliquer un bonus supplémentaire pour les manches cibles avec des séquences consécutives
                    for i in range(len(sample_weights)):
                        if late_game_mask[i] and consecutive_bonus[i] > 0:
                            sample_weights[i] *= 1.5  # Bonus supplémentaire pour les séquences consécutives dans les manches cibles

                    logger.info(f"Poids calculés avec bonus de consécutivité. Max: {np.max(sample_weights):.2f}, Min: {np.min(sample_weights):.2f}")
            except Exception as e:
                logger.error(f"Erreur lors du calcul des poids avec bonus de consécutivité: {e}", exc_info=True)

        # Si le modèle LGBM n'est pas entraîné, appliquer une stratégie simplifiée
        if not lgbm_model_fitted:
            # Appliquer un poids supplémentaire aux échantillons des manches cibles
            sample_weights[late_game_mask] *= 1.5

            # Appliquer un poids aléatoire pour introduire de la diversité
            np.random.seed(42)  # Pour la reproductibilité
            random_weights = np.random.uniform(0.8, 1.2, size=len(sample_weights))
            sample_weights *= random_weights

            logger.info(f"Poids simplifiés calculés (LGBM non entraîné). Max: {np.max(sample_weights):.2f}, Min: {np.min(sample_weights):.2f}")

        return sample_weights