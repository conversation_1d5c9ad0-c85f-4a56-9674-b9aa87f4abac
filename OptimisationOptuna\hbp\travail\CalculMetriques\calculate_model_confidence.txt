# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 8426 à 8489
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_model_confidence(self, prob_banker: float, method: str) -> float:
        """
        Calcule la confiance d'un modèle en utilisant plusieurs méthodes avancées.

        Args:
            prob_banker (float): Probabilité prédite pour 'banker'
            method (str): Nom du modèle ('markov', 'lgbm', 'lstm')

        Returns:
            float: Score de confiance entre 0 et 1
        """
        # 1. Confiance basée sur la distance à 0.5 (méthode de base)
        confidence_normalization_factor = getattr(self.config, 'confidence_normalization_factor', 2.0)
        confidence_min_clip = getattr(self.config, 'confidence_min_clip', 0.0)
        confidence_max_clip = getattr(self.config, 'confidence_max_clip', 1.0)
        base_confidence = np.clip(confidence_normalization_factor * abs(prob_banker - 0.5), confidence_min_clip, confidence_max_clip)

        # 2. Ajustement basé sur la performance historique du modèle
        historical_factor = 1.0
        with self.weights_lock:
            if method in self.method_performance:
                perf_data = self.method_performance[method]
                # Utiliser la précision récente comme facteur d'ajustement
                acc_history = perf_data.get('accuracy_history', [])
                if acc_history:
                    recent_acc = np.mean(acc_history[-10:]) if len(acc_history) >= 10 else np.mean(acc_history)
                    # Transformer l'accuracy en facteur (0.5 → 1.0, 1.0 → 2.0)
                    historical_factor = 1.0 + (recent_acc - 0.5) * 2.0
                    historical_factor_min = getattr(self.config, 'historical_factor_min', 0.8)
                    historical_factor_max = getattr(self.config, 'historical_factor_max', 2.0)
                    historical_factor = max(historical_factor_min, min(historical_factor_max, historical_factor))

        # 3. Ajustement contextuel basé sur la séquence actuelle
        context_factor = 1.0
        if hasattr(self, 'sequence') and self.sequence:
            # Détection de streaks (séquences répétitives)
            if len(self.sequence) >= 3:
                last_3 = self.sequence[-3:]
                if all(x == last_3[0] for x in last_3):
                    # Streak détecté, ajuster la confiance en fonction du modèle
                    if method == 'markov':  # Markov est bon pour détecter les patterns
                        context_factor = getattr(self.config, 'markov_streak_factor', 1.2)
                    elif method == 'lstm':  # LSTM est bon pour les séquences temporelles
                        context_factor = getattr(self.config, 'lstm_streak_factor', 1.15)

            # Ajustement basé sur la longueur de la séquence
            seq_length = len(self.sequence)
            lstm_long_sequence_threshold = getattr(self.config, 'lstm_long_sequence_threshold', 30)
            lstm_long_sequence_factor = getattr(self.config, 'lstm_long_sequence_factor', 1.3)
            markov_short_sequence_threshold = getattr(self.config, 'markov_short_sequence_threshold', 20)
            markov_short_sequence_factor = getattr(self.config, 'markov_short_sequence_factor', 0.7)

            if method == 'lstm' and seq_length > lstm_long_sequence_threshold:
                # LSTM est plus fiable avec plus de données
                context_factor *= min(lstm_long_sequence_factor, 1.0 + (seq_length - lstm_long_sequence_threshold) / 100)
            elif method == 'markov' and seq_length < markov_short_sequence_threshold:
                # Markov est moins fiable avec peu de données
                context_factor *= max(markov_short_sequence_factor, seq_length / markov_short_sequence_threshold)

        # Combiner tous les facteurs
        final_confidence = base_confidence * historical_factor * context_factor

        # Assurer que la confiance reste dans [0, 1]
        return np.clip(final_confidence, 0.0, 1.0)