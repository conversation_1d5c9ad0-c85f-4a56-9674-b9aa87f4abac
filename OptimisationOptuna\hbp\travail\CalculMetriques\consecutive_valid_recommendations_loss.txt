# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 84 à 170
# Type: Méthode de la classe HybridBaccaratPredictor

    def consecutive_valid_recommendations_loss(self, outputs, targets, weights=None, sequence_positions=None):
        """
        Fonction de perte personnalisée qui optimise pour les recommandations NON-WAIT valides consécutives,
        en tenant compte des positions de séquence pour donner plus de poids aux manches 31-60.

        Cette fonction donne plus de poids aux échantillons qui contribuent à maintenir ou augmenter
        une séquence de recommandations NON-WAIT valides consécutives, en tenant compte du fait que
        les recommandations WAIT ne brisent pas la séquence.

        Args:
            outputs (torch.Tensor): Sorties du modèle (logits)
            targets (torch.Tensor): Labels cibles
            weights (torch.Tensor, optional): Poids des échantillons
            sequence_positions (torch.Tensor, optional): Positions des échantillons dans la séquence

        Returns:
            torch.Tensor: Valeur de perte pondérée
        """
        import torch.nn.functional as F
        device = outputs.device

        # Calculer les probabilités à partir des logits
        probas = F.softmax(outputs, dim=1)

        # Calculer la confiance (max des probabilités)
        confidence, predicted_classes = torch.max(probas, dim=1)

        # Déterminer si c'est une recommandation NON-WAIT ou WAIT
        # Utiliser le seuil de confiance de la configuration
        min_confidence = self.config.min_confidence_for_recommendation
        non_wait_mask = confidence >= min_confidence

        # Déterminer si la prédiction est correcte
        correct_predictions = (predicted_classes == targets)

        # Initialiser les poids à 1.0
        sample_weights = torch.ones_like(confidence, device=device)

        # Appliquer les poids fournis si disponibles
        if weights is not None:
            sample_weights = sample_weights * weights

        # Paramètres configurables pour l'objectif N°1
        consecutive_focus_factor = self.config.consecutive_focus_factor
        late_game_weight_factor = self.config.late_game_weight_factor
        target_round_min = self.config.target_round_min
        target_round_max = self.config.target_round_max

        # Donner plus de poids aux échantillons des manches cibles (31-60)
        if sequence_positions is not None:
            # Convertir les positions 0-indexées en positions 1-indexées pour correspondre aux numéros de manches
            positions_1_indexed = sequence_positions + 1

            # Créer un masque pour les manches cibles
            late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)

            # Appliquer le facteur de poids pour les manches cibles
            sample_weights[late_game_mask] *= late_game_weight_factor

            # Donner un poids progressif en fonction de la position dans la plage cible
            # Plus on avance dans la plage cible, plus le poids est important
            for i, pos in enumerate(positions_1_indexed):
                if target_round_min <= pos <= target_round_max:
                    # Calculer la position relative dans la plage cible (0 à 1)
                    relative_pos = (pos - target_round_min) / (target_round_max - target_round_min)
                    # Appliquer un facteur progressif (1.0 à 1.5)
                    progressive_factor = 1.0 + 0.5 * relative_pos
                    sample_weights[i] *= progressive_factor

        # Donner plus de poids aux recommandations NON-WAIT correctes
        # pour favoriser les séquences consécutives
        valid_non_wait = non_wait_mask & correct_predictions
        sample_weights[valid_non_wait] *= consecutive_focus_factor

        # Pénaliser davantage les recommandations NON-WAIT incorrectes
        # car elles brisent les séquences consécutives
        invalid_non_wait = non_wait_mask & ~correct_predictions
        sample_weights[invalid_non_wait] *= consecutive_focus_factor * 1.5

        # Calculer la perte CrossEntropy pour chaque échantillon
        per_sample_losses = F.cross_entropy(outputs, targets, reduction='none')

        # Appliquer les poids
        weighted_losses = per_sample_losses * sample_weights

        # Retourner la moyenne des pertes pondérées
        return weighted_losses.mean()