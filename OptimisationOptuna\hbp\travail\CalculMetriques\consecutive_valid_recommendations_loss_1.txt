# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 10278 à 10348
# Type: Méthode de la classe HybridBaccaratPredictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def consecutive_valid_recommendations_loss(self, outputs, targets, weights=None, sequence_positions=None):
        """
        Fonction de perte personnalisée qui met l'accent sur les recommandations consécutives valides.

        Cette fonction est spécialement conçue pour l'objectif N°1 : améliorer les recommandations
        consécutives valides dans les manches 31-60. Elle combine la perte d'entropie croisée
        avec une pénalité supplémentaire pour les erreurs dans les manches cibles.

        IMPORTANT: Cette fonction utilise le système zero-based standard de PyTorch:
        - 0 = Player
        - 1 = Banker

        Args:
            outputs (torch.Tensor): Sorties du modèle (logits)
            targets (torch.Tensor): Labels cibles (0 = Player, 1 = Banker)
            weights (torch.Tensor, optional): Poids des échantillons
            sequence_positions (torch.Tensor, optional): Positions des échantillons dans la séquence

        Returns:
            torch.Tensor: Valeur de perte pondérée
        """
        # Vérifier que les cibles sont bien des indices 0-based
        if targets.min() < 0 or targets.max() > 1:
            import logging
            logging.getLogger(__name__).warning(
                f"ATTENTION: Indices invalides détectés dans les cibles: min={targets.min().item()}, max={targets.max().item()}"
            )

        # Utiliser la fonction de perte pondérée par l'incertitude comme base
        base_loss = self.uncertainty_weighted_loss(outputs, targets, weights, sequence_positions)

        # Si les positions de séquence ne sont pas fournies, retourner simplement la perte de base
        if sequence_positions is None:
            return base_loss

        # Paramètres configurables
        target_round_min = getattr(self.config, 'target_round_min', 31)
        target_round_max = getattr(self.config, 'target_round_max', 60)
        consecutive_penalty_factor = getattr(self.config, 'consecutive_penalty_factor', 2.0)

        # Convertir les positions 0-indexées en positions 1-indexées
        positions_1_indexed = sequence_positions + 1

        # Créer un masque pour les manches cibles (31-60)
        target_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)

        # Si aucune manche cible n'est présente dans le batch, retourner la perte de base
        if not torch.any(target_mask):
            return base_loss

        # Calculer les prédictions
        _, predicted = torch.max(outputs, 1)

        # Calculer les erreurs (1 pour les erreurs, 0 pour les prédictions correctes)
        errors = (predicted != targets).float()

        # Appliquer une pénalité supplémentaire pour les erreurs dans les manches cibles
        # Plus la position est avancée dans la plage cible, plus la pénalité est importante
        penalty = torch.zeros_like(errors)

        for i, pos in enumerate(positions_1_indexed):
            if target_round_min <= pos <= target_round_max:
                # Calculer la position relative dans la plage cible (0 à 1)
                relative_pos = (pos - target_round_min) / (target_round_max - target_round_min)
                # Appliquer une pénalité progressive
                penalty[i] = errors[i] * consecutive_penalty_factor * (1.0 + relative_pos)

        # Ajouter la pénalité à la perte de base
        total_loss = base_loss + torch.mean(penalty)

        return total_loss