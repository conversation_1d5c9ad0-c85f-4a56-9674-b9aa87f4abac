# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 10992 à 11122
# Type: Méthode de la classe HybridBaccaratPredictor

    def _load_latest_state(self) -> None:
        """
        Charge automatiquement le dernier état (.joblib ou .pkl) trouvé dans MODEL_SAVE_DIR.
        MODIFIÉ: Affiche une barre de progression verte si le chargement est réussi. MODIFIÉ: Ne fait PLUS de hard reset si aucun état n'est trouvé ou si le chargement échoue.
                 Initialise simplement les modèles et effectue un soft reset de la session.
                 Réinitialise l'affichage visuel après un chargement réussi, tout en conservant les données internes.
        """
        # Note: Pas de `global state_loaded_successfully` nécessaire
        save_dir = MODEL_SAVE_DIR
        historical_txt_path = "historical_data.txt"
        ui_available = self.is_ui_available()

        logger.info("--- Début Chargement Automatique État ---")
        if ui_available: self._update_progress(0, "Recherche dernier état...")

        latest_path, latest_ext = self._find_latest_state_file(save_dir)
        state_loaded_successfully = False  # Initialiser le flag ici

        if latest_path is None:
            logger.info(f"Aucun état sauvegardé trouvé dans {save_dir}. Initialisation de session vide.")
            # Le message "Prêt (Nouvelle Session)" sera mis par reset_data via _update_progress
            # if ui_available: self._update_progress(100, "Prêt (Nouvelle Session)") # Redondant
            # --- Initialisation Vide (Pas Hard Reset) ---
            logger.info("Initialisation des modèles avec poids par défaut.")
            self.init_ml_models(reset_weights=True)  # Crée placeholders et reset poids
            logger.info("Réinitialisation session (soft) car aucun état trouvé.")
            #self.reset_data('soft', confirm=False)  # Reset session interne et affichage visuel
            # --- Fin Modification ---
            logger.info("État initial (modèles vierges, session vide) appliqué.")
            if ui_available:
                self.root.after(0, self._reset_session_display)
            # La MaJ UI/Progression est gérée par reset_data('soft') via after()
            return  # Sortir car un état initial vide a été appliqué

        # --- Partie Chargement ---
        logger.info(f"Fichier le plus récent détecté: {os.path.basename(latest_path)} (Type: {latest_ext})")
        if ui_available: self._update_progress(10, f"Détecté: {os.path.basename(latest_path)}")

        hist_load_ok_for_joblib = False

        # --- Tentative Joblib ---
        if latest_ext == ".joblib":
            logger.info("Tentative chargement état .joblib (nécessite historique)...")
            if ui_available: self._update_progress(20, "Vérif. historique...")
            if not os.path.exists(historical_txt_path):
                 logger.error(f"Historique '{historical_txt_path}' INTROUVABLE pour .joblib.")
                 if ui_available: self._update_progress(0, "Erreur: Hist. manquant")
            else:
                 if ui_available: self._update_progress(30, "Chargement hist...")
                 hist_load_ok_for_joblib = self._load_historical_txt(historical_txt_path)
                 if hist_load_ok_for_joblib:
                     logger.info("Chargement historique OK. Tentative chargement .joblib...")
                     if ui_available: self._update_progress(40, f"Chargement: {os.path.basename(latest_path)}...")

                     if self.load_trained_models(latest_path):
                         logger.info(f"Chargement état .joblib {os.path.basename(latest_path)} interne réussi.")
                         state_loaded_successfully = True  # Mettre le flag de succès

                         # Redemarrage L'UI
                         if ui_available:
                              self.root.after(70, lambda: self._update_progress(100, "Chargement état: historique + joblib OK"))

                     else:
                         # load_trained_models a retourné False (erreur sans hard reset)
                         logger.error(f"Échec chargement état .joblib {os.path.basename(latest_path)} (malgré hist OK).")
                         if ui_available: self._update_progress(0, "Échec chargement .joblib")
                 else:
                     logger.error(f"Échec chargement historique '{historical_txt_path}'. Chargement .joblib annulé.")
                     if ui_available: self._update_progress(0, "Erreur: hist. invalide")

        # --- Tentative .pkl (si joblib échoue OU si pkl est le plus récent) ---
        if not state_loaded_successfully:
            target_pkl_path = None
            # ... (logique fallback pkl inchangée) ...
            if latest_ext == ".pkl":
                target_pkl_path = latest_path
            else:
                 logger.info("Recherche fichier .pkl le plus récent comme fallback...")
                 pkl_path, pkl_ext = self._find_latest_state_file(save_dir)
                 if pkl_path and pkl_ext == ".pkl":
                      target_pkl_path = pkl_path
                      logger.info(f"Fichier .pkl trouvé pour fallback: {os.path.basename(target_pkl_path)}")
                 else: logger.info("Aucun fichier .pkl trouvé pour fallback.")

            if target_pkl_path:
                 logger.info(f"Tentative chargement état .pkl: {os.path.basename(target_pkl_path)}")
                 if ui_available: self._update_progress(40, f"Chargement pkl: {os.path.basename(target_pkl_path)}...")
                 self.loaded_historical = False
                 self.historical_games_at_startup_or_reset = 0
                 self.historical_data = []
                 if self.load_trained_models(target_pkl_path):
                     logger.info(f"Chargement auto état .pkl {os.path.basename(target_pkl_path)} réussi.")
                     state_loaded_successfully = True  # Mettre le flag de succès

                   # Remise a zero visuelle
                     logger.info("Fin chargement UI -> Reset l'historique si nécessaire")

                     if ui_available:

                           self.root.after(70, lambda: self._update_progress(100, "Chargement état: .pkl OK"))
                 else:
                     logger.error(f"Échec chargement auto état .pkl {os.path.basename(target_pkl_path)}.")
                     if ui_available: self._update_progress(0, "Échec chargement .pkl")

        # --- CAS: TOUT A ÉCHOUÉ (Chargement essayé mais raté) ---
        if not state_loaded_successfully:
            logger.warning("Aucun état valide (.joblib+hist ou .pkl) n'a pu être chargé. Initialisation session vide.")
            if ui_available: self._update_progress(0, "Échec chargement état.")

            # --- Initialisation Vide (Pas Hard Reset) ---
            needs_init_reset = (len(self.sequence) != 0 or self.loaded_historical or self.lgbm_base is None)
            if needs_init_reset:
                 logger.info("Initialisation des modèles avec poids par défaut.")
                 self.init_ml_models(reset_weights=True)
                 #self.reset_data('soft', confirm=False) J'ai mis en commentaire car on ne souhaitai pas reset la session
                 if ui_available:
                     self.root.after(0, self._reset_session_display)
                 # MAJ PROG
                 self._update_progress(100, "Pas de save : Session Vierge")
                 logger.info("Pas de save - Chargement Terminé (État vierge appliqué)")
            # --- Fin Modificati

        #On remet la progress bar
        if state_loaded_successfully :

             if ui_available:
                  self.root.after(0, self._reset_session_display)

                  self.root.after(70, lambda: self._update_progress(100, "État chargé avec succès!")) # Ligne ajouée (set Vert 100%)

        logger.info("--- Fin Chargement Automatique État ---")