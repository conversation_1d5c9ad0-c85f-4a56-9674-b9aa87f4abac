# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2333 à 2357
# Type: Méthode de la classe HybridBaccaratPredictor

    def _save_params_to_file(self, params: Dict[str, Any]) -> bool:
        """
        Sauvegarde les paramètres optimisés dans un fichier params.txt.

        Args:
            params (Dict[str, Any]): Dictionnaire des paramètres à sauvegarder

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        try:
            # Vérifier que les paramètres sont valides
            if not params or not isinstance(params, dict):
                logger.error("Paramètres invalides pour la sauvegarde.")
                return False

            # Créer le fichier params.txt
            with open("params.txt", "w", encoding="utf-8") as f:
                json.dump(params, f, indent=4, sort_keys=True)

            logger.info(f"Paramètres optimisés sauvegardés dans params.txt: {len(params)} paramètres.")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des paramètres dans params.txt: {e}", exc_info=True)
            return False