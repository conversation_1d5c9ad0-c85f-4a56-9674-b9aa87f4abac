# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2172 à 2210
# Type: Méthode de la classe HybridBaccaratPredictor

    def load_optimized_params(self):
        """Charge les paramètres optimisés depuis params.txt et les applique à la configuration."""
        try:
            from utils import load_params_from_file, apply_params_to_config

            # Charger les paramètres
            params = load_params_from_file("params.txt")
            if not params:
                logger.warning("Échec du chargement des paramètres depuis params.txt.")
                messagebox.showwarning("Échec du Chargement", "Échec du chargement des paramètres depuis params.txt.")
                return

            # Appliquer les paramètres
            success = apply_params_to_config(self.config, params)

            if success:
                # Compter les paramètres de poids
                weight_params = [p for p in params.keys() if p.startswith('weight_')]
                weight_params_str = ", ".join(weight_params) if weight_params else "aucun"

                # Compter les autres paramètres
                other_params = [p for p in params.keys() if not p.startswith('weight_')]
                other_params_str = ", ".join(other_params) if other_params else "aucun"

                # Message de succès détaillé
                success_msg = (
                    f"Les paramètres optimisés ont été chargés depuis params.txt et appliqués avec succès.\n\n"
                    f"Paramètres de poids ({len(weight_params)}):\n{weight_params_str}\n\n"
                    f"Autres paramètres ({len(other_params)}):\n{other_params_str}"
                )

                logger.info("Paramètres optimisés chargés depuis params.txt et appliqués avec succès.")
                messagebox.showinfo("Paramètres Chargés", success_msg)
            else:
                logger.warning("Échec de l'application des paramètres depuis params.txt.")
                messagebox.showwarning("Échec de l'Application", "Échec de l'application des paramètres depuis params.txt.")
        except Exception as e:
            logger.error(f"Erreur lors du chargement des paramètres optimisés: {e}", exc_info=True)
            messagebox.showerror("Erreur", f"Erreur lors du chargement des paramètres optimisés: {e}")