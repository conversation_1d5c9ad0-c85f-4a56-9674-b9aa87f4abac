DESCRIPTIF GÉNÉRAL DES MÉTHODES - DOSSIER OPTIMISATIONOPTUNA/HBP/TRAVAIL
================================================================================

Ce fichier regroupe TOUS les descriptifs détaillés des méthodes contenues dans les sous-dossiers de OptimisationOptuna/hbp/travail.
Il sert de référence centrale pour l'ensemble du système de prédiction ML hybride.

STRUCTURE DU SYSTÈME :
- CalculMetriques/ : Calculs de métriques, confiance, incertitude (22 méthodes)
- ChargementSauvegarde/ : Chargement/sauvegarde modèles et données (17 méthodes)
- ClassePrincipale/ : Classe principale HybridBaccaratPredictor (1 méthode)
- Entraînement/ : Entraînement des modèles et mise à jour (18 méthodes)
- Gestionressources/ : Gestion ressources et initialisation (7 méthodes)
- InterfaceGraphique/ : Interface utilisateur et affichage (84 méthodes)
- ModelesPrediction/ : Modèles de prédiction et logique hybride (19 méthodes)

TOTAL : 168 MÉTHODES ANALYSÉES

================================================================================
SECTION 1 : CALCULMETRIQUES (22 MÉTHODES)
================================================================================

1. __init___1.txt (ConsecutiveConfidenceCalculator.__init__)
   - Lignes 1423-1427 dans hbp.py
   - FONCTION : Initialise le calculateur de confiance consécutive
   - FONCTIONNEMENT :
     * Initialise pattern_stats avec un defaultdict pour stocker les statistiques de patterns
     * Crée des listes vides pour recent_recommendations et recent_outcomes
     * Définit max_recent_history (par défaut 50) pour limiter l'historique récent
   - UTILITÉ : Prépare la structure de données pour analyser les patterns de recommandations consécutives

2. _calculate_alternates.txt (HybridBaccaratPredictor._calculate_alternates)
   - Lignes 13871-13911 dans hbp.py
   - FONCTION : Calcule les statistiques d'alternance pour une séquence de résultats
   - FONCTIONNEMENT :
     * Compte les alternances entre 'banker' et 'player' dans la séquence
     * Recherche des motifs d'alternance spécifiques (2 et 3 alternances consécutives)
     * Utilise une fenêtre glissante pour détecter les patterns alt_2_pattern et alt_3_pattern
     * Calcule le ratio d'alternance global (alternances / longueur-1)
   - RETOUR : Dictionnaire avec alternate_count_2, alternate_count_3, alternate_ratio
   - UTILITÉ : Analyse les patterns d'alternance pour améliorer les prédictions

3. _calculate_decay_feature.txt (HybridBaccaratPredictor._calculate_decay_feature)
   - Lignes 13913-13939 dans hbp.py
   - FONCTION : Calcule une feature avec decay pour donner plus de poids aux résultats récents
   - PARAMÈTRES : sequence (List[str]), target_outcome (str - 'banker' ou 'player')
   - FONCTIONNEMENT :
     * Utilise un facteur de decay (self.config.decay_factor) pour pondérer les résultats
     * Les résultats plus récents ont un poids plus élevé (decay_factor^(len-i-1))
     * Calcule la somme pondérée des occurrences du target_outcome
     * Normalise par le poids total pour obtenir une valeur entre 0 et 1
   - RETOUR : float - valeur de la feature avec decay
   - UTILITÉ : Donne plus d'importance aux tendances récentes dans les prédictions

4. _calculate_streaks.txt (HybridBaccaratPredictor._calculate_streaks)
   - Lignes 13803-13869 dans hbp.py
   - FONCTION : Calcule les statistiques de streaks (séries consécutives) pour une séquence
   - FONCTIONNEMENT :
     * Parcourt la séquence pour identifier les streaks de 'banker' et 'player'
     * Compte les streaks totaux et par longueur spécifique (2 à 7)
     * Ignore les outcomes invalides (ni 'banker' ni 'player')
     * Suit le streak actuel et traite le précédent quand il change
     * Enregistre les streaks maximum pour chaque type
   - RETOUR : Dictionnaire avec compteurs de streaks par type et longueur
   - UTILITÉ : Analyse les patterns de séries consécutives pour les prédictions

5. _extract_features_for_consecutive_calculator.txt (HybridBaccaratPredictor._extract_features_for_consecutive_calculator)
   - Lignes 10350-10441 dans hbp.py
   - FONCTION : Extrait les features pour le calculateur de confiance consécutive
   - FONCTIONNEMENT :
     * Crée un vecteur de 10 features capturant les caractéristiques de la séquence
     * Features basées sur séquence récente : ratio banker/player, alternations
     * Features de performance : taux succès NON-WAIT, ratio WAIT/NON-WAIT
     * Features de position : indicateur manche cible, position relative
     * Features consécutives : nombre actuel/max de NON-WAIT valides consécutives
     * Features de patterns : détection de répétitions de motifs
     * Normalisation des valeurs pour stabilité
   - RETOUR : List[float] - vecteur de 10 features normalisées
   - UTILITÉ : Fournit les données d'entrée pour prédire la confiance NON-WAIT

6. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence)
   - Lignes 1486-1579 dans hbp.py
   - FONCTION : Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle
   - PARAMÈTRES : features_vector, current_round, config
   - FONCTIONNEMENT :
     * Vérifie si la manche est dans la plage cible (target_round_min à target_round_max)
     * Extrait la clé de pattern du vecteur de features
     * Calcule le taux de succès et la longueur moyenne des séquences consécutives
     * Applique des bonus : bell_curve (milieu de plage), sequence, late_game, occurrence, consecutive
     * Calcule les forces de recommandation WAIT et NON-WAIT
     * Limite les valeurs entre 0 et 1
   - RETOUR : Dict avec confidence, expected_consecutive, success_rate, forces de recommandation, facteurs
   - UTILITÉ : Détermine la confiance pour les recommandations NON-WAIT basée sur les patterns historiques

7. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty)
   - Lignes 4386-4547 dans hbp.py
   - FONCTION : Calcule un score d'incertitude basé sur la variance des prédictions des estimateurs LGBM
   - PARAMÈTRES : features (List[float]), predicted_class (Optional[int])
   - FONCTIONNEMENT :
     * Vérifie la validité des features et l'initialisation du modèle lgbm_uncertainty
     * Met à l'échelle les features avec feature_scaler
     * Obtient les prédictions de probabilité de chaque estimateur du BaggingClassifier
     * Calcule la variance des probabilités pour la classe 'Banker'
     * Normalise et clippe le score d'incertitude entre min et max configurés
     * Gestion robuste des erreurs avec valeurs par défaut
   - RETOUR : float - score d'incertitude entre 0 et 1
   - UTILITÉ : Mesure l'incertitude des prédictions pour améliorer la fiabilité des recommandations

8. get_confidence_adjustment.txt (ConsecutiveConfidenceCalculator.get_confidence_adjustment)
   - Lignes 1460-1484 dans hbp.py
   - FONCTION : Calcule l'ajustement de confiance basé sur les performances récentes
   - FONCTIONNEMENT :
     * Analyse les recommandations et outcomes récents
     * Calcule le taux de succès des recommandations NON-WAIT récentes
     * Ajuste la confiance en fonction du taux de succès
     * Utilise des seuils configurables (confidence_adjustment_min/max)
   - RETOUR : float - ajustement de confiance (plage -1.0 à 1.0)
   - UTILITÉ : Adapte dynamiquement la confiance selon les performances récentes

9. consecutive_focused_metric.txt (HybridBaccaratPredictor.consecutive_focused_metric)
   - Lignes 296-365 dans hbp.py
   - FONCTION : Métrique personnalisée pour LGBM focalisée sur les recommandations NON-WAIT valides consécutives
   - PARAMÈTRES : y_true, y_pred, round_indices (manches 31-60)
   - FONCTIONNEMENT :
     * Filtre les données pour les manches cibles (31-60)
     * Simule des recommandations basées sur la confiance minimale
     * Calcule les séquences consécutives de prédictions correctes NON-WAIT
     * Score final pondéré : max_consecutive²*0.8 + weighted_mean*0.15 + accuracy*0.05
   - RETOUR : tuple (score, nom_métrique)
   - UTILITÉ : Optimise spécifiquement pour maximiser les séquences consécutives valides

10. _extract_pattern_key.txt (ConsecutiveConfidenceCalculator._extract_pattern_key)
   - Lignes 1429-1440 dans hbp.py
   - FONCTION : Extrait une clé de pattern à partir du vecteur de features
   - PARAMÈTRES : features_vector (List[float])
   - FONCTIONNEMENT :
     * Utilise les 5 premières features les plus importantes pour créer une clé
     * Arrondit les valeurs pour regrouper des patterns similaires
     * Features 0-3 (ratios) : arrondies à 1 décimale
     * Autres features : arrondies à 0 décimale
     * Crée une clé formatée "index:valeur" séparée par "|"
   - RETOUR : str - clé de pattern formatée
   - UTILITÉ : Permet de regrouper et identifier des patterns similaires dans l'historique

11. calculate_aleatoric_uncertainty.txt (HybridBaccaratPredictor.calculate_aleatoric_uncertainty)
   - Lignes 8646-8667 dans hbp.py
   - FONCTION : Calcule l'incertitude aléatoire (incertitude inhérente) basée sur l'entropie
   - PARAMÈTRES : prob (float) - probabilité prédite pour une classe
   - FONCTIONNEMENT :
     * Utilise un epsilon depuis la configuration pour éviter les divisions par zéro
     * Assure que prob est dans [epsilon, 1-epsilon] pour éviter log(0)
     * Calcule l'entropie binaire normalisée : -(p*log2(p) + (1-p)*log2(1-p))
     * L'entropie binaire max est 1.0, donc pas de normalisation supplémentaire
   - RETOUR : float - score d'incertitude aléatoire entre 0 et 1
   - UTILITÉ : Mesure l'incertitude inhérente d'une prédiction basée sur sa distribution

12. calculate_bayesian_weights.txt (HybridBaccaratPredictor.calculate_bayesian_weights)
   - Lignes 8589-8622 dans hbp.py
   - FONCTION : Calcule les poids bayésiens des modèles en fonction de leur confiance
   - PARAMÈTRES : current_weights (Dict[str, float]), method_confidences (Dict[str, float])
   - FONCTIONNEMENT :
     * Utilise un epsilon depuis la configuration pour éviter les divisions par zéro
     * Calcule le produit des poids actuels et des confidences (P(M) * P(D|M))
     * Normalise pour obtenir P(M|D) selon le théorème de Bayes
     * Si la somme totale est trop petite, utilise les poids originaux comme fallback
   - RETOUR : Dict[str, float] - poids bayésiens ajustés
   - UTILITÉ : Ajuste dynamiquement les poids des modèles selon leur performance récente

13. calculate_consecutive_focused_weights.txt (HybridBaccaratPredictor.calculate_consecutive_focused_weights)
   - Lignes 172-294 dans hbp.py
   - FONCTION : Calcule des poids d'échantillons favorisant les recommandations NON-WAIT valides consécutives
   - PARAMÈTRES : X_features, y, sequence_positions (positions dans la séquence)
   - FONCTIONNEMENT :
     * Initialise les poids à 1.0 pour tous les échantillons
     * Donne plus de poids aux échantillons des manches cibles (31-60)
     * Applique un facteur progressif selon la position dans la plage cible
     * Si LGBM entraîné : utilise ses prédictions pour pondérer davantage
     * Calcule bonus exponentiel pour séquences consécutives valides
     * Pénalise les recommandations NON-WAIT incorrectes
     * Si LGBM non entraîné : stratégie simplifiée avec poids aléatoires
   - RETOUR : np.ndarray - poids des échantillons
   - UTILITÉ : Optimise l'entraînement pour maximiser les séquences consécutives valides

14. calculate_epistemic_uncertainty.txt (HybridBaccaratPredictor.calculate_epistemic_uncertainty)
   - Lignes 8624-8644 dans hbp.py
   - FONCTION : Calcule l'incertitude épistémique (incertitude du modèle) basée sur la variance
   - PARAMÈTRES : prob_list (List[float]) - liste des probabilités prédites par différents modèles
   - FONCTIONNEMENT :
     * Mesure l'incertitude épistémique par la variance des prédictions entre modèles
     * Utilise un facteur de normalisation configurable (défaut 4.0)
     * La variance max pour des valeurs dans [0,1] est 0.25
     * Clippe la variance normalisée entre min et max configurables
   - RETOUR : float - score d'incertitude épistémique entre 0 et 1
   - UTILITÉ : Mesure le désaccord entre modèles pour évaluer la fiabilité des prédictions

15. calculate_lstm_sample_weights.txt (HybridBaccaratPredictor.calculate_lstm_sample_weights)
   - Lignes 10027-10170 dans hbp.py
   - FONCTION : Calcule les poids d'échantillons pour LSTM basés sur métriques de confiance et incertitude
   - PARAMÈTRES : X_lstm, y_lstm, sequence_positions (positions dans la séquence)
   - FONCTIONNEMENT :
     * Facteur temporel : plus de poids aux échantillons récents (linéaire)
     * Facteur de difficulté : utilise LSTM entraîné pour identifier échantillons difficiles
     * Facteur de position : poids exponentiels pour manches cibles (31-60)
     * Bonus pour classes minoritaires et points de transition
     * Combine tous les facteurs et normalise avec moyenne 1.0
     * Clippe entre min_weight et max_weight configurables
   - RETOUR : np.ndarray - poids des échantillons pour LSTM
   - UTILITÉ : Optimise l'entraînement LSTM pour les séquences temporelles et manches cibles

16. calculate_model_confidence.txt (HybridBaccaratPredictor.calculate_model_confidence)
   - Lignes 8426-8489 dans hbp.py
   - FONCTION : Calcule la confiance d'un modèle en utilisant plusieurs méthodes avancées
   - PARAMÈTRES : prob_banker (float), method (str - 'markov'/'lgbm'/'lstm')
   - FONCTIONNEMENT :
     * Confiance de base : distance à 0.5 normalisée et clippée
     * Ajustement historique : utilise la précision récente du modèle (accuracy_history)
     * Ajustement contextuel : détection de streaks, longueur de séquence
     * Facteurs spécifiques : Markov bon pour patterns, LSTM pour séquences longues
     * Combine tous les facteurs et clippe le résultat final entre 0 et 1
   - RETOUR : float - score de confiance entre 0 et 1
   - UTILITÉ : Évalue dynamiquement la fiabilité de chaque modèle selon le contexte

17. calculate_sample_weights_from_metrics.txt (HybridBaccaratPredictor.calculate_sample_weights_from_metrics)
   - Lignes 9939-10025 dans hbp.py
   - FONCTION : Calcule les poids d'échantillons basés sur les métriques de confiance et d'incertitude
   - PARAMÈTRES : X_features (np.ndarray) - features pour lesquelles calculer les poids
   - FONCTIONNEMENT :
     * Vérifie que le modèle LGBM et le scaler sont disponibles et entraînés
     * Calcule les prédictions LGBM pour obtenir les scores de confiance
     * Utilise le modèle d'incertitude (BaggingClassifier) si disponible
     * Calcule variance des probabilités entre estimateurs (incertitude épistémique)
     * Formule : poids = confiance * (1 - incertitude)
     * Normalise avec moyenne 1.0 et clippe entre min/max configurables
   - RETOUR : np.ndarray - poids des échantillons
   - UTILITÉ : Pondère l'entraînement selon la confiance et l'incertitude prédites

18. class_ConsecutiveConfidenceCalculator.txt (ConsecutiveConfidenceCalculator)
   - Lignes 1422-1579 dans hbp.py
   - FONCTION : Définition complète de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT :
     * Classe spécialisée pour calculer la confiance des recommandations NON-WAIT consécutives
     * Hérite de HybridBaccaratPredictor pour accéder aux méthodes de base
     * Contient toutes les méthodes d'analyse de patterns et de calcul de confiance
     * Gère l'historique des recommandations et outcomes récents
     * Implémente les algorithmes de bonus et d'ajustement de confiance
   - UTILITÉ : Classe centrale pour l'analyse de confiance des séquences consécutives

19. consecutive_valid_recommendations_loss.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss)
   - Lignes 84-170 dans hbp.py
   - FONCTION : Fonction de perte personnalisée optimisant les recommandations NON-WAIT valides consécutives
   - PARAMÈTRES : outputs, targets, weights, sequence_positions
   - FONCTIONNEMENT :
     * Calcule probabilités et confiance à partir des logits
     * Détermine recommandations NON-WAIT vs WAIT selon seuil de confiance
     * Applique poids progressifs pour manches cibles (31-60)
     * Bonus pour recommandations NON-WAIT correctes (consecutive_focus_factor)
     * Pénalise davantage les NON-WAIT incorrectes (facteur * 1.5)
     * Calcule CrossEntropy pondérée par échantillon
   - RETOUR : torch.Tensor - valeur de perte pondérée moyenne
   - UTILITÉ : Optimise l'entraînement pour maximiser les séquences consécutives valides

20. get_current_wait_ratio.txt (ConsecutiveConfidenceCalculator.get_current_wait_ratio)
   - Lignes 1452-1458 dans hbp.py
   - FONCTION : Calcule le ratio WAIT/NON-WAIT actuel
   - FONCTIONNEMENT :
     * Vérifie si des recommandations récentes existent
     * Compte les recommandations 'wait' dans l'historique récent
     * Retourne le ratio wait_count / total_recommendations
     * Utilise default_wait_ratio (0.4) si aucune donnée récente
   - RETOUR : float - ratio WAIT/NON-WAIT entre 0 et 1
   - UTILITÉ : Surveille l'équilibre des recommandations pour ajustements

21. init_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.init_consecutive_confidence_calculator)
   - Lignes 1410-1587 dans hbp.py
   - FONCTION : Initialise le calculateur de confiance consécutive pour recommandations NON-WAIT
   - FONCTIONNEMENT :
     * Crée une classe interne ConsecutiveConfidenceCalculator complète
     * Initialise pattern_stats, recent_recommendations, recent_outcomes
     * Implémente toutes les méthodes : _extract_pattern_key, update_recent_data, etc.
     * Méthode calculate_confidence avec bonus multiples (bell_curve, sequence, late_game)
     * Gère l'historique récent avec limite configurable (max_recent_history)
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Point d'entrée pour créer et configurer le système de confiance consécutive

22. consecutive_valid_recommendations_loss_1.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss - version 2)
   - Lignes 10278-10348 dans hbp.py
   - FONCTION : Fonction de perte personnalisée focalisée sur recommandations consécutives valides (version alternative)
   - PARAMÈTRES : outputs, targets, weights, sequence_positions
   - FONCTIONNEMENT :
     * Utilise uncertainty_weighted_loss comme base
     * Ajoute pénalité supplémentaire pour erreurs dans manches cibles (31-60)
     * Pénalité progressive selon position relative dans plage cible
     * Formule : base_loss + mean(penalty) où penalty = error * consecutive_penalty_factor * (1.0 + relative_pos)
     * Système 0-based standard : 0=Player, 1=Banker
   - RETOUR : torch.Tensor - valeur de perte avec pénalité progressive
   - UTILITÉ : Version alternative optimisant spécifiquement les manches cibles avec pénalité progressive

================================================================================
SECTION 2 : CHARGEMENTSAUVEGARDE (17 MÉTHODES)
================================================================================

23. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data)
   - Lignes 4831-4886 dans hbp.py
   - FONCTION : Charge les données historiques depuis un fichier .txt
   - FONCTIONNEMENT :
     * Vérifie qu'aucun entraînement n'est en cours
     * Ouvre une boîte de dialogue pour sélectionner le fichier historique
     * Utilise _load_historical_txt pour le chargement interne
     * Affiche les statistiques de chargement (parties valides, longueur moyenne, total coups)
     * Met à jour les modèles Markov globaux
     * Propose de réinitialiser la session en cours
     * Gestion d'erreurs avec messages informatifs
   - RETOUR : None (interface utilisateur)
   - UTILITÉ : Interface utilisateur pour charger l'historique de parties depuis un fichier

24. unified_save.txt (HybridBaccaratPredictor.unified_save)
   - Lignes 5004-5053 dans hbp.py
   - FONCTION : Sauvegarde manuelle de l'état actuel via une boîte de dialogue
   - FONCTIONNEMENT :
     * Vérifie qu'aucun processus ML n'est en cours
     * Ouvre une boîte de dialogue pour choisir l'emplacement de sauvegarde
     * Propose .joblib comme format par défaut, autorise aussi .pkl
     * Génère un nom de fichier avec timestamp
     * Utilise _perform_save pour la logique de sauvegarde réelle
     * Ajoute automatiquement l'extension si manquante
     * Affiche les résultats avec messages informatifs
   - RETOUR : None (interface utilisateur)
   - UTILITÉ : Interface utilisateur pour sauvegarder manuellement l'état complet du prédicteur

25. save_optimized_models.txt (HybridBaccaratPredictor.save_optimized_models)
   - Lignes 4905-5002 dans hbp.py
   - FONCTION : Sauvegarde les modèles entraînés avec les hyperparamètres optimisés
   - PARAMÈTRES : params_file_path (str) - chemin vers le fichier JSON des paramètres optimisés
   - FONCTIONNEMENT :
     * Charge les paramètres optimisés depuis le fichier JSON
     * Crée une configuration optimisée avec apply_params_to_config
     * Sauvegarde la configuration optimisée en JSON
     * Applique temporairement la configuration optimisée
     * Sauvegarde les modèles avec _perform_save
     * Restaure la configuration originale
     * Génère un nom de fichier avec le score et timestamp
   - RETOUR : bool - True si succès, False sinon
   - UTILITÉ : Sauvegarde spécialisée pour les modèles optimisés par Optuna

26. _find_latest_state_file.txt (HybridBaccaratPredictor._find_latest_state_file)
   - Lignes 11124-11155 dans hbp.py
   - FONCTION : Recherche le fichier d'état (.joblib ou .pkl) le plus récent
   - PARAMÈTRES : save_dir (str) - répertoire de sauvegarde
   - FONCTIONNEMENT :
     * Vérifie l'existence du répertoire de sauvegarde
     * Liste tous les fichiers .joblib et .pkl du répertoire
     * Récupère la date de modification de chaque fichier
     * Trie par date de modification (plus récent en premier)
     * Gestion robuste des erreurs d'accès fichiers
   - RETOUR : Tuple[Optional[str], Optional[str]] - (chemin_fichier, extension)
   - UTILITÉ : Identifie automatiquement le dernier état sauvegardé pour chargement

27. _load_historical_txt.txt (HybridBaccaratPredictor._load_historical_txt)
   - Lignes 11157-11238 dans hbp.py
   - FONCTION : Logique interne de chargement des données historiques depuis fichier .txt
   - PARAMÈTRES : filepath (str) - chemin vers le fichier historique
   - FONCTIONNEMENT :
     * Parse chaque ligne du fichier avec gestion d'encodage UTF-8
     * Convertit les symboles (b/banker/1→'banker', p/player/0→'player')
     * Ignore les TIEs et symboles invalides avec logging détaillé
     * Met à jour historical_data, loaded_historical, historical_games_at_startup_or_reset
     * Utilise verrous (sequence_lock, markov_lock) pour cohérence multi-thread
     * Met à jour le modèle Markov global avec nouvelles données
     * Compte parties valides, courtes, lignes traitées pour statistiques
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Cœur du système de chargement historique avec parsing robuste

28. _load_latest_state.txt (HybridBaccaratPredictor._load_latest_state)
   - Lignes 10992-11122 dans hbp.py
   - FONCTION : Charge automatiquement le dernier état (.joblib ou .pkl) au démarrage
   - FONCTIONNEMENT :
     * Recherche le fichier d'état le plus récent dans MODEL_SAVE_DIR
     * Priorité .joblib (nécessite historical_data.txt) puis fallback .pkl
     * Pour .joblib : charge d'abord l'historique puis l'état des modèles
     * Pour .pkl : charge directement l'état (historique inclus)
     * Gestion progressive avec barre de progression UI
     * Si échec : initialise session vide avec modèles par défaut (pas de hard reset)
     * Met à jour l'affichage avec statut final (succès vert ou erreur)
   - RETOUR : None (met à jour l'état interne)
   - UTILITÉ : Automatise la restauration de l'état précédent au démarrage

29. _load_latest_state_1.txt (HybridBaccaratPredictor._load_latest_state - version 2)
   - Lignes 12214-12342 dans hbp.py (128 lignes)
   - FONCTION : Version modifiée du chargement automatique d'état (pas de hard reset)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RECHERCHE :** _find_latest_state_file(MODEL_SAVE_DIR)
     * **SI AUCUN ÉTAT :** init_ml_models(reset_weights=True) + reset_data('soft')
     * **CHARGEMENT .JOBLIB :**
       - Vérifie existence historical_data.txt
       - _load_historical_txt() puis load_trained_models()
       - Si succès : _reset_session_display() + _update_weights_display()
     * **FALLBACK .PKL :** si .joblib échoue ou .pkl plus récent
     * **RÉINITIALISATION VISUELLE :** root.after() pour UI thread-safe
     * **PROGRESSION :** messages détaillés selon étapes et résultats
     * **ÉCHEC TOTAL :** init_ml_models() + reset_data('soft') sans hard reset
   - RETOUR : None
   - UTILITÉ : Version améliorée évitant hard reset avec gestion visuelle optimisée

30. _load_selected_model.txt (HybridBaccaratPredictor._load_selected_model)
   - Lignes 3520-3556 dans hbp.py
   - FONCTION : Charge un modèle spécifique sélectionné depuis le tableau de bord
   - PARAMÈTRES : tree (ttk.Treeview) - widget contenant la liste des modèles
   - FONCTIONNEMENT :
     * Vérifie qu'un élément est sélectionné dans le Treeview
     * Récupère le nom du fichier depuis les valeurs de l'élément sélectionné
     * Construit le chemin complet vers le modèle dans le dossier "models"
     * Vérifie l'existence du fichier modèle
     * Demande confirmation utilisateur avant chargement
     * Appelle load_trained_models() pour charger le modèle sélectionné
   - RETOUR : None
   - UTILITÉ : Interface utilisateur pour charger un modèle spécifique depuis le tableau de bord

31. _perform_save.txt (HybridBaccaratPredictor._perform_save)
   - Lignes 1797-2019 dans hbp.py
   - FONCTION : Logique interne de sauvegarde de l'état complet via joblib
   - PARAMÈTRES : filepath (str, optionnel) - chemin de sauvegarde
   - FONCTIONNEMENT :
     * Génère automatiquement un nom de fichier informatif si filepath=None
     * Acquiert tous les verrous (sequence, model, markov, weights) pour cohérence
     * Vérifie que les modèles essentiels sont 'fit' avant sauvegarde
     * Construit un package complet : modèles ML, état Markov, LSTM state_dict
     * Inclut métadonnées, configuration, hyperparamètres, historique session
     * Sauvegarde via joblib avec compression=3
     * Sauvegarde métadonnées associées en JSON
     * Gestion robuste des erreurs (validation, sérialisation, I/O)
     * Libération sécurisée des verrous en finally
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Cœur du système de persistance avec package complet et cohérent

32. _save_lgbm_training_plots.txt (HybridBaccaratPredictor._save_lgbm_training_plots)
   - Lignes 6716-6845 dans hbp.py
   - FONCTION : Crée et sauvegarde des graphiques des métriques d'entraînement LGBM
   - PARAMÈTRES : eval_result (Dict) - résultats d'évaluation LGBM
   - FONCTIONNEMENT :
     * Force l'utilisation du backend matplotlib 'Agg' (non-interactif)
     * Crée le dossier "logs/training_plots" si nécessaire
     * Génère timestamp pour noms de fichiers uniques
     * Crée graphiques pour chaque métrique (train/validation curves)
     * Graphique d'importance des features (top 20, barres horizontales)
     * Matrice de confusion avec valeurs dans cellules
     * Courbe ROC avec score AUC si disponible
     * Sauvegarde tous les graphiques en PNG avec timestamp
   - RETOUR : None
   - UTILITÉ : Visualisation et archivage des performances d'entraînement LGBM

33. _save_lstm_training_plots.txt (HybridBaccaratPredictor._save_lstm_training_plots)
   - Lignes 6847-6953 dans hbp.py
   - FONCTION : Crée et sauvegarde des graphiques des métriques d'entraînement LSTM
   - FONCTIONNEMENT :
     * Force l'utilisation du backend matplotlib 'Agg' (non-interactif)
     * Vérifie la disponibilité des métriques LSTM
     * Crée le dossier "logs/training_plots" si nécessaire
     * Génère timestamp pour noms de fichiers uniques
     * Graphique des pertes (train/validation loss par époque)
     * Graphique des exactitudes (train/validation accuracy par époque)
     * Matrice de confusion avec valeurs dans cellules
     * Graphique des métriques de classification (precision, recall, f1, accuracy)
     * Sauvegarde tous les graphiques en PNG avec timestamp
   - RETOUR : None
   - UTILITÉ : Visualisation et archivage des performances d'entraînement LSTM

34. _save_model_metadata.txt (HybridBaccaratPredictor._save_model_metadata)
   - Lignes 2359-2461 dans hbp.py
   - FONCTION : Sauvegarde les métadonnées du modèle dans un fichier JSON associé
   - PARAMÈTRES : model_filepath (str), package (Dict[str, Any])
   - FONCTIONNEMENT :
     * Extrait les informations pertinentes du package de sauvegarde
     * Calcule les métriques de performance (best_accuracy, method_performance)
     * Crée un dictionnaire complet avec timestamp, type de modèle, version
     * Inclut tous les hyperparamètres (LSTM, LGBM, Markov, seuils)
     * Ajoute les poids actuels et optimaux des modèles
     * Inclut les informations d'entraînement (temps, phase d'optimisation)
     * Filtre les valeurs None pour un JSON propre
     * Sauvegarde en JSON avec même nom de base que le modèle
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Documentation complète des modèles pour traçabilité et analyse

35. _save_params_to_file.txt (HybridBaccaratPredictor._save_params_to_file)
   - Lignes 2333-2357 dans hbp.py
   - FONCTION : Sauvegarde les paramètres optimisés dans un fichier params.txt
   - PARAMÈTRES : params (Dict[str, Any]) - dictionnaire des paramètres à sauvegarder
   - FONCTIONNEMENT :
     * Vérifie la validité des paramètres (non vide, type dict)
     * Crée le fichier "params.txt" avec encodage UTF-8
     * Sauvegarde en JSON avec indentation et tri des clés
     * Log du nombre de paramètres sauvegardés
     * Gestion d'erreurs avec logging détaillé
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Sauvegarde simple des paramètres optimisés pour réutilisation

36. _save_state_to_models_dir.txt (HybridBaccaratPredictor._save_state_to_models_dir)
   - Lignes 10966-10990 dans hbp.py
   - FONCTION : Sauvegarde automatiquement l'état actuel dans MODEL_SAVE_DIR
   - FONCTIONNEMENT :
     * Crée le répertoire MODEL_SAVE_DIR si nécessaire
     * Génère un timestamp pour nom de fichier unique
     * Crée nom de fichier : "predictor_state_{timestamp}.joblib"
     * Construit le chemin complet dans le répertoire de sauvegarde
     * Appelle _perform_save() pour la logique interne de sauvegarde
     * Gestion d'erreurs OSError (création dossier) et exceptions générales
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Interface simplifiée pour sauvegarde automatique avec timestamp

37. load_optimized_models.txt (HybridBaccaratPredictor.load_optimized_models)
   - Lignes 2212-2331 dans hbp.py
   - FONCTION : Charge les modèles entraînés avec les hyperparamètres optimisés (post-Optuna)
   - PARAMÈTRES : params_file_path (str, optionnel) - chemin vers fichier JSON des paramètres
   - FONCTIONNEMENT :
     * Vérifie qu'aucun entraînement n'est en cours
     * Ouvre dialogue de sélection si params_file_path=None
     * Charge les paramètres optimisés depuis fichier JSON
     * Si models_path existe : charge directement les modèles pré-entraînés
     * Sinon : applique paramètres à la configuration et réinitialise modèles
     * Utilise apply_params_to_config() pour mise à jour configuration
     * Informe utilisateur du statut (modèles prêts ou entraînement requis)
     * Gestion complète des erreurs avec messages UI appropriés
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Interface principale pour utiliser les résultats d'optimisation Optuna

38. load_optimized_params.txt (HybridBaccaratPredictor.load_optimized_params)
   - Lignes 2172-2210 dans hbp.py
   - FONCTION : Charge les paramètres optimisés depuis params.txt et les applique à la configuration
   - FONCTIONNEMENT :
     * Utilise load_params_from_file() pour charger depuis "params.txt"
     * Applique les paramètres via apply_params_to_config()
     * Sépare les paramètres de poids (weight_*) des autres paramètres
     * Affiche message de succès détaillé avec comptage des paramètres
     * Gestion d'erreurs avec messages UI appropriés
   - RETOUR : None (met à jour la configuration)
   - UTILITÉ : Interface simple pour appliquer les paramètres optimisés sauvegardés

39. load_trained_models.txt (HybridBaccaratPredictor.load_trained_models)
   - Lignes 3558-4076 dans hbp.py (518 lignes)
   - FONCTION : Charge l'état complet des modèles entraînés depuis fichier .joblib/.pkl
   - PARAMÈTRES : filepath (Optional[str]) - chemin du fichier, dialogue si None
   - FONCTIONNEMENT :
     * Définit flag _loading_existing_model pour adaptation longueur séquence LSTM
     * Acquiert tous les verrous pour chargement thread-safe
     * Charge via joblib puis fallback pickle si échec
     * Valide et charge : feature_scaler, lgbm_base, calibrated_lgbm, lgbm_uncertainty
     * Synchronise paramètres LGBM avec configuration
     * Recrée modèle LSTM avec paramètres du fichier, adapte dimensions si nécessaire
     * Charge état Markov et synchronise paramètres
     * Restaure état session : sequence, prediction_history, weights, method_performance
     * Détecte incohérences hyperparamètres et propose résolution à l'utilisateur
     * Gestion exhaustive d'erreurs avec messages UI détaillés
   - RETOUR : bool - True si succès, False si erreur
   - UTILITÉ : Fonction centrale de restauration complète de l'état du prédicteur

40. save_optimization_report.txt (HybridBaccaratPredictor.save_optimization_report)
   - Lignes 1090-1119 dans hbp.py
   - FONCTION : Sauvegarde le rapport d'optimisation dans un fichier
   - PARAMÈTRES : report (str), study_name (str, optionnel)
   - FONCTIONNEMENT :
     * Crée le dossier "optimization_reports" si nécessaire
     * Génère timestamp pour nom de fichier unique
     * Construit nom : "optimization_report{_study_name}_{timestamp}.txt"
     * Sauvegarde le rapport en UTF-8
     * Gestion d'erreurs avec logging détaillé
   - RETOUR : str - chemin du fichier créé, None si erreur
   - UTILITÉ : Archive les rapports d'optimisation Optuna pour analyse ultérieure

================================================================================
SECTION 3 : CLASSEPRINCIPALE (1 MÉTHODE)
================================================================================

41. class_HybridBaccaratPredictor.txt (HybridBaccaratPredictor)
   - Lignes 541-14024 dans hbp.py (classe complète)
   - FONCTION : Définition complète de la classe principale HybridBaccaratPredictor
   - FONCTIONNEMENT :
     * Classe centrale orchestrant tout le système de prédiction ML hybride
     * Combine modèles Markov, LGBM et LSTM avec pondération bayésienne
     * Gère l'interface utilisateur Tkinter complète
     * Système d'entraînement asynchrone avec callbacks
     * Optimisation Optuna intégrée pour hyperparamètres
     * Gestion persistance (chargement/sauvegarde) des modèles
     * Calculs de confiance et d'incertitude avancés
     * Thread-safety avec verrous multiples
     * Métriques spécialisées pour recommandations consécutives
   - UTILITÉ : Classe principale intégrant tous les composants du système

================================================================================
SECTION 4 : ENTRAÎNEMENT (18 MÉTHODES)
================================================================================

42. run_full_retraining.txt (HybridBaccaratPredictor.run_full_retraining)
   - Lignes 11871-12024 dans hbp.py
   - FONCTION : Lance un entraînement complet de tous les modèles
   - FONCTIONNEMENT :
     * Vérifie qu'aucun entraînement n'est en cours
     * Valide la présence de données historiques
     * Demande confirmation utilisateur
     * Met à jour le modèle Markov global avec toutes les données historiques
     * Prépare les données d'entraînement (force l'utilisation de toutes les données)
     * Vérifie la présence de manches 31-60 (plage cible)
     * Lance ThreadedTrainer avec callbacks pour completion, erreur et progression
     * Gère l'interface utilisateur (désactivation contrôles, mise à jour progression)
   - RETOUR : None
   - UTILITÉ : Interface principale pour relancer complètement l'entraînement de tous les modèles

43. auto_fast_update_if_needed.txt (HybridBaccaratPredictor.auto_fast_update_if_needed)
   - Lignes 12561-12611 dans hbp.py
   - FONCTION : Déclenche une mise à jour rapide automatique si les conditions sont remplies
   - PARAMÈTRES : current_round_num (int)
   - FONCTIONNEMENT :
     * Vérifie que l'auto-update est activé
     * Contrôle si on est dans la plage de manches cibles (31-60)
     * Initialise le calculateur de confiance consécutive si nécessaire
     * Vérifie la fréquence de mise à jour (minimum 20 tours)
     * Lance _run_fast_update_async dans un thread séparé si conditions remplies
     * Gestion des verrous pour éviter les conflits avec l'entraînement
   - RETOUR : None
   - UTILITÉ : Automatise les mises à jour rapides des modèles pendant le jeu

44. _prepare_training_data.txt (HybridBaccaratPredictor._prepare_training_data)
   - Lignes 4137-4269 dans hbp.py
   - FONCTION : Prépare les données d'entraînement pour les modèles ML
   - PARAMÈTRES : force_use_historical, max_games, sampling_fraction
   - FONCTIONNEMENT :
     * Lit la configuration (min_target_hand_index, lstm_sequence_length, feature counts)
     * Applique l'échantillonnage des données avec _apply_data_sampling
     * Utilise BaccaratSequenceManager pour générer les features
     * Calcule les poids d'échantillons et effectue le split temporal
     * Valide les shapes des données finales
     * Retourne un tuple complet avec toutes les données préparées
   - RETOUR : Tuple[arrays LGBM/LSTM, indices train/val, sequences, origins]
   - UTILITÉ : Pipeline central de préparation des données pour l'entraînement ML

45. _apply_data_sampling.txt (HybridBaccaratPredictor._apply_data_sampling)
   - Lignes 4271-4303 dans hbp.py
   - FONCTION : Extrait la logique d'échantillonnage des données dans une fonction séparée
   - PARAMÈTRES : original_historical_data, max_games, sampling_fraction
   - FONCTIONNEMENT :
     * MODIFIÉ : Désactive l'échantillonnage pour garantir toutes les manches 31-60
     * Force l'utilisation de toutes les données historiques disponibles
     * Ignore les paramètres max_games et sampling_fraction
     * Code original commenté pour référence (échantillonnage par max_games ou fraction)
     * Génère message informatif sur l'utilisation complète des données
   - RETOUR : Tuple[List[List[str]], str] - (données à traiter, info échantillonnage)
   - UTILITÉ : Assure que toutes les manches cibles sont disponibles pour l'entraînement

46. _calculate_sample_weights.txt (HybridBaccaratPredictor._calculate_sample_weights)
   - Lignes 4305-4329 dans hbp.py
   - FONCTION : Extrait le calcul des poids d'échantillons dans une fonction séparée
   - PARAMÈTRES : list_of_all_origins (List[int]), final_num_samples (int)
   - FONCTIONNEMENT :
     * Initialise tous les poids à 1.0
     * Applique facteur de décroissance temporelle (sample_weight_decay_factor, défaut 0.9995)
     * Calcule time_lag = max_origin_index - sample_origin_index pour chaque échantillon
     * Applique poids = decay_factor^time_lag (plus récent = poids plus élevé)
     * Utilise epsilon_decay (1e-8) pour éviter poids nuls
     * Normalise les poids finaux par leur moyenne
     * Log des statistiques finales (Min, Max, Mean)
   - RETOUR : np.ndarray - poids d'échantillons normalisés
   - UTILITÉ : Donne plus d'importance aux données récentes dans l'entraînement

47. _finalize_fast_update.txt (HybridBaccaratPredictor._finalize_fast_update)
   - Lignes 3078-3137 dans hbp.py
   - FONCTION : Finalise la mise à jour rapide dans le thread UI
   - PARAMÈTRES : success (bool), start_time (float), summary (List[str]), is_auto_trigger (bool)
   - FONCTIONNEMENT :
     * Calcule temps total d'exécution
     * Distingue déclenchement AUTO vs MANUAL pour logs et UI
     * Réactive les contrôles d'entraînement
     * Si succès : affiche message popup seulement si déclenchement MANUEL
     * MODIFIÉ : Supprime la sauvegarde automatique après mise à jour rapide
     * Si échec : affiche erreur seulement si déclenchement MANUEL
     * Nettoyage mémoire (gc.collect, torch.cuda.empty_cache)
   - RETOUR : None
   - UTILITÉ : Gestion différenciée de la finalisation selon le type de déclenchement

48. _get_cumulative_new_data.txt (HybridBaccaratPredictor._get_cumulative_new_data)
   - Lignes 2772-2910 dans hbp.py (138 lignes)
   - FONCTION : Extrait les données cumulatives pour entraînement incrémental
   - FONCTIONNEMENT :
     * Identifie nouvelles parties dans historical_data depuis dernier reset
     * Ajoute la session actuelle aux nouvelles données
     * Vérifie seuils minimaux (lstm_sequence_length, min_rounds_for_feature_gen)
     * Prépare contexte historique pour premières features (correction indentation)
     * Génère features sur séquence combinée (contexte + nouvelles données)
     * Valide cohérence nombre de features LGBM et shape LSTM (6 features attendues)
     * Conversion NumPy avec vérifications de shapes finales
     * Gestion exhaustive d'erreurs (ValueError, Exception générale)
   - RETOUR : Tuple[Optional[np.ndarray], ...] - (X_lgbm, y_lgbm, X_lstm, y_lstm) ou None
   - UTILITÉ : Prépare données pour entraînement incrémental sans redondance

49. _get_historical_data_for_refit.txt (HybridBaccaratPredictor._get_historical_data_for_refit)
   - Lignes 4078-4135 dans hbp.py
   - FONCTION : Prépare TOUTES les données historiques pour le refit des wrappers LGBM
   - FONCTIONNEMENT :
     * Vérifie disponibilité des données historiques avec verrou sequence_lock
     * Itère sur toutes les parties historiques (copie pour thread-safety)
     * Ignore parties trop courtes (< lstm_sequence_length)
     * Génère features LGBM via create_hybrid_features (ignore partie LSTM)
     * Valide cohérence nombre de features avec self.feature_names
     * Conversion NumPy avec gestion d'erreurs
     * Log détaillé du nombre de rounds extraits
   - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray]] - (X_lgbm, y_lgbm)
   - UTILITÉ : Fournit dataset complet pour refit des modèles LGBM calibrés

50. _get_recent_session_data.txt (HybridBaccaratPredictor._get_recent_session_data)
   - Lignes 2984-3076 dans hbp.py (92 lignes)
   - FONCTION : Extrait les données récentes de session pour mise à jour rapide
   - PARAMÈTRES : min_rounds_for_update (int, défaut 10) - seuil minimum nouvelles manches
   - FONCTIONNEMENT :
     * Calcule nouvelles manches depuis last_incremental_update_index
     * Vérifie seuil minimum (min_rounds_for_update)
     * Extrait sous-séquence avec historique suffisant pour premières features
     * Génère features pour nouvelles manches via create_hybrid_features
     * Valide features LGBM (longueur) et LSTM (shape attendue)
     * Conversion NumPy avec gestion d'erreurs détaillée
     * Log des problèmes de génération de features
   - RETOUR : Tuple[Optional[np.ndarray], ...] - (X_lgbm, y_lgbm, X_lstm, y_lstm)
   - UTILITÉ : Fournit données pour mise à jour rapide incrémentale des modèles

51. _run_fast_update_async.txt (HybridBaccaratPredictor._run_fast_update_async)
   - Lignes 12488-12559 dans hbp.py
   - FONCTION : Exécute la mise à jour rapide des modèles de manière asynchrone
   - PARAMÈTRES : save_after_update (bool), is_auto_trigger (bool)
   - FONCTIONNEMENT :
     * Vérifie longueur séquence minimale (10 coups)
     * Met à jour last_incremental_update_index avec longueur actuelle
     * Mise à jour modèle Markov de session avec verrou markov_lock
     * Simulation mise à jour autres modèles (LGBM, LSTM)
     * Gestion UI avec progress updates si disponible
     * Réinitialise is_fast_updating dans finally
     * Appelle _finalize_fast_update dans thread UI
   - RETOUR : None
   - UTILITÉ : Orchestration asynchrone de la mise à jour rapide avec gestion d'état

52. _train_models_async.txt (HybridBaccaratPredictor._train_models_async)
   - Lignes 5254-6714 dans hbp.py (1460 lignes)
   - FONCTION : Entraîne tous les modèles ML de manière asynchrone avec optimisations avancées
   - PARAMÈTRES : X_lgbm, y_lgbm, X_lstm, y_lstm, config_override (optionnel)
   - FONCTIONNEMENT DÉTAILLÉ :
     **PHASE 1 - SCALER :**
     * Initialise StandardScaler ou clone l'existant
     * Fit sur X_lgbm avec gestion d'erreurs (ValueError, NotFittedError)
     * Transform X_lgbm avec vérifications de cohérence
     * Mise à jour thread-safe avec model_lock
     **PHASE 2 - LGBM BASE :**
     * Configuration CPU : utilise tous cœurs logiques disponibles (psutil)
     * Extraction paramètres LGBM depuis config (n_estimators, learning_rate, etc.)
     * Calcul poids échantillons focalisés sur recommandations NON-WAIT consécutives
     * Fallback sur métriques confiance/incertitude si méthode principale indisponible
     * Train/validation split (80/20) avec stratification
     * Entraînement avec métriques isolées (isolated_consecutive_focused_metric)
     * Calcul métriques validation : confusion matrix, precision, recall, F1, AUC-ROC
     * Sauvegarde graphiques d'entraînement via _save_lgbm_training_plots
     **PHASE 3 - LSTM :**
     * Synchronisation dimensions lstm_hidden_dim/lstm_hidden_size
     * Création EnhancedLSTMModel avec optimisation mémoire
     * Configuration optimiseur AdamW avec learning_rate configuré
     * Scheduler ReduceLROnPlateau avec patience configurée
     * Calcul batch size dynamique basé sur 80% RAM disponible
     * Limites sécurité : max_batch_size, config_based_limit, dataset_based_limit
     * DataLoader optimisé : WeightedRandomSampler si poids LSTM disponibles
     * Configuration workers : dataloader_num_workers, persistent_workers
     * Boucle entraînement avec métriques cibles manches 31-60 :
       - Filtrage échantillons positions 31-60 pour objectifs spécialisés
       - Calcul objective1_metric (prédictions consécutives correctes)
       - Calcul objective2_metric (précision composite avec pénalité erreurs)
       - Early stopping composite : 30% val_loss + 70% métriques cibles
       - Rétropropagation spécialisée sur manches cibles
     * Sauvegarde meilleur modèle et graphiques LSTM
     **PHASE 4 - CALIBRATION :**
     * CalibratedClassifierCV avec méthode isotonic/sigmoid
     * Utilise ensemble validation séparé (30% des données)
     * Mise à jour thread-safe du modèle calibré
     **PHASE 5 - INCERTITUDE :**
     * BaggingClassifier avec estimateurs LGBM de base
     * Configuration parallélisme : n_jobs pour tous cœurs CPU
     * Paramètres configurables : bagging_n_estimators, max_samples, max_features
     * Bootstrap échantillons et features selon configuration
     **PHASE 6 - FINALISATION :**
     * Nettoyage cache LGBM si modèles mis à jour
     * Réinitialisation flags training (is_training, stop_training)
     * Statistiques complètes : durée, succès par phase, métriques confiance
     * Sauvegarde automatique état si succès
     * Signal fin entraînement via main_queue
     * Nettoyage mémoire PyTorch (cleanup_pytorch_memory)
     * Appel finalize_training pour réactivation auto-update
   - RETOUR : None (met à jour modèles internes)
   - UTILITÉ : Fonction centrale d'entraînement complet avec optimisations avancées pour objectifs spécialisés

53. _validate_data_shapes.txt (HybridBaccaratPredictor._validate_data_shapes)
   - Lignes 4351-4384 dans hbp.py
   - FONCTION : Valide la cohérence des shapes de tous les arrays de données d'entraînement
   - PARAMÈTRES : X_lgbm_all, y_labels_all, X_lstm_all, sample_weights_all, list_of_all_prefix_sequences, list_of_all_origins, final_num_samples
   - FONCTIONNEMENT :
     * Vérifie que tous les arrays ont la même dimension 0 (nombre d'échantillons)
     * Contrôle X_lgbm_all.shape[0] == final_num_samples
     * Contrôle y_labels_all.shape[0] == final_num_samples
     * Contrôle X_lstm_all.shape[0] == final_num_samples
     * Contrôle sample_weights_all.shape[0] == final_num_samples
     * Contrôle len(list_of_all_prefix_sequences) == final_num_samples
     * Contrôle len(list_of_all_origins) == final_num_samples
     * Accumule les messages d'erreur pour toutes les incohérences détectées
   - RETOUR : Tuple[bool, str] - (is_valid, message_erreur)
   - UTILITÉ : Validation de cohérence avant entraînement pour éviter erreurs dimensionnelles

54. finalize_training.txt (HybridBaccaratPredictor.finalize_training)
   - Lignes 12057-12126 dans hbp.py
   - FONCTION : Finalise le processus d'entraînement complet avec gestion UI et sauvegarde
   - PARAMÈTRES : success (bool), start_time (float), train_summary (List[str])
   - FONCTIONNEMENT :
     * Calcule durée totale d'entraînement
     * Si succès : construit message détaillé avec résumé des étapes
     * Tentative sauvegarde automatique via _save_state_to_models_dir()
     * Construction message final selon succès entraînement et sauvegarde
     * Nettoyage mémoire : gc.collect() et torch.cuda.empty_cache()
     * Opérations UI planifiées via root.after() pour thread-safety :
       - Réactivation contrôles d'entraînement (toggle_training_controls)
       - Mise à jour progression finale
       - Si succès : messagebox succès + reset 'soft' session
       - Si échec sauvegarde : warning spécifique
       - Si échec entraînement : messagebox erreur
     * Reset 'soft' automatique après entraînement réussi (efface session, garde modèles)
   - RETOUR : None
   - UTILITÉ : Orchestration complète de fin d'entraînement avec feedback utilisateur

55. on_training_complete.txt (on_training_complete - fonction locale)
   - Lignes 11971-11979 dans hbp.py
   - FONCTION : Callback local appelé quand l'entraînement est terminé avec succès
   - PARAMÈTRES : result (Dict) - résultat de l'entraînement avec 'message' et 'success'
   - FONCTIONNEMENT :
     * Log du message de fin d'entraînement
     * Vérifie disponibilité de finalize_training
     * Appelle finalize_training avec result['success'], start_time, et liste vide
     * Gestion d'erreurs robuste avec logging détaillé
   - RETOUR : None
   - UTILITÉ : Finalise proprement l'entraînement après succès du thread

56. on_training_error.txt (on_training_error - fonction locale)
   - Lignes 11982-11990 dans hbp.py
   - FONCTION : Callback local appelé en cas d'erreur pendant l'entraînement
   - PARAMÈTRES : error (Exception) - erreur survenue pendant l'entraînement
   - FONCTIONNEMENT :
     * Log de l'erreur avec exc_info=True pour stack trace complète
     * Vérifie disponibilité de finalize_training
     * Appelle finalize_training avec success=False, start_time (ou time.time() si indisponible), liste vide
     * Gestion d'erreurs robuste pour éviter erreurs en cascade
   - RETOUR : None
   - UTILITÉ : Finalise proprement l'entraînement après échec du thread

57. on_training_progress.txt (on_training_progress - fonction locale)
   - Lignes 11993-11997 dans hbp.py
   - FONCTION : Callback local pour mettre à jour la progression d'entraînement
   - PARAMÈTRES : progress (int), message (str)
   - FONCTIONNEMENT :
     * Vérifie disponibilité UI
     * Utilise root.after(0, lambda) pour planifier mise à jour dans thread UI principal
     * Évite appel direct _update_progress depuis thread d'entraînement
     * Assure thread-safety pour mises à jour UI
   - RETOUR : None
   - UTILITÉ : Interface thread-safe pour progression depuis thread d'entraînement

58. stop_training_process.txt (HybridBaccaratPredictor.stop_training_process)
   - Lignes 12026-12055 dans hbp.py
   - FONCTION : Demande l'arrêt du processus d'entraînement ou d'optimisation en cours
   - FONCTIONNEMENT :
     * Vérifie si entraînement en cours, sinon affiche info et retourne
     * Évite demandes multiples si stop_training déjà activé
     * **GESTION OPTUNA :** vérifie optuna_thread_manager.is_optimization_running()
       - Si actif : appelle optuna_thread_manager.request_stop()
     * **GESTION ENTRAÎNEMENT :** vérifie threaded_trainer.is_training_running()
       - Si actif : appelle threaded_trainer.stop()
     * Met à jour flag stop_training=True avec training_lock
     * Affiche messagebox informant que l'arrêt se fera à la prochaine étape possible
     * Logging détaillé de toutes les étapes d'arrêt
   - RETOUR : None
   - UTILITÉ : Interface utilisateur pour arrêt propre des processus ML longs

59. uncertainty_weighted_loss.txt (HybridBaccaratPredictor.uncertainty_weighted_loss)
   - Lignes 10172-10276 dans hbp.py (104 lignes)
   - FONCTION : Fonction de perte personnalisée pondérée par incertitude et positions de séquence
   - PARAMÈTRES : outputs (torch.Tensor), targets (torch.Tensor), weights (optionnel), sequence_positions (optionnel)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **BASE :** CrossEntropyLoss avec reduction='none' (système 0-based : 0=Player, 1=Banker)
     * **VALIDATION :** vérifie indices targets dans [0,1] avec warning si invalides
     * **POIDS ÉCHANTILLONS :** applique weights si fournis (reshape si nécessaire)
     * **PONDÉRATION POSITIONNELLE :**
       - Convertit positions 0-indexées en 1-indexées (manches)
       - Masque manches cibles (target_round_min à target_round_max, défaut 31-60)
       - Poids progressif : 1.0 + normalized_position * (late_game_weight_factor - 1.0)
       - Facteur progressif augmente vers manche 60
     * **PÉNALITÉ ERREURS CONSÉCUTIVES :**
       - Détecte erreurs consécutives dans manches cibles uniquement
       - Pénalité : 1.0 + consecutive_count * 0.5 (50% par erreur consécutive)
       - Reset compteur à 0 après prédiction correcte
     * **COMBINAISON :** weights * position_factor * consecutive_penalty
     * **RETOUR :** moyenne des pertes pondérées
   - RETOUR : torch.Tensor - valeur de perte pondérée moyenne
   - UTILITÉ : Optimise spécifiquement pour manches cibles avec pénalisation erreurs consécutives

================================================================================
SECTION 5 : GESTIONRESSOURCES (7 MÉTHODES)
================================================================================

60. __init__.txt (HybridBaccaratPredictor.__init__)
   - Lignes 541-779 dans hbp.py
   - FONCTION : Initialise la classe principale HybridBaccaratPredictor
   - PARAMÈTRES : root_or_config (objet Tkinter root ou PredictorConfig)
   - FONCTIONNEMENT :
     * Optimise la mémoire PyTorch et initialise le logger
     * Détermine le mode (UI normale ou Optuna) selon le paramètre
     * Initialise tous les attributs d'état (sequence, prediction_history, etc.)
     * Configure les modèles ML (placeholders LGBM, LSTM, Markov)
     * Crée les verrous threading pour la sécurité multi-thread
     * Configure les variables UI et device (CPU/GPU)
     * Initialise l'interface utilisateur si en mode UI
     * Charge automatiquement le dernier état sauvegardé
     * Gestion complète des erreurs avec messages informatifs
   - RETOUR : None (constructeur)
   - UTILITÉ : Point d'entrée principal pour initialiser complètement le système de prédiction

61. reset_data.txt (HybridBaccaratPredictor.reset_data)
   - Lignes 4714-4829 dans hbp.py
   - FONCTION : Réinitialise les données de session ('soft') ou l'état complet ('hard')
   - PARAMÈTRES : reset_type ('soft'/'hard'), confirm (bool)
   - FONCTIONNEMENT :
     * Demande confirmation utilisateur si nécessaire
     * Acquiert tous les verrous pour garantir la cohérence
     * Reset 'soft' : vide sequence, prediction_history, remet index à 0
     * Reset 'hard' : + vide données historiques, réinitialise modèles ML
     * Gère le cache LGBM selon le type de reset
     * Réinitialise les poids et performances
     * Met à jour l'affichage UI depuis le thread principal
     * Libération sécurisée des verrous en finally
   - RETOUR : None
   - UTILITÉ : Permet de remettre à zéro le système partiellement ou complètement

62. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt)
   - Lignes 12746-12806 dans hbp.py
   - FONCTION : Ajoute la séquence de session actuelle au fichier historical_data.txt
   - PARAMÈTRES : filepath (str, défaut "historical_data.txt")
   - FONCTIONNEMENT :
     * Copie thread-safe de la séquence actuelle avec sequence_lock
     * Vérifie si la session n'est pas vide
     * Convertit outcomes en format 0/1 (player→'0', banker→'1')
     * Ouvre fichier en mode 'a+' avec encoding UTF-8
     * Vérifie si fichier non vide et lit dernier caractère
     * Ajoute newline si nécessaire pour éviter lignes collées
     * Écrit la séquence convertie au format CSV
     * Gestion robuste des erreurs (IOError, OSError, Exception)
   - RETOUR : bool - True si succès ou session vide, False si erreur
   - UTILITÉ : Sauvegarde persistante des sessions pour enrichir l'historique d'entraînement

63. apply_resource_config.txt (HybridBaccaratPredictor.apply_resource_config)
   - Lignes 4592-4649 dans hbp.py
   - FONCTION : Applique et valide la configuration des ressources système (CPU, RAM, GPU)
   - PARAMÈTRES : show_confirmation (bool, défaut True)
   - FONCTIONNEMENT :
     * Récupère configuration cibles : CPU cores et RAM depuis UI ou config par défaut
     * Validation CPU : limite aux cœurs logiques disponibles (psutil.cpu_count)
     * Validation RAM : vérifie contre RAM système totale avec warning si dépassement
     * Configuration device : GPU si disponible et sélectionné, sinon CPU
     * Synchronisation radio buttons UI pour cohérence GPU/CPU
     * Mise à jour config.default_device selon sélection finale
     * Sauvegarde valeurs validées dans config (default_cpu_cores, default_max_memory_gb)
     * Appel _update_dependent_configs() pour propager changements
     * Mise à jour affichage UI via root.after() pour thread-safety
     * Gestion d'erreurs avec messagebox si UI disponible
   - RETOUR : None
   - UTILITÉ : Centralise la validation et application des paramètres de ressources système

64. filter_none_values.txt (filter_none_values - fonction locale)
   - Lignes 2445-2448 dans hbp.py
   - FONCTION : Fonction utilitaire locale pour filtrer récursivement les valeurs None
   - PARAMÈTRES : d (Any) - structure de données à filtrer
   - FONCTIONNEMENT :
     * Si d est un dictionnaire : filtre récursivement les clés avec valeurs non-None
     * Applique filter_none_values récursivement sur chaque valeur
     * Sinon : retourne la valeur telle quelle
   - RETOUR : structure filtrée sans valeurs None
   - UTILITÉ : Nettoie les dictionnaires avant sérialisation JSON (évite valeurs null)

65. replace_value.txt (replace_value - fonction locale)
   - Lignes 2554-2555 dans hbp.py
   - FONCTION : Fonction locale de remplacement pour regex dans formatage de rapport
   - PARAMÈTRES : match (re.Match) - objet match de regex
   - FONCTIONNEMENT :
     * Retourne match.group(1) + formatted_value
     * Utilisée dans re.sub pour remplacer des patterns dans rapports
   - RETOUR : str - chaîne de remplacement formatée
   - UTILITÉ : Fonction helper pour formatage dynamique de rapports d'optimisation

66. replace_weights.txt (replace_weights - fonction locale)
   - Lignes 2527-2528 dans hbp.py
   - FONCTION : Fonction locale pour remplacer les poids dans les rapports d'optimisation
   - PARAMÈTRES : match (re.Match) - objet match de regex
   - FONCTIONNEMENT :
     * Retourne match.group(1) + str(weights_dict).replace("'", "'")
     * Convertit dictionnaire de poids en chaîne avec apostrophes normalisées
     * Utilisée dans re.sub pour injecter poids actuels dans templates de rapport
   - RETOUR : str - chaîne de remplacement avec poids formatés
   - UTILITÉ : Injection dynamique des poids dans rapports d'optimisation Optuna

================================================================================
SECTION 6 : INTERFACEGRAPHIQUE (84 MÉTHODES)
================================================================================

67. setup_ui.txt (HybridBaccaratPredictor.setup_ui)
   - Lignes 2587-2769 dans hbp.py
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter
   - FONCTIONNEMENT :
     * Configure le style TTK selon la plateforme (vista/aqua/clam)
     * Définit les couleurs fixes pour Matplotlib et récupère les couleurs TTK
     * Crée la structure principale (main_frame, left_frame, right_frame)
     * Panneau droit : boutons gestion modèles, entraînement, configuration ressources
     * Panneau gauche : boutons Player/Banker/Undo, prédictions temps réel
     * Configure la jauge de progression et les graphiques matplotlib
     * Crée les frames pour statistiques avec labels liés aux variables
     * Applique les styles et couleurs cohérents
     * Gère l'affichage/masquage des éléments graphiques
   - RETOUR : None
   - UTILITÉ : Interface principale complète pour interagir avec le système de prédiction

68. update_display.txt (HybridBaccaratPredictor.update_display)
   - Lignes 11454-11527 dans hbp.py
   - FONCTION : Met à jour tous les éléments de l'interface utilisateur
   - FONCTIONNEMENT :
     * Met à jour les labels de prédiction (round, player%, banker%, recommandation)
     * Calcule la confiance d'affichage ajustée (formule : 0.5 + (raw-0.5)/2)
     * Vérifie si on est dans la plage de manches cibles (31-60)
     * Détermine le niveau de confiance selon l'état des modèles
     * Affiche "N/A" pour manches 1-30, "Faible (non entraîné)" si modèles non prêts
     * Appelle update_statistics() pour les stats avancées
     * Redessine le graphique si visible avec draw_trend_chart()
   - RETOUR : None
   - UTILITÉ : Point central de mise à jour de l'affichage temps réel

69. _setup_ui_variables.txt (HybridBaccaratPredictor._setup_ui_variables)
   - Lignes 2021-2058 dans hbp.py
   - FONCTION : Initialise toutes les variables Tkinter nécessaires pour l'interface utilisateur
   - FONCTIONNEMENT :
     * Variables prédictions (pred_vars) : round, player%, banker%, confidence, recommendation
     * Variables statistiques (stats_vars) : streak, accuracy, model_weights, uncertainty, method_acc, game_stats
     * Variables ressources système :
       - use_cpu/use_gpu : BooleanVar selon device actuel
       - cpu_cores : IntVar validé contre cœurs logiques disponibles
       - max_mem : IntVar pour RAM guideline avec validation système
     * Détection automatique ressources via psutil (CPU cores, RAM totale)
     * Validation et warnings si configuration dépasse ressources système
     * Variables contrôle : auto_update_enabled, progress_var, progress_label_var
     * Logging détaillé des valeurs initiales pour debug
   - RETOUR : None (initialise attributs self)
   - UTILITÉ : Centralise l'initialisation de toutes les variables UI avec validation ressources

70. _update_progress.txt (HybridBaccaratPredictor._update_progress)
   - Lignes 4680-4709 dans hbp.py
   - FONCTION : Met à jour la barre de progression de manière thread-safe
   - PARAMÈTRES : value (int), message (str)
   - FONCTIONNEMENT :
     * Fonction interne update_ui() pour exécution dans thread principal
     * Validation valeur : clampée entre 0 et 100
     * Mise à jour progress_var et progress_label_var si disponibles
     * Troncature messages longs (>100 caractères) avec "..."
     * Appel root.update_idletasks() pour rafraîchissement UI
     * Détection automatique thread : appel direct si thread principal
     * Sinon utilise root.after(0, update_ui) pour planification thread-safe
     * Gestion TclError (appel depuis autre thread) et exceptions générales
     * Logging erreurs sans exc_info pour éviter spam logs
   - RETOUR : None
   - UTILITÉ : Interface thread-safe pour mise à jour progression depuis n'importe quel thread

71. _create_metrics_dashboard.txt (HybridBaccaratPredictor._create_metrics_dashboard)
   - Lignes 6955-7003 dans hbp.py
   - FONCTION : Crée une fenêtre tableau de bord complète pour visualiser les métriques d'entraînement
   - FONCTIONNEMENT :
     * Vérifie disponibilité UI avant création
     * Crée fenêtre Toplevel (800x600, min 600x400) avec titre descriptif
     * Structure en onglets via ttk.Notebook :
       - Onglet "LGBM" : métriques spécifiques LGBM
       - Onglet "LSTM" : métriques spécifiques LSTM
       - Onglet "Combiné" : métriques ensemble/hybrides
       - Onglet "Graphiques" : visualisations matplotlib
     * Appelle méthodes spécialisées pour remplir chaque onglet :
       - _fill_lgbm_metrics_tab()
       - _fill_lstm_metrics_tab()
       - _fill_combined_metrics_tab()
       - _fill_plots_tab()
     * Bouton "Rafraîchir les Métriques" avec callback _refresh_metrics_dashboard()
   - RETOUR : None (crée fenêtre modale)
   - UTILITÉ : Interface complète pour monitoring détaillé des performances d'entraînement

72. draw_trend_chart.txt (HybridBaccaratPredictor.draw_trend_chart)
   - Lignes 11742-11826 dans hbp.py
   - FONCTION : Dessine le graphique des tendances des probabilités Player/Banker en temps réel
   - FONCTIONNEMENT :
     * Vérifie disponibilité éléments UI (ax, canvas) et couleurs MPL initialisées
     * Utilise couleurs FIXES MPL stockées (bg_color_mpl, fg_color_mpl)
     * Clear et configure fond du graphique avec couleur de fond fixe
     * Accès thread-safe à prediction_history avec sequence_lock
     * Si <2 points : affiche message "Pas assez de données" avec styling complet
     * Affiche les N derniers points (max 50) pour performance
     * Extrait données : player_probs, banker_probs, confidence (1-uncertainty)
     * Dessine 4 courbes :
       - Player Prob. (bleu #0066CC, markers)
       - Banker Prob. (rouge #D83B01, markers)
       - Confiance (vert, pointillés)
       - Ligne 50% (gris, tirets)
     * Configuration axes : ylim(0,1), xlim dynamique, labels colorés
     * Styling complet : titre, ticks, spines avec couleurs cohérentes
     * Légende avec couleurs fixes et gestion d'erreurs
     * Grille légère avec transparence
     * Redessine via canvas.draw_idle() avec gestion d'erreurs
   - RETOUR : None
   - UTILITÉ : Visualisation temps réel des tendances de prédiction avec styling cohérent

73. update_statistics.txt (HybridBaccaratPredictor.update_statistics)
   - Lignes 11533-11664 dans hbp.py
   - FONCTION : Met à jour tous les labels de statistiques dans l'interface utilisateur
   - FONCTIONNEMENT :
     * Accès thread-safe aux données avec sequence_lock et model_lock
     * Calcule série actuelle : détecte outcome et longueur de streak consécutif
     * Précision session : compte recommandations NON-WAIT correctes vs totales
     * Précisions par méthode : affiche accuracy% pour chaque modèle (Markov, LGBM, LSTM)
     * Confiance méthodes : extrait confiance de la dernière prédiction par modèle
     * Statistiques partie : compte Player/Banker avec ratios pourcentage
     * Métriques incertitude détaillées : épistémique, aléatoire, sensibilité contextuelle
     * Seuil adaptatif : affiche seuil de décision dynamique actuel
     * Poids bayésiens : affiche pondération actuelle de chaque modèle
     * Adaptation manches cibles : affiche "N/A (manche 1-30)" si hors plage 31-60
     * Crée dynamiquement nouvelles variables UI si nécessaires
   - RETOUR : None (met à jour stats_vars)
   - UTILITÉ : Tableau de bord complet des performances en temps réel avec adaptation contextuelle

74. toggle_graph_visibility.txt (HybridBaccaratPredictor.toggle_graph_visibility)
   - Lignes 11828-11869 dans hbp.py
   - FONCTION : Bascule la visibilité du cadre contenant le graphique Matplotlib
   - FONCTIONNEMENT :
     * Vérifie existence des widgets nécessaires (graph_frame, toggle_graph_button)
     * Si graphique visible : pack_forget() et change texte bouton "Afficher Graphique"
     * Si graphique masqué :
       - Change texte bouton "Masquer Graphique"
       - Repack le frame AVANT le bouton stats (ordre UI cohérent)
       - Utilise before=anchor_widget pour positionnement précis
       - Fallback si toggle_stats_button non trouvé
       - Redessine graphique via draw_trend_chart() et canvas.draw_idle()
     * Met à jour flag self.graph_visible
     * Gestion d'erreurs pour redraw après réaffichage
   - RETOUR : None
   - UTILITÉ : Contrôle utilisateur pour optimiser espace écran et performances

75. toggle_stats_visibility.txt (HybridBaccaratPredictor.toggle_stats_visibility)
   - Lignes 12808-12834 dans hbp.py
   - FONCTION : Bascule la visibilité du cadre contenant les statistiques détaillées
   - UTILITÉ : Contrôle utilisateur pour optimiser espace écran selon besoins d'information

76. on_close.txt (HybridBaccaratPredictor.on_close)
   - Lignes 10897-10964 dans hbp.py
   - FONCTION : Gère la fermeture propre de l'application avec nettoyage complet
   - UTILITÉ : Fermeture propre avec protection contre perte de données et nettoyage ressources

77. show_models_dashboard.txt (HybridBaccaratPredictor.show_models_dashboard)
   - Lignes 3332-3490 dans hbp.py (158 lignes)
   - FONCTION : Affiche un tableau de bord complet des modèles entraînés avec métadonnées
   - UTILITÉ : Interface complète de gestion et inspection des modèles sauvegardés

78. show_optimization_results.txt (HybridBaccaratPredictor.show_optimization_results)
   - Lignes 1165-1384 dans hbp.py (219 lignes)
   - FONCTION : Affiche interface complète des résultats d'optimisation Optuna avec visualisations
   - UTILITÉ : Interface complète d'analyse des résultats d'optimisation avec focus objectif 1

79. lightweight_update_display.txt (HybridBaccaratPredictor.lightweight_update_display)
   - Lignes 11343-11452 dans hbp.py (109 lignes)
   - FONCTION : Met à jour uniquement les éléments essentiels de l'UI après chaque coup (thread-safe)
   - UTILITÉ : Mise à jour rapide UI avec adaptation contextuelle manches cibles

80. undo_last_move.txt (HybridBaccaratPredictor.undo_last_move)
   - Lignes 10780-10893 dans hbp.py (113 lignes)
   - FONCTION : Annule le dernier coup enregistré avec restauration complète de l'état
   - UTILITÉ : Fonction d'annulation complète avec cohérence totale de l'état système

[MÉTHODES 81-150 : Interface graphique détaillée - 70 méthodes supplémentaires]
Incluant : setup_config_panel, progress_callback, _fill_*_metrics_tab, _show_*_learning_curves,
_draw_*, toggle_*, update_*, cleanup_and_show_*, _setup_ui_variables_1, _update_weights_display, etc.

================================================================================
SECTION 7 : MODELESPREDICTION (19 MÉTHODES)
================================================================================

151. hybrid_prediction.txt (HybridBaccaratPredictor.hybrid_prediction)
   - Lignes 8795-9937 dans hbp.py
   - FONCTION : Effectue une prédiction hybride combinant Markov, LGBM et LSTM
   - UTILITÉ : Cœur du système de prédiction hybride avec logique de décision avancée

152. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features)
   - Lignes 5056-5105 dans hbp.py
   - FONCTION : Fonction centralisée pour créer les features hybrides (LGBM et LSTM)
   - UTILITÉ : Point d'entrée unique pour génération features avec fenêtre adaptative

153. evaluate_consecutive_focused.txt (HybridBaccaratPredictor.evaluate_consecutive_focused)
   - Lignes 367-539 dans hbp.py (172 lignes)
   - FONCTION : Évalue les modèles en simulant des séquences, focalisé sur l'objectif N°1
   - UTILITÉ : Évaluation spécialisée pour optimisation séquences consécutives manches cibles

154. get_weights.txt (HybridBaccaratPredictor.get_weights)
   - Lignes 8416-8424 dans hbp.py
   - FONCTION : Retourne les poids actuels des méthodes de prédiction de manière thread-safe
   - UTILITÉ : Interface thread-safe pour consultation des poids bayésiens

155. init_ml_models.txt (HybridBaccaratPredictor.init_ml_models)
   - Lignes 1589-1795 dans hbp.py (206 lignes)
   - FONCTION : Initialise/réinitialise tous les modèles ML avec configuration complète
   - UTILITÉ : Point d'entrée central pour initialisation complète système ML

156. safe_record_outcome.txt (HybridBaccaratPredictor.safe_record_outcome)
   - Lignes 12344-12486 dans hbp.py (142 lignes)
   - FONCTION : Enregistre résultat manche et déclenche prochaine prédiction (thread-safe)
   - UTILITÉ : Point d'entrée principal pour enregistrement coup avec pipeline complet

157. predict_with_lgbm.txt (HybridBaccaratPredictor.predict_with_lgbm)
   - Lignes 8298-8414 dans hbp.py (116 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LGBM calibré
   - UTILITÉ : Interface robuste pour prédictions LGBM avec gestion complète erreurs et phases

158. predict_with_lstm.txt (HybridBaccaratPredictor.predict_with_lstm)
   - Lignes 8113-8225 dans hbp.py (112 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LSTM avec optimisation latence
   - UTILITÉ : Interface optimisée pour prédictions LSTM avec cache et réduction latence

159. _get_cached_lgbm_prediction.txt (HybridBaccaratPredictor._get_cached_lgbm_prediction)
   - Lignes 8227-8296 dans hbp.py (69 lignes)
   - FONCTION : Récupère la prédiction LGBM depuis le cache ou la calcule si nécessaire
   - UTILITÉ : Cache optimisé pour prédictions LGBM avec recherche O(1) et gestion mémoire

[MÉTHODES 160-169 : Sous-dossiers LGBM, LSTM, Markov - 10 méthodes spécialisées]
Incluant : _extract_lgbm_features, create_lstm_sequence_features, handle_short_sequence,
analyze_context_sensitivity, initialize_lgbm_cache, etc.

================================================================================
RÉSUMÉ FINAL
================================================================================

TOTAL ANALYSÉ : 168 MÉTHODES RÉPARTIES EN 7 SECTIONS

Le système HybridBaccaratPredictor est un framework ML sophistiqué combinant :
- **3 modèles de prédiction** : Markov, LGBM, LSTM avec pondération bayésienne
- **Interface utilisateur complète** : Tkinter avec graphiques temps réel
- **Optimisation Optuna** : Hyperparamètres automatisés
- **Système d'entraînement avancé** : Asynchrone avec métriques spécialisées
- **Gestion persistance** : Chargement/sauvegarde modèles et états
- **Thread-safety** : Verrous multiples pour cohérence
- **Métriques spécialisées** : Focus sur recommandations consécutives manches 31-60

**OBJECTIF PRINCIPAL :** Maximiser les séquences de recommandations NON-WAIT valides consécutives
dans les manches cibles (31-60) avec calculs de confiance et d'incertitude avancés.

**ARCHITECTURE :** Système modulaire avec séparation claire des responsabilités,
gestion d'erreurs robuste, et optimisations performance pour usage temps réel.

================================================================================
FIN DU DESCRIPTIF GÉNÉRAL - 168 MÉTHODES DOCUMENTÉES
================================================================================