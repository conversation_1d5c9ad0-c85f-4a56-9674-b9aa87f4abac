DESCRIPTIF DES MÉTHODES - DOSSIER ENTRAÎNEMENT
==============================================

Ce dossier contient toutes les méthodes liées à l'entraînement des modèles, la préparation des données, la validation et les processus d'apprentissage.

MÉTHODES ANALYSÉES :

1. run_full_retraining.txt (HybridBaccaratPredictor.run_full_retraining)
   - Lignes 11871-12024 dans hbp.py
   - FONCTION : Lance un entraînement complet de tous les modèles
   - FONCTIONNEMENT :
     * Vérifie qu'aucun entraînement n'est en cours
     * Valide la présence de données historiques
     * Demande confirmation utilisateur
     * Met à jour le modèle Markov global avec toutes les données historiques
     * Prépare les données d'entraînement (force l'utilisation de toutes les données)
     * V<PERSON>rifie la présence de manches 31-60 (plage cible)
     * Lance ThreadedTrainer avec callbacks pour completion, erreur et progression
     * Gère l'interface utilisateur (désactivation contrôles, mise à jour progression)
   - RETOUR : None
   - UTILITÉ : Interface principale pour relancer complètement l'entraînement de tous les modèles

2. auto_fast_update_if_needed.txt (HybridBaccaratPredictor.auto_fast_update_if_needed)
   - Lignes 12561-12611 dans hbp.py
   - FONCTION : Déclenche une mise à jour rapide automatique si les conditions sont remplies
   - PARAMÈTRES : current_round_num (int)
   - FONCTIONNEMENT :
     * Vérifie que l'auto-update est activé
     * Contrôle si on est dans la plage de manches cibles (31-60)
     * Initialise le calculateur de confiance consécutive si nécessaire
     * Vérifie la fréquence de mise à jour (minimum 20 tours)
     * Lance _run_fast_update_async dans un thread séparé si conditions remplies
     * Gestion des verrous pour éviter les conflits avec l'entraînement
   - RETOUR : None
   - UTILITÉ : Automatise les mises à jour rapides des modèles pendant le jeu

3. _prepare_training_data.txt (HybridBaccaratPredictor._prepare_training_data)
   - Lignes 4137-4269 dans hbp.py
   - FONCTION : Prépare les données d'entraînement pour les modèles ML
   - PARAMÈTRES : force_use_historical, max_games, sampling_fraction
   - FONCTIONNEMENT :
     * Lit la configuration (min_target_hand_index, lstm_sequence_length, feature counts)
     * Applique l'échantillonnage des données avec _apply_data_sampling
     * Utilise BaccaratSequenceManager pour générer les features
     * Calcule les poids d'échantillons et effectue le split temporal
     * Valide les shapes des données finales
     * Retourne un tuple complet avec toutes les données préparées
   - RETOUR : Tuple[arrays LGBM/LSTM, indices train/val, sequences, origins]
   - UTILITÉ : Pipeline central de préparation des données pour l'entraînement ML

AUTRES MÉTHODES IMPORTANTES :

4. _apply_data_sampling.txt (HybridBaccaratPredictor._apply_data_sampling)
   - Lignes 4271-4303 dans hbp.py
   - FONCTION : Extrait la logique d'échantillonnage des données dans une fonction séparée
   - PARAMÈTRES : original_historical_data, max_games, sampling_fraction
   - FONCTIONNEMENT :
     * MODIFIÉ : Désactive l'échantillonnage pour garantir toutes les manches 31-60
     * Force l'utilisation de toutes les données historiques disponibles
     * Ignore les paramètres max_games et sampling_fraction
     * Code original commenté pour référence (échantillonnage par max_games ou fraction)
     * Génère message informatif sur l'utilisation complète des données
   - RETOUR : Tuple[List[List[str]], str] - (données à traiter, info échantillonnage)
   - UTILITÉ : Assure que toutes les manches cibles sont disponibles pour l'entraînement
5. _calculate_sample_weights.txt (HybridBaccaratPredictor._calculate_sample_weights)
   - Lignes 4305-4329 dans hbp.py
   - FONCTION : Extrait le calcul des poids d'échantillons dans une fonction séparée
   - PARAMÈTRES : list_of_all_origins (List[int]), final_num_samples (int)
   - FONCTIONNEMENT :
     * Initialise tous les poids à 1.0
     * Applique facteur de décroissance temporelle (sample_weight_decay_factor, défaut 0.9995)
     * Calcule time_lag = max_origin_index - sample_origin_index pour chaque échantillon
     * Applique poids = decay_factor^time_lag (plus récent = poids plus élevé)
     * Utilise epsilon_decay (1e-8) pour éviter poids nuls
     * Normalise les poids finaux par leur moyenne
     * Log des statistiques finales (Min, Max, Mean)
   - RETOUR : np.ndarray - poids d'échantillons normalisés
   - UTILITÉ : Donne plus d'importance aux données récentes dans l'entraînement
6. _finalize_fast_update.txt (HybridBaccaratPredictor._finalize_fast_update)
   - Lignes 3078-3137 dans hbp.py
   - FONCTION : Finalise la mise à jour rapide dans le thread UI
   - PARAMÈTRES : success (bool), start_time (float), summary (List[str]), is_auto_trigger (bool)
   - FONCTIONNEMENT :
     * Calcule temps total d'exécution
     * Distingue déclenchement AUTO vs MANUAL pour logs et UI
     * Réactive les contrôles d'entraînement
     * Si succès : affiche message popup seulement si déclenchement MANUEL
     * MODIFIÉ : Supprime la sauvegarde automatique après mise à jour rapide
     * Si échec : affiche erreur seulement si déclenchement MANUEL
     * Nettoyage mémoire (gc.collect, torch.cuda.empty_cache)
   - RETOUR : None
   - UTILITÉ : Gestion différenciée de la finalisation selon le type de déclenchement
7. _get_cumulative_new_data.txt (HybridBaccaratPredictor._get_cumulative_new_data)
   - Lignes 2772-2910 dans hbp.py (138 lignes)
   - FONCTION : Extrait les données cumulatives pour entraînement incrémental
   - FONCTIONNEMENT :
     * Identifie nouvelles parties dans historical_data depuis dernier reset
     * Ajoute la session actuelle aux nouvelles données
     * Vérifie seuils minimaux (lstm_sequence_length, min_rounds_for_feature_gen)
     * Prépare contexte historique pour premières features (correction indentation)
     * Génère features sur séquence combinée (contexte + nouvelles données)
     * Valide cohérence nombre de features LGBM et shape LSTM (6 features attendues)
     * Conversion NumPy avec vérifications de shapes finales
     * Gestion exhaustive d'erreurs (ValueError, Exception générale)
   - RETOUR : Tuple[Optional[np.ndarray], ...] - (X_lgbm, y_lgbm, X_lstm, y_lstm) ou None
   - UTILITÉ : Prépare données pour entraînement incrémental sans redondance
8. _get_historical_data_for_refit.txt (HybridBaccaratPredictor._get_historical_data_for_refit)
   - Lignes 4078-4135 dans hbp.py
   - FONCTION : Prépare TOUTES les données historiques pour le refit des wrappers LGBM
   - FONCTIONNEMENT :
     * Vérifie disponibilité des données historiques avec verrou sequence_lock
     * Itère sur toutes les parties historiques (copie pour thread-safety)
     * Ignore parties trop courtes (< lstm_sequence_length)
     * Génère features LGBM via create_hybrid_features (ignore partie LSTM)
     * Valide cohérence nombre de features avec self.feature_names
     * Conversion NumPy avec gestion d'erreurs
     * Log détaillé du nombre de rounds extraits
   - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray]] - (X_lgbm, y_lgbm)
   - UTILITÉ : Fournit dataset complet pour refit des modèles LGBM calibrés
9. _get_recent_session_data.txt (HybridBaccaratPredictor._get_recent_session_data)
   - Lignes 2984-3076 dans hbp.py (92 lignes)
   - FONCTION : Extrait les données récentes de session pour mise à jour rapide
   - PARAMÈTRES : min_rounds_for_update (int, défaut 10) - seuil minimum nouvelles manches
   - FONCTIONNEMENT :
     * Calcule nouvelles manches depuis last_incremental_update_index
     * Vérifie seuil minimum (min_rounds_for_update)
     * Extrait sous-séquence avec historique suffisant pour premières features
     * Génère features pour nouvelles manches via create_hybrid_features
     * Valide features LGBM (longueur) et LSTM (shape attendue)
     * Conversion NumPy avec gestion d'erreurs détaillée
     * Log des problèmes de génération de features
   - RETOUR : Tuple[Optional[np.ndarray], ...] - (X_lgbm, y_lgbm, X_lstm, y_lstm)
   - UTILITÉ : Fournit données pour mise à jour rapide incrémentale des modèles
10. _run_fast_update_async.txt (HybridBaccaratPredictor._run_fast_update_async)
   - Lignes 12488-12559 dans hbp.py
   - FONCTION : Exécute la mise à jour rapide des modèles de manière asynchrone
   - PARAMÈTRES : save_after_update (bool), is_auto_trigger (bool)
   - FONCTIONNEMENT :
     * Vérifie longueur séquence minimale (10 coups)
     * Met à jour last_incremental_update_index avec longueur actuelle
     * Mise à jour modèle Markov de session avec verrou markov_lock
     * Simulation mise à jour autres modèles (LGBM, LSTM)
     * Gestion UI avec progress updates si disponible
     * Réinitialise is_fast_updating dans finally
     * Appelle _finalize_fast_update dans thread UI
   - RETOUR : None
   - UTILITÉ : Orchestration asynchrone de la mise à jour rapide avec gestion d'état
11. _train_models_async.txt (HybridBaccaratPredictor._train_models_async)
   - Lignes 5254-6714 dans hbp.py (1460 lignes)
   - FONCTION : Entraîne tous les modèles ML de manière asynchrone avec optimisations avancées
   - PARAMÈTRES : X_lgbm, y_lgbm, X_lstm, y_lstm, config_override (optionnel)
   - FONCTIONNEMENT DÉTAILLÉ :

     **PHASE 1 - SCALER :**
     * Initialise StandardScaler ou clone l'existant
     * Fit sur X_lgbm avec gestion d'erreurs (ValueError, NotFittedError)
     * Transform X_lgbm avec vérifications de cohérence
     * Mise à jour thread-safe avec model_lock

     **PHASE 2 - LGBM BASE :**
     * Configuration CPU : utilise tous cœurs logiques disponibles (psutil)
     * Extraction paramètres LGBM depuis config (n_estimators, learning_rate, etc.)
     * Calcul poids échantillons focalisés sur recommandations NON-WAIT consécutives
     * Fallback sur métriques confiance/incertitude si méthode principale indisponible
     * Train/validation split (80/20) avec stratification
     * Entraînement avec métriques isolées (isolated_consecutive_focused_metric)
     * Calcul métriques validation : confusion matrix, precision, recall, F1, AUC-ROC
     * Sauvegarde graphiques d'entraînement via _save_lgbm_training_plots

     **PHASE 3 - LSTM :**
     * Synchronisation dimensions lstm_hidden_dim/lstm_hidden_size
     * Création EnhancedLSTMModel avec optimisation mémoire
     * Configuration optimiseur AdamW avec learning_rate configuré
     * Scheduler ReduceLROnPlateau avec patience configurée
     * Calcul batch size dynamique basé sur 80% RAM disponible
     * Limites sécurité : max_batch_size, config_based_limit, dataset_based_limit
     * DataLoader optimisé : WeightedRandomSampler si poids LSTM disponibles
     * Configuration workers : dataloader_num_workers, persistent_workers
     * Boucle entraînement avec métriques cibles manches 31-60 :
       - Filtrage échantillons positions 31-60 pour objectifs spécialisés
       - Calcul objective1_metric (prédictions consécutives correctes)
       - Calcul objective2_metric (précision composite avec pénalité erreurs)
       - Early stopping composite : 30% val_loss + 70% métriques cibles
       - Rétropropagation spécialisée sur manches cibles
     * Sauvegarde meilleur modèle et graphiques LSTM

     **PHASE 4 - CALIBRATION :**
     * CalibratedClassifierCV avec méthode isotonic/sigmoid
     * Utilise ensemble validation séparé (30% des données)
     * Mise à jour thread-safe du modèle calibré

     **PHASE 5 - INCERTITUDE :**
     * BaggingClassifier avec estimateurs LGBM de base
     * Configuration parallélisme : n_jobs pour tous cœurs CPU
     * Paramètres configurables : bagging_n_estimators, max_samples, max_features
     * Bootstrap échantillons et features selon configuration

     **PHASE 6 - FINALISATION :**
     * Nettoyage cache LGBM si modèles mis à jour
     * Réinitialisation flags training (is_training, stop_training)
     * Statistiques complètes : durée, succès par phase, métriques confiance
     * Sauvegarde automatique état si succès
     * Signal fin entraînement via main_queue
     * Nettoyage mémoire PyTorch (cleanup_pytorch_memory)
     * Appel finalize_training pour réactivation auto-update

   - RETOUR : None (met à jour modèles internes)
   - UTILITÉ : Fonction centrale d'entraînement complet avec optimisations avancées pour objectifs spécialisés
12. _validate_data_shapes.txt (HybridBaccaratPredictor._validate_data_shapes)
   - Lignes 4351-4384 dans hbp.py
   - FONCTION : Valide la cohérence des shapes de tous les arrays de données d'entraînement
   - PARAMÈTRES : X_lgbm_all, y_labels_all, X_lstm_all, sample_weights_all, list_of_all_prefix_sequences, list_of_all_origins, final_num_samples
   - FONCTIONNEMENT :
     * Vérifie que tous les arrays ont la même dimension 0 (nombre d'échantillons)
     * Contrôle X_lgbm_all.shape[0] == final_num_samples
     * Contrôle y_labels_all.shape[0] == final_num_samples
     * Contrôle X_lstm_all.shape[0] == final_num_samples
     * Contrôle sample_weights_all.shape[0] == final_num_samples
     * Contrôle len(list_of_all_prefix_sequences) == final_num_samples
     * Contrôle len(list_of_all_origins) == final_num_samples
     * Accumule les messages d'erreur pour toutes les incohérences détectées
   - RETOUR : Tuple[bool, str] - (is_valid, message_erreur)
   - UTILITÉ : Validation de cohérence avant entraînement pour éviter erreurs dimensionnelles
13. finalize_training.txt (HybridBaccaratPredictor.finalize_training)
   - Lignes 12057-12126 dans hbp.py
   - FONCTION : Finalise le processus d'entraînement complet avec gestion UI et sauvegarde
   - PARAMÈTRES : success (bool), start_time (float), train_summary (List[str])
   - FONCTIONNEMENT :
     * Calcule durée totale d'entraînement
     * Si succès : construit message détaillé avec résumé des étapes
     * Tentative sauvegarde automatique via _save_state_to_models_dir()
     * Construction message final selon succès entraînement et sauvegarde
     * Nettoyage mémoire : gc.collect() et torch.cuda.empty_cache()
     * Opérations UI planifiées via root.after() pour thread-safety :
       - Réactivation contrôles d'entraînement (toggle_training_controls)
       - Mise à jour progression finale
       - Si succès : messagebox succès + reset 'soft' session
       - Si échec sauvegarde : warning spécifique
       - Si échec entraînement : messagebox erreur
     * Reset 'soft' automatique après entraînement réussi (efface session, garde modèles)
   - RETOUR : None
   - UTILITÉ : Orchestration complète de fin d'entraînement avec feedback utilisateur
14. on_training_complete.txt (on_training_complete - fonction locale)
   - Lignes 11971-11979 dans hbp.py
   - FONCTION : Callback local appelé quand l'entraînement est terminé avec succès
   - PARAMÈTRES : result (Dict) - résultat de l'entraînement avec 'message' et 'success'
   - FONCTIONNEMENT :
     * Log du message de fin d'entraînement
     * Vérifie disponibilité de finalize_training
     * Appelle finalize_training avec result['success'], start_time, et liste vide
     * Gestion d'erreurs robuste avec logging détaillé
   - RETOUR : None
   - UTILITÉ : Finalise proprement l'entraînement après succès du thread
15. on_training_error.txt (on_training_error - fonction locale)
   - Lignes 11982-11990 dans hbp.py
   - FONCTION : Callback local appelé en cas d'erreur pendant l'entraînement
   - PARAMÈTRES : error (Exception) - erreur survenue pendant l'entraînement
   - FONCTIONNEMENT :
     * Log de l'erreur avec exc_info=True pour stack trace complète
     * Vérifie disponibilité de finalize_training
     * Appelle finalize_training avec success=False, start_time (ou time.time() si indisponible), liste vide
     * Gestion d'erreurs robuste pour éviter erreurs en cascade
   - RETOUR : None
   - UTILITÉ : Finalise proprement l'entraînement après échec du thread
16. on_training_progress.txt (on_training_progress - fonction locale)
   - Lignes 11993-11997 dans hbp.py
   - FONCTION : Callback local pour mettre à jour la progression d'entraînement
   - PARAMÈTRES : progress (int), message (str)
   - FONCTIONNEMENT :
     * Vérifie disponibilité UI
     * Utilise root.after(0, lambda) pour planifier mise à jour dans thread UI principal
     * Évite appel direct _update_progress depuis thread d'entraînement
     * Assure thread-safety pour mises à jour UI
   - RETOUR : None
   - UTILITÉ : Interface thread-safe pour progression depuis thread d'entraînement
17. stop_training_process.txt (HybridBaccaratPredictor.stop_training_process)
   - Lignes 12026-12055 dans hbp.py
   - FONCTION : Demande l'arrêt du processus d'entraînement ou d'optimisation en cours
   - FONCTIONNEMENT :
     * Vérifie si entraînement en cours, sinon affiche info et retourne
     * Évite demandes multiples si stop_training déjà activé
     * **GESTION OPTUNA :** vérifie optuna_thread_manager.is_optimization_running()
       - Si actif : appelle optuna_thread_manager.request_stop()
     * **GESTION ENTRAÎNEMENT :** vérifie threaded_trainer.is_training_running()
       - Si actif : appelle threaded_trainer.stop()
     * Met à jour flag stop_training=True avec training_lock
     * Affiche messagebox informant que l'arrêt se fera à la prochaine étape possible
     * Logging détaillé de toutes les étapes d'arrêt
   - RETOUR : None
   - UTILITÉ : Interface utilisateur pour arrêt propre des processus ML longs
18. uncertainty_weighted_loss.txt (HybridBaccaratPredictor.uncertainty_weighted_loss)
   - Lignes 10172-10276 dans hbp.py (104 lignes)
   - FONCTION : Fonction de perte personnalisée pondérée par incertitude et positions de séquence
   - PARAMÈTRES : outputs (torch.Tensor), targets (torch.Tensor), weights (optionnel), sequence_positions (optionnel)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **BASE :** CrossEntropyLoss avec reduction='none' (système 0-based : 0=Player, 1=Banker)
     * **VALIDATION :** vérifie indices targets dans [0,1] avec warning si invalides
     * **POIDS ÉCHANTILLONS :** applique weights si fournis (reshape si nécessaire)
     * **PONDÉRATION POSITIONNELLE :**
       - Convertit positions 0-indexées en 1-indexées (manches)
       - Masque manches cibles (target_round_min à target_round_max, défaut 31-60)
       - Poids progressif : 1.0 + normalized_position * (late_game_weight_factor - 1.0)
       - Facteur progressif augmente vers manche 60
     * **PÉNALITÉ ERREURS CONSÉCUTIVES :**
       - Détecte erreurs consécutives dans manches cibles uniquement
       - Pénalité : 1.0 + consecutive_count * 0.5 (50% par erreur consécutive)
       - Reset compteur à 0 après prédiction correcte
     * **COMBINAISON :** weights * position_factor * consecutive_penalty
     * **RETOUR :** moyenne des pertes pondérées
   - RETOUR : torch.Tensor - valeur de perte pondérée moyenne
   - UTILITÉ : Optimise spécifiquement pour manches cibles avec pénalisation erreurs consécutives

RÉSUMÉ : Ce dossier gère tous les aspects de l'entraînement des modèles ML, incluant la préparation des données, l'entraînement complet, les mises à jour rapides, la validation, et les callbacks. Il coordonne l'entraînement des modèles LGBM, LSTM et Markov avec gestion asynchrone et interface utilisateur.
