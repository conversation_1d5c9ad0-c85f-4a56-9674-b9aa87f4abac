# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2772 à 2910
# Type: Méthode de la classe HybridBaccaratPredictor

    def _get_cumulative_new_data(self) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Extrait les données (features et labels) pour LGBM et LSTM basées sur
        TOUTES les données ajoutées depuis le dernier démarrage ou reset complet.
        Ceci inclut les NOUVELLES parties dans historical_data et la session actuelle.
        """
        logger.info("Préparation des données CUMULATIVES pour entraînement incrémental.")
        min_hist_needed_for_features = self.config.lstm_sequence_length
        combined_new_sequence = []

        with self.sequence_lock:
            # 1. Identifier les NOUVELLES parties dans l'historique
            current_total_games_in_history = len(self.historical_data)
            start_index_new_history = self.historical_games_at_startup_or_reset # <- Utilise la variable membre
            num_new_games_in_history = current_total_games_in_history - start_index_new_history

            if num_new_games_in_history > 0:
                logger.info(f"Utilisation de {num_new_games_in_history} NOUVELLES parties de l'historique (indices {start_index_new_history} à {current_total_games_in_history - 1}).")
                for i in range(start_index_new_history, current_total_games_in_history):
                    combined_new_sequence.extend(self.historical_data[i])
            else:
                logger.info("Aucune NOUVELLE partie détectée dans l'historique depuis le dernier reset/démarrage.")

            # 2. Ajouter la session ACTUELLE (qui vient d'être sauvegardée mais pas encore reset)
            current_session_len = len(self.sequence)
            if current_session_len > 0:
                logger.info(f"Ajout des {current_session_len} coups de la session actuelle.")
                combined_new_sequence.extend(self.sequence)
            else:
                logger.info("Session actuelle vide (normal si appelée juste après reset?).")

            # 3. Vérifier si on a des données à traiter dans la partie CUMULATIVE
            total_new_rounds = len(combined_new_sequence)
            min_rounds_for_feature_gen = 5 # Un seuil minimal de nouveaux points à générer
            if total_new_rounds <= min_hist_needed_for_features or total_new_rounds < min_rounds_for_feature_gen:
                 logger.warning(f"Pas assez de NOUVELLES données cumulatives ({total_new_rounds}) pour générer des features. "
                                f"Minimum historique {min_hist_needed_for_features}, Minimum features à générer {min_rounds_for_feature_gen}. "
                                "Entraînement incrémental annulé.")
                 return None, None, None, None

            logger.info(f"Total de {total_new_rounds} NOUVEAUX coups cumulatifs à traiter pour l'entraînement incrémental.")

            # 4. Préparer le contexte historique global nécessaire pour les PREMIERES features
            context_sequence = []
            if start_index_new_history > 0: # Si il y avait un historique AVANT les nouvelles données
                context_needed_rounds = min_hist_needed_for_features + 5 # Marge
                temp_context = []
                # Itérer en arrière sur l'historique ANCIEN
                for i in range(start_index_new_history - 1, -1, -1):
                    game = self.historical_data[i]
                    temp_context.extend(reversed(game))
                    # ******** CORRECTION INDENTATION ********
                    if len(temp_context) >= context_needed_rounds:
                        break # Sortir de la boucle FOR i
                    # ******** FIN CORRECTION ********
                context_sequence = list(reversed(temp_context))[-context_needed_rounds:]
                logger.debug(f"Contexte historique pré-calculé depuis hist. ancien: {len(context_sequence)} coups.")
            elif self.historical_data : # Cas où start_index=0 mais historique existe
                context_needed_rounds = min_hist_needed_for_features + 5
                temp_context = [] # Réinitialiser temp_context ici
                for game in self.historical_data:
                    temp_context.extend(game)
                    # ******** CORRECTION INDENTATION ********
                    if len(temp_context) >= context_needed_rounds:
                        break # Sortir de la boucle FOR game
                    # ******** FIN CORRECTION ********
                context_sequence = temp_context[:context_needed_rounds]
                logger.debug(f"Contexte historique pré-calculé depuis début hist.: {len(context_sequence)} coups.")
            else: logger.warning("Pas d'historique pré-existant trouvé pour le contexte des premières features cumulatives.")


            # 5. Générer les features sur la séquence combinée (contexte + nouvelles données)
            full_sequence_for_gen = context_sequence + combined_new_sequence
            X_lgbm_new, y_lgbm_new = [], []
            X_lstm_new, y_lstm_new = [], []

            logger.info(f"Génération des features sur {len(full_sequence_for_gen)} coups (contexte + nouveau)...")
            start_gen_idx_in_full = len(context_sequence) # Début des NOUVELLES données dans full_sequence_for_gen

            lstm_expected_num_features = 12

            # Itérer sur les indices correspondant aux coups pour lesquels on veut une paire (feature, label)
            for i in range(start_gen_idx_in_full, len(full_sequence_for_gen)):
                 input_sub_sequence = full_sequence_for_gen[:i]
                 actual_outcome = full_sequence_for_gen[i]

                 # create_hybrid_features retourne maintenant 6 features LSTM
                 feat_lgbm, feat_lstm = self.create_hybrid_features(input_sub_sequence)

                 if feat_lgbm is not None and feat_lstm is not None:
                    # Vérifier la cohérence du nombre de features LGBM
                    if len(feat_lgbm) != len(self.feature_names):
                         logger.warning(f"Incohérence nbre features LGBM généré à l'indice {i}. Attendu {len(self.feature_names)}, Obtenu {len(feat_lgbm)}. Ignoré.")
                         continue

                    # Vérifier contre la taille 6
                    lstm_expected_shape = (self.config.lstm_sequence_length, lstm_expected_num_features)
                    if feat_lstm.shape != lstm_expected_shape:
                          logger.warning(f"Incohérence shape features LSTM généré à l'indice {i}. Attendu {lstm_expected_shape}, Obtenu {feat_lstm.shape}. Ignoré.")
                          continue

                    # Si features valides, ajouter
                    label = 1 if actual_outcome == 'banker' else 0
                    X_lgbm_new.append(feat_lgbm)
                    y_lgbm_new.append(label)
                    X_lstm_new.append(feat_lstm)
                    y_lstm_new.append(label)

            num_samples_generated = len(y_lgbm_new)
            logger.info(f"{num_samples_generated} paires (feature, label) CUMULATIVES générées.")

            if not X_lgbm_new:
                 logger.warning("Aucune feature valide n'a pu être générée pour l'entraînement incrémental (cumulatif).")
                 return None, None, None, None

        # 6. Conversion NumPy
        try:
            X_lgbm_np = np.array(X_lgbm_new, dtype=np.float32)
            y_lgbm_np = np.array(y_lgbm_new, dtype=np.int64)
            X_lstm_np = np.stack(X_lstm_new, axis=0).astype(np.float32) # Shape sera (N, seq_len, 6)
            y_lstm_np = np.array(y_lstm_new, dtype=np.int64)

            # Vérification finale shapes
            if X_lgbm_np.shape[0] != num_samples_generated or X_lstm_np.shape[0] != num_samples_generated or y_lgbm_np.shape[0] != num_samples_generated:
                raise ValueError(f"Incohérence de nombre d'échantillons après conversion NumPy (Générés={num_samples_generated}, LGBM={X_lgbm_np.shape[0]}, LSTM={X_lstm_np.shape[0]})")

            # Vérifier contre la taille 6
            expected_lstm_shape_part = (self.config.lstm_sequence_length, lstm_expected_num_features)
            if X_lstm_np.shape[1:] != expected_lstm_shape_part:
                 raise ValueError(f"Incohérence dans la shape cumulative finale des features LSTM. Attendu (samples, {expected_lstm_shape_part[0]}, {expected_lstm_shape_part[1]}), Obtenu {X_lstm_np.shape[1:]}.")

            logger.info(f"Données cumulatives préparées: LGBM {X_lgbm_np.shape}, LSTM {X_lstm_np.shape}")
            return X_lgbm_np, y_lgbm_np, X_lstm_np, y_lstm_np
        except ValueError as ve: # Erreur de stacking ou shape
             logger.error(f"Erreur Valeur lors conversion NumPy/Stack données cumulatives: {ve}", exc_info=True)
             return None, None, None, None
        except Exception as e:
            logger.error(f"Erreur inattendue lors conversion NumPy données cumulatives: {e}", exc_info=True)
            return None, None, None, None