# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 11971 à 11979
# Type: Méthode de la classe HybridBaccaratPredictor

        def on_training_complete(result):
            logger_instance.info(f"Entraînement terminé: {result['message']}")
            # Appeler finalize_training pour finaliser l'entraînement
            if hasattr(self, 'finalize_training'):
                try:
                    logger_instance.info("Appel de finalize_training pour finaliser l'entraînement")
                    self.finalize_training(result['success'], self.threaded_trainer.start_time, [])
                except Exception as e_finalize:
                    logger_instance.error(f"Erreur lors de l'appel à finalize_training: {e_finalize}", exc_info=True)