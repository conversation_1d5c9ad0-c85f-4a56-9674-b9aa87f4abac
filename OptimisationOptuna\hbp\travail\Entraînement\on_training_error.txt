# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 11982 à 11990
# Type: Méthode de la classe HybridBaccaratPredictor

        def on_training_error(error):
            logger_instance.error(f"Erreur lors de l'entraînement: {error}", exc_info=True)
            # Appeler finalize_training pour finaliser l'entraînement
            if hasattr(self, 'finalize_training'):
                try:
                    logger_instance.info("Appel de finalize_training pour finaliser l'entraînement (erreur)")
                    self.finalize_training(False, self.threaded_trainer.start_time if hasattr(self, 'threaded_trainer') else time.time(), [])
                except Exception as e_finalize:
                    logger_instance.error(f"Erreur lors de l'appel à finalize_training: {e_finalize}", exc_info=True)