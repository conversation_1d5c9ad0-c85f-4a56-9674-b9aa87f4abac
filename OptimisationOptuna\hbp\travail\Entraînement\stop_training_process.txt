# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 12026 à 12055
# Type: Méthode de la classe HybridBaccaratPredictor

    def stop_training_process(self) -> None:
        """Demande l'arrêt du processus d'entraînement en cours.
        Gère également l'arrêt de l'optimisation Optuna via OptunaThreadManager."""
        if not self.is_training:
            messagebox.showinfo("Information", "Aucun entraînement n'est en cours.")
            return

        if self.stop_training:
             logger.info("Demande d'arrêt déjà envoyée.")
             return

        # Vérifier si une optimisation Optuna est en cours via OptunaThreadManager
        if hasattr(self, 'optuna_thread_manager') and self.optuna_thread_manager:
            if self.optuna_thread_manager.is_optimization_running():
                logger.warning("Demande d'arrêt de l'optimisation Optuna via OptunaThreadManager...")
                self.optuna_thread_manager.request_stop()
                logger.warning("Demande d'arrêt envoyée à OptunaThreadManager.")

        # Vérifier si un entraînement est en cours via ThreadedTrainer
        if hasattr(self, 'threaded_trainer') and self.threaded_trainer:
            if self.threaded_trainer.is_training_running():
                logger.warning("Demande d'arrêt de l'entraînement via ThreadedTrainer...")
                self.threaded_trainer.stop()
                logger.warning("Demande d'arrêt envoyée à ThreadedTrainer.")

        logger.info("Demande d'arrêt de l'entraînement envoyée.")
        messagebox.showinfo("Arrêt Demandé", "Tentative d'arrêt de l'entraînement en cours...\n"
                                           "Le processus s'arrêtera à la prochaine étape possible.")
        with self.training_lock:
            self.stop_training = True # Mettre le flag pour arrêter le thread