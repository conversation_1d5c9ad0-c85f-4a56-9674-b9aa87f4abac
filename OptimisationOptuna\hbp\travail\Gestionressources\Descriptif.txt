DESCRIPTIF DES MÉTHODES - DOSSIER GESTIONRESSOURCES
==================================================

Ce dossier contient toutes les méthodes liées à la gestion des ressources système, l'initialisation, la configuration et les utilitaires généraux.

MÉTHODES ANALYSÉES :

1. __init__.txt (HybridBaccaratPredictor.__init__)
   - Lignes 541-779 dans hbp.py
   - FONCTION : Initialise la classe principale HybridBaccaratPredictor
   - PARAMÈTRES : root_or_config (objet Tkinter root ou PredictorConfig)
   - FONCTIONNEMENT :
     * Optimise la mémoire PyTorch et initialise le logger
     * Détermine le mode (UI normale ou Optuna) selon le paramètre
     * Initialise tous les attributs d'état (sequence, prediction_history, etc.)
     * Configure les modèles ML (placeholders LGBM, LSTM, Markov)
     * Crée les verrous threading pour la sécurité multi-thread
     * Configure les variables UI et device (CPU/GPU)
     * Initialise l'interface utilisateur si en mode UI
     * Charge automatiquement le dernier état sauvegardé
     * Gestion complète des erreurs avec messages informatifs
   - RETOUR : None (constructeur)
   - UTILITÉ : Point d'entrée principal pour initialiser complètement le système de prédiction

2. reset_data.txt (HybridBaccaratPredictor.reset_data)
   - Lignes 4714-4829 dans hbp.py
   - FONCTION : Réinitialise les données de session ('soft') ou l'état complet ('hard')
   - PARAMÈTRES : reset_type ('soft'/'hard'), confirm (bool)
   - FONCTIONNEMENT :
     * Demande confirmation utilisateur si nécessaire
     * Acquiert tous les verrous pour garantir la cohérence
     * Reset 'soft' : vide sequence, prediction_history, remet index à 0
     * Reset 'hard' : + vide données historiques, réinitialise modèles ML
     * Gère le cache LGBM selon le type de reset
     * Réinitialise les poids et performances
     * Met à jour l'affichage UI depuis le thread principal
     * Libération sécurisée des verrous en finally
   - RETOUR : None
   - UTILITÉ : Permet de remettre à zéro le système partiellement ou complètement

AUTRES MÉTHODES IMPORTANTES :

3. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt)
   - Lignes 12746-12806 dans hbp.py
   - FONCTION : Ajoute la séquence de session actuelle au fichier historical_data.txt
   - PARAMÈTRES : filepath (str, défaut "historical_data.txt")
   - FONCTIONNEMENT :
     * Copie thread-safe de la séquence actuelle avec sequence_lock
     * Vérifie si la session n'est pas vide
     * Convertit outcomes en format 0/1 (player→'0', banker→'1')
     * Ouvre fichier en mode 'a+' avec encoding UTF-8
     * Vérifie si fichier non vide et lit dernier caractère
     * Ajoute newline si nécessaire pour éviter lignes collées
     * Écrit la séquence convertie au format CSV
     * Gestion robuste des erreurs (IOError, OSError, Exception)
   - RETOUR : bool - True si succès ou session vide, False si erreur
   - UTILITÉ : Sauvegarde persistante des sessions pour enrichir l'historique d'entraînement
4. apply_resource_config.txt (HybridBaccaratPredictor.apply_resource_config)
   - Lignes 4592-4649 dans hbp.py
   - FONCTION : Applique et valide la configuration des ressources système (CPU, RAM, GPU)
   - PARAMÈTRES : show_confirmation (bool, défaut True)
   - FONCTIONNEMENT :
     * Récupère configuration cibles : CPU cores et RAM depuis UI ou config par défaut
     * Validation CPU : limite aux cœurs logiques disponibles (psutil.cpu_count)
     * Validation RAM : vérifie contre RAM système totale avec warning si dépassement
     * Configuration device : GPU si disponible et sélectionné, sinon CPU
     * Synchronisation radio buttons UI pour cohérence GPU/CPU
     * Mise à jour config.default_device selon sélection finale
     * Sauvegarde valeurs validées dans config (default_cpu_cores, default_max_memory_gb)
     * Appel _update_dependent_configs() pour propager changements
     * Mise à jour affichage UI via root.after() pour thread-safety
     * Gestion d'erreurs avec messagebox si UI disponible
   - RETOUR : None
   - UTILITÉ : Centralise la validation et application des paramètres de ressources système
5. filter_none_values.txt (filter_none_values - fonction locale)
   - Lignes 2445-2448 dans hbp.py
   - FONCTION : Fonction utilitaire locale pour filtrer récursivement les valeurs None
   - PARAMÈTRES : d (Any) - structure de données à filtrer
   - FONCTIONNEMENT :
     * Si d est un dictionnaire : filtre récursivement les clés avec valeurs non-None
     * Applique filter_none_values récursivement sur chaque valeur
     * Sinon : retourne la valeur telle quelle
   - RETOUR : structure filtrée sans valeurs None
   - UTILITÉ : Nettoie les dictionnaires avant sérialisation JSON (évite valeurs null)
6. replace_value.txt (replace_value - fonction locale)
   - Lignes 2554-2555 dans hbp.py
   - FONCTION : Fonction locale de remplacement pour regex dans formatage de rapport
   - PARAMÈTRES : match (re.Match) - objet match de regex
   - FONCTIONNEMENT :
     * Retourne match.group(1) + formatted_value
     * Utilisée dans re.sub pour remplacer des patterns dans rapports
   - RETOUR : str - chaîne de remplacement formatée
   - UTILITÉ : Fonction helper pour formatage dynamique de rapports d'optimisation
7. replace_weights.txt (replace_weights - fonction locale)
   - Lignes 2527-2528 dans hbp.py
   - FONCTION : Fonction locale pour remplacer les poids dans les rapports d'optimisation
   - PARAMÈTRES : match (re.Match) - objet match de regex
   - FONCTIONNEMENT :
     * Retourne match.group(1) + str(weights_dict).replace("'", "'")
     * Convertit dictionnaire de poids en chaîne avec apostrophes normalisées
     * Utilisée dans re.sub pour injecter poids actuels dans templates de rapport
   - RETOUR : str - chaîne de remplacement avec poids formatés
   - UTILITÉ : Injection dynamique des poids dans rapports d'optimisation Optuna

RÉSUMÉ : Ce dossier gère l'initialisation du système, la configuration des ressources, la réinitialisation des données, et les utilitaires généraux pour maintenir la cohérence et la stabilité du prédicteur.
