# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 4592 à 4649
# Type: Méthode de la classe HybridBaccaratPredictor

    def apply_resource_config(self, show_confirmation=True):
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        ui_available = self.is_ui_available()

        try:
            target_cores = 8
            target_ram_gb_ui = 28

            if ui_available:
                target_cores = self.cpu_cores.get()
                target_ram_gb_ui = self.max_mem.get()
            elif hasattr(self.config, 'default_cpu_cores'):
                 target_cores = self.config.default_cpu_cores
                 target_ram_gb_ui = self.config.default_max_memory_gb

            max_logical_cores = psutil.cpu_count(logical=True) if psutil else target_cores
            validated_cores = max(1, min(target_cores, max_logical_cores))
            if validated_cores != target_cores and ui_available:
                self.cpu_cores.set(validated_cores)
            self.target_cpu_cores = validated_cores

            total_sys_mem_gb = 4
            if psutil:
                try: total_sys_mem_gb = max(1, int(psutil.virtual_memory().total / (1024**3)))
                except Exception: pass

            validated_ram_gb = max(1, target_ram_gb_ui)


            if validated_ram_gb > total_sys_mem_gb:
                 logger_instance.warning(f"La Guideline RAM ({validated_ram_gb} Go) dépasse la RAM système détectée ({total_sys_mem_gb} Go). Utilisation de {validated_ram_gb} Go comme guideline.")


            self.target_max_ram_gb = validated_ram_gb

            use_gpu_flag = self.use_gpu.get() if ui_available else (self.config.default_device == 'cuda')
            use_cpu_flag = self.use_cpu.get() if ui_available else (self.config.default_device == 'cpu')
            if use_gpu_flag and torch.cuda.is_available() and not use_cpu_flag:
                self.device = torch.device("cuda")
            else:
                self.device = torch.device("cpu")
                if ui_available: # Assurer la cohérence des radio buttons
                    self.use_gpu.set(False)
                    self.use_cpu.set(True)
            self.config.default_device = self.device.type # Update config device based on selection

            if hasattr(self.config, 'default_cpu_cores'): self.config.default_cpu_cores = self.target_cpu_cores
            if hasattr(self.config, 'default_max_memory_gb'): self.config.default_max_memory_gb = self.target_max_ram_gb

            self._update_dependent_configs()

            if ui_available and hasattr(self, '_update_weight_display'):
                self.root.after(20, self._update_weight_display) # Donner une chance à l'UI de se rafraîchir

        except Exception as e:
            logger_instance.error(f"Erreur lors de l'application de la configuration des ressources: {e}", exc_info=True)
            if ui_available and show_confirmation:
                messagebox.showerror("Erreur Configuration", f"Impossible d'appliquer la configuration : {e}")