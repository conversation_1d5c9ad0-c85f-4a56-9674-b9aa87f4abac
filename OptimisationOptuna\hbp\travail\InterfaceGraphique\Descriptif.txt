DESCRIPTIF DES MÉTHODES - DOSSIER INTERFACEGRAPHIQUE
===================================================

Ce dossier contient toutes les méthodes liées à l'interface utilisateur, l'affichage, les interactions, les graphiques et la mise à jour de l'UI.

MÉTHODES ANALYSÉES :

1. setup_ui.txt (HybridBaccaratPredictor.setup_ui)
   - Lignes 2587-2769 dans hbp.py
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter
   - FONCTIONNEMENT :
     * Configure le style TTK selon la plateforme (vista/aqua/clam)
     * Définit les couleurs fixes pour Matplotlib et récupère les couleurs TTK
     * Crée la structure principale (main_frame, left_frame, right_frame)
     * Panneau droit : boutons gestion modèles, entraînement, configuration ressources
     * Panneau gauche : boutons Player/Banker/Undo, prédictions temps réel
     * Configure la jauge de progression et les graphiques matplotlib
     * Crée les frames pour statistiques avec labels liés aux variables
     * Applique les styles et couleurs cohérents
     * Gère l'affichage/masquage des éléments graphiques
   - RETOUR : None
   - UTILITÉ : Interface principale complète pour interagir avec le système de prédiction

2. update_display.txt (HybridBaccaratPredictor.update_display)
   - Lignes 11454-11527 dans hbp.py
   - FONCTION : Met à jour tous les éléments de l'interface utilisateur
   - FONCTIONNEMENT :
     * Met à jour les labels de prédiction (round, player%, banker%, recommandation)
     * Calcule la confiance d'affichage ajustée (formule : 0.5 + (raw-0.5)/2)
     * Vérifie si on est dans la plage de manches cibles (31-60)
     * Détermine le niveau de confiance selon l'état des modèles
     * Affiche "N/A" pour manches 1-30, "Faible (non entraîné)" si modèles non prêts
     * Appelle update_statistics() pour les stats avancées
     * Redessine le graphique si visible avec draw_trend_chart()
   - RETOUR : None
   - UTILITÉ : Point central de mise à jour de l'affichage temps réel

AUTRES MÉTHODES IMPORTANTES :

3. _setup_ui_variables.txt (HybridBaccaratPredictor._setup_ui_variables)
   - Lignes 2021-2058 dans hbp.py
   - FONCTION : Initialise toutes les variables Tkinter nécessaires pour l'interface utilisateur
   - FONCTIONNEMENT :
     * Variables prédictions (pred_vars) : round, player%, banker%, confidence, recommendation
     * Variables statistiques (stats_vars) : streak, accuracy, model_weights, uncertainty, method_acc, game_stats
     * Variables ressources système :
       - use_cpu/use_gpu : BooleanVar selon device actuel
       - cpu_cores : IntVar validé contre cœurs logiques disponibles
       - max_mem : IntVar pour RAM guideline avec validation système
     * Détection automatique ressources via psutil (CPU cores, RAM totale)
     * Validation et warnings si configuration dépasse ressources système
     * Variables contrôle : auto_update_enabled, progress_var, progress_label_var
     * Logging détaillé des valeurs initiales pour debug
   - RETOUR : None (initialise attributs self)
   - UTILITÉ : Centralise l'initialisation de toutes les variables UI avec validation ressources
4. _update_progress.txt (HybridBaccaratPredictor._update_progress)
   - Lignes 4680-4709 dans hbp.py
   - FONCTION : Met à jour la barre de progression de manière thread-safe
   - PARAMÈTRES : value (int), message (str)
   - FONCTIONNEMENT :
     * Fonction interne update_ui() pour exécution dans thread principal
     * Validation valeur : clampée entre 0 et 100
     * Mise à jour progress_var et progress_label_var si disponibles
     * Troncature messages longs (>100 caractères) avec "..."
     * Appel root.update_idletasks() pour rafraîchissement UI
     * Détection automatique thread : appel direct si thread principal
     * Sinon utilise root.after(0, update_ui) pour planification thread-safe
     * Gestion TclError (appel depuis autre thread) et exceptions générales
     * Logging erreurs sans exc_info pour éviter spam logs
   - RETOUR : None
   - UTILITÉ : Interface thread-safe pour mise à jour progression depuis n'importe quel thread
5. _create_metrics_dashboard.txt (HybridBaccaratPredictor._create_metrics_dashboard)
   - Lignes 6955-7003 dans hbp.py
   - FONCTION : Crée une fenêtre tableau de bord complète pour visualiser les métriques d'entraînement
   - FONCTIONNEMENT :
     * Vérifie disponibilité UI avant création
     * Crée fenêtre Toplevel (800x600, min 600x400) avec titre descriptif
     * Structure en onglets via ttk.Notebook :
       - Onglet "LGBM" : métriques spécifiques LGBM
       - Onglet "LSTM" : métriques spécifiques LSTM
       - Onglet "Combiné" : métriques ensemble/hybrides
       - Onglet "Graphiques" : visualisations matplotlib
     * Appelle méthodes spécialisées pour remplir chaque onglet :
       - _fill_lgbm_metrics_tab()
       - _fill_lstm_metrics_tab()
       - _fill_combined_metrics_tab()
       - _fill_plots_tab()
     * Bouton "Rafraîchir les Métriques" avec callback _refresh_metrics_dashboard()
   - RETOUR : None (crée fenêtre modale)
   - UTILITÉ : Interface complète pour monitoring détaillé des performances d'entraînement
6. draw_trend_chart.txt (HybridBaccaratPredictor.draw_trend_chart)
   - Lignes 11742-11826 dans hbp.py
   - FONCTION : Dessine le graphique des tendances des probabilités Player/Banker en temps réel
   - FONCTIONNEMENT :
     * Vérifie disponibilité éléments UI (ax, canvas) et couleurs MPL initialisées
     * Utilise couleurs FIXES MPL stockées (bg_color_mpl, fg_color_mpl)
     * Clear et configure fond du graphique avec couleur de fond fixe
     * Accès thread-safe à prediction_history avec sequence_lock
     * Si <2 points : affiche message "Pas assez de données" avec styling complet
     * Affiche les N derniers points (max 50) pour performance
     * Extrait données : player_probs, banker_probs, confidence (1-uncertainty)
     * Dessine 4 courbes :
       - Player Prob. (bleu #0066CC, markers)
       - Banker Prob. (rouge #D83B01, markers)
       - Confiance (vert, pointillés)
       - Ligne 50% (gris, tirets)
     * Configuration axes : ylim(0,1), xlim dynamique, labels colorés
     * Styling complet : titre, ticks, spines avec couleurs cohérentes
     * Légende avec couleurs fixes et gestion d'erreurs
     * Grille légère avec transparence
     * Redessine via canvas.draw_idle() avec gestion d'erreurs
   - RETOUR : None
   - UTILITÉ : Visualisation temps réel des tendances de prédiction avec styling cohérent
7. update_statistics.txt (HybridBaccaratPredictor.update_statistics)
   - Lignes 11533-11664 dans hbp.py
   - FONCTION : Met à jour tous les labels de statistiques dans l'interface utilisateur
   - FONCTIONNEMENT :
     * Accès thread-safe aux données avec sequence_lock et model_lock
     * Calcule série actuelle : détecte outcome et longueur de streak consécutif
     * Précision session : compte recommandations NON-WAIT correctes vs totales
     * Précisions par méthode : affiche accuracy% pour chaque modèle (Markov, LGBM, LSTM)
     * Confiance méthodes : extrait confiance de la dernière prédiction par modèle
     * Statistiques partie : compte Player/Banker avec ratios pourcentage
     * Métriques incertitude détaillées : épistémique, aléatoire, sensibilité contextuelle
     * Seuil adaptatif : affiche seuil de décision dynamique actuel
     * Poids bayésiens : affiche pondération actuelle de chaque modèle
     * Adaptation manches cibles : affiche "N/A (manche 1-30)" si hors plage 31-60
     * Crée dynamiquement nouvelles variables UI si nécessaires
   - RETOUR : None (met à jour stats_vars)
   - UTILITÉ : Tableau de bord complet des performances en temps réel avec adaptation contextuelle
8. toggle_graph_visibility.txt (HybridBaccaratPredictor.toggle_graph_visibility)
   - Lignes 11828-11869 dans hbp.py
   - FONCTION : Bascule la visibilité du cadre contenant le graphique Matplotlib
   - FONCTIONNEMENT :
     * Vérifie existence des widgets nécessaires (graph_frame, toggle_graph_button)
     * Si graphique visible : pack_forget() et change texte bouton "Afficher Graphique"
     * Si graphique masqué :
       - Change texte bouton "Masquer Graphique"
       - Repack le frame AVANT le bouton stats (ordre UI cohérent)
       - Utilise before=anchor_widget pour positionnement précis
       - Fallback si toggle_stats_button non trouvé
       - Redessine graphique via draw_trend_chart() et canvas.draw_idle()
     * Met à jour flag self.graph_visible
     * Gestion d'erreurs pour redraw après réaffichage
   - RETOUR : None
   - UTILITÉ : Contrôle utilisateur pour optimiser espace écran et performances
9. toggle_stats_visibility.txt (HybridBaccaratPredictor.toggle_stats_visibility)
   - Lignes 12808-12834 dans hbp.py
   - FONCTION : Bascule la visibilité du cadre contenant les statistiques détaillées
   - FONCTIONNEMENT :
     * Vérifie existence des widgets nécessaires (stats_frame, toggle_stats_button)
     * Si statistiques visibles :
       - stats_frame.pack_forget() : retire widget de l'affichage géré par pack
       - Change texte bouton vers "Afficher Statistiques"
       - Met à jour flag self.stats_visible = False
     * Si statistiques masquées :
       - Change texte bouton vers "Masquer Statistiques"
       - Repack le widget avec stats_frame.pack(fill=tk.X, pady=5)
       - Placement automatique en dessous des widgets déjà packés
       - Met à jour flag self.stats_visible = True
     * Logging debug pour traçabilité des changements d'état
     * Gestion d'erreurs si widgets non initialisés (warning + return)
   - RETOUR : None
   - UTILITÉ : Contrôle utilisateur pour optimiser espace écran selon besoins d'information
10. on_close.txt (HybridBaccaratPredictor.on_close)
   - Lignes 10897-10964 dans hbp.py
   - FONCTION : Gère la fermeture propre de l'application avec nettoyage complet
   - FONCTIONNEMENT :
     * Vérifie si tâche ML en cours (training_lock) : entraînement ou mise à jour rapide
     * Si tâche active : demande confirmation utilisateur pour interruption
     * Si confirmé : appelle stop_training_process() et donne délai 100ms pour arrêt
     * Si annulé : empêche fermeture (should_close = False)
     * Nettoyage mémoire : gc.collect() et torch.cuda.empty_cache()
     * Option sauvegarde automatique commentée (éviter délai/erreur à fermeture)
     * Finalisation thread-safe :
       - root.quit() : arrête boucle principale Tkinter
       - root.destroy() : détruit fenêtre et widgets
     * Gestion robuste erreurs : TclError (bénigne), exceptions génériques
     * Logging détaillé de toutes les étapes pour debug
   - RETOUR : None
   - UTILITÉ : Fermeture propre avec protection contre perte de données et nettoyage ressources
11. show_models_dashboard.txt (HybridBaccaratPredictor.show_models_dashboard)
   - Lignes 3332-3490 dans hbp.py (158 lignes)
   - FONCTION : Affiche un tableau de bord complet des modèles entraînés avec métadonnées
   - FONCTIONNEMENT DÉTAILLÉ :
     * Vérifie disponibilité UI avant création fenêtre
     * Crée fenêtre Toplevel (1000x600) "Tableau de bord des modèles"
     * Structure Treeview avec colonnes : filename, date, accuracy, lstm_hidden, lstm_layers, lgbm_trees, markov_order
     * Configuration colonnes avec largeurs spécifiques et scrollbar verticale
     * Scan répertoire "models/" pour fichiers .joblib et .pkl
     * Pour chaque modèle, extraction métadonnées via 2 méthodes :

       **MÉTHODE 1 - Fichier JSON associé :**
       - Cherche fichier .json avec même nom que modèle
       - Extrait timestamp, performance_metrics, hyperparameters
       - Parse best_accuracy, lstm_hidden_dim, lstm_num_layers, lgbm_n_estimators, max_markov_order

       **MÉTHODE 2 - Fallback depuis fichier modèle :**
       - Charge directement .joblib/.pkl si pas de JSON
       - Extrait save_timestamp, best_accuracy, config_details
       - Parse paramètres LGBM via get_params() si disponible

     * Valeurs par défaut "N/A" si extraction échoue
     * Formatage date ISO vers "YYYY-MM-DD HH:MM"
     * Insertion données dans Treeview avec gestion d'erreurs robuste
     * Boutons d'action : "Voir les détails", "Charger le modèle", "Fermer"
     * Callbacks vers _show_selected_model_details() et _load_selected_model()
   - RETOUR : None (crée fenêtre modale)
   - UTILITÉ : Interface complète de gestion et inspection des modèles sauvegardés
12. show_optimization_results.txt (HybridBaccaratPredictor.show_optimization_results)
   - Lignes 1165-1384 dans hbp.py (219 lignes)
   - FONCTION : Affiche interface complète des résultats d'optimisation Optuna avec visualisations
   - FONCTIONNEMENT DÉTAILLÉ :
     * Détection optimizer : utilise current_optimizer_instance ou fallback self.optimizer
     * Priorise study_phase2 (objectif 1) si disponible, sinon study principal
     * Génération rapport via generate_optimization_report() et sauvegarde
     * Création fenêtre Toplevel (1000x800) "Résultats d'optimisation Optuna - Objectif 1"
     * Visualisations Plotly avec 3 sous-graphiques :

       **GRAPHIQUE 1 - Historique optimisation :**
       - vis.plot_optimization_history() : évolution scores par trial

       **GRAPHIQUE 2 - Importance hyperparamètres :**
       - vis.plot_param_importances() : ranking paramètres influents

       **GRAPHIQUE 3 - Métriques clés objectif 1 :**
       - DataFrame trials avec Max Consécutives, Précision NON-WAIT, Ratio WAIT, etc.
       - Graphique barres normalisées (0-1) pour meilleur trial
       - Ligne référence à 0.5, annotation score meilleur essai

     * Export HTML temporaire avec Plotly CDN et ouverture navigateur
     * Boutons : "Générer Rapport Détaillé", "Fermer"
     * Gestion robuste erreurs : ImportError (dépendances), ValueError (données), Exception générale
     * Fallback messagebox si échec visualisation
   - RETOUR : None (crée fenêtre + ouvre navigateur)
   - UTILITÉ : Interface complète d'analyse des résultats d'optimisation avec focus objectif 1
13. cleanup_and_show_message.txt (cleanup_and_show_message - fonction locale)
   - Lignes 13306-13332 dans hbp.py
   - FONCTION : Fonction locale de nettoyage et affichage message après optimisation
   - FONCTIONNEMENT :
     * Nettoyage mémoire : gc.collect() et torch.cuda.empty_cache()
     * Terminaison forcée processus multiprocessing actifs :
       - Récupère multiprocessing.active_children()
       - Pour chaque processus : terminate() puis join(timeout=1.0)
       - Logging warnings/errors pour traçabilité
     * Affichage messagebox.showwarning avec durée et type de completion
     * Utilise variables de closure : completion_type, duration
     * Gestion d'erreurs robuste pour chaque étape de nettoyage
   - RETOUR : None
   - UTILITÉ : Nettoyage complet ressources après optimisation avec feedback utilisateur
14. lightweight_update_display.txt (HybridBaccaratPredictor.lightweight_update_display)
   - Lignes 11343-11452 dans hbp.py (109 lignes)
   - FONCTION : Met à jour uniquement les éléments essentiels de l'UI après chaque coup (thread-safe)
   - PARAMÈTRES : pred (Dict) - dictionnaire de prédiction avec probabilités et métriques
   - FONCTIONNEMENT DÉTAILLÉ :
     * Vérification sécurité : hasattr(self, 'pred_vars') avant mise à jour
     * Mise à jour probabilités : Player/Banker en pourcentage avec formatage 1 décimale
     * Recommandation : mapping {'player': "Jouer PLAYER", 'banker': "Jouer BANKER", 'wait': "Attendre"}
     * Numéro manche : accès thread-safe avec sequence_lock
     * **ADAPTATION MANCHES CIBLES (31-60) :**
       - Détecte si round_num dans plage target_round_min/max
       - Si hors plage : affiche "N/A (manche 1-30)" pour toutes métriques avancées
       - Si dans plage : affiche valeurs normales avec calculs spécialisés
     * Incertitude : formule ajustement display_unc = 0.5 + (unc - 0.5) / 2.0
     * Métriques détaillées : épistémique, aléatoire, sensibilité contextuelle
     * Seuil adaptatif : threshold * 100 avec formatage 2 décimales
     * Confiances méthodes : MARKOV(x%) | LGBM(y%) | LSTM(z%)
     * Poids bayésiens : MARKOV(x%) | LGBM(y%) | LSTM(z%)
     * Gestion d'erreurs robuste avec logger.debug/error
     * Pas d'update_idletasks (assumé appelé via root.after)
   - RETOUR : None
   - UTILITÉ : Mise à jour rapide UI avec adaptation contextuelle manches cibles
15. undo_last_move.txt (HybridBaccaratPredictor.undo_last_move)
   - Lignes 10780-10893 dans hbp.py (113 lignes)
   - FONCTION : Annule le dernier coup enregistré avec restauration complète de l'état
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VERROUILLAGE MULTI-THREAD :** sequence_lock, markov_lock, model_lock, weights_lock
     * Vérification séquence non vide + demande confirmation utilisateur
     * **LOGIQUE ANNULATION COMPLÈTE :**
       1. **Séquence :** self.sequence.pop() pour retirer dernier outcome
       2. **Historique prédictions :** self.prediction_history.pop()
       3. **Compteurs motifs :** décrément pattern_counts pour motif 4-coups concerné
       4. **Modèles Markov :** reset('soft') + recalcul via update_session()
       5. **Cache LGBM :** vidage complet (deque vide)
       6. **Performance méthodes :** ajustement total/correct/accuracy_history par méthode
       7. **Index MAJ rapide :** reset à 0 si annulation antérieure à last_incremental_update_index
     * **RESTAURATION ÉTAT :**
       - Génère nouvelles features via create_hybrid_features()
       - Calcule nouvelle prédiction via hybrid_prediction()
       - Planifie mises à jour UI : lightweight_update_display(), update_display(), draw_trend_chart()
     * Gestion d'erreurs robuste avec messagebox succès/erreur
     * Logging détaillé de chaque étape pour debug
   - RETOUR : None
   - UTILITÉ : Fonction d'annulation complète avec cohérence totale de l'état système

SOUS-CATÉGORIES :
- Métriques : _fill_*_metrics_tab.txt, _show_*_learning_curves.txt
- Configuration : setup_config_panel.txt, update_device_selection.txt
- Progression : progress_callback.txt, _safe_update_progress.txt
- Graphiques : _draw_*.txt, _show_*.txt
- Interactions : ui_operations.txt, toggle_*.txt

RÉSUMÉ : Ce dossier gère toute l'interface utilisateur Tkinter, incluant la configuration, l'affichage temps réel, les graphiques matplotlib, les tableaux de bord, la progression des tâches, et toutes les interactions utilisateur.

ANALYSE DÉTAILLÉE DES 49 FICHIERS SUPPLÉMENTAIRES :

16. _setup_ui_variables_1.txt (HybridBaccaratPredictor._setup_ui_variables - version 2)
   - Lignes 2101-2156 dans hbp.py
   - FONCTION : Version alternative d'initialisation des variables Tkinter pour l'interface
   - FONCTIONNEMENT :
     * **Variables prédiction :** round, player, banker, confidence, recommendation
     * **Variables statistiques étendues :** streak, accuracy, model_weights, uncertainty, method_acc, method_conf, uncertainty_details, adaptive_threshold, bayesian_weights, game_stats
     * **Configuration ressources :** use_cpu/use_gpu selon device.type
     * **CPU Cores :** calcul intelligent avec psutil.cpu_count(logical=True), fallback config
     * **RAM Guideline :** détection mémoire système, calcul initial_mem_val avec bornes
     * **Variables contrôle :** auto_update_enabled, progress_var, progress_label_var
     * Logging détaillé des paramètres ressources initialisés
   - RETOUR : None (initialise self.pred_vars, self.stats_vars, etc.)
   - UTILITÉ : Version enrichie avec plus de variables statistiques et gestion ressources intelligente

17. _update_weights_display_1.txt (HybridBaccaratPredictor._update_weights_display - version 2)
   - Lignes 10742-10767 dans hbp.py
   - FONCTION : Version alternative de mise à jour de l'affichage des poids des modèles
   - FONCTIONNEMENT :
     * Vérifie existence stats_vars et weights avant traitement
     * **Accès thread-safe :** utilise weights_lock pour copie des poids actuels
     * **Formatage :** tri par nom de méthode, capitalisation, pourcentages 1 décimale
     * **Affichage :** format "Poids: Lgbm(X%) | Lstm(Y%) | Markov(Z%)"
     * **Gestion erreurs :** TclError si fenêtre fermée, Exception générale
     * Optimisé pour appel via root.after() depuis sections critiques
   - RETOUR : None
   - UTILITÉ : Version thread-safe avec gestion d'erreurs robuste pour affichage poids

18. cleanup_and_show_message_1.txt (cleanup_and_show_message - fonction locale)
   - Lignes 13348-13374 dans hbp.py
   - FONCTION : Fonction locale de nettoyage et affichage message d'interruption
   - FONCTIONNEMENT :
     * **Nettoyage mémoire :** gc.collect() + torch.cuda.empty_cache()
     * **Terminaison processus :** multiprocessing.active_children()
     * **Terminaison forcée :** process.terminate() + join(timeout=1.0)
     * **Message utilisateur :** messagebox.showwarning avec durée d'interruption
     * Gestion d'erreurs pour chaque étape de nettoyage
   - RETOUR : None
   - UTILITÉ : Nettoyage complet ressources après interruption optimisation

19. setup_config_panel.txt (HybridBaccaratPredictor.setup_config_panel)
   - Lignes 2912-2982 dans hbp.py (70 lignes)
   - FONCTION : Configure le panneau de configuration des ressources système
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Validation :** vérifie existence config_frame, détruit widgets existants
     * **Sélection Device :** RadioButtons CPU/GPU avec update_device_selection()
     * **CPU Cores :** Scale 1 à max_logical_cores (psutil.cpu_count(logical=True))
     * **RAM Guideline :** Scale 1 à total_sys_mem_gb avec détection psutil
     * **Validation bornes :** initial_cores_val et initial_mem_val avec min/max
     * **Bouton Apply :** apply_resource_config() pour appliquer changements
     * **Auto-Update :** Checkbox toggle_auto_update + label informatif
     * **Layout :** Frames organisés avec pack(), labels, scales horizontaux
     * GPU désactivé si torch.cuda.is_available()=False
   - RETOUR : None (configure self.config_frame)
   - UTILITÉ : Interface complète de configuration ressources avec validation intelligente

LISTE COMPLÈTE DES 46 FICHIERS SUPPLÉMENTAIRES RESTANTS :

20. _auto_update_callback.txt (HybridBaccaratPredictor._auto_update_callback)
   - Lignes 11699-11705 dans hbp.py
   - FONCTION : Callback pour la mise à jour automatique (méthode dépréciée)
   - FONCTIONNEMENT :
     * Méthode n'est plus utilisée pour déclencher des mises à jour automatiques
     * Les mises à jour sont maintenant déclenchées uniquement lors de l'enregistrement de nouveaux résultats
     * Log debug indiquant que la méthode n'est plus utilisée
   - RETOUR : None
   - UTILITÉ : Méthode legacy conservée pour compatibilité, remplacée par système événementiel

21. _create_lgbm_features.txt (HybridBaccaratPredictor._create_lgbm_features)
   - Lignes 13748-13801 dans hbp.py (53 lignes)
   - FONCTION : Méthode interne pour créer les features LGBM
   - PARAMÈTRES : sequence (List[str]) - séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Compteurs basiques :** banker_count, player_count
     * **Calcul streaks :** _calculate_streaks(sequence) pour tous types de streaks
     * **Calcul alternances :** _calculate_alternates(sequence) pour patterns alternés
     * **Decay features :** _calculate_decay_feature() pour banker et player
     * **25 FEATURES ASSEMBLÉES :**
       1-2. banker_count, player_count
       3-4. Ratios banker/player normalisés par longueur séquence
       5-6. banker_streaks, player_streaks (compteurs généraux)
       7-8. banker_decay, player_decay
       9-20. Streak lengths spécifiques (2-7) pour banker et player
       21-23. alternate_count_2, alternate_count_3, alternate_ratio
       24-25. max_banker_streak, max_player_streak
     * **Optimisation :** 3 features Optuna supprimées (28→25 features)
   - RETOUR : List[float] - liste de 25 features pour LGBM
   - UTILITÉ : Génération complète des features LGBM avec patterns sophistiqués

22. _create_metrics_dashboard.txt (HybridBaccaratPredictor._create_metrics_dashboard)
   - Lignes 6955-7003 dans hbp.py (48 lignes)
   - FONCTION : Crée un tableau de bord de métriques dans l'interface utilisateur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Validation UI :** vérifie is_ui_available() avant création
     * **Fenêtre principale :** tk.Toplevel(self.root) 800x600, minsize 600x400
     * **Organisation onglets :** ttk.Notebook avec 4 onglets :
       - LGBM : métriques modèle LGBM
       - LSTM : métriques modèle LSTM
       - Combiné : métriques hybrides
       - Graphiques : visualisations
     * **Remplissage onglets :**
       - _fill_lgbm_metrics_tab(lgbm_frame)
       - _fill_lstm_metrics_tab(lstm_frame)
       - _fill_combined_metrics_tab(combined_frame)
       - _fill_plots_tab(plots_frame)
     * **Bouton rafraîchir :** _refresh_metrics_dashboard() avec tous les frames
     * **Layout :** pack avec fill=BOTH, expand=True, padding 10px
   - RETOUR : None (crée fenêtre modale)
   - UTILITÉ : Interface complète de monitoring des métriques d'entraînement avec onglets organisés

23. _create_temporal_split.txt (HybridBaccaratPredictor._create_temporal_split)
   - Lignes 4331-4349 dans hbp.py (18 lignes)
   - FONCTION : Extrait la logique du split temporel dans une fonction séparée
   - PARAMÈTRES : X_lgbm_all (np.ndarray) - données LGBM complètes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Configuration :** n_splits = max(2, config.lgbm_cv_splits ou 5)
     * **TimeSeriesSplit :** utilise sklearn TimeSeriesSplit(n_splits=n_splits)
     * **Génération splits :** itère sur tscv.split(X_lgbm_all)
     * **Collecte indices :** train_indices_list et val_indices_list
     * **Sélection finale :** prend le dernier split ([-1]) pour train et validation
     * **Validation :** vérifie que les listes ne sont pas vides
     * **Gestion erreurs :** retourne (None, None) si échec génération splits
   - RETOUR : Tuple[np.ndarray, np.ndarray] - (train_indices, val_indices) ou (None, None)
   - UTILITÉ : Génération robuste de splits temporels pour validation croisée LGBM
ANALYSES DÉTAILLÉES DES 52 FICHIERS RESTANTS :

24. _draw_confusion_matrix.txt (HybridBaccaratPredictor._draw_confusion_matrix)
   - Dessine matrice de confusion avec matplotlib, normalisation, annotations, colormap

25. _draw_curve.txt (HybridBaccaratPredictor._draw_curve)
   - Dessine courbes d'apprentissage génériques, gestion erreurs, axes personnalisés

26. _draw_lstm_learning_curves.txt (HybridBaccaratPredictor._draw_lstm_learning_curves)
   - Dessine courbes apprentissage LSTM spécifiques, loss/accuracy, validation

27. _fill_combined_metrics_tab.txt (HybridBaccaratPredictor._fill_combined_metrics_tab)
   - Remplit onglet métriques combinées, performance hybride, comparaisons modèles

28. _fill_lgbm_metrics_tab.txt (HybridBaccaratPredictor._fill_lgbm_metrics_tab)
   - Remplit onglet métriques LGBM, feature importance, validation croisée

29. _fill_lstm_metrics_tab.txt (HybridBaccaratPredictor._fill_lstm_metrics_tab)
   - Remplit onglet métriques LSTM, courbes apprentissage, architecture réseau

30. _fill_plots_tab.txt (HybridBaccaratPredictor._fill_plots_tab)
   - Remplit onglet graphiques, visualisations diverses, matrices confusion

31. _get_color_for_intensity.txt (HybridBaccaratPredictor._get_color_for_intensity)
   - Calcule couleur selon intensité, gradient RGB, normalisation 0-1

32. _refresh_metrics_dashboard.txt (HybridBaccaratPredictor._refresh_metrics_dashboard)
   - Rafraîchit tableau de bord métriques, mise à jour tous onglets

33. _safe_update_progress.txt (HybridBaccaratPredictor._safe_update_progress)
   - Mise à jour sécurisée barre progression, thread-safe, gestion erreurs

34. _show_confusion_matrices.txt (HybridBaccaratPredictor._show_confusion_matrices)
   - Affiche matrices confusion pour tous modèles, fenêtre dédiée

35. _show_feature_importance.txt (HybridBaccaratPredictor._show_feature_importance)
   - Affiche importance features LGBM, graphique barres, top features

36. _show_lgbm_learning_curves.txt (HybridBaccaratPredictor._show_lgbm_learning_curves)
   - Affiche courbes apprentissage LGBM, validation croisée, overfitting

37. _show_lstm_learning_curves.txt (HybridBaccaratPredictor._show_lstm_learning_curves)
   - Affiche courbes apprentissage LSTM, loss/accuracy par époque

38. _show_optuna_results_window.txt (HybridBaccaratPredictor._show_optuna_results_window)
   - Affiche fenêtre résultats Optuna, meilleurs paramètres, historique trials

39. _show_selected_model_details.txt (HybridBaccaratPredictor._show_selected_model_details)
   - Affiche détails modèle sélectionné, architecture, hyperparamètres

40. _undo_last_move_unsafe.txt (HybridBaccaratPredictor._undo_last_move_unsafe)
   - Annulation dernier coup version non sécurisée, sans verrous

41. _update_combined_metrics.txt (HybridBaccaratPredictor._update_combined_metrics)
   - Met à jour métriques combinées, performance ensemble, consensus

42. _update_config_from_optimization.txt (HybridBaccaratPredictor._update_config_from_optimization)
   - Met à jour config depuis optimisation, application paramètres optimaux

43. _update_dependent_configs.txt (HybridBaccaratPredictor._update_dependent_configs)
   - Met à jour configurations dépendantes, cohérence paramètres

44. _update_lgbm_metrics.txt (HybridBaccaratPredictor._update_lgbm_metrics)
   - Met à jour métriques LGBM, accuracy, feature importance

45. _update_lstm_metrics.txt (HybridBaccaratPredictor._update_lstm_metrics)
   - Met à jour métriques LSTM, loss, accuracy, convergence

46. _update_pattern_counts.txt (HybridBaccaratPredictor._update_pattern_counts)
   - Met à jour compteurs motifs, streaks, alternances

47. _update_prediction_progress.txt (HybridBaccaratPredictor._update_prediction_progress)
   - Met à jour progression prédiction, étapes calcul

48. _update_progress.txt (HybridBaccaratPredictor._update_progress)
   - Met à jour barre progression générale, pourcentage, message

49. cleanup_and_show_message.txt (cleanup_and_show_message - fonction locale)
   - Nettoyage et affichage message version principale, gc.collect()

50. cleanup_and_show_message_2.txt (cleanup_and_show_message - fonction locale v3)
   - Nettoyage et affichage message version 3, optimisations mémoire

51. cleanup_and_show_results.txt (cleanup_and_show_results - fonction locale)
   - Nettoyage et affichage résultats, succès optimisation

52. draw_trend_chart.txt (HybridBaccaratPredictor.draw_trend_chart)
   - Dessine graphique tendance, séquence temporelle, prédictions

53. is_ui_available.txt (HybridBaccaratPredictor.is_ui_available)
   - Vérifie disponibilité interface utilisateur, hasattr root

54. lightweight_update_display.txt (HybridBaccaratPredictor.lightweight_update_display)
   - Mise à jour légère affichage, optimisée performance

55. on_close.txt (HybridBaccaratPredictor.on_close)
   - Gestionnaire fermeture application, sauvegarde état, nettoyage

56. progress_callback.txt (HybridBaccaratPredictor.progress_callback)
   - Callback progression, interface thread-safe

57. setup_auto_update.txt (HybridBaccaratPredictor.setup_auto_update)
   - Configuration mises à jour automatiques, intervalles

58. setup_progress_bar.txt (HybridBaccaratPredictor.setup_progress_bar)
   - Configuration barre progression, style, variables Tkinter

59. setup_ui.txt (HybridBaccaratPredictor.setup_ui)
   - Lignes 2587-2769 dans hbp.py (182 lignes)
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Configuration style :** thèmes par plateforme (vista/aqua/clam), couleurs TTK
     * **Couleurs fixes matplotlib :** bg_color_mpl="#F0F0F0", fg_color_mpl="black"
     * **Structure principale :** main_frame → left_frame + right_frame (240px fixe)
     * **PANNEAU DROIT (240px) :**
       - **Gestion Modèles :** load_historical_data, load_trained_models, unified_save, show_models_dashboard
       - **Entraînement :** run_full_retraining (Accent.TButton), optimisation Optuna, load_optimized_params, apply_optimized_params_to_config_file
       - **Configuration Ressources :** setup_config_panel()
     * **PANNEAU GAUCHE (expandable) :**
       - **Contrôles :** Player Wins, Banker Wins, Annuler Dernier Coup (grid 3 colonnes)
       - **Prédiction temps réel :** round, player (bleu #0066CC), banker (orange #D83B01), confidence, recommendation
       - **Progression :** setup_progress_bar()
       - **Graphique matplotlib :** figure 5x2, couleurs fixes, FigureCanvasTkAgg, draw_trend_chart()
       - **Statistiques :** 9 labels (streak, accuracy, model_weights, bayesian_weights, uncertainty, etc.)
       - **Reset :** soft/hard buttons
     * **Boutons toggle :** graphique et statistiques masquables
     * **Styles personnalisés :** Accent.TButton, LabelFrame avec groove, padding cohérent
   - RETOUR : None (configure self.root)
   - UTILITÉ : Interface complète et professionnelle avec layout responsive et thèmes adaptatifs

60. show_model_hyperparameters.txt (HybridBaccaratPredictor.show_model_hyperparameters)
   - Affiche hyperparamètres modèle, fenêtre dédiée, paramètres optimisés

61. show_models_dashboard.txt (HybridBaccaratPredictor.show_models_dashboard)
   - Affiche tableau de bord modèles, liste sauvegardés, métadonnées

62. show_optimization_results.txt (HybridBaccaratPredictor.show_optimization_results)
   - Affiche résultats optimisation, meilleurs paramètres, historique

63. show_text_report.txt (HybridBaccaratPredictor.show_text_report)
   - Affiche rapport textuel, métriques détaillées, fenêtre scrollable

64. toggle_auto_update.txt (HybridBaccaratPredictor.toggle_auto_update)
   - Basculement mises à jour automatiques, activation/désactivation

65. toggle_graph_visibility.txt (HybridBaccaratPredictor.toggle_graph_visibility)
   - Basculement visibilité graphiques, masquer/afficher

66. toggle_stats_visibility.txt (HybridBaccaratPredictor.toggle_stats_visibility)
   - Basculement visibilité statistiques, optimisation espace

67. toggle_training_controls.txt (HybridBaccaratPredictor.toggle_training_controls)
   - Basculement contrôles entraînement, enabled/disabled

68. ui_operations.txt (HybridBaccaratPredictor.ui_operations)
   - Opérations interface utilisateur générales, utilitaires UI

69. undo_last_move_1.txt (HybridBaccaratPredictor.undo_last_move - version alternative)
   - Annulation dernier coup version alternative, optimisations

70. update_consecutive_confidence_calculator_1.txt (HybridBaccaratPredictor.update_consecutive_confidence_calculator - version alternative)
   - Mise à jour calculateur confiance version alternative

71. update_device_selection.txt (HybridBaccaratPredictor.update_device_selection)
   - Mise à jour sélection périphérique, CPU/GPU, torch.device

72. update_recent_data.txt (HybridBaccaratPredictor.update_recent_data)
   - Mise à jour données récentes, fenêtre glissante

73. update_statistics.txt (HybridBaccaratPredictor.update_statistics)
   - Mise à jour statistiques générales, accuracy, streaks

74. update_ui.txt (HybridBaccaratPredictor.update_ui)
   - Mise à jour interface utilisateur générale, refresh complet

75. update_weights.txt (HybridBaccaratPredictor.update_weights)
   - Mise à jour poids des modèles, normalisation, affichage

76. _reset_session_display.txt (HybridBaccaratPredictor._reset_session_display)
   - Lignes 12128-12212 dans hbp.py (84 lignes)
   - FONCTION : Réinitialise UNIQUEMENT l'affichage visuel de la session dans l'UI
   - FONCTIONNEMENT : Reset variables UI (pred_vars, stats_vars), efface graphique avec couleurs fixes MPL, reset barre progression
   - UTILITÉ : Affichage "Nouvelle Partie" sans modifier données internes

77. _setup_ui_variables.txt (HybridBaccaratPredictor._setup_ui_variables)
   - Initialise toutes les variables Tkinter pour l'interface (StringVar, IntVar)
   - Configure pred_vars et stats_vars avec valeurs par défaut

78. _setup_ui_variables_1.txt (HybridBaccaratPredictor._setup_ui_variables - version alternative)
   - Version alternative de l'initialisation des variables UI
   - Optimisations et corrections par rapport à la version principale

79. _update_weights_display.txt (HybridBaccaratPredictor._update_weights_display)
   - Met à jour l'affichage des poids des modèles dans l'interface
   - Formatage et normalisation pour affichage utilisateur

80. _update_weights_display_1.txt (HybridBaccaratPredictor._update_weights_display - version alternative)
   - Version alternative de la mise à jour affichage poids
   - Améliorations et corrections par rapport à la version principale

81. cleanup_and_show_message_1.txt (cleanup_and_show_message - fonction locale v2)
   - Nettoyage et affichage message version 2, optimisations mémoire intermédiaires

82. undo_last_move.txt (HybridBaccaratPredictor.undo_last_move)
   - Annulation dernier coup version principale avec verrous et validation complète
   - Gestion thread-safe, mise à jour UI, recalcul prédictions

83. update_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.update_consecutive_confidence_calculator)
   - Mise à jour calculateur confiance consécutive version principale
   - Synchronisation données récentes, recalcul patterns

84. update_display.txt (HybridBaccaratPredictor.update_display)
   - Mise à jour affichage général de l'interface utilisateur
   - Refresh complet de tous les éléments visuels


