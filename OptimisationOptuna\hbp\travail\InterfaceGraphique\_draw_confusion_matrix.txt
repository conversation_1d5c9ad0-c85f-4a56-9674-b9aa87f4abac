# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 8031 à 8099
# Type: Méthode de la classe HybridBaccaratPredictor

    def _draw_confusion_matrix(self, parent_frame, cm):
        """Dessine une matrice de confusion dans un cadre parent."""
        # Créer un canvas pour afficher la matrice
        canvas = tk.Canvas(parent_frame, bg='white')
        canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Obtenir les dimensions du canvas
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        # Si le canvas n'est pas encore rendu, utiliser des dimensions par défaut
        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width = 300
            canvas_height = 300

        # Définir les marges
        margin = 50

        # Calculer les dimensions de la matrice
        matrix_size = min(canvas_width, canvas_height) - 2 * margin
        cell_size = matrix_size / 2

        # Dessiner les cellules de la matrice
        for i in range(2):
            for j in range(2):
                # Calculer les coordonnées de la cellule
                x1 = margin + j * cell_size
                y1 = margin + i * cell_size
                x2 = x1 + cell_size
                y2 = y1 + cell_size

                # Déterminer la couleur de la cellule (plus foncée pour les valeurs plus élevées)
                max_val = max(cm[0, 0], cm[0, 1], cm[1, 0], cm[1, 1])
                intensity = cm[i, j] / max_val if max_val > 0 else 0
                color = self._get_color_for_intensity(intensity)

                # Dessiner la cellule
                canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")

                # Ajouter la valeur dans la cellule
                canvas.create_text(
                    (x1 + x2) / 2,
                    (y1 + y2) / 2,
                    text=str(cm[i, j]),
                    fill="white" if intensity > 0.5 else "black",
                    font=("Arial", 12, "bold")
                )

        # Ajouter les étiquettes des classes (système zero-based: 0 = Player, 1 = Banker)
        classes = ['Player', 'Banker']

        # Étiquettes des colonnes
        for j in range(2):
            canvas.create_text(
                margin + j * cell_size + cell_size / 2,
                margin - 20,
                text=f"Prédit: {classes[j]}",
                fill="black"
            )

        # Étiquettes des lignes
        for i in range(2):
            canvas.create_text(
                margin - 20,
                margin + i * cell_size + cell_size / 2,
                text=f"Réel: {classes[i]}",
                fill="black",
                angle=90
            )