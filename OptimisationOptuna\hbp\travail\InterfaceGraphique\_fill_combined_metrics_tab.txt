# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 7422 à 7471
# Type: Méthode de la classe HybridBaccaratPredictor

    def _fill_combined_metrics_tab(self, parent_frame):
        """Remplit l'onglet des métriques combinées."""
        # Créer un cadre pour les métriques d'incertitude
        uncertainty_frame = ttk.LabelFrame(parent_frame, text="Métriques d'Incertitude")
        uncertainty_frame.pack(fill=tk.X, expand=False, padx=10, pady=5)

        # Créer des variables pour les métriques d'incertitude
        self.combined_metric_vars = {
            'epistemic_uncertainty': tk.StringVar(value="Incertitude Épistémique: N/A"),
            'aleatoric_uncertainty': tk.StringVar(value="Incertitude Aléatoire: N/A"),
            'context_sensitivity': tk.StringVar(value="Sensibilité Contextuelle: N/A"),
            'total_uncertainty': tk.StringVar(value="Incertitude Totale: N/A"),
        }

        # Ajouter les étiquettes pour les métriques d'incertitude
        for i, (key, var) in enumerate(self.combined_metric_vars.items()):
            ttk.Label(uncertainty_frame, textvariable=var).grid(row=i//2, column=i%2, padx=10, pady=5, sticky=tk.W)

        # Créer un cadre pour les poids des modèles
        weights_frame = ttk.LabelFrame(parent_frame, text="Poids des Modèles")
        weights_frame.pack(fill=tk.X, expand=False, padx=10, pady=5)

        # Créer des variables pour les poids des modèles
        self.weights_vars = {
            'markov_weight': tk.StringVar(value="Poids Markov: N/A"),
            'lgbm_weight': tk.StringVar(value="Poids LGBM: N/A"),
            'lstm_weight': tk.StringVar(value="Poids LSTM: N/A"),
        }

        # Ajouter les étiquettes pour les poids des modèles
        for i, (key, var) in enumerate(self.weights_vars.items()):
            ttk.Label(weights_frame, textvariable=var).grid(row=0, column=i, padx=10, pady=5, sticky=tk.W)

        # Créer un cadre pour les métriques de performance combinées
        performance_frame = ttk.LabelFrame(parent_frame, text="Performance Combinée")
        performance_frame.pack(fill=tk.X, expand=False, padx=10, pady=5)

        # Créer des variables pour les métriques de performance combinées
        self.combined_performance_vars = {
            'combined_accuracy': tk.StringVar(value="Exactitude Combinée: N/A"),
            'recommendation_rate': tk.StringVar(value="Taux de Recommandation: N/A"),
            'average_confidence': tk.StringVar(value="Confiance Moyenne: N/A"),
        }

        # Ajouter les étiquettes pour les métriques de performance combinées
        for i, (key, var) in enumerate(self.combined_performance_vars.items()):
            ttk.Label(performance_frame, textvariable=var).grid(row=0, column=i, padx=10, pady=5, sticky=tk.W)

        # Mettre à jour les métriques combinées
        self._update_combined_metrics()