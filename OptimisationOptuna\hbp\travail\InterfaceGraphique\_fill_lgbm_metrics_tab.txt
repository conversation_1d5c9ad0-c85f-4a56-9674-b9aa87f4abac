# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 7005 à 7059
# Type: Méthode de la classe HybridBaccaratPredictor

    def _fill_lgbm_metrics_tab(self, parent_frame):
        """Remplit l'onglet des métriques LGBM."""
        # Créer un cadre pour les métriques de base
        basic_metrics_frame = ttk.LabelFrame(parent_frame, text="Métriques de Base")
        basic_metrics_frame.pack(fill=tk.X, expand=False, padx=10, pady=5)

        # Créer des variables pour les métriques
        self.lgbm_metric_vars = {
            'accuracy': tk.StringVar(value="Exactitude: N/A"),
            'precision': tk.StringVar(value="Précision: N/A"),
            'recall': tk.StringVar(value="Rappel: N/A"),
            'f1': tk.StringVar(value="F1-Score: N/A"),
            'auc_roc': tk.StringVar(value="AUC-ROC: N/A"),
        }

        # Ajouter les étiquettes pour les métriques de base
        for i, (key, var) in enumerate(self.lgbm_metric_vars.items()):
            ttk.Label(basic_metrics_frame, textvariable=var).grid(row=i//3, column=i%3, padx=10, pady=5, sticky=tk.W)

        # Créer un cadre pour la matrice de confusion
        confusion_frame = ttk.LabelFrame(parent_frame, text="Matrice de Confusion")
        confusion_frame.pack(fill=tk.X, expand=False, padx=10, pady=5)

        # Créer une grille pour la matrice de confusion
        self.lgbm_cm_vars = []
        for i in range(2):
            row_vars = []
            for j in range(2):
                var = tk.StringVar(value="N/A")
                row_vars.append(var)
                ttk.Label(confusion_frame, textvariable=var, width=10, anchor='center',
                         borderwidth=1, relief="solid").grid(row=i+1, column=j+1, padx=5, pady=5)
            self.lgbm_cm_vars.append(row_vars)

        # Ajouter les étiquettes pour les classes
        ttk.Label(confusion_frame, text="").grid(row=0, column=0)
        ttk.Label(confusion_frame, text="Prédit: Banker").grid(row=0, column=1)
        ttk.Label(confusion_frame, text="Prédit: Player").grid(row=0, column=2)
        ttk.Label(confusion_frame, text="Réel: Banker").grid(row=1, column=0)
        ttk.Label(confusion_frame, text="Réel: Player").grid(row=2, column=0)

        # Créer un cadre pour l'importance des caractéristiques
        feature_importance_frame = ttk.LabelFrame(parent_frame, text="Importance des Caractéristiques")
        feature_importance_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Créer un widget Treeview pour afficher l'importance des caractéristiques
        self.lgbm_feature_tree = ttk.Treeview(feature_importance_frame, columns=('feature', 'importance'), show='headings')
        self.lgbm_feature_tree.heading('feature', text='Caractéristique')
        self.lgbm_feature_tree.heading('importance', text='Importance')
        self.lgbm_feature_tree.column('feature', width=200)
        self.lgbm_feature_tree.column('importance', width=100)
        self.lgbm_feature_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Mettre à jour les métriques LGBM
        self._update_lgbm_metrics()