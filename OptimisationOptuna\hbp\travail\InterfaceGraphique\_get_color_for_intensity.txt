# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 8101 à 8105
# Type: Méthode de la classe HybridBaccaratPredictor

    def _get_color_for_intensity(self, intensity):
        """Retourne une couleur en fonction de l'intensité (0-1)."""
        # Convertir l'intensité en une couleur bleue
        blue = int(255 * intensity)
        return f"#{0:02x}{0:02x}{blue:02x}"