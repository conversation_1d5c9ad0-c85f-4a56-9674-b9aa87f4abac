# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 12128 à 12212
# Type: Méthode de la classe HybridBaccaratPredictor

    def _reset_session_display(self) -> None:
        """
        Réinitialise UNIQUEMENT l'affichage visuel de la session dans l'UI
        à un état "Nouvelle Partie" (Manche 0, stats N/A, graph vide),
        sans modifier les données internes chargées (séquence, historique, etc.).
        A appeler depuis le thread UI (ou via root.after).
        UTILISE LES COULEURS FIXES self.bg_color_mpl et self.fg_color_mpl.
        """
        logger.info("Réinitialisation de l'affichage visuel de la session (état 'Nouvelle Partie')...") # Ligne ~4925
        ui_available = self.is_ui_available()
        if not ui_available:
            logger.warning("_reset_session_display: UI non disponible.")
            return

        # Vérifier que les variables UI et les couleurs MPL existent
        if not hasattr(self, 'pred_vars') or not hasattr(self, 'stats_vars') or \
           not hasattr(self, 'bg_color_mpl') or not hasattr(self, 'fg_color_mpl'):
            logger.warning("_reset_session_display: Variables UI ou couleurs MPL non trouvées.")
            return

        try:
            # 1. Réinitialiser les variables du panneau de prédiction
            self.pred_vars['round'].set("Manche: 0")
            self.pred_vars['player'].set("Player: N/A")
            self.pred_vars['banker'].set("Banker: N/A")
            self.pred_vars['confidence'].set("Confiance: N/A")
            self.pred_vars['recommendation'].set("Recommandation: En attente...")

            # 2. Réinitialiser les variables de statistiques liées à la session
            self.stats_vars['streak'].set("Série actuelle: -")
            self.stats_vars['accuracy'].set("Précision (Session): N/A")
            self.stats_vars['game_stats'].set("Partie: P 0 (0.0%) | B 0 (0.0%)")
            self.stats_vars['uncertainty'].set("Incertitude Prediction: N/A")
            self.stats_vars['method_acc'].set("Précisions Méthodes: N/A")
            self.stats_vars['method_conf'].set("Confiance Méthodes: N/A")
            self.stats_vars['uncertainty_details'].set("Incertitude Détaillée: N/A")
            self.stats_vars['adaptive_threshold'].set("Seuil Adaptatif: N/A")
            self.stats_vars['bayesian_weights'].set("Poids Bayésiens: N/A")

            # 3. Effacer le graphique et utiliser les couleurs FIXES MPL
            if hasattr(self, 'ax') and hasattr(self, 'canvas'):
                # --- PAS DE VALIDATION ICI - Utilisation directe des couleurs fixes ---
                # Les logs originaux (~4973, ~4984) ne devraient plus être générés ici
                bg_color_mpl = self.bg_color_mpl # Utilise #F0F0F0
                fg_color_mpl = self.fg_color_mpl # Utilise black
                # --- FIN ---

                self.ax.clear()
                self.ax.set_facecolor(bg_color_mpl)
                self.ax.text(0.5, 0.5, 'Prêt (Nouvelle Partie)',
                             horizontalalignment='center', verticalalignment='center',
                             transform=self.ax.transAxes, color=fg_color_mpl) # Utilise couleur fixe
                # Effacer labels/ticks/spines et appliquer couleurs fixes
                self.ax.set_xlabel('')
                self.ax.set_ylabel('')
                self.ax.set_title('')
                self.ax.set_xticks([])
                self.ax.set_yticks([])
                self.ax.spines['bottom'].set_color(fg_color_mpl)
                self.ax.spines['left'].set_color(fg_color_mpl)
                self.ax.spines['top'].set_color(bg_color_mpl)
                self.ax.spines['right'].set_color(bg_color_mpl)

                try:
                    self.canvas.draw_idle()
                    logger.debug("Graphique effacé pour démarrage visuel.")
                except Exception as e_draw:
                    logger.error(f"Erreur draw_idle pendant reset visuel: {e_draw}")
            else:
                logger.warning("_reset_session_display: Graphique (ax/canvas) non trouvé.")

            # 4. Réinitialiser la barre de progression
            final_label = "Prêt (Nouvelle Partie)"
            final_progress = 100
            if hasattr(self, 'progress_var'): self.progress_var.set(final_progress)
            if hasattr(self, 'progress_label_var'): self.progress_label_var.set(final_label)

            # 5. Mettre à jour l'UI
            self.root.update_idletasks()
            logger.info("Affichage visuel de la session réinitialisé à 'Nouvelle Partie'.")

        except tk.TclError as e_tk:
            logger.error(f"Erreur Tkinter pendant _reset_session_display: {e_tk}")
        except Exception as e:
            logger.error(f"Erreur inattendue pendant _reset_session_display: {e}", exc_info=True)