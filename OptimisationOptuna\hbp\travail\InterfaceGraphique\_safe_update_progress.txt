# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 5236 à 5252
# Type: Méthode de la classe HybridBaccaratPredictor

    def _safe_update_progress(self, progress, message):
        """
        Méthode utilitaire pour mettre à jour la progression de manière sécurisée.
        Si un observateur de progression est disponible, il est appelé.
        Sinon, la méthode _update_progress est appelée directement.

        Args:
            progress: Pourcentage de progression (0-100)
            message: Message à afficher
        """
        # Vérifier si un observateur de progression est disponible
        if hasattr(self, '_progress_observer'):
            # Appeler l'observateur de progression
            self._progress_observer(progress, message)
        elif self.is_ui_available():
            # Appeler la méthode _update_progress directement
            self.root.after(0, lambda p=progress, m=message: self._update_progress(p, m))