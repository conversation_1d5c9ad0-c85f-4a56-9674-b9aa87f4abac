# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2021 à 2058
# Type: Méthode de la classe HybridBaccaratPredictor

    def _setup_ui_variables(self):
        self.pred_vars = {
            'round': tk.StringVar(value="Manche: 0"),
            'player': tk.StringVar(value="Player: 50.0%"),
            'banker': tk.StringVar(value="Banker: 50.0%"),
            'confidence': tk.StringVar(value="Confiance: N/A"),
            'recommendation': tk.StringVar(value="Recommandation: En attente...")
        }
        self.stats_vars = {
            'streak': tk.StringVar(value="Série actuelle: -"),
            'accuracy': tk.StringVar(value="Précision (Session): N/A"),
            'model_weights': tk.StringVar(value="Poids: Chargement..."),
            'uncertainty': tk.StringVar(value="Incertitude Prediction: N/A"),
            'method_acc': tk.StringVar(value="Précisions Méthodes: N/A"),
            'game_stats': tk.StringVar(value="Partie: P 0 (0.0%) | B 0 (0.0%)")
        }
        self.use_cpu = tk.BooleanVar(value=(self.device.type == 'cpu'))
        self.use_gpu = tk.BooleanVar(value=(self.device.type == 'cuda'))
        default_cores_config = getattr(self.config, 'default_cpu_cores', 2)
        max_logical_cores = psutil.cpu_count(logical=True) if psutil else default_cores_config
        initial_cores_val = max(1, min(default_cores_config, max_logical_cores))
        self.cpu_cores = tk.IntVar(value=initial_cores_val)
        total_sys_mem_gb = 4
        if psutil:
            try:
                total_sys_mem_gb = max(1, int(psutil.virtual_memory().total / (1024**3)))
            except Exception:
                pass
        configured_mem_gb = getattr(self.config, 'default_max_memory_gb', total_sys_mem_gb // 2)
        initial_mem_val = max(1, configured_mem_gb)
        if initial_mem_val > total_sys_mem_gb:
            logger.warning(f"La Guideline RAM configurée ({initial_mem_val} Go) dépasse la RAM système détectée ({total_sys_mem_gb} Go).")
        self.max_mem = tk.IntVar(value=initial_mem_val)
        self.target_max_ram_gb: int = initial_mem_val
        logger.info(f"Variables ressources UI: CPU Cores Init={initial_cores_val} (Max Logic={max_logical_cores}), RAM Guideline Init={initial_mem_val} Go (Total Sys={total_sys_mem_gb} Go)")
        self.auto_update_enabled = tk.BooleanVar(value=False)
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_label_var = tk.StringVar(value="Prêt")