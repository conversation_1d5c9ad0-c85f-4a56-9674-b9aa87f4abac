# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2101 à 2156
# Type: Méthode de la classe HybridBaccaratPredictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _setup_ui_variables(self):
        """Initialise les variables Tkinter pour l'interface.
           RAM Max est une guideline influençant les paramètres internes.
        """
        # Variables pour le panneau de prédiction
        self.pred_vars = {
            'round': tk.StringVar(value="Manche: 0"),
            'player': tk.StringVar(value="Player: 50.0%"),
            'banker': tk.StringVar(value="Banker: 50.0%"),
            'confidence': tk.StringVar(value="Confiance: N/A"),
            'recommendation': tk.StringVar(value="Recommandation: En attente...")
        }
        # Variables pour le panneau de statistiques
        self.stats_vars = {
            'streak': tk.StringVar(value="Série actuelle: -"),
            'accuracy': tk.StringVar(value="Précision (Session): N/A"),
            'model_weights': tk.StringVar(value="Poids: Chargement..."), # Message initial
            'uncertainty': tk.StringVar(value="Incertitude Prediction: N/A"), # Renommé pour clarté
            'method_acc': tk.StringVar(value="Précisions Méthodes: N/A"),
            'method_conf': tk.StringVar(value="Confiance Méthodes: N/A"), # Nouvelle variable pour la confiance des méthodes
            'uncertainty_details': tk.StringVar(value="Incertitude Détaillée: N/A"), # Nouvelle variable pour les détails d'incertitude
            'adaptive_threshold': tk.StringVar(value="Seuil Adaptatif: N/A"), # Nouvelle variable pour le seuil adaptatif
            'bayesian_weights': tk.StringVar(value="Poids Bayésiens: N/A"), # Nouvelle variable pour les poids bayésiens
            'game_stats': tk.StringVar(value="Partie: P 0 (0.0%) | B 0 (0.0%)") # Stats partie actuelle
            # 'best_accuracy': tk.StringVar(value=f"Meilleure précision globale: {self.best_accuracy*100:.1f}%") # Mis à jour dynamiquement
        }

        # Variables pour le panneau de configuration des ressources
        self.use_cpu = tk.BooleanVar(value=(self.device.type == 'cpu'))
        self.use_gpu = tk.BooleanVar(value=(self.device.type == 'cuda'))

        # CPU Cores: Le défaut peut être la moitié, mais le max visé sera tous les logiques.
        default_cores_config = getattr(self.config, 'default_cpu_cores', 2)
        max_logical_cores = psutil.cpu_count(logical=True) if psutil else default_cores_config
        initial_cores_val = max(1, min(default_cores_config, max_logical_cores))
        self.cpu_cores = tk.IntVar(value=initial_cores_val)

        # RAM Guideline
        total_sys_mem_gb = 4 # Fallback sûr
        if psutil:
            try: total_sys_mem_gb = max(1, int(psutil.virtual_memory().total / (1024**3)))
            except Exception: pass
        default_mem_config_base = getattr(self.config, 'default_max_memory_gb', total_sys_mem_gb // 2)
        default_mem_config = max(1, min(default_mem_config_base, total_sys_mem_gb))
        initial_mem_val = max(1, min(default_mem_config, total_sys_mem_gb)) # S'assurer que init <= total sys
        self.max_mem = tk.IntVar(value=initial_mem_val) # Min 1 Go
        self.target_max_ram_gb: int = initial_mem_val # Stocker la valeur appliquée

        logger.info(f"Variables ressources UI: CPU Cores Init={initial_cores_val} (Max Logic={max_logical_cores}), RAM Guideline Init={initial_mem_val} Go (Total Sys={total_sys_mem_gb} Go)")

        # Variable pour contrôler l'activation/désactivation des mises à jour rapides
        self.auto_update_enabled = tk.BooleanVar(value=False)

        # Variables pour la barre de progression
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_label_var = tk.StringVar(value="Prêt")