# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 3492 à 3518
# Type: Méthode de la classe HybridBaccaratPredictor

    def _show_selected_model_details(self, tree: ttk.Treeview) -> None:
        """
        Affiche les détails du modèle sélectionné dans le tableau de bord.

        Args:
            tree (ttk.Treeview): Widget Treeview contenant les modèles
        """
        selected_items = tree.selection()
        if not selected_items:
            messagebox.showinfo("Information", "Veuillez sélectionner un modèle dans la liste.")
            return

        # Récupérer le nom du fichier sélectionné
        item = selected_items[0]
        values = tree.item(item, 'values')
        if not values:
            return

        filename = values[0]
        model_path = os.path.join(os.getcwd(), "models", filename)

        if not os.path.exists(model_path):
            messagebox.showerror("Erreur", f"Le fichier {filename} n'existe pas.")
            return

        # Afficher les détails du modèle
        self.show_model_hyperparameters(model_path)