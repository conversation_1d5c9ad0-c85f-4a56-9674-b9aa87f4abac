# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 936 à 986
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_config_from_optimization(self, best_params):
        if not best_params:
            logger.warning("Aucun paramètre à mettre à jour")
            return

        with self.model_lock:
            logger.info("Mise à jour configuration avec les paramètres optimisés")

            for param, value in best_params.items():
                if param.startswith('weight_') or param in ('decision_threshold', 'min_confidence'):
                    if param == 'decision_threshold': logger.debug(f" Ignoré paramètre optimisé 'decision_threshold'={value}")
                    elif param == 'min_confidence': logger.debug(f" Ignoré paramètre optimisé 'min_confidence'={value}")
                    continue

                if hasattr(self.config, param):
                    try:
                        current_value = getattr(self.config, param); expected_type = type(current_value)
                        if expected_type is bool and not isinstance(value, bool): value = str(value).lower() in ('true', '1', 'yes')
                        elif expected_type is int and isinstance(value, float) and value.is_integer(): value = int(value)
                        elif expected_type is float and isinstance(value, int): value = float(value)
                        elif expected_type != type(value): value = expected_type(value)
                        setattr(self.config, param, value)
                        logger.debug(f" Config appliquée: {param} = {value} (type: {type(value).__name__})")
                    except (TypeError, ValueError) as e: logger.warning(f"Échec apply/convert {param}={value}: {e}. Ignoré.")
                else: logger.warning(f"Paramètre optimisé inconnu '{param}' ignoré.")

            new_weights = {}
            found_weights = False
            if hasattr(self.config, 'initial_weights') and isinstance(self.config.initial_weights, dict):
                initial_weight_keys = list(self.config.initial_weights.keys())
                for key in initial_weight_keys:
                    best_param_key = f'weight_{key}'
                    if best_param_key in best_params:
                        new_weights[key] = best_params[best_param_key]; found_weights = True
                    else: new_weights[key] = self.config.initial_weights.get(key, 0.0)
            else: logger.warning("Attribut 'initial_weights' non trouvé/invalide."); initial_weight_keys = []

            if found_weights:
                total = sum(new_weights.values())
                if total > 1e-9:
                    final_weights = {k: new_weights.get(k, 0.0) / total for k in initial_weight_keys}
                    self.config.initial_weights = final_weights
                    logger.info(f"Poids mis à jour: { {k: f'{v:.3f}' for k,v in self.config.initial_weights.items()} }")
                else:
                    logger.warning("Poids optimisés nuls. Reset défauts."); default_config = PredictorConfig()
                    self.config.initial_weights = default_config.initial_weights.copy()
                    logger.info(f"Poids réinitialisés: { {k: f'{v:.3f}' for k,v in self.config.initial_weights.items()} }")
            else: logger.info("Aucun poids optimisé trouvé. Poids initiaux conservés.")

            self.init_ml_models(reset_weights=True)
            logger.info("Configuration mise à jour et modèles réinitialisés.")