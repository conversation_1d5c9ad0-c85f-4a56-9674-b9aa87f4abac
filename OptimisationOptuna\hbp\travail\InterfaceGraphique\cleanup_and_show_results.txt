# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 13263 à 13290
# Type: Méthode de la classe HybridBaccaratPredictor

                    def cleanup_and_show_results():
                        # Nettoyage de la mémoire
                        gc.collect()
                        if torch.cuda.is_available():
                            try:
                                torch.cuda.empty_cache()
                            except Exception:
                                pass

                        # Forcer la terminaison des processus multiprocessing qui pourraient être encore actifs
                        try:
                            import multiprocessing
                            # Récupérer tous les processus actifs
                            active_children = multiprocessing.active_children()
                            if active_children:
                                logger.warning(f"Terminaison de {len(active_children)} processus multiprocessing actifs")
                                for process in active_children:
                                    try:
                                        process.terminate()
                                        process.join(timeout=1.0)  # Attendre 1 seconde maximum
                                    except Exception as e:
                                        logger.error(f"Erreur lors de la terminaison du processus {process.name}: {e}")
                        except Exception as e:
                            logger.error(f"Erreur lors de la terminaison des processus multiprocessing: {e}")

                        # Activer les contrôles et afficher la fenêtre de résultats
                        self.toggle_training_controls(enabled=True)
                        self._show_optuna_results_window(best_params)