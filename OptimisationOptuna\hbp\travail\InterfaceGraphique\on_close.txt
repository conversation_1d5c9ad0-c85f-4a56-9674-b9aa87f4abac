# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 10897 à 10964
# Type: Méthode de la classe HybridBaccaratPredictor

    def on_close(self) -> None:
        """Gère la fermeture propre de l'application."""
        logger.info("Demande de fermeture de l'application...")
        should_close = True
        ui_available = self.is_ui_available()

        try:
            # Vérifier si une tâche ML est en cours
            with self.training_lock:
                 task_running = self.is_training or self.is_fast_updating
                 task_type = "Entraînement complet" if self.is_training else "Mise à jour rapide" if self.is_fast_updating else "Aucune"

            if task_running:
                if ui_available:
                    response = messagebox.askyesno("Tâche en Cours",
                                           f"Une tâche ({task_type}) est en cours. Voulez-vous vraiment quitter ?\n"
                                           "La tâche sera interrompue.")
                else: response = True # Quitter sans confirmer si pas d'UI

                if response:
                    logger.warning(f"Fermeture pendant '{task_type}'. Arrêt demandé...")
                    self.stop_training_process() # Met le flag stop_training
                    # Donner une chance au thread de voir le flag, mais ne pas bloquer indéfiniment
                    if ui_available: self.root.after(100)
                else:
                    logger.info("Fermeture annulée car tâche ML en cours.")
                    should_close = False

            # Si on doit fermer (pas annulé par l'utilisateur)
            if should_close:
                # Optionnel: Sauvegarder l'état avant de quitter?
                # Peut être long ou échouer, potentiellement demander confirmation
                # Par défaut: ne pas sauvegarder automatiquement à la fermeture pour éviter délai/erreur
                # if ui_available and messagebox.askyesno("Sauvegarder avant de quitter?", ...):
                #     save_ok = self._save_state_to_models_dir()
                #     if not save_ok: logger.error("Echec sauvegarde état avant fermeture.")

                logger.info("Nettoyage avant fermeture...")
                # Forcer le garbage collection
                gc.collect()
                if torch.cuda.is_available():
                     try:
                         torch.cuda.empty_cache()
                         logger.debug("Cache CUDA vidé.")
                     except Exception as e_cuda:
                         logger.warning(f"Erreur vidage cache CUDA: {e_cuda}")

        except Exception as e:
            logger.error(f"Erreur pendant le nettoyage dans on_close: {e}", exc_info=True)
            # Continuer vers destroy/quit même si erreur nettoyage

        finally:
            # Assurer arrêt de la boucle et destruction fenêtre si nécessaire
            if should_close:
                logger.info("Arrêt mainloop et destruction fenêtre Tkinter...")
                try:
                    if ui_available:
                        self.root.quit()    # Arrête la boucle principale Tkinter
                        self.root.destroy() # Détruit la fenêtre et ses widgets
                        logger.info("Fenêtre Tkinter détruite.")
                    else:
                         logger.info("UI non disponible, pas de root.quit/destroy à appeler.")
                except tk.TclError as e_destroy:
                     logger.warning(f"Erreur Tcl (bénigne?) lors destruction fenêtre: {e_destroy}")
                except Exception as e_generic_destroy:
                     logger.error(f"Erreur générique destruction fenêtre: {e_generic_destroy}", exc_info=True)

        logger.info("Fin de on_close.")