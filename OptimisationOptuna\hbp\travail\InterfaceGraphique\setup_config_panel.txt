# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2912 à 2982
# Type: Méthode de la classe HybridBaccaratPredictor

    def setup_config_panel(self):
        """Configure le panneau de configuration des ressources.
           Permet de viser une utilisation maximale (avec risques).
        """
        if not hasattr(self, 'config_frame') or not self.config_frame:
            logger.error("Tentative config panneau config avant init.")
            return
        for widget in self.config_frame.winfo_children(): widget.destroy()

        # Device Selection (inchangé)
        device_frame = ttk.Frame(self.config_frame); device_frame.pack(fill=tk.X, pady=3)
        ttk.Label(device_frame, text="CPU/GPU:").pack(side=tk.LEFT, padx=5)
        self.device_choice = tk.StringVar(value=self.device.type)
        rb_cpu = ttk.Radiobutton(device_frame, text="CPU", variable=self.device_choice, value="cpu", command=self.update_device_selection)
        rb_cpu.pack(side=tk.LEFT, padx=5)
        rb_gpu = ttk.Radiobutton(device_frame, text="GPU", variable=self.device_choice, value="cuda", command=self.update_device_selection)
        rb_gpu.pack(side=tk.LEFT, padx=5)
        rb_gpu.configure(state=tk.NORMAL if torch.cuda.is_available() else tk.DISABLED)

        # CPU Cores: Le Scale va jusqu'au max logique.
        cores_frame = ttk.Frame(self.config_frame); cores_frame.pack(fill=tk.X, pady=3)
        ttk.Label(cores_frame, text="Threads CPU max:").pack(side=tk.LEFT, padx=5)
        max_logical_cores = psutil.cpu_count(logical=True) if psutil else getattr(self.config, 'default_cpu_cores', 2)
        max_cores = max(1, max_logical_cores)
        initial_cores_val = max(1, min(self.cpu_cores.get(), max_cores)) # Validation init
        self.cpu_cores.set(initial_cores_val)
        # Le scale permet de choisir *jusqu'à* max_cores
        cores_scale = ttk.Scale(cores_frame, from_=1, to=max_cores, variable=self.cpu_cores, orient=tk.HORIZONTAL, command=lambda val: self.cpu_cores.set(int(float(val))))
        cores_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        cores_label = ttk.Label(cores_frame, textvariable=self.cpu_cores, width=3, anchor='e')
        cores_label.pack(side=tk.LEFT, padx=5)

        # RAM Guideline (widgets identiques, sémantique change)
        mem_frame = ttk.Frame(self.config_frame); mem_frame.pack(fill=tk.X, pady=3)
        # Label ajusté pour refléter l'intention et le risque
        ttk.Label(mem_frame, text="RAM").pack(side=tk.LEFT, padx=5)
        total_sys_mem_gb = 4 # Fallback
        if psutil:
            try: total_sys_mem_gb = max(1, int(psutil.virtual_memory().total / (1024**3)))
            except Exception as e_psutil_mem: logger.warning(f"Erreur psutil mémoire: {e_psutil_mem}")

        initial_mem_val = max(1, min(self.max_mem.get(), total_sys_mem_gb))
        self.max_mem.set(initial_mem_val)
        mem_scale = ttk.Scale(mem_frame, from_=1, to=total_sys_mem_gb, variable=self.max_mem, orient=tk.HORIZONTAL, command=lambda val: self.max_mem.set(int(float(val))))
        mem_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        mem_label = ttk.Label(mem_frame, textvariable=self.max_mem, width=3, anchor='e')
        mem_label.pack(side=tk.LEFT, padx=5)

        # Bouton Appliquer (inchangé)
        apply_button = ttk.Button(self.config_frame, text="Appliquer Config Ressources", command=self.apply_resource_config)
        apply_button.pack(pady=5)

        # Ajout du bouton pour activer/désactiver les mises à jour rapides
        auto_update_frame = ttk.Frame(self.config_frame)
        auto_update_frame.pack(fill=tk.X, pady=3)

        # Checkbox pour activer/désactiver les mises à jour rapides
        self.auto_update_checkbox = ttk.Checkbutton(
            auto_update_frame,
            text="Activer les mises à jour rapides",
            variable=self.auto_update_enabled,
            command=self.toggle_auto_update
        )
        self.auto_update_checkbox.pack(side=tk.LEFT, padx=5)

        # Étiquette d'information sur les mises à jour rapides
        ttk.Label(
            auto_update_frame,
            text="(Ajustements incrémentiels des modèles)",
            font=('Segoe UI', 8, 'italic')
        ).pack(side=tk.LEFT, padx=5)