# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 2587 à 2769
# Type: Méthode de la classe HybridBaccaratPredictor

    def setup_ui(self) -> None:
        """Configure l'interface utilisateur principale avec Tkinter."""
        self.logger.debug("Configuration de l'interface utilisateur...")
        style = ttk.Style()
        # --- Configuration du style (thèmes) ---
        try:
            if sys.platform == "win32": style.theme_use('vista')
            elif sys.platform == "darwin": style.theme_use('aqua')
            else: style.theme_use('clam')
        except tk.TclError: style.theme_use('default') # Fallback

        # --- RÉCUPÉRATION DES COULEURS TTK (Pour l'UI Tkinter UNIQUEMENT) ---
        bg_color_ttk_raw = "#F0F0F0"; fg_color_ttk_raw = "black"
        try: bg_color_ttk_raw = style.lookup('TFrame', 'background')
        except tk.TclError: pass
        try: fg_color_ttk_raw = style.lookup('TLabel', 'foreground')
        except tk.TclError: pass
        logger.debug(f"Couleurs Tkinter brutes récupérées -> BG: {bg_color_ttk_raw}, FG: {fg_color_ttk_raw}")

        # --- DÉFINITION DES COULEURS FIXES POUR MATPLOTLIB ---
        self.bg_color_mpl = "#F0F0F0"
        self.fg_color_mpl = "black"
        logger.info(f"Utilisation COULEURS FIXES pour Matplotlib -> BG: {self.bg_color_mpl}, FG: {self.fg_color_mpl}")

        # --- Style Tkinter général (utilise les couleurs ttk brutes) ---
        style.configure('.', background=bg_color_ttk_raw, foreground=fg_color_ttk_raw, font=('Segoe UI', 9))
        style.configure('TButton', padding=5, font=('Segoe UI', 9))
        style.configure('Accent.TButton', foreground='white', background='#0078D4', font=('Segoe UI', 9, 'bold'))
        style.map('Accent.TButton', background=[('active', '#106EBE')])
        style.configure('TLabel', padding=2)
        style.configure('TFrame', background=bg_color_ttk_raw)
        style.configure('TLabelframe', background=bg_color_ttk_raw, relief='groove', borderwidth=1, padding=5)
        style.configure('TLabelframe.Label', font=('Segoe UI', 10, 'bold'), foreground='#005A9E')

        # --- Structure principale des frames ---
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        right_frame = ttk.Frame(main_frame, width=240) # Largeur fixe
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        right_frame.pack_propagate(False)

        # == Panneau Droit: Widgets ==
        self.model_frame = ttk.LabelFrame(right_frame, text="Gestion Modèles & Données", padding="5")
        self.model_frame.pack(fill=tk.X, pady=5, padx=5)
        ttk.Button(self.model_frame, text="Charger Historique (.txt)", command=self.load_historical_data).pack(fill=tk.X, pady=2)
        ttk.Button(self.model_frame, text="Charger un État Spécifique", command=lambda: self.load_trained_models(None)).pack(fill=tk.X, pady=2)
        ttk.Button(self.model_frame, text="Sauvegarder État Actuel", command=self.unified_save).pack(fill=tk.X, pady=2)
        ttk.Button(self.model_frame, text="Tableau de Bord des Modèles", command=self.show_models_dashboard).pack(fill=tk.X, pady=2)

        self.train_frame = ttk.LabelFrame(right_frame, text="Entraînement", padding="5")
        self.train_frame.pack(fill=tk.X, pady=5, padx=5)

        self.full_retrain_button = ttk.Button(self.train_frame,
                                            text="Entraîner Complètement",
                                            command=self.run_full_retraining,
                                            style='Accent.TButton')
        self.full_retrain_button.pack(fill=tk.X, pady=2)

        ttk.Separator(self.train_frame).pack(fill=tk.X, pady=5)

        ttk.Button(self.train_frame,
                 text="Optimiser Hyperparamètres",
                 command=self.run_hyperparameter_optimization).pack(fill=tk.X, pady=2)

        ttk.Button(self.train_frame,
                 text="Voir Résultats Optuna",
                 command=self.show_optimization_results).pack(fill=tk.X, pady=2)

        ttk.Button(self.train_frame,
                 text="Appliquer Paramètres Optimisés (nécessite entraînement)",
                 command=self.load_optimized_params).pack(fill=tk.X, pady=2)

        ttk.Button(self.train_frame,
                 text="Charger Modèles Pré-entraînés Optimisés",
                 command=lambda: self.load_optimized_models()).pack(fill=tk.X, pady=2)

        ttk.Button(self.train_frame,
                 text="Sauvegarder Modèles Optimisés Actuels",
                 command=lambda: self._select_and_save_optimized_models()).pack(fill=tk.X, pady=2)

        ttk.Button(self.train_frame,
                 text="Appliquer Paramètres Optimisés au Fichier Config",
                 command=self.apply_optimized_params_to_config_file).pack(fill=tk.X, pady=2)

        self.stop_train_button = ttk.Button(self.train_frame,
                                          text="Arrêter Tâche ML",
                                          command=self.stop_training_process)
        self.stop_train_button.pack(fill=tk.X, pady=2)

        self.config_frame = ttk.LabelFrame(right_frame, text="Configuration Ressources", padding="5")
        self.config_frame.pack(fill=tk.X, pady=5, padx=5)
        self.setup_config_panel()

        # == Panneau Gauche: Widgets ==
        self.ctrl_frame = ttk.Frame(left_frame)
        self.ctrl_frame.pack(fill=tk.X, pady=5)
        self.ctrl_frame.columnconfigure(0, weight=1)
        self.ctrl_frame.columnconfigure(1, weight=1)
        self.ctrl_frame.columnconfigure(2, weight=1)

        ttk.Button(self.ctrl_frame,
                  text="Player Wins",
                  command=lambda: self.safe_record_outcome('player')).grid(row=0, column=0, padx=5, sticky='ew', ipady=5)

        ttk.Button(self.ctrl_frame,
                  text="Banker Wins",
                  command=lambda: self.safe_record_outcome('banker')).grid(row=0, column=1, padx=5, sticky='ew', ipady=5)

        ttk.Button(self.ctrl_frame,
                  text="Annuler Dernier Coup",
                  command=self.undo_last_move).grid(row=0, column=2, padx=5, sticky='ew', ipady=5)

        pred_frame = ttk.LabelFrame(left_frame, text="Prédiction Temps Réel", padding="10")
        pred_frame.pack(fill=tk.X, pady=5)
        ttk.Label(pred_frame, textvariable=self.pred_vars['round'], font=('Segoe UI', 10)).grid(row=0, column=0, sticky='w', pady=2)
        ttk.Label(pred_frame, textvariable=self.pred_vars['player'], font=('Segoe UI', 12, 'bold'), foreground='#0066CC').grid(row=1, column=0, sticky='w', pady=3)
        ttk.Label(pred_frame, textvariable=self.pred_vars['banker'], font=('Segoe UI', 12, 'bold'), foreground='#D83B01').grid(row=2, column=0, sticky='w', pady=3)
        ttk.Label(pred_frame, textvariable=self.pred_vars['confidence'], font=('Segoe UI', 10)).grid(row=3, column=0, sticky='w', pady=2)
        ttk.Label(pred_frame, textvariable=self.pred_vars['recommendation'], font=('Segoe UI', 11, 'bold')).grid(row=4, column=0, sticky='w', pady=3)

        # --- Jauge de progression ---
        self.progress_frame = ttk.LabelFrame(left_frame, text="Progression Tâches", padding="5")
        self.progress_frame.pack(fill=tk.X, pady=5, padx=5)
        self.setup_progress_bar()

        # --- Graphique ---
        self.toggle_graph_button = ttk.Button(left_frame, text="Masquer Graphique", command=self.toggle_graph_visibility)
        self.toggle_graph_button.pack(fill=tk.X, pady=(5, 0))

        self.graph_frame = ttk.Frame(left_frame)
        self.graph_frame.pack(pady=10, fill=tk.BOTH, expand=True)

        # --- CRÉATION FIGURE (Utilise couleurs FIXES pour MPL) ---
        self.figure, self.ax = plt.subplots(figsize=(5, 2), dpi=100)
        self.figure.patch.set_facecolor(self.bg_color_mpl)
        self.ax.set_facecolor(self.bg_color_mpl)
        self.ax.tick_params(axis='x', colors=self.fg_color_mpl)
        self.ax.tick_params(axis='y', colors=self.fg_color_mpl)
        self.ax.spines['bottom'].set_color(self.fg_color_mpl)
        self.ax.spines['left'].set_color(self.fg_color_mpl)
        self.ax.spines['top'].set_color(self.bg_color_mpl)
        self.ax.spines['right'].set_color(self.bg_color_mpl)

        self.canvas = FigureCanvasTkAgg(self.figure, master=self.graph_frame)
        self.canvas_widget = self.canvas.get_tk_widget()
        self.canvas_widget.pack(fill=tk.BOTH, expand=True)
        self.draw_trend_chart()

        # --- Bouton Toggle Statistiques ---
        self.toggle_stats_button = ttk.Button(left_frame, text="Masquer Statistiques", command=self.toggle_stats_visibility)
        self.toggle_stats_button.pack(fill=tk.X, pady=(0, 5))

        # --- Boutons Reset ---
        self.reset_soft_button = ttk.Button(left_frame,
                                          text="Réinitialiser Données Session",
                                          command=lambda: self.reset_data('soft'))
        self.reset_soft_button.pack(fill=tk.X, pady=(15, 2))

        self.reset_hard_button = ttk.Button(left_frame,
                                          text="Réinitialiser TOUT (Hard)",
                                          command=lambda: self.reset_data('hard'))
        self.reset_hard_button.pack(fill=tk.X, pady=(2, 5))

        # --- Frame Statistiques ---
        self.stats_frame = ttk.LabelFrame(left_frame, text="Analyse & Statistiques", padding="10")
        self.stats_frame.pack(fill=tk.X, pady=5)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['streak']).grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['accuracy']).grid(row=0, column=1, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['model_weights']).grid(row=1, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['bayesian_weights']).grid(row=2, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['uncertainty']).grid(row=3, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['uncertainty_details']).grid(row=4, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['method_acc']).grid(row=5, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['method_conf']).grid(row=6, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['adaptive_threshold']).grid(row=7, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        ttk.Label(self.stats_frame, textvariable=self.stats_vars['game_stats']).grid(row=8, column=0, columnspan=2, sticky='w', padx=5, pady=2)

        # Ajouter un bouton pour afficher le tableau de bord des métriques d'entraînement
        ttk.Button(self.stats_frame, text="Afficher Métriques d'Entraînement", command=self._create_metrics_dashboard).grid(row=9, column=0, columnspan=2, sticky='ew', padx=5, pady=5)

        self.logger.debug("Configuration de l'interface utilisateur terminée.")