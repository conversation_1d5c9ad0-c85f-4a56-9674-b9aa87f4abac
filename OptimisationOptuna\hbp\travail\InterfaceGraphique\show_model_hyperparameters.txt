# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 3140 à 3291
# Type: Méthode de la classe HybridBaccaratPredictor

    def show_model_hyperparameters(self, model_filepath: str) -> None:
        """
        Affiche les hyperparamètres d'un modèle dans une fenêtre.

        Args:
            model_filepath (str): Chemin du fichier du modèle
        """
        if not self.is_ui_available():
            logger.warning("Interface utilisateur non disponible pour afficher les hyperparamètres")
            return

        # Vérifier si un fichier de métadonnées existe
        json_filepath = os.path.splitext(model_filepath)[0] + '.json'
        metadata = None

        if os.path.exists(json_filepath):
            # Charger les métadonnées depuis le fichier JSON
            try:
                with open(json_filepath, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                logger.info(f"Métadonnées chargées depuis {json_filepath}")
            except Exception as e:
                logger.error(f"Erreur lors du chargement des métadonnées: {e}", exc_info=True)

        # Si aucun fichier de métadonnées n'existe, essayer de charger les hyperparamètres depuis le modèle
        if metadata is None:
            try:
                # Déterminer le format du fichier
                if model_filepath.endswith('.joblib'):
                    loaded_package = joblib.load(model_filepath)
                else:  # .pkl
                    with open(model_filepath, 'rb') as f:
                        loaded_package = pickle.load(f)

                if 'config_details' in loaded_package:
                    # Créer un dictionnaire de métadonnées minimal
                    metadata = {
                        'timestamp': loaded_package.get('save_timestamp', 'Inconnue'),
                        'model_file': os.path.basename(model_filepath),
                        'hyperparameters': loaded_package['config_details'],
                        'performance_metrics': {
                            'best_accuracy': loaded_package.get('best_accuracy', 'Inconnue')
                        }
                    }
                    logger.info(f"Métadonnées extraites du fichier modèle {model_filepath}")
                else:
                    messagebox.showinfo("Information", "Aucune information d'hyperparamètres disponible pour ce modèle.")
                    return
            except Exception as e:
                messagebox.showerror("Erreur", f"Impossible de charger les informations d'hyperparamètres: {e}")
                return

        # Créer une fenêtre pour afficher les hyperparamètres
        hyperparams_window = tk.Toplevel(self.root)
        hyperparams_window.title(f"Hyperparamètres du modèle: {os.path.basename(model_filepath)}")
        hyperparams_window.geometry("800x600")

        # Créer un widget Text avec scrollbar
        frame = ttk.Frame(hyperparams_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        scrollbar = ttk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget = tk.Text(frame, wrap=tk.WORD, yscrollcommand=scrollbar.set)
        text_widget.pack(fill=tk.BOTH, expand=True)

        scrollbar.config(command=text_widget.yview)

        # Configurer les styles de texte
        text_widget.tag_configure("title", font=("Arial", 14, "bold"))
        text_widget.tag_configure("section", font=("Arial", 12, "bold"))
        text_widget.tag_configure("normal", font=("Arial", 10))
        text_widget.tag_configure("value", font=("Courier New", 10))

        # Afficher les informations générales
        text_widget.insert(tk.END, "INFORMATIONS DU MODÈLE\n", "title")
        text_widget.insert(tk.END, "======================\n\n", "title")

        text_widget.insert(tk.END, f"Fichier: {metadata.get('model_file', 'Inconnu')}\n", "normal")
        text_widget.insert(tk.END, f"Date: {metadata.get('timestamp', 'Inconnue')}\n", "normal")
        text_widget.insert(tk.END, f"Version: {metadata.get('version', 'Inconnue')}\n", "normal")
        text_widget.insert(tk.END, "\n")

        # Afficher les métriques de performance
        text_widget.insert(tk.END, "PERFORMANCE:\n", "section")
        performance_metrics = metadata.get('performance_metrics', {})
        for metric, value in performance_metrics.items():
            text_widget.insert(tk.END, f"  {metric}: ", "normal")
            text_widget.insert(tk.END, f"{value}\n", "value")
        text_widget.insert(tk.END, "\n")

        # Afficher les performances par méthode
        method_performance = metadata.get('method_performance', {})
        if method_performance:
            text_widget.insert(tk.END, "PERFORMANCE PAR MÉTHODE:\n", "section")
            for method, value in method_performance.items():
                text_widget.insert(tk.END, f"  {method}: ", "normal")
                text_widget.insert(tk.END, f"{value}\n", "value")
            text_widget.insert(tk.END, "\n")

        # Afficher les hyperparamètres par catégorie
        hyperparams = metadata.get('hyperparameters', {})

        categories = {
            'LSTM': ['lstm_'],
            'LGBM': ['lgbm_'],
            'Markov': ['markov_', 'max_markov_order'],
            'Seuils': ['threshold', 'confidence', 'uncertainty', 'wait_'],
            'Poids': ['weight', 'weights'],
            'Autres': []
        }

        for category, prefixes in categories.items():
            # Filtrer les hyperparamètres pour cette catégorie
            category_params = {}
            for param, value in hyperparams.items():
                if any(param.startswith(prefix) or prefix in param for prefix in prefixes) or (category == 'Autres' and not any(param.startswith(prefix) or prefix in param for cat, prefixes_list in categories.items() for prefix in prefixes_list if cat != 'Autres')):
                    category_params[param] = value

            if category_params:
                text_widget.insert(tk.END, f"{category}:\n", "section")

                # Afficher les hyperparamètres de cette catégorie
                for param, value in sorted(category_params.items()):
                    text_widget.insert(tk.END, f"  {param}: ", "normal")
                    text_widget.insert(tk.END, f"{value}\n", "value")

                text_widget.insert(tk.END, "\n")

        # Afficher les informations d'entraînement
        training_info = metadata.get('training_info', {})
        if training_info:
            text_widget.insert(tk.END, "INFORMATIONS D'ENTRAÎNEMENT:\n", "section")
            for info, value in training_info.items():
                text_widget.insert(tk.END, f"  {info}: ", "normal")
                text_widget.insert(tk.END, f"{value}\n", "value")
            text_widget.insert(tk.END, "\n")

        # Rendre le widget en lecture seule
        text_widget.config(state=tk.DISABLED)

        # Ajouter des boutons pour appliquer les hyperparamètres ou fermer
        button_frame = ttk.Frame(hyperparams_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Appliquer ces hyperparamètres",
                  command=lambda: self._apply_hyperparameters_from_metadata(metadata)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Charger ce modèle",
                  command=lambda: self.load_trained_models(model_filepath)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Fermer",
                  command=hyperparams_window.destroy).pack(side=tk.RIGHT, padx=5)