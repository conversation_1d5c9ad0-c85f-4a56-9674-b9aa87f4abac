# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 12095 à 12123
# Type: Méthode de la classe HybridBaccaratPredictor

            def ui_operations():
                # Activer les contrôles d'entraînement
                self.toggle_training_controls(enabled=True)

                # Mettre à jour la progression
                self._update_progress(100, "Entraînement terminé" if success else "Échec entraînement")

                if success:
                    # Afficher le message de succès
                    messagebox.showinfo("Entraînement Terminé", success_details)

                    # Mettre à jour la progression pour la sauvegarde
                    self._update_progress(95, "Sauvegarde état...")

                    # Afficher le message d'échec de sauvegarde si nécessaire
                    if not save_successful:
                        messagebox.showwarning("Sauvegarde Auto Échouée", "Entraînement réussi, mais sauvegarde auto échouée. Vérifiez logs.")

                    # Mettre à jour la progression finale
                    self._update_progress(100, final_message)

                    # Réinitialiser la session
                    logger.info("Réinitialisation session interne après entraînement COMPLET réussi...")
                    self.reset_data('soft', confirm=False) # Reset soft (efface session, pas modèles)
                    logger.info("Reset 'soft' post-entraînement complet terminé.")
                else:
                    # Afficher le message d'erreur
                    self._update_progress(0, final_message)
                    messagebox.showerror("Erreur Entraînement", "L'entraînement principal a échoué.\nConsultez les logs pour les détails.")