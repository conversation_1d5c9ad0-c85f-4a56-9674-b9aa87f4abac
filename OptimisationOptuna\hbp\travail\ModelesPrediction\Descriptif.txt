DESCRIPTIF DES MÉTHODES - DOSSIER MODELESPREDICTION
==================================================

Ce dossier contient toutes les méthodes liées aux modèles de prédiction (LGBM, LSTM, Markov), aux prédictions hybrides et à l'évaluation des modèles.

MÉTHODES ANALYSÉES :

1. hybrid_prediction.txt (HybridBaccaratPredictor.hybrid_prediction)
   - Lignes 8795-9937 dans hbp.py
   - FONCTION : Effectue une prédiction hybride combinant <PERSON>ov, LGBM et LSTM
   - PARAMÈTRES : lgbm_feat, lstm_feat, optimization_phase (1/2/None)
   - FONCTIONNEMENT :
     * Obtient les prédictions individuelles de chaque modèle (Markov, LGBM, LSTM)
     * Calcule la confiance pour chaque modèle entraîné
     * Applique la pondération bayésienne des modèles
     * Calcule l'incertitude avancée (épistémique, aléatoire, sensibilité contextuelle)
     * Utilise le calculateur de confiance consécutive pour manches 31-60
     * Détermine le seuil de décision adaptatif selon la phase d'optimisation
     * Prend la décision finale (WAIT/NON-WAIT) basée sur score et validité
     * Collecte les statistiques si en mode optimisation
   - RETOUR : Dict avec prédictions, recommandation, métriques de confiance
   - UTILITÉ : Cœur du système de prédiction hybride avec logique de décision avancée

AUTRES MÉTHODES IMPORTANTES :

2. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features)
   - Lignes 5056-5105 dans hbp.py
   - FONCTION : Fonction centralisée pour créer les features hybrides (LGBM et LSTM)
   - PARAMÈTRES : sequence (List[str]) - séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT :
     * Utilise fenêtre adaptative : pour manche N, utilise N-1 manches précédentes
     * Vérifie longueur minimale séquence (≥2)
     * Création features LGBM via _create_lgbm_features() avec gestion d'erreurs
     * Création features LSTM via create_lstm_sequence_features() :
       - Utilise lstm_sequence_length de la configuration
       - Désactive messages debug après premier run (évite spam Optuna)
       - Gestion d'erreurs séparée pour chaque type
     * Si aucune feature créée : warning global
     * Retourne tuple avec features valides ou None selon succès
   - RETOUR : Tuple[Optional[List[float]], Optional[np.ndarray]] - (features LGBM, features LSTM)
   - UTILITÉ : Point d'entrée unique pour génération features avec fenêtre adaptative
3. evaluate_consecutive_focused.txt (HybridBaccaratPredictor.evaluate_consecutive_focused)
   - Lignes 367-539 dans hbp.py (172 lignes)
   - FONCTION : Évalue les modèles en simulant des séquences, focalisé sur l'objectif N°1 (recommandations NON-WAIT valides consécutives manches 31-60)
   - PARAMÈTRES : model_type (str, défaut 'all'), num_simulations (int, défaut 5)
   - FONCTIONNEMENT DÉTAILLÉ :
     * Vérifie disponibilité modèles selon model_type ('all', 'lgbm', 'lstm', 'markov')
     * Initialise métriques avec noms standardisés config.py
     * Boucle simulations :
       - Simule séquence 60 manches via _simulate_game_sequence()
       - Extrait manches 31-60 (late_game_sequence) pour évaluation ciblée
       - Pour chaque position : génère features et prédiction hybride
       - Convertit prédictions en recommandations selon min_confidence_for_recommendation
       - Calcule précision NON-WAIT : correct_non_wait / total_non_wait
       - Calcule précision WAIT : utilise WAIT_PRECISION_DEFAULT
       - Calcule taux recommandation : total_non_wait / total_recommendations
       - Analyse séquences consécutives valides :
         * Compte recommandations NON-WAIT correctes consécutives
         * Brise séquence si recommandation NON-WAIT incorrecte
         * WAIT ne brise pas la séquence (continue comptage)
       - Calcule max_consecutive et weighted_mean (pondération quadratique)
       - Score composite : max_consecutive² * WEIGHT + weighted_mean * WEIGHT + precision * WEIGHT
     * Agrégation finale : moyenne pondérée par scores consécutifs²
     * Métriques retournées : CONSECUTIVE_SCORE, MAX_CONSECUTIVE, PRECISION_NON_WAIT, PRECISION_WAIT, RECOMMENDATION_RATE
   - RETOUR : Dict - métriques d'évaluation focalisées objectif N°1
   - UTILITÉ : Évaluation spécialisée pour optimisation séquences consécutives manches cibles
4. get_weights.txt (HybridBaccaratPredictor.get_weights)
   - Lignes 8416-8424 dans hbp.py
   - FONCTION : Retourne les poids actuels des méthodes de prédiction de manière thread-safe
   - FONCTIONNEMENT :
     * Accès protégé avec weights_lock pour éviter lectures concurrentes
     * Retourne copie de self.weights pour éviter modifications externes
     * Dictionnaire format : {'markov': float, 'lgbm': float, 'lstm': float}
   - RETOUR : Dict[str, float] - copie des poids actuels
   - UTILITÉ : Interface thread-safe pour consultation des poids bayésiens
5. init_ml_models.txt (HybridBaccaratPredictor.init_ml_models)
   - Lignes 1589-1795 dans hbp.py (206 lignes)
   - FONCTION : Initialise/réinitialise tous les modèles ML avec configuration complète
   - PARAMÈTRES : reset_weights (bool, défaut True) - réinitialise poids et performances
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION RÉCURSION :** flag _initializing_models pour éviter appels multiples
     * **VERROUILLAGE :** model_lock + weights_lock pour modifications thread-safe
     * **COMPOSANTS INITIALISÉS :**

       **1. Calculateurs spécialisés :**
       - init_consecutive_confidence_calculator()
       - init_wait_placement_optimizer()

       **2. Features et Scaler :**
       - feature_names depuis config.selected_features
       - StandardScaler() non-fitted

       **3. Modèles LGBM :**
       - Calcul n_jobs optimal via psutil (cpu_count logique)
       - Extraction paramètres depuis config (lgbm_n_estimators, learning_rate, etc.)
       - Synchronisation avec config.lgbm_params si disponible
       - LGBMClassifier avec paramètres configurés
       - Réinitialisation calibrated_lgbm et lgbm_uncertainty à None

       **4. Modèle LSTM :**
       - Synchronisation lstm_hidden_dim/lstm_hidden_size
       - EnhancedLSTMModel avec paramètres config
       - Optimisation mémoire via optimize_lstm_memory()
       - Déplacement sur device approprié (GPU/CPU)
       - Initialisation poids : xavier_uniform_ (weight_ih), orthogonal_ (weight_hh)
       - AdamW optimizer + ReduceLROnPlateau scheduler

     * **RESET OPTIONNEL (reset_weights=True) :**
       - weights = config.initial_weights.copy()
       - _initialize_method_performance()
       - best_accuracy = 0.5, early_stopping_counter = 0
     * Cache LGBM vidé (deque maxlen=100)
     * Gestion d'erreurs robuste avec fallback None pour tous modèles
   - RETOUR : bool - succès de l'initialisation
   - UTILITÉ : Point d'entrée central pour initialisation complète système ML
6. safe_record_outcome.txt (HybridBaccaratPredictor.safe_record_outcome)
   - Lignes 12344-12486 dans hbp.py (142 lignes)
   - FONCTION : Enregistre résultat manche et déclenche prochaine prédiction (thread-safe)
   - PARAMÈTRES : outcome (str) - résultat de la manche ('player' ou 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LIMITE 60 MANCHES :** vérifie len(sequence) < 60, sinon ignore avec warning
     * **VERROUILLAGE COMPLET :** sequence_lock, markov_lock, model_lock, weights_lock
     * **TRAITEMENT SÉQUENTIEL :**
       1. **Mise à jour séquence :** self.sequence.append(outcome)
       2. **Auto-update ciblé :** auto_fast_update_if_needed(current_round_num)
       3. **Markov session :** markov.update_session(sequence)
       4. **Compteurs patterns :** _update_pattern_counts(outcome)
       5. **Features nouvelles :** create_hybrid_features(sequence)
       6. **Prédiction suivante :** hybrid_prediction() avec optimization_phase
       7. **Historique :** prediction_history.append(next_prediction)
       8. **Poids bayésiens :** update_weights(prediction_précédente, outcome)
       9. **Confiance consécutive :** update_consecutive_confidence_calculator()
       10. **UI planifiée :** lightweight_update_display(), update_display(), _update_weights_display()
     * **GESTION MANCHES CIBLES (31-60) :**
       - Initialise consecutive_confidence_calculator si manquant
       - Mise à jour calculateur avec recommandation et outcome
     * **OPTIMISATIONS :** cache LGBM non vidé (modification), timing détaillé
     * Gestion d'erreurs robuste : NotFittedError, exceptions générales
   - RETOUR : None
   - UTILITÉ : Point d'entrée principal pour enregistrement coup avec pipeline complet
7. _analyze_sequence_context.txt (HybridBaccaratPredictor._analyze_sequence_context)
   - Lignes 13941-13979 dans hbp.py
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles
   - PARAMÈTRES : sequence (List[str]) - séquence de résultats
   - FONCTIONNEMENT :
     * Si séquence < 10 : retourne 0.5 (données insuffisantes)
     * **Volatilité récente :** compte alternances dans 10 derniers coups
     * **Streak actuel :** longueur série consécutive actuelle (normalisée /10)
     * **Facteur contextuel :** volatility * 0.7 + streak_factor * 0.3
   - RETOUR : float (0-1) - 0=stable (global pertinent), 1=volatile (session pertinent)
   - UTILITÉ : Adaptation dynamique des poids selon contexte de jeu
8. _initialize_method_performance.txt (HybridBaccaratPredictor._initialize_method_performance)
   - Lignes 2060-2090 dans hbp.py
   - FONCTION : Initialise la structure pour suivre la performance de chaque méthode
   - FONCTIONNEMENT :
     * Détermine méthodes actives depuis config.initial_weights.keys()
     * Exclut 'markov' si use_markov_model=False
     * Pour chaque méthode active : initialise {'correct': 0, 'total': 0, 'accuracy_history': []}
     * Toujours inclut entrée 'markov' (même si désactivé) pour éviter erreurs
     * Logging debug des méthodes initialisées
   - RETOUR : None (initialise self.method_performance)
   - UTILITÉ : Préparation structure de suivi des performances par méthode
9. _models_are_trained.txt (HybridBaccaratPredictor._models_are_trained)
   - Lignes 9204-9222 dans hbp.py
   - FONCTION : Vérifie si les modèles ont été entraînés (thread-safe)
   - FONCTIONNEMENT :
     * Accès protégé avec model_lock
     * **LGBM entraîné :** calibrated_lgbm not None + hasattr('classes_')
     * **LSTM entraîné :** lstm not None + hasattr('trained') + getattr('trained', False)
     * Retourne True si au moins un des modèles principaux est entraîné
   - RETOUR : bool - True si LGBM ou LSTM entraîné
   - UTILITÉ : Vérification état d'entraînement avant prédictions
10. predict_with_lgbm.txt (HybridBaccaratPredictor.predict_with_lgbm)
   - Lignes 8298-8414 dans hbp.py (116 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LGBM calibré
   - PARAMÈTRES : feature (Optional[List[float]]) - feature pour la prédiction LGBM
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE :** si feature=None, retourne valeurs par défaut config
     * **ACCÈS THREAD-SAFE :** utilise model_lock pour accès au calibrated_lgbm
     * **GESTION PHASES :** détecte is_training et is_optuna_running pour logging adapté
     * **VÉRIFICATIONS MODÈLE :**
       - Si calibrated_lgbm=None : retourne valeurs par défaut avec logging throttlé
       - Si feature_scaler=None : retourne valeurs par défaut avec logging throttlé
       - Logging throttlé : évite spam avec log_warning_interval (défaut 60s)
     * **PRÉDICTION :**
       - check_is_fitted(model_to_use) pour validation état
       - Reshape feature en array (1, -1) et scaling via feature_scaler
       - predict_proba() avec système 0-based standard : 0=Player, 1=Banker
       - Normalisation probabilités si somme ≠ 1.0 (tolérance configurable)
     * **PROGRESSION :** appelle _update_prediction_progress() au lieu de logging
     * **GESTION ERREURS :**
       - NotFittedError : logging adapté selon phase (debug si training/optuna)
       - ValueError : erreur nombre features probablement
       - Exception générale : logging avec exc_info=True
     * **FALLBACK :** retourne {'player': 0.5, 'banker': 0.5} en cas d'erreur
   - RETOUR : Dict[str, float] - probabilités {'player': float, 'banker': float}
   - UTILITÉ : Interface robuste pour prédictions LGBM avec gestion complète erreurs et phases
11. predict_with_lstm.txt (HybridBaccaratPredictor.predict_with_lstm)
   - Lignes 8113-8225 dans hbp.py (112 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LSTM avec optimisation latence
   - PARAMÈTRES : lstm_features (Optional[np.ndarray]) - features LSTM pour la prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MESURE PERFORMANCE :** timing start_time pour monitoring latence
     * **GESTION PHASES :** détecte is_training et is_optuna_running pour logging adapté
     * **VALIDATION MODÈLE :** si lstm=None, retourne valeurs par défaut config
     * **VALIDATION FEATURES :**
       - Vérifie shape attendu (lstm_sequence_length, input_size)
       - Si invalide : fallback handle_short_sequence(self.sequence)
       - Si toujours invalide : retourne valeurs par défaut
     * **CACHE LSTM :**
       - Initialise lstm_cache={} si absent
       - Clé cache : hash(input_array.tobytes())
       - Si hit cache : retourne résultat immédiatement
       - Limite taille cache (lstm_cache_max_size, défaut 1000)
       - Suppression aléatoire si dépassement
     * **PRÉDICTION OPTIMISÉE :**
       - lstm.eval() + torch.no_grad() pour inférence
       - Conversion directe en tensor sans DataLoader (réduction latence)
       - Déplacement sur device approprié
       - torch.softmax(outputs, dim=1) pour probabilités
       - Système 0-based standard : 0=Player, 1=Banker
     * **CACHE STORAGE :** stocke résultat dans cache après prédiction
     * **PROGRESSION :** appelle _update_prediction_progress() au lieu de logging
     * **GESTION ERREURS :** logging avec timing et exc_info=True
   - RETOUR : Dict[str, float] - probabilités {'player': float, 'banker': float}
   - UTILITÉ : Interface optimisée pour prédictions LSTM avec cache et réduction latence
12. _get_cached_lgbm_prediction.txt (HybridBaccaratPredictor._get_cached_lgbm_prediction)
   - Lignes 8227-8296 dans hbp.py (69 lignes)
   - FONCTION : Récupère la prédiction LGBM depuis le cache ou la calcule si nécessaire
   - PARAMÈTRES : lgbm_feat (Optional[List[float]]) - feature pour la prédiction LGBM
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MESURE PERFORMANCE :** timing start_time pour monitoring latence
     * **VALIDATION :** si lgbm_feat=None, retourne {'player': 0.5, 'banker': 0.5}
     * **CACHE HYBRIDE :**
       - Convertit lgbm_feat en tuple pour clé dictionnaire
       - Initialise lgbm_cache_dict si absent (conversion depuis deque)
       - Migre données existantes de lgbm_cache (deque) vers dictionnaire
       - Recherche O(1) dans dictionnaire vs O(n) dans deque
     * **CACHE HIT :** si feat_tuple dans lgbm_cache_dict, retourne immédiatement
     * **CACHE MISS :**
       - Appelle predict_with_lgbm(lgbm_feat) pour calcul
       - Stocke dans lgbm_cache_dict[feat_tuple] et lgbm_cache.append()
       - Maintient cohérence entre dictionnaire (recherche) et deque (ordre)
     * **GESTION TAILLE :**
       - Limite lgbm_cache_max_size (défaut 1000)
       - Supprime entrée la plus ancienne (popleft) si dépassement
       - Synchronise suppression dans dictionnaire
     * **PROGRESSION :** appelle _update_prediction_progress() pour UI
     * **GESTION ERREURS :**
       - NotFittedError : logging adapté selon phase (debug si training/optuna)
       - Exception générale : logging avec timing et exc_info=True
   - RETOUR : Dict[str, float] - probabilités {'player': float, 'banker': float}
   - UTILITÉ : Cache optimisé pour prédictions LGBM avec recherche O(1) et gestion mémoire
13. _update_method_performance.txt - FICHIER NON TROUVÉ (méthode probablement intégrée ailleurs)

SOUS-DOSSIERS ET LEURS MÉTHODES :

SOUS-DOSSIER LGBM/ (4 fichiers) :

14. _extract_lgbm_features.txt (HybridBaccaratPredictor._extract_lgbm_features)
   - Lignes 13981-13992 dans hbp.py
   - FONCTION : Méthode interne pour extraire les features LGBM
   - PARAMÈTRES : sequence (List[str]) - séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT : Wrapper simple pour _create_lgbm_features(sequence)
   - RETOUR : List[float] - liste des features pour LGBM
   - UTILITÉ : Interface simplifiée pour extraction features LGBM

15. initialize_lgbm_cache.txt (HybridBaccaratPredictor.initialize_lgbm_cache)
   - Lignes 11734-11740 dans hbp.py
   - FONCTION : Initialise le cache des prédictions LGBM
   - PARAMÈTRES : maxlen (int, optionnel) - taille maximale du cache
   - FONCTIONNEMENT :
     * Utilise maxlen fourni ou config.lgbm_cache_maxlen (défaut 100)
     * Initialise self.lgbm_cache = deque(maxlen=maxlen)
     * Initialise self.lgbm_cache_dict = {} pour accès rapide O(1)
     * Logging de la taille maximale configurée
   - RETOUR : None
   - UTILITÉ : Configuration du système de cache hybride deque+dict pour LGBM

- _get_cached_lgbm_prediction.txt - Récupération prédiction LGBM depuis cache (ANALYSÉ ci-dessus #12)
- predict_with_lgbm.txt - Prédiction avec modèle LGBM (ANALYSÉ ci-dessus #10)

SOUS-DOSSIER LSTM/ (4 fichiers) :

16. _extract_lstm_features.txt (HybridBaccaratPredictor._extract_lstm_features)
   - Lignes 13994-14024 dans hbp.py (30 lignes)
   - FONCTION : Méthode interne pour extraire les features LSTM
   - PARAMÈTRES : sequence (List[str]) - séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT :
     * Utilise config.lstm_sequence_length pour longueur cible
     * Appelle create_lstm_sequence_features(sequence, lstm_sequence_length)
     * **Fallback robuste :** si None ou erreur, crée matrice zéros
     * **Logging détaillé :** longueur séquence, shape résultat, erreurs
     * **Gestion erreurs :** try/except avec exc_info=True
     * Matrice fallback : np.zeros((lstm_sequence_length, config.lstm_input_size))
   - RETOUR : np.ndarray - matrice de features pour LSTM
   - UTILITÉ : Wrapper robuste pour extraction features LSTM avec fallback sécurisé

17. create_lstm_sequence_features.txt (HybridBaccaratPredictor.create_lstm_sequence_features)
   - Lignes 13570-13745 dans hbp.py (175 lignes)
   - FONCTION : Crée une matrice de features pour le modèle LSTM à partir d'une séquence
   - PARAMÈTRES : sequence (List[str]), keep_history_length (int, optionnel)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Fenêtre adaptative :** utilise toute la séquence disponible avec taille sortie fixe
     * **12 FEATURES CALCULÉES :**
       1. Position relative normalisée dans séquence
       2. Est banker (1) ou player (0) - système 0-based standard
       3. Ratio cumulatif banker
       4. Ratio cumulatif player
       5. Est répétition du résultat précédent
       6. Banker count dans N derniers coups (normalisé)
       7. Longueur streak actuel (normalisé par streak_norm_factor)
       8. Type streak actuel (banker=1, player=0)
       9. Ratio d'alternance
       10. Phase du jeu (début/milieu/fin)
       11. Déséquilibre banker-player récent
       12. Proximité du dernier changement de streak
     * **Configuration :** use_adaptive_window, streak_norm_factor, recent_window_size, etc.
     * **Gestion taille :** padding au début si séquence courte, échantillonnage si longue
     * **Matrice sortie :** (keep_history_length, lstm_input_size) avec dtype=float32
   - RETOUR : Optional[np.ndarray] - matrice features ou None si erreur
   - UTILITÉ : Cœur de l'extraction features LSTM avec 12 features sophistiquées

18. handle_short_sequence.txt (HybridBaccaratPredictor.handle_short_sequence)
   - Lignes 5107-5233 dans hbp.py (126 lignes)
   - FONCTION : Gère les séquences trop courtes pour LSTM avec padding intelligent
   - PARAMÈTRES : sequence (Optional[List[str]]) - séquence potentiellement courte ou None
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Cas 1 - Séquence vide/None :** retourne np.zeros((target_length, num_features))
     * **Cas 2 - Séquence longue :** utilise create_hybrid_features() normalement
     * **Cas 3 - Séquence courte :** applique pré-padding + génération features manuelles
     * **FEATURES GÉNÉRÉES (12) :**
       1. Position normalisée globale (avec padding)
       2. Est banker (1) ou player (0)
       3. Ratio banker cumulatif
       4. Ratio player cumulatif
       5. Est répétition du résultat précédent
       6. Nombre banker dans 3 derniers coups
       7. Déséquilibre banker-player
       8. Longueur streak actuel
       9. Position paire/impaire
       10-12. Features Optuna par défaut (0.5)
     * **Validation :** vérification shape finale, fallback zeros si erreur
     * **Padding :** pré-padding avec zeros, puis features réelles
   - RETOUR : np.ndarray - shape (lstm_sequence_length, lstm_input_size) garantie
   - UTILITÉ : Gestion robuste séquences courtes avec padding intelligent et fallback sécurisé
- predict_with_lstm.txt - Prédiction avec modèle LSTM (ANALYSÉ ci-dessus #11)

SOUS-DOSSIER Markov/ (1 fichier) :

19. analyze_context_sensitivity.txt (HybridBaccaratPredictor.analyze_context_sensitivity)
   - Lignes 8669-8793 dans hbp.py (124 lignes)
   - FONCTION : Analyse la sensibilité contextuelle de la prédiction Markov
   - PARAMÈTRES : sequence (List[str]), prob_banker (float) - séquence et probabilité prédite
   - FONCTIONNEMENT DÉTAILLÉ :
     * **Validation :** vérifie sequence valide, longueur ≥ 5, éléments 'player'/'banker'
     * **Variations créées :**
       - Variation 1 : inverse avant-dernier élément
       - Variation 2 : inverse troisième dernier élément
     * **Vérifications Markov :**
       - use_markov_model activé
       - self.markov initialisé et non None
       - Séquence suffisamment longue pour max_order
     * **Prédictions variations :** markov.get_combined_probs() avec paramètres config
     * **Calcul sensibilité :**
       - Différence moyenne : abs(prob_banker - var_prob)
       - Normalisation : avg_diff * sensitivity_normalization_factor
       - Clipping : uncertainty_min_clip à uncertainty_max_clip
     * **Fallbacks :** retourne 0.5 si Markov désactivé/invalide ou erreurs
     * **Thread-safety :** utilise markov_lock pour accès concurrent
   - RETOUR : float - score sensibilité 0-1 (0=stable, 1=très sensible)
   - UTILITÉ : Évalue stabilité prédictions Markov face aux variations contextuelles

RÉSUMÉ : Ce dossier centralise tous les modèles de prédiction et leur coordination. Il gère les prédictions individuelles, la combinaison hybride, l'évaluation des performances, et la logique de décision pour les recommandations WAIT/NON-WAIT. Les sous-dossiers contiennent 9 fichiers supplémentaires spécialisés par type de modèle.
