# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 13981 à 13992
# Type: Méthode de la classe HybridBaccaratPredictor

    def _extract_lgbm_features(self, sequence: List[str]) -> List[float]:
        """
        Méthode interne pour extraire les features LGBM.
        Wrapper pour _create_lgbm_features.

        Args:
            sequence (List[str]): Sé<PERSON> de résultats ('player', 'banker')

        Returns:
            List[float]: Liste des features pour LGBM
        """
        return self._create_lgbm_features(sequence)