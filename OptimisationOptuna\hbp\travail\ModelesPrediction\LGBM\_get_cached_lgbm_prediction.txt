# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 8227 à 8296
# Type: Méthode de la classe HybridBaccaratPredictor

    def _get_cached_lgbm_prediction(self, lgbm_feat: Optional[List[float]]) -> Dict[str, float]:
        """
        Récupère la prédiction LGBM depuis le cache ou la calcule si nécessaire.

        Args:
            lgbm_feat (Optional[List[float]]): Feature pour la prédiction LGBM.

        Returns:
            Dict[str, float]: Prédiction LGBM (probabilités pour 'player' et 'banker').
        """
        # Importer NotFittedError ici pour éviter les problèmes d'importation circulaire
        from sklearn.exceptions import NotFittedError
        import time

        # Mesurer le temps d'exécution
        start_time = time.time()

        if lgbm_feat is None:
            return {'player': 0.5, 'banker': 0.5}

        # Convertir en tuple pour pouvoir l'utiliser comme clé de dictionnaire
        feat_tuple = tuple(lgbm_feat)

        # Vérifier si nous avons un cache LGBM sous forme de dictionnaire
        if not hasattr(self, 'lgbm_cache_dict'):
            # Convertir le cache existant en dictionnaire pour des recherches plus rapides
            self.lgbm_cache_dict = {}
            for feat, pred in self.lgbm_cache:
                self.lgbm_cache_dict[tuple(feat)] = pred
            logger.info("Cache LGBM converti en dictionnaire pour des recherches plus rapides")

        # Vérifie si la prédiction est déjà dans le cache
        if feat_tuple in self.lgbm_cache_dict:
            cached_pred = self.lgbm_cache_dict[feat_tuple]
            # Mettre à jour la progression au lieu d'afficher un log
            self._update_prediction_progress()
            return cached_pred

        # Si la prédiction n'est pas dans le cache, la calculer
        # Mettre à jour la progression au lieu d'afficher un log
        self._update_prediction_progress()
        try:
            lgbm_pred = self.predict_with_lgbm(lgbm_feat)

            # Ajouter la prédiction au cache
            self.lgbm_cache_dict[feat_tuple] = lgbm_pred
            self.lgbm_cache.append((lgbm_feat, lgbm_pred))

            # Limiter la taille du cache
            # Récupérer la taille maximale du cache depuis la configuration
            max_cache_size = getattr(self.config, 'lgbm_cache_max_size', 1000)
            if len(self.lgbm_cache) > max_cache_size:
                # Supprimer l'entrée la plus ancienne
                oldest_feat, _ = self.lgbm_cache.popleft()
                del self.lgbm_cache_dict[tuple(oldest_feat)]

            # Pas besoin de mettre à jour la progression ici, car elle a déjà été mise à jour avant le calcul
            return lgbm_pred
        except NotFittedError as e:
            elapsed = time.time() - start_time
            # Vérifier si nous sommes en phase d'entraînement ou d'optimisation Optuna
            is_training_phase = getattr(self, '_is_training', False)
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            log_method = logger.debug if (is_training_phase or is_optuna_phase) else logger.warning
            log_method(f"Modèle LGBM non entraîné dans _get_cached_lgbm_prediction (temps: {elapsed*1000:.1f}ms): {e}")
            return {'player': 0.5, 'banker': 0.5}
        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"Erreur lors de la prédiction LGBM dans _get_cached_lgbm_prediction (temps: {elapsed*1000:.1f}ms): {e}", exc_info=True)
            return {'player': 0.5, 'banker': 0.5}