# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 13994 à 14024
# Type: Méthode de la classe HybridBaccaratPredictor

    def _extract_lstm_features(self, sequence: List[str]) -> np.ndarray:
        """
        Méthode interne pour extraire les features LSTM.
        Wrapper pour create_lstm_sequence_features.

        Args:
            sequence (List[str]): Séquence de résultats ('player', 'banker')

        Returns:
            np.ndarray: Matrice de features pour LSTM
        """
        # Utiliser la longueur de séquence LSTM configurée
        lstm_sequence_length = self.config.lstm_sequence_length

        # Ajouter des logs pour déboguer
        logger.info(f"_extract_lstm_features: Séquence de longueur {len(sequence)}, lstm_sequence_length={lstm_sequence_length}")

        # Appeler la méthode avec gestion d'erreur
        try:
            features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)
            if features is None:
                logger.error("_extract_lstm_features: create_lstm_sequence_features a retourné None")
                # Créer une matrice de zéros comme fallback
                features = np.zeros((lstm_sequence_length, self.config.lstm_input_size), dtype=np.float32)
            else:
                logger.info(f"_extract_lstm_features: Features créées avec succès, shape={features.shape}")
            return features
        except Exception as e:
            logger.error(f"_extract_lstm_features: Erreur lors de la création des features LSTM: {e}", exc_info=True)
            # Créer une matrice de zéros comme fallback
            return np.zeros((lstm_sequence_length, self.config.lstm_input_size), dtype=np.float32)