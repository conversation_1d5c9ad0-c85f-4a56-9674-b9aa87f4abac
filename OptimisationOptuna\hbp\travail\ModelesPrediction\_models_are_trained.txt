# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 9204 à 9222
# Type: Méthode de la classe HybridBaccaratPredictor

                def _models_are_trained(self) -> bool:
                    """
                    Vérifie si les modèles ont été entraînés.

                    Returns:
                        bool: True si au moins un des modèles principaux (LGBM, LSTM) a été entraîné, False sinon.
                    """
                    with self.model_lock:
                        # Vérifier si LGBM est entraîné
                        lgbm_trained = (self.calibrated_lgbm is not None and
                                      hasattr(self.calibrated_lgbm, 'classes_'))

                        # Vérifier si LSTM est entraîné
                        lstm_trained = (self.lstm is not None and
                                      hasattr(self.lstm, 'trained') and
                                      getattr(self.lstm, 'trained', False))

                        # Considérer les modèles comme entraînés si au moins un des modèles principaux est entraîné
                        return lgbm_trained or lstm_trained