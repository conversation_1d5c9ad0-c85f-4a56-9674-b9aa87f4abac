DESCRIPTIF DÉTAILLÉ DES MÉTHODES - RÉSEAUX NEURONAUX
================================================================================

Ce fichier contient la description détaillée des méthodes relatives aux réseaux
de neurones du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : RÉSEAUX NEURONAUX
Méthodes relatives aux réseaux de neurones LSTM, prédictions et modèles
de deep learning pour l'analyse séquentielle.

TOTAL : 3 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES RÉSEAUX NEURONAUX
================================================================================

1. predict_with_lgbm.txt (HybridBaccaratPredictor.predict_with_lgbm - Prédiction LGBM calibrée)
   - Lignes 8298-8414 dans hbp.py (117 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LGBM calibré avec gestion complète des erreurs et logging intelligent
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * feature (Optional[List[float]]) - Vecteur de features pour la prédiction LGBM
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURE :** Vérifie que le vecteur de features n'est pas None
     * **PROTECTION MODÈLE :** Utilise model_lock pour accès thread-safe au modèle calibré
     * **DÉTECTION PHASE :** Identifie si en phase d'entraînement ou optimisation Optuna pour adapter le logging
     * **VÉRIFICATION MODÈLE :** Contrôle que le modèle calibré et le scaler sont initialisés et fittés
     * **NORMALISATION FEATURES :** Applique le feature_scaler pour normaliser les données d'entrée
     * **PRÉDICTION PROBABILISTE :** Utilise predict_proba pour obtenir les probabilités Player/Banker
     * **NORMALISATION PROBABILITÉS :** Vérifie et normalise la somme des probabilités si nécessaire
     * **GESTION ERREURS :** Traite NotFittedError, ValueError et autres exceptions avec logging adaptatif
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' (défaut 0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LGBM robustes avec gestion intelligente des cas d'erreur et logging adapté au contexte

2. predict_with_lstm.txt (HybridBaccaratPredictor.predict_with_lstm - Prédiction LSTM optimisée)
   - Lignes 8113-8225 dans hbp.py (113 lignes)
   - FONCTION : Effectue une prédiction LSTM avec approche optimisée pour réduire la latence et gestion robuste des erreurs
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lstm_features (Optional[np.ndarray]) - Features LSTM préparées ou None pour génération automatique
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** Vérifie `if not hasattr(self, 'lstm_model') or self.lstm_model is None:` avec logging adaptatif selon phase optimisation
     * **GESTION FEATURES :** Si `lstm_features is None`, appelle `lstm_features = self.handle_short_sequence(self.sequence)` pour génération automatique
     * **VALIDATION SHAPE :** Contrôle `if lstm_features.shape != (self.config.lstm_sequence_length, self.config.lstm_input_size):` avec reshape si nécessaire
     * **OPTIMISATION BATCH :** Ajoute dimension batch avec `lstm_features_batch = np.expand_dims(lstm_features, axis=0)` pour compatibilité TensorFlow
     * **PRÉDICTION TENSORFLOW :** Exécute `predictions = self.lstm_model.predict(lstm_features_batch, verbose=0)` avec gestion erreurs
     * **EXTRACTION PROBABILITÉS :** Récupère `prob_banker = float(predictions[0][1])` et calcule `prob_player = 1.0 - prob_banker`
     * **VALIDATION BORNES :** Applique `np.clip(prob_banker, 0.0, 1.0)` et `np.clip(prob_player, 0.0, 1.0)` pour sécurité numérique
     * **GESTION ERREURS :** Capture `NotFittedError` et exceptions générales avec fallback vers `{'player': 0.5, 'banker': 0.5}`
     * **LOGGING CONDITIONNEL :** Évite spam logs pendant optimisation Optuna avec vérification `is_optuna_running` et `optimization_stats_collector`
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' normalisées (0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LSTM robustes avec gestion intelligente des cas d'erreur et optimisation performance

3. hybrid_prediction.txt (HybridBaccaratPredictor.hybrid_prediction - Prédiction hybride avancée)
   - Lignes 8795-9937 dans hbp.py (1143 lignes)
   - FONCTION : Effectue une prédiction hybride sophistiquée combinant Markov, LGBM et LSTM avec pondération bayésienne et calcul d'incertitude multi-niveaux
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lgbm_feat (Optional[List[float]]) - Features pour le modèle LGBM
     * lstm_feat (Optional[np.ndarray]) - Features pour le modèle LSTM
     * optimization_phase (Optional[int]) - Phase d'optimisation (1=équilibre WAIT/NON-WAIT, 2=recommandations consécutives, None=normal)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PRÉDICTIONS INDIVIDUELLES :** Collecte prédictions Markov, LGBM et LSTM avec gestion d'erreurs robuste
     * **VALIDATION MODÈLES :** Vérifie quels modèles sont entraînés et disponibles pour la combinaison
     * **PONDÉRATION BAYÉSIENNE :** Calcule poids adaptatifs basés sur performance historique et confiance
     * **COMBINAISON PONDÉRÉE :** Fusionne prédictions avec poids effectifs ajustés par confiance
     * **INCERTITUDE ÉPISTÉMIQUE :** Mesure désaccord entre modèles pour évaluer fiabilité
     * **INCERTITUDE ALÉATOIRE :** Calcule entropie de la prédiction finale
     * **SENSIBILITÉ CONTEXTUELLE :** Analyse adaptation aux patterns de séquence
     * **CONFIANCE CONSÉCUTIVE :** Utilise calculateur spécialisé pour manches 31-60
     * **SYSTÈME DÉCISION :** Détermine recommandation finale avec seuils adaptatifs
   - RETOUR : Dict - Dictionnaire complet avec prédictions, recommandation, incertitudes et métriques détaillées
   - UTILITÉ : Cœur du système de prédiction avec intelligence artificielle avancée et gestion d'incertitude sophistiquée
