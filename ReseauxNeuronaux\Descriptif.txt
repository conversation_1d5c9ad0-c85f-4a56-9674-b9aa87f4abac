DESCRIPTIF DÉTAILLÉ DES MÉTHODES - RÉSEAUX NEURONAUX
================================================================================

Ce fichier contient la description détaillée des méthodes relatives aux réseaux
de neurones du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : RÉSEAUX NEURONAUX
Méthodes relatives aux réseaux de neurones LSTM, prédictions et modèles
de deep learning pour l'analyse séquentielle.

TOTAL : 2 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES RÉSEAUX NEURONAUX
================================================================================

1. predict_with_lgbm.txt (HybridBaccaratPredictor.predict_with_lgbm - Prédiction LGBM calibrée)
   - Lignes 8298-8414 dans hbp.py (117 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LGBM calibré avec gestion complète des erreurs et logging intelligent
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * feature (Optional[List[float]]) - Vecteur de features pour la prédiction LGBM
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURE :** Vérifie que le vecteur de features n'est pas None
     * **PROTECTION MODÈLE :** Utilise model_lock pour accès thread-safe au modèle calibré
     * **DÉTECTION PHASE :** Identifie si en phase d'entraînement ou optimisation Optuna pour adapter le logging
     * **VÉRIFICATION MODÈLE :** Contrôle que le modèle calibré et le scaler sont initialisés et fittés
     * **NORMALISATION FEATURES :** Applique le feature_scaler pour normaliser les données d'entrée
     * **PRÉDICTION PROBABILISTE :** Utilise predict_proba pour obtenir les probabilités Player/Banker
     * **NORMALISATION PROBABILITÉS :** Vérifie et normalise la somme des probabilités si nécessaire
     * **GESTION ERREURS :** Traite NotFittedError, ValueError et autres exceptions avec logging adaptatif
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' (défaut 0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LGBM robustes avec gestion intelligente des cas d'erreur et logging adapté au contexte

2. predict_with_lstm.txt (HybridBaccaratPredictor.predict_with_lstm - Prédiction LSTM optimisée)
   - Lignes 8113-8225 dans hbp.py (113 lignes)
   - FONCTION : Effectue une prédiction LSTM avec approche optimisée pour réduire la latence et cache intelligent
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lstm_features (Optional[np.ndarray]) - Features LSTM préparées ou None pour génération automatique
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** Vérifie que le modèle LSTM est initialisé avec logging adaptatif
     * **GESTION FEATURES :** Utilise features fournies ou génère via handle_short_sequence si nécessaire
     * **VALIDATION SHAPE :** Contrôle que les features ont la forme attendue (sequence_length, input_size)
     * **CACHE INTELLIGENT :** Utilise hash des features comme clé pour éviter recalculs identiques
     * **PRÉDICTION DIRECTE :** Évite DataLoader pour réduire latence, utilise torch.no_grad()
     * **GESTION DEVICE :** Déplace automatiquement les tensors sur le device approprié (CPU/GPU)
     * **SOFTMAX PROBABILITÉS :** Applique softmax pour obtenir probabilités normalisées
     * **GESTION CACHE :** Limite taille cache et supprime entrées anciennes si nécessaire
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' (0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LSTM rapides avec cache pour optimiser les performances en temps réel
