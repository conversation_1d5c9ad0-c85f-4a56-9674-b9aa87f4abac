DESCRIPTIF DÉTAILLÉ DES MÉTHODES - RÉSEAUX DE NEURONES
================================================================================

Ce fichier contient la description détaillée des méthodes liées aux réseaux de neurones
du système ML, incluant LSTM avancé, mécanismes d'attention et architectures spécialisées.

DOMAINE FONCTIONNEL : Réseaux de neurones avancés avec LSTM, attention et optimisations

TOTAL : 8 MÉTHODES ANALYSÉES

================================================================================



1. forward.txt (AttentionLayer.forward - MÉTHODE FORWARD ATTENTION)
   - Lignes 52-60 dans utils.py (9 lignes)
   - FONCTION : Calcule vecteur contexte pondéré par mécanisme d'attention sur sorties LSTM
   - PARAMÈTRES :
     * self - Instance de la classe AttentionLayer
     * lstm_output (torch.Tensor) - Sorties LSTM shape (batch_size, seq_len, hidden_size)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL POIDS :** Applique couche linéaire attention puis softmax sur dimension temporelle
     * **NORMALISATION :** Softmax assure somme des poids = 1 pour chaque séquence
     * **AGRÉGATION :** Multiplie poids d'attention par lstm_output et somme sur seq_len
     * **VECTEUR CONTEXTE :** Produit représentation agrégée focalisée sur éléments importants
   - RETOUR : Tuple[torch.Tensor, torch.Tensor] - (context_vector, attention_weights)
   - UTILITÉ : Mécanisme central d'attention pour focus adaptatif sur informations temporelles pertinentes

2. forward_1.txt (AdvancedLSTM.forward - MÉTHODE FORWARD LSTM AVANCÉ - DOUBLON 1)
   - Lignes 108-146 dans utils.py (39 lignes)
   - FONCTION : Propagation avant complète du réseau LSTM avec attention et connexions résiduelles
   - PARAMÈTRES :
     * self - Instance de la classe AdvancedLSTM
     * x (torch.Tensor) - Données d'entrée shape (batch_size, seq_len, input_size)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DROPOUT ENTRÉE :** Applique dropout_in pour régularisation des données d'entrée
     * **INITIALISATION ÉTATS :** Crée h0 et c0 zéros avec dimensions appropriées pour LSTM
     * **PROPAGATION LSTM :** Passe données à travers LSTM bidirectionnel avec états initiaux
     * **MÉCANISME ATTENTION :** Utilise attention si activé, sinon prend dernière sortie LSTM
     * **COUCHE FC1 :** Applique transformation linéaire + BatchNorm + ReLU sur vecteur contexte
     * **CONNEXION RÉSIDUELLE :** Ajoute context_vector à fc1_out si dimensions compatibles
     * **DROPOUT SORTIE :** Applique dropout_out avant classification finale
     * **CLASSIFICATION :** Couche fc2 produit logits pour 2 classes (banker/player)
   - RETOUR : torch.Tensor - Logits de classification shape (batch_size, 2)
   - UTILITÉ : Pipeline complet de prédiction avec optimisations avancées pour performance maximale

3. forward_2.txt (FocalLoss.forward - MÉTHODE FORWARD PERTE FOCALE - DOUBLON 2)
   - Lignes 344-364 dans utils.py (21 lignes)
   - FONCTION : Calcule perte focale avec pondération adaptative pour exemples difficiles
   - PARAMÈTRES :
     * self - Instance de la classe FocalLoss
     * inputs (torch.Tensor) - Logits du modèle shape (batch_size, num_classes)
     * targets (torch.Tensor) - Étiquettes vraies shape (batch_size,)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PERTE BASE :** Calcule CrossEntropyLoss avec réduction='none' pour traitement individuel
     * **PROBABILITÉS :** Calcule pt = exp(-ce_loss) pour probabilités prédites des classes cibles
     * **FACTEUR FOCAL :** Applique (1-pt)^gamma pour amplifier perte sur exemples difficiles
     * **LIMITATION :** Limite focal_weight à max_focal_weight=4.0 pour éviter instabilité
     * **PONDÉRATION :** Multiplie focal_weight par ce_loss pour perte focale finale
     * **MOYENNAGE :** Retourne moyenne des pertes focales sur le batch
   - RETOUR : torch.Tensor - Perte focale scalaire moyennée
   - UTILITÉ : Améliore focus apprentissage sur exemples mal classés tout en préservant stabilité

4. __init__.txt (AttentionLayer.__init__ - CONSTRUCTEUR ATTENTION)
   - Lignes 47-50 dans utils.py (4 lignes)
   - FONCTION : Initialise mécanisme d'attention avec couche linéaire pour calcul des poids
   - PARAMÈTRES :
     * self - Instance de la classe AttentionLayer
     * hidden_size (int) - Taille des états cachés LSTM pour dimensionnement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **APPEL PARENT :** super(AttentionLayer, self).__init__() pour héritage nn.Module
     * **STOCKAGE TAILLE :** self.hidden_size = hidden_size pour référence interne
     * **COUCHE ATTENTION :** nn.Linear(hidden_size, 1) pour projection vers scores d'attention
     * **ARCHITECTURE :** Transformation linéaire simple pour efficacité computationnelle
   - RETOUR : None (constructeur)
   - UTILITÉ : Initialisation légère et efficace du mécanisme d'attention pour LSTM

5. __init___1.txt (AdvancedLSTM.__init__ - CONSTRUCTEUR LSTM AVANCÉ - DOUBLON 1)
   - Lignes 66-106 dans utils.py (41 lignes)
   - FONCTION : Initialise architecture LSTM complète avec configuration flexible et optimisations avancées
   - PARAMÈTRES :
     * self - Instance de la classe AdvancedLSTM
     * config - Configuration contenant tous paramètres LSTM et entraînement
     * input_size (int, optionnel) - Taille entrée (défaut depuis config.lstm_input_size=12)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **APPEL PARENT :** super(AdvancedLSTM, self).__init__() pour héritage nn.Module
     * **EXTRACTION CONFIG :** Récupère hidden_size=320, num_layers=2, bidirectional=True depuis config
     * **CONFIGURATION DROPOUT :** dropout_input=0.1, dropout_hidden=0.2, dropout_output=0.15
     * **FACTEUR DIRECTION :** direction_factor=2 si bidirectionnel, 1 sinon pour dimensionnement
     * **COUCHES DROPOUT :** Crée nn.Dropout pour entrée et sortie avec taux configurés
     * **LSTM PRINCIPAL :** nn.LSTM avec batch_first=True, dropout entre couches si num_layers>1
     * **ATTENTION CONDITIONNELLE :** AttentionLayer si use_attention=True depuis config
     * **COUCHES CLASSIFICATION :** fc1 (hidden*direction → hidden) + BatchNorm + fc2 (→ 2 classes)
   - RETOUR : None (constructeur)
   - UTILITÉ : Configuration complète architecture LSTM avec toutes optimisations pour performance maximale
