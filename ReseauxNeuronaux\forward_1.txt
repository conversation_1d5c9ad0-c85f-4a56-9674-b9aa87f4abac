# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 108 à 146
# Type: Méthode de la classe AdvancedLSTM
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        batch_size = x.size(0)

        # Appliquer dropout à l'entrée
        x = self.dropout_in(x)

        # Initialiser les états cachés
        h0 = torch.zeros(self.num_layers * self.direction_factor, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers * self.direction_factor, batch_size, self.hidden_size).to(x.device)

        # Passer à travers LSTM
        lstm_out, _ = self.lstm(x, (h0, c0))
        # lstm_out shape: (batch_size, seq_len, hidden_size * direction_factor)

        # Appliquer l'attention si activée
        if self.use_attention:
            context_vector, _ = self.attention(lstm_out)
            # context_vector shape: (batch_size, hidden_size * direction_factor)
        else:
            # Utiliser la dernière sortie si pas d'attention
            context_vector = lstm_out[:, -1, :]

        # Première couche fully connected avec batch normalization
        fc1_out = self.fc1(context_vector)
        fc1_out = self.bn1(fc1_out)
        fc1_out = F.relu(fc1_out)

        # Connexion résiduelle si activée et dimensions compatibles
        if self.use_residual and context_vector.size(1) == fc1_out.size(1):
            fc1_out = fc1_out + context_vector

        # Dropout avant la couche de sortie
        fc1_out = self.dropout_out(fc1_out)

        # Couche de sortie
        output = self.fc2(fc1_out)

        return output