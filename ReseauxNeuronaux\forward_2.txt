# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 344 à 364
# Type: Méthode de la classe FocalLoss
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

            def forward(self, inputs, targets):
                # Calculer la perte d'entropie croisée standard
                ce_loss = self.ce_loss(inputs, targets)

                # Calculer les probabilités prédites pour les classes cibles
                pt = torch.exp(-ce_loss)

                # Appliquer un facteur focal modifié pour réduire l'impact sur la val_loss
                # Utiliser une version plus douce du facteur focal pour les exemples très difficiles
                # Cela aide à réduire la val_loss tout en maintenant l'attention sur les exemples difficiles
                focal_weight = ((1 - pt) ** self.gamma)

                # Limiter l'impact maximal du facteur focal pour éviter des pertes extrêmes
                # qui peuvent déstabiliser l'entraînement et augmenter la val_loss
                max_focal_weight = 4.0  # Limite l'amplification maximale
                focal_weight = torch.min(focal_weight, torch.tensor(max_focal_weight, device=focal_weight.device))

                focal_loss = focal_weight * ce_loss

                # Retourner la moyenne
                return focal_loss.mean()