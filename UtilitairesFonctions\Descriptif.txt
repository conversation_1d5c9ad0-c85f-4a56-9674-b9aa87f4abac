DESCRIPTIF DÉTAILLÉ DES MÉTHODES - UTILITAIRES FONCTIONS
================================================================================

Ce fichier contient la description détaillée des fonctions utilitaires
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : UTILITAIRES FONCTIONS
Fonctions utilitaires, helpers et méthodes de support pour le système.

TOTAL : 26 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES UTILITAIRES FONCTIONS
================================================================================

1. __init__.txt (HybridBaccaratPredictor.__init__ - Constructeur principal de la classe)
   - Lignes 541-779 dans hbp.py (239 lignes)
   - FONCTION : Initialise une instance de HybridBaccaratPredictor avec configuration complète des modèles ML, interface utilisateur et gestion des ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * root_or_config (Union[tk.Tk, PredictorConfig]) - Objet root Tkinter pour mode UI ou objet PredictorConfig pour mode Optuna
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OPTIMISATION MÉMOIRE :** Optimise l'utilisation mémoire PyTorch avant toute opération
     * **CONFIGURATION LOGGER :** Initialise le système de logging avec handlers console et fichier
     * **DÉTECTION MODE :** Détermine si c'est un mode UI (Tkinter) ou mode Optuna (sans UI)
     * **INITIALISATION ÉTAT :** Configure les attributs d'état (séquences, historique, cache LGBM)
     * **MODÈLES ML :** Initialise les placeholders pour LGBM, LSTM, optimiseurs et schedulers
     * **MODÈLE MARKOV :** Configure le modèle PersistentMarkov avec paramètres adaptatifs
     * **GESTION PERFORMANCE :** Initialise le suivi des performances et poids des méthodes
     * **VERROUS THREADING :** Crée les verrous pour accès concurrent sécurisé
     * **CONFIGURATION DEVICE :** Détecte et configure l'utilisation CPU/GPU
     * **INTERFACE UTILISATEUR :** Configure l'UI Tkinter si en mode interface
     * **CHARGEMENT AUTO :** Tente le chargement automatique de l'état précédent
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Point d'entrée principal pour créer une instance fonctionnelle du système de prédiction

2. safe_record_outcome.txt (HybridBaccaratPredictor.safe_record_outcome - Enregistrement sécurisé résultat)
   - Lignes 12344-12486 dans hbp.py (143 lignes)
   - FONCTION : Enregistre le résultat d'une manche de manière thread-safe avec limite 60 manches et auto-update
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * outcome (str) - Résultat de la manche ('player' ou 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION LIMITE :** Contrôle limite 60 manches avant enregistrement
     * **ACQUISITION VERROUS :** Prend tous les verrous nécessaires pour cohérence thread-safe
     * **MISE À JOUR SÉQUENCE :** Ajoute outcome à la séquence et calcule numéro manche
     * **AUTO-UPDATE :** Appelle auto_fast_update_if_needed pour manches cibles
     * **MISE À JOUR MARKOV :** Met à jour modèle Markov session avec nouvelle séquence
     * **PATTERNS :** Met à jour compteurs de patterns avec nouveau résultat
     * **PRÉDICTION SUIVANTE :** Génère features et effectue hybrid_prediction pour coup suivant
     * **MISE À JOUR POIDS :** Ajuste poids méthodes basé sur prédiction précédente vs résultat actuel
     * **CONFIANCE CONSÉCUTIVE :** Met à jour calculateur pour manches 31-60
     * **PLANIFICATION UI :** Programme mises à jour interface via root.after
   - RETOUR : None - Méthode d'enregistrement ne retourne rien
   - UTILITÉ : Cœur du système de traitement des résultats avec gestion complète de l'état et optimisations avec tous les composants initialisés

3. undo_last_move.txt (HybridBaccaratPredictor.undo_last_move - Annulation dernier coup)
   - Lignes 10780-10893 dans hbp.py (114 lignes)
   - FONCTION : Annule le dernier coup enregistré de manière thread-safe avec restauration complète état précédent et mise à jour UI
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
     * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
     * **VALIDATION SÉQUENCE :** Teste `if not self.sequence:` avec `messagebox.showwarning("Annulation Impossible", "La séquence de jeu est vide.")` et retour
     * **CONFIRMATION UTILISATEUR :** Si UI disponible, affiche `messagebox.askyesno("Confirmation", "Voulez-vous vraiment annuler la dernière manche enregistrée ?")` pour validation
     * **SUPPRESSION SÉQUENCE :** Exécute `last_outcome = self.sequence.pop()` pour retirer dernier élément avec sauvegarde valeur
     * **NETTOYAGE HISTORIQUE :** Si `self.prediction_history:`, supprime `removed_prediction = self.prediction_history.pop()` pour cohérence prédictions
     * **MISE À JOUR PATTERNS :** Si `len(self.sequence) >= 3:` :
       - Calcule `pattern_to_decrement = tuple(self.sequence[-3:] + [last_outcome])` pour motif à décrémenter
       - Décrémente `self.pattern_counts[dict_key][pattern_to_decrement] -= 1` et supprime si `<= 0`
     * **RÉINITIALISATION MARKOV :** Si `self.markov:`, appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul
     * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation cache
     * **AJUSTEMENT INDEX :** Si `len(self.sequence) < self.last_incremental_update_index:`, remet `self.last_incremental_update_index = 0` pour mise à jour complète
     * **NOUVELLE PRÉDICTION :** Appelle `lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)` puis `current_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)` pour état actuel
     * **MISE À JOUR UI :** Si UI disponible, planifie `self.root.after(0, lambda p=pred_copy: self.lightweight_update_display(p))` et `self.root.after(10, self.update_display)` pour rafraîchissement
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error()` et `messagebox.showerror()` pour feedback utilisateur
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Correction sécurisée erreurs saisie avec restauration complète état système, synchronisation modèles et interface responsive

4. unified_save.txt (HybridBaccaratPredictor.unified_save - Sauvegarde unifiée)
   - Lignes 10895-10978 dans hbp.py (84 lignes)
   - FONCTION : Sauvegarde unifiée de l'état complet du système avec validation et gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * save_path (str, optionnel) - Chemin de sauvegarde personnalisé (défaut: None pour auto-génération)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
     * **GÉNÉRATION CHEMIN :** Si `save_path is None:`, génère `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` puis `save_path = f"hbp_save_{timestamp}.joblib"` pour nom unique
     * **CONFIRMATION UTILISATEUR :** Si UI disponible, affiche `messagebox.askyesno("Confirmation de Sauvegarde", f"Sauvegarder l'état actuel vers:\n{save_path}")` pour validation
     * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
     * **COLLECTE ÉTAT :** Appelle `state_to_save = self._collect_state_for_save()` pour rassembler toutes données système
     * **VALIDATION ÉTAT :** Vérifie `if state_to_save is None:` avec logging erreur et messagebox si échec collecte
     * **SAUVEGARDE FICHIER :** Utilise `joblib.dump(state_to_save, save_path, compress=3)` avec compression niveau 3 pour optimisation espace
     * **VALIDATION FICHIER :** Teste `if os.path.exists(save_path) and os.path.getsize(save_path) > 0:` pour confirmer création réussie
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Sauvegarde unifiée réussie: {save_path} ({os.path.getsize(save_path)} octets)")` avec taille fichier
     * **FEEDBACK UTILISATEUR :** Si UI disponible, affiche `messagebox.showinfo("Sauvegarde Réussie", f"État sauvegardé avec succès:\n{save_path}\nTaille: {os.path.getsize(save_path)} octets")` pour confirmation
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde unifiée: {e}", exc_info=True)` et messagebox d'erreur détaillé
   - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
   - UTILITÉ : Point d'entrée principal pour sauvegarde complète et cohérente avec validation utilisateur et gestion d'erreurs robuste

5. is_ui_available.txt (HybridBaccaratPredictor.is_ui_available - Vérification disponibilité UI)
   - Lignes 619-620 dans hbp.py (2 lignes)
   - FONCTION : Vérifie si interface utilisateur Tkinter est disponible et fonctionnelle avec validation simple et efficace
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ROOT :** Teste `return hasattr(self, 'root') and self.root is not None` pour vérifier existence objet root Tkinter
     * **LOGIQUE SIMPLE :** Retourne directement le résultat du test d'existence sans validation complexe
     * **PERFORMANCE OPTIMISÉE :** Méthode ultra-légère appelée fréquemment pour adaptation interface
   - RETOUR : bool - True si interface Tkinter disponible, False sinon
   - UTILITÉ : Validation rapide disponibilité UI pour adaptation comportement selon mode (interface/headless)

5. _safe_update_progress.txt (HybridBaccaratPredictor._safe_update_progress - MAJ progression sécurisée)
   - Lignes 5236-5252 dans hbp.py (17 lignes)
   - FONCTION : Met à jour progression de manière thread-safe avec validation et protection complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * value (int) - Valeur de progression entre 0 et 100
     * message (str) - Message de statut à afficher
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `if not self.is_ui_available(): return` pour éviter erreurs si interface fermée
     * **VALIDATION VALEUR :** Clamp valeur avec `value_clamped = max(0, min(100, int(value)))` pour assurer bornes [0,100]
     * **GESTION EXCEPTIONS :** Encapsule dans `try-except` avec capture `Exception as e:` pour robustesse
     * **MISE À JOUR THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._update_progress(value_clamped, message))` pour exécution dans thread UI principal
     * **LOGGING ERREURS :** En cas d'exception, log avec `logger.error(f"Erreur lors de la mise à jour de la progression: {e}")` pour debugging
     * **FALLBACK SÉCURISÉ :** Continue exécution même en cas d'erreur pour éviter blocage du système
   - RETOUR : None - Met à jour directement l'interface utilisateur
   - UTILITÉ : Évite conflits lors mise à jour interface depuis threads avec protection complète contre erreurs

6. _perform_save.txt (HybridBaccaratPredictor._perform_save - Exécution sauvegarde)
   - Lignes 10980-11118 dans hbp.py (139 lignes)
   - FONCTION : Exécute sauvegarde effective avec validation, collecte état et gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * save_path (str) - Chemin de sauvegarde spécifié
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CHEMIN :** Vérifie validité du chemin avec `os.path.dirname(save_path)` et création répertoire si nécessaire
     * **COLLECTE ÉTAT :** Appelle `state_to_save = self._collect_state_for_save()` pour rassembler toutes données système
     * **VALIDATION ÉTAT :** Teste `if state_to_save is None:` avec logging erreur et retour False si échec collecte
     * **SAUVEGARDE JOBLIB :** Utilise `joblib.dump(state_to_save, save_path, compress=3)` avec compression niveau 3 pour optimisation
     * **VALIDATION FICHIER :** Vérifie `if os.path.exists(save_path) and os.path.getsize(save_path) > 0:` pour confirmer création réussie
     * **CALCUL TAILLE :** Détermine `file_size = os.path.getsize(save_path)` pour logging et feedback
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Sauvegarde réussie: {save_path} ({file_size} octets)")` avec détails
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde: {e}", exc_info=True)` et retour False
   - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
   - UTILITÉ : Méthode interne pour sauvegarde sécurisée avec validation complète et gestion d'erreurs

7. replace_value.txt (HybridBaccaratPredictor.replace_value - Remplacement valeur)
   - Lignes 11202-11465 dans hbp.py (264 lignes)
   - FONCTION : Remplace valeur dans séquence avec validation, mise à jour modèles et synchronisation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * index (int) - Index de la valeur à remplacer dans la séquence
     * new_value (str) - Nouvelle valeur ('Player' ou 'Banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
     * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
     * **VALIDATION INDEX :** Teste `if not (0 <= index < len(self.sequence)):` avec `messagebox.showerror("Index invalide", f"L'index {index} n'est pas valide.")` et retour
     * **VALIDATION VALEUR :** Vérifie `if new_value not in ['Player', 'Banker']:` avec messagebox d'erreur et retour
     * **CONFIRMATION UTILISATEUR :** Si UI disponible, affiche `messagebox.askyesno("Confirmation", f"Remplacer '{old_value}' par '{new_value}' à l'index {index} ?")` pour validation
     * **SAUVEGARDE ANCIEN :** Stocke `old_value = self.sequence[index]` pour logging et rollback potentiel
     * **REMPLACEMENT :** Exécute `self.sequence[index] = new_value` pour modification effective
     * **MISE À JOUR PATTERNS :** Recalcule patterns affectés :
       - Identifie patterns contenant l'index modifié avec `affected_patterns = []`
       - Décrémente anciens patterns et incrémente nouveaux patterns dans `self.pattern_counts`
     * **MISE À JOUR HISTORIQUE :** Si `index < len(self.prediction_history):`, met à jour `self.prediction_history[index]['actual_outcome'] = new_value` pour cohérence
     * **RÉINITIALISATION MARKOV :** Appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul complet
     * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation cache
     * **AJUSTEMENT INDEX :** Si `index < self.last_incremental_update_index:`, remet `self.last_incremental_update_index = 0` pour mise à jour complète
     * **NOUVELLE PRÉDICTION :** Génère nouvelle prédiction avec `lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)` puis `current_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)`
     * **MISE À JOUR UI :** Si UI disponible, planifie `self.root.after(0, lambda p=pred_copy: self.lightweight_update_display(p))` et `self.root.after(10, self.update_display)` pour rafraîchissement
     * **LOGGING :** Enregistre `logger.info(f"Valeur remplacée à l'index {index}: '{old_value}' -> '{new_value}'")` pour traçabilité
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error()` et `messagebox.showerror()` pour feedback utilisateur
   - RETOUR : None - Méthode de modification ne retourne rien
   - UTILITÉ : Modification sécurisée des données historiques avec synchronisation complète modèles et interface

8. replace_weights.txt (HybridBaccaratPredictor.replace_weights - Remplacement poids)
   - Lignes 11529-11531 dans hbp.py (3 lignes)
   - FONCTION : Remplace poids des méthodes avec validation et normalisation automatique pour cohérence système
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * new_weights (Dict[str, float]) - Nouveaux poids pour les méthodes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION POIDS :** Vérifie que tous poids sont positifs avec `if any(w < 0 for w in new_weights.values()):`
     * **NORMALISATION :** Calcule `total = sum(new_weights.values())` puis normalise avec `self.weights = {k: v/total for k, v in new_weights.items()}`
     * **MISE À JOUR BEST :** Met à jour `self.best_weights = self.weights.copy()` pour cohérence
   - RETOUR : None - Méthode de configuration ne retourne rien
   - UTILITÉ : Ajustement manuel des poids avec normalisation automatique et cohérence système

9. filter_none_values.txt (HybridBaccaratPredictor.filter_none_values - Filtrage valeurs None)
   - Lignes 2445-2448 dans hbp.py (4 lignes)
   - FONCTION : Fonction utilitaire récursive qui filtre et nettoie valeurs None des structures de données imbriquées
   - PARAMÈTRES :
     * d - Structure de données à nettoyer (dict, list, ou valeur simple)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **TEST TYPE :** Vérifie `if isinstance(d, dict):` pour traitement spécialisé dictionnaires
     * **FILTRAGE RÉCURSIF :** Utilise compréhension `{k: filter_none_values(v) for k, v in d.items() if v is not None}` pour nettoyer récursivement
     * **APPEL RÉCURSIF :** Applique `filter_none_values(v)` sur chaque valeur pour nettoyage en profondeur
     * **CONDITION FILTRAGE :** Exclut entrées avec `if v is not None` pour éliminer valeurs None
     * **RETOUR DIRECT :** Si pas dictionnaire, retourne `return d` sans modification
   - RETOUR : Structure nettoyée - Dictionnaire sans valeurs None ou valeur inchangée
   - UTILITÉ : Nettoyage récursif données pour éviter erreurs de traitement avec valeurs None dans structures imbriquées

10. undo_last_move_1.txt (HybridBaccaratPredictor.undo_last_move_1 - Annulation dernier coup v1)
    - Lignes 11666-11779 dans hbp.py (114 lignes)
    - FONCTION : Version alternative d'annulation avec options avancées et gestion différenciée selon contexte
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force (bool, optionnel) - Force annulation sans confirmation (défaut: False)
      * update_ui (bool, optionnel) - Met à jour interface après annulation (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
      * **VALIDATION SÉQUENCE :** Teste `if not self.sequence:` avec `messagebox.showwarning("Annulation Impossible", "La séquence de jeu est vide.")` et retour
      * **CONFIRMATION CONDITIONNELLE :** Si `not force` et UI disponible, affiche `messagebox.askyesno("Confirmation", "Voulez-vous vraiment annuler la dernière manche enregistrée ?")` pour validation
      * **SUPPRESSION SÉQUENCE :** Exécute `last_outcome = self.sequence.pop()` pour retirer dernier élément avec sauvegarde valeur
      * **NETTOYAGE HISTORIQUE :** Si `self.prediction_history:`, supprime `removed_prediction = self.prediction_history.pop()` pour cohérence prédictions
      * **MISE À JOUR PATTERNS :** Recalcule patterns affectés avec décrémentation anciens compteurs
      * **RÉINITIALISATION MARKOV :** Appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul
      * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation cache
      * **AJUSTEMENT INDEX :** Si `len(self.sequence) < self.last_incremental_update_index:`, remet `self.last_incremental_update_index = 0` pour mise à jour complète
      * **NOUVELLE PRÉDICTION :** Génère nouvelle prédiction avec `lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)` puis `current_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)`
      * **MISE À JOUR UI CONDITIONNELLE :** Si `update_ui and ui_available:`, planifie mises à jour interface avec `self.root.after()`
      * **LOGGING :** Enregistre `logger.info(f"Mouvement annulé (v1): {last_outcome}. Force: {force}, Update UI: {update_ui}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error()` et `messagebox.showerror()` pour feedback utilisateur
    - RETOUR : bool - True si annulation réussie, False en cas d'erreur
    - UTILITÉ : Annulation avec paramètres configurables pour intégration dans workflows automatisés ou manuels

11. _undo_last_move_unsafe.txt (HybridBaccaratPredictor._undo_last_move_unsafe - Annulation non sécurisée)
    - Lignes 11781-11825 dans hbp.py (45 lignes)
    - FONCTION : Annulation rapide sans toutes les validations de sécurité pour performance optimisée dans contextes contrôlés
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MINIMALE :** Teste uniquement `if not self.sequence:` avec retour False si séquence vide
      * **SUPPRESSION DIRECTE :** Exécute `last_outcome = self.sequence.pop()` sans confirmation utilisateur
      * **NETTOYAGE HISTORIQUE :** Si `self.prediction_history:`, supprime `self.prediction_history.pop()` pour cohérence
      * **MISE À JOUR PATTERNS RAPIDE :** Décrémente patterns affectés sans validation extensive
      * **RESET MARKOV MINIMAL :** Appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul
      * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation
      * **AJUSTEMENT INDEX :** Remet `self.last_incremental_update_index = 0` si nécessaire
      * **PAS DE CONFIRMATION :** Aucune validation utilisateur ou messagebox pour performance
      * **PAS DE GESTION ERREURS :** Laisse exceptions se propager pour debugging
      * **LOGGING MINIMAL :** Enregistre uniquement `logger.debug(f"Annulation unsafe: {last_outcome}")` pour traçabilité
    - RETOUR : bool - True si annulation réussie, False si séquence vide
    - UTILITÉ : Annulation optimisée pour cas d'usage spécifiques où performance prime sur sécurité (tests, batch processing)

12. _find_latest_state_file.txt (HybridBaccaratPredictor._find_latest_state_file - Recherche dernier fichier état)
    - Lignes 11124-11155 dans hbp.py (32 lignes)
    - FONCTION : Recherche et identifie le fichier d'état (.joblib ou .pkl) le plus récent dans répertoire spécifié
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * save_dir (str) - Répertoire de recherche des fichiers d'état
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION RÉPERTOIRE :** Vérifie `if not os.path.isdir(save_dir):` avec logging warning et retour `(None, None)` si inexistant
      * **INITIALISATION LISTE :** Crée `all_files = []` pour collecter fichiers candidats avec métadonnées
      * **PARCOURS FICHIERS :** Itère `for filename in os.listdir(save_dir):` pour examiner chaque fichier
      * **FILTRAGE EXTENSIONS :** Vérifie `ext_lower = os.path.splitext(filename)[1].lower()` puis `if os.path.isfile(filepath) and ext_lower in [".joblib", ".pkl"]:`
      * **EXTRACTION MÉTADONNÉES :** Pour chaque fichier valide, récupère `mod_time = os.path.getmtime(filepath)` et ajoute `(mod_time, filepath, ext_lower)` à la liste
      * **GESTION ERREURS MTIME :** Capture `OSError as e_mtime:` avec logging warning si impossible d'obtenir date modification
      * **GESTION ERREURS GLOBALES :** Capture `Exception as e:` avec logging error et `exc_info=True` pour erreurs listage répertoire
      * **VALIDATION RÉSULTATS :** Vérifie `if not all_files:` avec logging info et retour `(None, None)` si aucun fichier trouvé
      * **TRI CHRONOLOGIQUE :** Utilise `all_files.sort(key=lambda item: item[0], reverse=True)` pour trier par date modification décroissante
      * **SÉLECTION PLUS RÉCENT :** Extrait `latest_mtime, latest_path, latest_ext = all_files[0]` pour récupérer le plus récent
      * **LOGGING RÉSULTAT :** Enregistre `logger.info(f"Dernier fichier identifié: {os.path.basename(latest_path)} ({latest_ext})")`
    - RETOUR : Tuple[Optional[str], Optional[str]] - (chemin_fichier, extension) ou (None, None) si aucun trouvé
    - UTILITÉ : Localisation automatique dernière sauvegarde avec support .joblib/.pkl et tri chronologique robuste

13. _load_latest_state.txt (HybridBaccaratPredictor._load_latest_state - Chargement dernier état)
    - Lignes 10992-11122 dans hbp.py (131 lignes)
    - FONCTION : Charge automatiquement le dernier état (.joblib ou .pkl) avec stratégie de fallback et initialisation douce
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `save_dir = MODEL_SAVE_DIR`, `historical_txt_path = "historical_data.txt"`, `ui_available = self.is_ui_available()`, `state_loaded_successfully = False`
      * **RECHERCHE FICHIER :** Appelle `latest_path, latest_ext = self._find_latest_state_file(save_dir)` pour identifier dernier fichier
      * **CAS AUCUN FICHIER :** Si `latest_path is None`, log "Aucun état sauvegardé trouvé", appelle `self.init_ml_models(reset_weights=True)` et met à jour progression "Prêt (Nouvelle Session)"
      * **STRATÉGIE JOBLIB :** Si `latest_ext == ".joblib"` :
        - Vérifie existence `historical_data.txt` avec `if not os.path.exists(historical_txt_path):`
        - Charge historique avec `hist_load_ok_for_joblib = self._load_historical_txt(historical_txt_path)`
        - Si succès, appelle `self.load_trained_models(latest_path)` et met `state_loaded_successfully = True`
        - Met à jour progression "Chargement état: historique + joblib OK"
      * **STRATÉGIE FALLBACK PKL :** Si `not state_loaded_successfully` :
        - Détermine `target_pkl_path` soit depuis `latest_path` si `.pkl`, soit recherche nouveau `.pkl`
        - Réinitialise `self.loaded_historical = False`, `self.historical_games_at_startup_or_reset = 0`, `self.historical_data = []`
        - Appelle `self.load_trained_models(target_pkl_path)` et met progression "Chargement état: .pkl OK"
      * **CAS ÉCHEC TOTAL :** Si `not state_loaded_successfully` :
        - Log warning "Aucun état valide n'a pu être chargé"
        - Vérifie `needs_init_reset = (len(self.sequence) != 0 or self.loaded_historical or self.lgbm_base is None)`
        - Appelle `self.init_ml_models(reset_weights=True)` et `self.root.after(0, self._reset_session_display)`
        - Met progression "Pas de save : Session Vierge"
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Restauration automatique intelligente avec fallback .pkl et initialisation douce sans hard reset

14. _load_latest_state_1.txt (HybridBaccaratPredictor._load_latest_state_1 - Chargement dernier état v1)
    - Lignes 12214-12342 dans hbp.py (129 lignes)
    - FONCTION : Version alternative chargement dernier état avec reset visuel et gestion améliorée des échecs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit variables similaires à `_load_latest_state` avec `save_dir = MODEL_SAVE_DIR`, `historical_txt_path = "historical_data.txt"`
      * **RECHERCHE FICHIER :** Utilise `latest_path, latest_ext = self._find_latest_state_file(save_dir)` pour identifier dernier fichier
      * **STRATÉGIE JOBLIB AMÉLIORÉE :** Si `.joblib` trouvé :
        - Charge historique avec `hist_load_ok_for_joblib = self._load_historical_txt(historical_txt_path)`
        - Si succès, appelle `self.load_trained_models(latest_path)` et met `state_loaded_successfully = True`
        - **RESET VISUEL :** Appelle `self.root.after(0, self._reset_session_display)` pour nettoyage affichage
        - **MISE À JOUR POIDS :** Appelle `self.root.after(60, self._update_weights_display)` pour affichage poids
        - **PROGRESSION FINALE :** Met `self.root.after(70, lambda: self._update_progress(100, f"Prêt (État chargé: {os.path.basename(latest_path)})"))`
      * **STRATÉGIE FALLBACK PKL :** Identique à version principale avec reset visuel ajouté
      * **CAS ÉCHEC AVEC RESET :** Si `not state_loaded_successfully` :
        - Vérifie `if len(self.sequence) != 0 or self.loaded_historical or self.lgbm_base is None:`
        - Appelle `self.init_ml_models(reset_weights=True)` pour réinitialisation
        - **RESET COMPLET :** Appelle `self.reset_data('soft', confirm=False)` pour reset session
        - Met progression finale "Nouvelle Session"
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Version améliorée avec reset visuel automatique et gestion plus robuste des échecs de chargement

15. _load_selected_model.txt (HybridBaccaratPredictor._load_selected_model - Chargement modèle sélectionné)
    - Lignes 3520-3556 dans hbp.py (37 lignes)
    - FONCTION : Charge modèle spécifique sélectionné depuis tableau de bord avec validation et confirmation utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * tree (ttk.Treeview) - Widget Treeview contenant la liste des modèles disponibles
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉLECTION :** Vérifie `selected_items = tree.selection()` puis `if not selected_items:` avec message "Veuillez sélectionner un modèle dans la liste."
      * **EXTRACTION DONNÉES :** Récupère `item = selected_items[0]` puis `values = tree.item(item, 'values')` pour obtenir informations modèle
      * **CONSTRUCTION CHEMIN :** Extrait `filename = values[0]` puis construit `model_path = os.path.join(os.getcwd(), "models", filename)` pour chemin complet
      * **VALIDATION EXISTENCE :** Vérifie `if not os.path.exists(model_path):` avec message d'erreur "Le fichier {filename} n'existe pas."
      * **CONFIRMATION UTILISATEUR :** Affiche dialogue `messagebox.askyesno()` avec message :
        - "Voulez-vous charger le modèle {filename}?"
        - "Cela remplacera le modèle actuellement chargé."
      * **CHARGEMENT CONDITIONNEL :** Si `response` positive, appelle `self.load_trained_models(model_path)` pour chargement effectif
      * **GESTION ANNULATION :** Si utilisateur refuse, retourne sans action
    - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
    - UTILITÉ : Interface sécurisée pour chargement sélectif de modèles depuis tableau de bord avec confirmation obligatoire

16. _save_model_metadata.txt (HybridBaccaratPredictor._save_model_metadata - Sauvegarde métadonnées modèle)
    - Lignes 2359-2461 dans hbp.py (103 lignes)
    - FONCTION : Sauvegarde métadonnées complètes modèle dans fichier JSON avec hyperparamètres et métriques performance
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * model_filepath (str) - Chemin du fichier modèle pour nommage JSON associé
      * package (Dict[str, Any]) - Package contenant données modèle et configuration
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EXTRACTION CONFIGURATION :** Récupère `config_details = package.get('config_details', {})` pour détails configuration
      * **MÉTRIQUES PERFORMANCE :** Collecte `performance_metrics = {}` avec `self.best_accuracy` si disponible
      * **PERFORMANCE MÉTHODES :** Extrait `method_performance = {}` depuis `self.method_performance` avec accuracy par méthode
      * **CONSTRUCTION MÉTADONNÉES :** Crée dictionnaire complet avec :
        - `timestamp` : horodatage ISO format avec `datetime.now().isoformat()`
        - `model_file` : nom fichier avec `os.path.basename(model_filepath)`
        - `model_type` : 'HybridBaccaratPredictor' fixe
        - `version` : depuis `package.get('predictor_version', 'unknown')`
        - `sequence_length` : longueur séquence avec `len(package.get('sequence', []))`
      * **HYPERPARAMÈTRES COMPLETS :** Collecte tous hyperparamètres système :
        - Paramètres généraux : `min_confidence_for_recommendation`, `error_pattern_threshold`
        - Paramètres LGBM : `n_estimators`, `learning_rate`, `max_depth`, `num_leaves`, etc.
        - Paramètres LSTM : `lstm_units`, `lstm_dropout`, `lstm_epochs`, `lstm_batch_size`
        - Paramètres Markov : `max_markov_order`, `markov_smoothing`
        - Poids modèles : `initial_weights`, `current_weights`, `best_weights`
      * **INFORMATIONS ENTRAÎNEMENT :** Ajoute `training_info` avec timestamps entraînement et sauvegarde
      * **FILTRAGE VALEURS :** Utilise fonction récursive `filter_none_values()` pour nettoyer métadonnées
      * **SAUVEGARDE JSON :** Crée `json_filepath = os.path.splitext(model_filepath)[0] + '.json'` puis sauvegarde avec `json.dump()`
      * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour booléen
    - RETOUR : bool - True si sauvegarde réussie, False sinon
    - UTILITÉ : Traçabilité complète modèles avec hyperparamètres et métriques pour analyse et reproduction

17. _save_params_to_file.txt (HybridBaccaratPredictor._save_params_to_file - Sauvegarde paramètres fichier)
    - Lignes 2333-2357 dans hbp.py (25 lignes)
    - FONCTION : Sauvegarde paramètres optimisés dans fichier params.txt au format JSON avec validation et gestion d'erreurs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * params (Dict[str, Any]) - Dictionnaire des paramètres à sauvegarder
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PARAMÈTRES :** Vérifie `if not params or not isinstance(params, dict):` avec logging erreur si invalides
      * **CRÉATION FICHIER :** Ouvre `with open("params.txt", "w", encoding="utf-8") as f:` pour écriture UTF-8
      * **SÉRIALISATION JSON :** Utilise `json.dump(params, f, indent=4, sort_keys=True)` pour formatage lisible avec indentation et tri des clés
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Paramètres optimisés sauvegardés dans params.txt: {len(params)} paramètres.")` avec comptage
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde des paramètres dans params.txt: {e}", exc_info=True)`
      * **RETOUR STATUT :** Retourne `True` si succès, `False` si erreur
    - RETOUR : bool - True si sauvegarde réussie, False sinon
    - UTILITÉ : Persistance paramètres optimisés avec format JSON standardisé pour restauration et partage

18. _save_state_to_models_dir.txt (HybridBaccaratPredictor._save_state_to_models_dir - Sauvegarde état répertoire modèles)
    - Lignes 10966-10990 dans hbp.py (25 lignes)
    - FONCTION : Sauvegarde automatique état actuel dans MODEL_SAVE_DIR avec timestamp et format .joblib via _perform_save
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **DÉFINITION RÉPERTOIRE :** Utilise `save_dir = MODEL_SAVE_DIR` pour répertoire de sauvegarde standard
      * **CRÉATION RÉPERTOIRE :** Appelle `os.makedirs(save_dir, exist_ok=True)` pour assurer existence dossier
      * **GÉNÉRATION TIMESTAMP :** Crée `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` pour horodatage unique
      * **CONSTRUCTION NOM :** Forme `filename = f"predictor_state_{timestamp}.joblib"` avec format .joblib obligatoire pour auto-save
      * **CHEMIN COMPLET :** Assemble `filepath = os.path.join(save_dir, filename)` pour chemin final
      * **LOGGING DÉBUT :** Enregistre `logger.info(f"Sauvegarde automatique de l'état vers: {filepath}")` pour traçabilité
      * **APPEL SAUVEGARDE :** Délègue à `return self._perform_save(filepath)` pour logique interne complète
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde automatique: {e}", exc_info=True)` et retour False
      * **FORMAT FIXE :** Force toujours extension .joblib pour cohérence auto-save contrairement à unified_save qui permet .pkl
    - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
    - UTILITÉ : Backup automatique avec nommage temporel pour traçabilité et récupération d'état sans intervention utilisateur

19. _initialize_method_performance.txt (HybridBaccaratPredictor._initialize_method_performance - Initialisation performance méthodes)
    - Lignes 2060-2090 dans hbp.py (31 lignes)
    - FONCTION : Initialise structure complète de suivi performance pour chaque méthode avec gestion conditionnelle Markov
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION DICTIONNAIRE :** Crée `self.method_performance = {}` pour stockage performance par méthode
      * **VÉRIFICATION MARKOV :** Récupère `use_markov_model = getattr(self.config, 'use_markov_model', True)` pour activation conditionnelle
      * **EXTRACTION CLÉS :** Utilise `keys_to_initialize = list(self.config.initial_weights.keys())` pour méthodes actives
      * **FILTRAGE MARKOV :** Si `not use_markov_model and 'markov' in keys_to_initialize:`, supprime avec `keys_to_initialize.remove('markov')` et logging debug
      * **INITIALISATION STRUCTURES :** Pour chaque `key in keys_to_initialize:`, crée dictionnaire avec :
        - `'correct': 0` pour compteur prédictions correctes
        - `'total': 0` pour compteur total prédictions
        - `'accuracy_history': []` pour historique précisions (fenêtre glissante)
      * **PROTECTION MARKOV :** Ajoute toujours entrée 'markov' même si désactivé avec `if 'markov' not in self.method_performance:` pour éviter erreurs
      * **STRUCTURE UNIFORME :** Assure même structure pour toutes méthodes : correct, total, accuracy_history
      * **LOGGING FINAL :** Enregistre `logger.debug(f"Structure de performance initialisée pour les méthodes: {list(self.method_performance.keys())}")` pour traçabilité
      * **COHÉRENCE CLÉS :** Garantit correspondance avec clés utilisées dans `hybrid_prediction` et `self.weights`
    - RETOUR : None - Initialise directement attribut self.method_performance
    - UTILITÉ : Préparation système monitoring performance avec structures uniformes et gestion conditionnelle Markov pour éviter erreurs

20. _get_color_for_intensity.txt (HybridBaccaratPredictor._get_color_for_intensity - Couleur selon intensité)
    - Lignes 8101-8105 dans hbp.py (5 lignes)
    - FONCTION : Convertit intensité numérique (0-1) en couleur hexadécimale bleue pour visualisation matrices de confusion
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * intensity (float) - Valeur d'intensité entre 0.0 et 1.0
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONVERSION INTENSITÉ :** Calcule `blue = int(255 * intensity)` pour mapper intensité [0,1] vers composante bleue [0,255]
      * **FORMATAGE HEXADÉCIMAL :** Retourne `f"#{0:02x}{0:02x}{blue:02x}"` avec rouge=0, vert=0, bleu=variable
      * **GRADIENT BLEU :** Crée gradient du noir (#000000) au bleu pur (#0000FF) selon intensité
      * **UTILISATION MATRICES :** Employée dans `_draw_confusion_matrix` pour colorer cellules selon valeurs
      * **FORMAT COULEUR :** Produit codes couleur HTML/CSS compatibles avec Tkinter Canvas
    - RETOUR : str - Code couleur hexadécimal format "#RRGGBB" avec gradient bleu
    - UTILITÉ : Visualisation colorée des métriques dans interface avec mapping intensité→couleur pour matrices de confusion

21. cleanup_and_show_message.txt (HybridBaccaratPredictor.cleanup_and_show_message - Nettoyage et message)
    - Lignes 13306-13332 dans hbp.py (27 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage message succès optimisation avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **AFFICHAGE SUCCÈS :** Utilise `messagebox.showinfo("Optimisation Réussie", f"Optimisation terminée avec succès!\n{success_msg}")` pour notification utilisateur
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour finalisation optimisation
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation propre optimisation avec nettoyage mémoire et feedback utilisateur positif

22. cleanup_and_show_message_1.txt (HybridBaccaratPredictor.cleanup_and_show_message_1 - Nettoyage et message v1)
    - Lignes 13348-13374 dans hbp.py (27 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage message d'erreur optimisation avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **AFFICHAGE ERREUR :** Utilise `messagebox.showerror("Échec Optimisation", f"L'optimisation a échoué.\n{error_msg}")` pour notification erreur
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour gestion échec optimisation
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation propre échec optimisation avec nettoyage mémoire et feedback utilisateur d'erreur

23. cleanup_and_show_message_2.txt (HybridBaccaratPredictor.cleanup_and_show_message_2 - Nettoyage et message v2)
    - Lignes 13385-13412 dans hbp.py (28 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage message échec optimisation multi-niveaux avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **PRÉPARATION MESSAGE :** Définit `display_error = error_msg if error_msg else "Aucun essai valide terminé ou erreur interne."`
      * **AFFICHAGE ERREUR :** Utilise `messagebox.showerror("Échec Optimisation multi-niveaux", f"L'optimisation a échoué ou n'a pas trouvé de résultat.\n{display_error[:500]}")` avec troncature message
      * **LIMITATION TEXTE :** Tronque message erreur à 500 caractères avec `[:500]` pour éviter débordement interface
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour gestion échec optimisation multi-niveaux
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation propre échec optimisation multi-niveaux avec nettoyage mémoire et feedback utilisateur d'erreur détaillé

24. cleanup_and_show_results.txt (HybridBaccaratPredictor.cleanup_and_show_results - Nettoyage et résultats)
    - Lignes 13263-13290 dans hbp.py (28 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage résultats optimisation avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **AFFICHAGE RÉSULTATS :** Appelle `self._show_optuna_results_window(best_params)` pour fenêtre détaillée résultats
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour présentation résultats optimisation
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation optimisation avec nettoyage mémoire et présentation résultats détaillés dans fenêtre dédiée

25. apply_resource_config.txt (HybridBaccaratPredictor.apply_resource_config - Application configuration ressources)
    - Lignes 4592-4649 dans hbp.py (58 lignes)
    - FONCTION : Applique configuration des ressources système (CPU/GPU/mémoire) avec validation et mise à jour configurations dépendantes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * show_confirmation (bool, optionnel) - Affiche confirmation utilisateur (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` et `ui_available = self.is_ui_available()`
      * **VALIDATION CPU :** Récupère `target_cores = getattr(self, 'target_cpu_cores', 1)` et valide avec `max_logical_cores = os.cpu_count() or 1`
      * **CLIPPING CPU :** Applique `self.target_cpu_cores = max(1, min(target_cores, max_logical_cores))` pour borner valeurs
      * **VALIDATION MÉMOIRE :** Récupère `target_ram = getattr(self, 'target_max_ram_gb', 2)` et valide avec détection système
      * **DÉTECTION RAM SYSTÈME :** Si `psutil` disponible, utilise `psutil.virtual_memory().total / (1024**3)` pour RAM totale
      * **CLIPPING MÉMOIRE :** Applique `self.target_max_ram_gb = max(1, min(target_ram, total_sys_mem_gb))` pour borner valeurs
      * **LOGGING VALIDATION :** Enregistre `logger_instance.info(f"Configuration ressources validée: CPU={self.target_cpu_cores} cœurs, RAM={self.target_max_ram_gb} Go")`
      * **MISE À JOUR CONFIG :** Si attributs existent, met à jour `self.config.default_cpu_cores = self.target_cpu_cores` et `self.config.default_max_memory_gb = self.target_max_ram_gb`
      * **APPEL DÉPENDANCES :** Exécute `self._update_dependent_configs()` pour adapter configurations liées (threads PyTorch, LGBM, etc.)
      * **MISE À JOUR UI :** Si UI disponible, programme `self.root.after(20, self._update_weight_display)` pour rafraîchissement interface
      * **CONFIRMATION UTILISATEUR :** Si `show_confirmation` et UI disponible, affiche `messagebox.showinfo("Configuration Appliquée", message_confirmation)`
      * **GESTION ERREURS :** Capture exceptions avec logging erreur et messagebox d'erreur si UI disponible
    - RETOUR : None - Met à jour directement configuration système
    - UTILITÉ : Optimisation utilisation ressources avec validation bornes système et mise à jour configurations dépendantes automatique

26. reset_system.txt (HybridBaccaratPredictor.reset_system - Réinitialisation système)
    - Lignes 4714-4829 dans hbp.py (116 lignes)
    - FONCTION : Réinitialise complètement le système à l'état initial avec gestion différenciée 'soft'/'hard' et acquisition verrous thread-safe
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * reset_type (Literal['soft', 'hard']) - Type de reset : 'soft' pour session, 'hard' pour tout (défaut: 'soft')
      * confirm (bool, optionnel) - Demande confirmation utilisateur (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **CONFIRMATION UTILISATEUR :** Si `confirm and ui_available:`, affiche `messagebox.askyesno("Confirmation Réinitialisation", f"Êtes-vous sûr de vouloir réinitialiser {'la session actuelle' if reset_type == 'soft' else 'TOUT (session ET modèles)'} ?")` pour validation
      * **ACQUISITION VERROUS :** Séquence d'acquisition thread-safe :
        - `self.sequence_lock.acquire()` et `self.model_lock.acquire()` pour état principal
        - Gestion `markov_lock_to_use` avec fallback `self._fallback_markov_lock = threading.RLock()` si nécessaire
        - `self.training_lock.acquire()` et `self.weights_lock.acquire()` pour cohérence complète
      * **RESET COMMUN (SOFT/HARD) :** Vide structures de base :
        - `self.sequence = []` pour historique jeu
        - `self.prediction_history = []` pour prédictions
        - `self.lgbm_cache = deque(maxlen=100)` pour cache (commenté pour soft selon modification)
        - `self.last_incremental_update_index = 0` pour index mise à jour rapide
        - `self.markov.reset(reset_type='soft')` pour modèles session Markov
      * **RESET SPÉCIFIQUE HARD :** Si `reset_type == 'hard':` :
        - Vide `self.loaded_historical = False` et `self.historical_data = []` pour données persistantes
        - Appelle `init_ok = self.init_ml_models(reset_weights=True)` pour réinitialisation modèles ML
        - Gestion échec avec `if not init_ok:` et `messagebox.showerror("Erreur Critique Reset", "Échec de la réinitialisation des modèles ML...")`
        - **VIDAGE CACHE EXPLICITE :** `self.lgbm_cache = deque(maxlen=100)` APRÈS init_ml_models selon modification
        - `self.markov.reset(reset_type='hard')` pour reset complet Markov incluant modèles persistants
      * **RESET POIDS SOFT :** Si `reset_type == 'soft':` :
        - Restaure `self.weights = self.config.initial_weights.copy()` et `self._initialize_method_performance()`
        - Remet `self.best_accuracy = 0.5`, `self.best_weights = self.weights.copy()`, `self.early_stopping_counter = 0`
      * **MISE À JOUR UI :** Si UI disponible, planifie mises à jour :
        - `self.root.after(0, self.update_display)` pour affichage principal
        - `self.root.after(50, self._update_weights_display)` pour poids
        - `self.root.after(10, lambda: self._update_progress(0, "Prêt."))` pour progression
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.critical()` et `messagebox.showerror()` pour erreurs critiques
      * **LIBÉRATION VERROUS :** Bloc `finally:` avec libération séquentielle tous verrous et gestion `RuntimeError` pour erreurs libération
    - RETOUR : None - Méthode de réinitialisation ne retourne rien
    - UTILITÉ : Réinitialisation robuste et thread-safe avec deux niveaux (session/complet) selon besoins utilisateur et gestion d'erreurs complète
