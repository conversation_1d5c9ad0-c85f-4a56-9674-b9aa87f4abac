DESCRIPTIF DÉTAILLÉ DES MÉTHODES - UTILITAIRES FONCTIONS
================================================================================

Ce fichier contient la description détaillée des fonctions utilitaires
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : UTILITAIRES FONCTIONS
Fonctions utilitaires, helpers et méthodes de support pour le système.

TOTAL : 2 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES UTILITAIRES FONCTIONS
================================================================================

1. __init__.txt (HybridBaccaratPredictor.__init__ - Constructeur principal de la classe)
   - Lignes 541-779 dans hbp.py (239 lignes)
   - FONCTION : Initialise une instance de HybridBaccaratPredictor avec configuration complète des modèles ML, interface utilisateur et gestion des ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * root_or_config (Union[tk.Tk, PredictorConfig]) - Objet root Tkinter pour mode UI ou objet PredictorConfig pour mode Optuna
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OPTIMISATION MÉMOIRE :** Optimise l'utilisation mémoire PyTorch avant toute opération
     * **CONFIGURATION LOGGER :** Initialise le système de logging avec handlers console et fichier
     * **DÉTECTION MODE :** Détermine si c'est un mode UI (Tkinter) ou mode Optuna (sans UI)
     * **INITIALISATION ÉTAT :** Configure les attributs d'état (séquences, historique, cache LGBM)
     * **MODÈLES ML :** Initialise les placeholders pour LGBM, LSTM, optimiseurs et schedulers
     * **MODÈLE MARKOV :** Configure le modèle PersistentMarkov avec paramètres adaptatifs
     * **GESTION PERFORMANCE :** Initialise le suivi des performances et poids des méthodes
     * **VERROUS THREADING :** Crée les verrous pour accès concurrent sécurisé
     * **CONFIGURATION DEVICE :** Détecte et configure l'utilisation CPU/GPU
     * **INTERFACE UTILISATEUR :** Configure l'UI Tkinter si en mode interface
     * **CHARGEMENT AUTO :** Tente le chargement automatique de l'état précédent
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Point d'entrée principal pour créer une instance fonctionnelle du système de prédiction

2. safe_record_outcome.txt (HybridBaccaratPredictor.safe_record_outcome - Enregistrement sécurisé résultat)
   - Lignes 12344-12486 dans hbp.py (143 lignes)
   - FONCTION : Enregistre le résultat d'une manche de manière thread-safe avec limite 60 manches et auto-update
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * outcome (str) - Résultat de la manche ('player' ou 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION LIMITE :** Contrôle limite 60 manches avant enregistrement
     * **ACQUISITION VERROUS :** Prend tous les verrous nécessaires pour cohérence thread-safe
     * **MISE À JOUR SÉQUENCE :** Ajoute outcome à la séquence et calcule numéro manche
     * **AUTO-UPDATE :** Appelle auto_fast_update_if_needed pour manches cibles
     * **MISE À JOUR MARKOV :** Met à jour modèle Markov session avec nouvelle séquence
     * **PATTERNS :** Met à jour compteurs de patterns avec nouveau résultat
     * **PRÉDICTION SUIVANTE :** Génère features et effectue hybrid_prediction pour coup suivant
     * **MISE À JOUR POIDS :** Ajuste poids méthodes basé sur prédiction précédente vs résultat actuel
     * **CONFIANCE CONSÉCUTIVE :** Met à jour calculateur pour manches 31-60
     * **PLANIFICATION UI :** Programme mises à jour interface via root.after
   - RETOUR : None - Méthode d'enregistrement ne retourne rien
   - UTILITÉ : Cœur du système de traitement des résultats avec gestion complète de l'état et optimisations avec tous les composants initialisés
