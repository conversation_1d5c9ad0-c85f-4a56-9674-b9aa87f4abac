DESCRIPTIF DÉTAILLÉ DES MÉTHODES - UTILITAIRES FONCTIONS
================================================================================

Ce fichier contient la description détaillée des fonctions utilitaires
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : UTILITAIRES FONCTIONS
Fonctions utilitaires, helpers et méthodes de support pour le système.

TOTAL : 26 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES UTILITAIRES FONCTIONS
================================================================================

1. __init__.txt (HybridBaccaratPredictor.__init__ - Constructeur principal de la classe)
   - Lignes 541-779 dans hbp.py (239 lignes)
   - FONCTION : Initialise une instance de HybridBaccaratPredictor avec configuration complète des modèles ML, interface utilisateur et gestion des ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * root_or_config (Union[tk.Tk, PredictorConfig]) - Objet root Tkinter pour mode UI ou objet PredictorConfig pour mode Optuna
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OPTIMISATION MÉMOIRE :** Optimise l'utilisation mémoire PyTorch avant toute opération
     * **CONFIGURATION LOGGER :** Initialise le système de logging avec handlers console et fichier
     * **DÉTECTION MODE :** Détermine si c'est un mode UI (Tkinter) ou mode Optuna (sans UI)
     * **INITIALISATION ÉTAT :** Configure les attributs d'état (séquences, historique, cache LGBM)
     * **MODÈLES ML :** Initialise les placeholders pour LGBM, LSTM, optimiseurs et schedulers
     * **MODÈLE MARKOV :** Configure le modèle PersistentMarkov avec paramètres adaptatifs
     * **GESTION PERFORMANCE :** Initialise le suivi des performances et poids des méthodes
     * **VERROUS THREADING :** Crée les verrous pour accès concurrent sécurisé
     * **CONFIGURATION DEVICE :** Détecte et configure l'utilisation CPU/GPU
     * **INTERFACE UTILISATEUR :** Configure l'UI Tkinter si en mode interface
     * **CHARGEMENT AUTO :** Tente le chargement automatique de l'état précédent
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Point d'entrée principal pour créer une instance fonctionnelle du système de prédiction

2. safe_record_outcome.txt (HybridBaccaratPredictor.safe_record_outcome - Enregistrement sécurisé résultat)
   - Lignes 12344-12486 dans hbp.py (143 lignes)
   - FONCTION : Enregistre le résultat d'une manche de manière thread-safe avec limite 60 manches et auto-update
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * outcome (str) - Résultat de la manche ('player' ou 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION LIMITE :** Contrôle limite 60 manches avant enregistrement
     * **ACQUISITION VERROUS :** Prend tous les verrous nécessaires pour cohérence thread-safe
     * **MISE À JOUR SÉQUENCE :** Ajoute outcome à la séquence et calcule numéro manche
     * **AUTO-UPDATE :** Appelle auto_fast_update_if_needed pour manches cibles
     * **MISE À JOUR MARKOV :** Met à jour modèle Markov session avec nouvelle séquence
     * **PATTERNS :** Met à jour compteurs de patterns avec nouveau résultat
     * **PRÉDICTION SUIVANTE :** Génère features et effectue hybrid_prediction pour coup suivant
     * **MISE À JOUR POIDS :** Ajuste poids méthodes basé sur prédiction précédente vs résultat actuel
     * **CONFIANCE CONSÉCUTIVE :** Met à jour calculateur pour manches 31-60
     * **PLANIFICATION UI :** Programme mises à jour interface via root.after
   - RETOUR : None - Méthode d'enregistrement ne retourne rien
   - UTILITÉ : Cœur du système de traitement des résultats avec gestion complète de l'état et optimisations avec tous les composants initialisés

3. undo_last_move.txt (HybridBaccaratPredictor.undo_last_move - Annulation dernier coup)
   - FONCTION : Annule le dernier coup enregistré avec restauration état précédent et mise à jour UI
   - UTILITÉ : Permet correction erreurs saisie avec restauration complète de l'état système

4. unified_save.txt (HybridBaccaratPredictor.unified_save - Sauvegarde unifiée)
   - FONCTION : Sauvegarde unifiée de l'état complet du système avec validation
   - UTILITÉ : Point d'entrée principal pour sauvegarde complète et cohérente

5. is_ui_available.txt (HybridBaccaratPredictor.is_ui_available - Vérification disponibilité UI)
   - Lignes 619-620 dans hbp.py (2 lignes)
   - FONCTION : Vérifie si interface utilisateur Tkinter est disponible et fonctionnelle avec validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ATTRIBUT :** Contrôle `hasattr(self, 'root')` pour s'assurer que l'attribut root existe
     * **VÉRIFICATION NON-NULL :** Vérifie `self.root is not None` pour éviter références null
     * **VÉRIFICATION MÉTHODE :** Contrôle `hasattr(self.root, 'winfo_exists')` pour s'assurer que c'est un objet Tkinter valide
     * **VÉRIFICATION EXISTENCE :** Appelle `self.root.winfo_exists()` pour confirmer que la fenêtre existe encore
   - RETOUR : bool - True si UI disponible et fonctionnelle, False sinon
   - UTILITÉ : Évite erreurs lors d'accès à l'interface en mode sans UI (Optuna) ou fenêtre fermée

5.5. _safe_update_progress.txt (HybridBaccaratPredictor._safe_update_progress - MAJ progression sécurisée)
   - Lignes 5236-5252 dans hbp.py (17 lignes)
   - FONCTION : Met à jour progression de manière thread-safe avec validation et protection complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * value (int) - Valeur de progression entre 0 et 100
     * message (str) - Message de statut à afficher
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `if not self.is_ui_available(): return` pour éviter erreurs si interface fermée
     * **VALIDATION VALEUR :** Clamp valeur avec `value_clamped = max(0, min(100, int(value)))` pour assurer bornes [0,100]
     * **GESTION EXCEPTIONS :** Encapsule dans `try-except` avec capture `Exception as e:` pour robustesse
     * **MISE À JOUR THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._update_progress(value_clamped, message))` pour exécution dans thread UI principal
     * **LOGGING ERREURS :** En cas d'exception, log avec `logger.error(f"Erreur lors de la mise à jour de la progression: {e}")` pour debugging
     * **FALLBACK SÉCURISÉ :** Continue exécution même en cas d'erreur pour éviter blocage du système
   - RETOUR : None - Met à jour directement l'interface utilisateur
   - UTILITÉ : Évite conflits lors mise à jour interface depuis threads avec protection complète contre erreurs

6. _perform_save.txt (HybridBaccaratPredictor._perform_save - Exécution sauvegarde)
   - FONCTION : Exécute sauvegarde effective avec gestion erreurs et validation
   - UTILITÉ : Mécanisme central de sauvegarde avec robustesse

7. replace_value.txt (HybridBaccaratPredictor.replace_value - Remplacement valeur)
   - FONCTION : Remplace valeur dans séquence avec validation et mise à jour
   - UTILITÉ : Modification sécurisée des données historiques

8. replace_weights.txt (HybridBaccaratPredictor.replace_weights - Remplacement poids)
   - FONCTION : Remplace poids des méthodes avec validation et normalisation
   - UTILITÉ : Ajustement manuel des poids avec cohérence système

9. filter_none_values.txt (HybridBaccaratPredictor.filter_none_values - Filtrage valeurs None)
   - Lignes 2445-2448 dans hbp.py (4 lignes)
   - FONCTION : Fonction utilitaire récursive qui filtre et nettoie valeurs None des structures de données imbriquées
   - PARAMÈTRES :
     * d - Structure de données à nettoyer (dict, list, ou valeur simple)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **TEST TYPE :** Vérifie `if isinstance(d, dict):` pour traitement spécialisé dictionnaires
     * **FILTRAGE RÉCURSIF :** Utilise compréhension `{k: filter_none_values(v) for k, v in d.items() if v is not None}` pour nettoyer récursivement
     * **APPEL RÉCURSIF :** Applique `filter_none_values(v)` sur chaque valeur pour nettoyage en profondeur
     * **CONDITION FILTRAGE :** Exclut entrées avec `if v is not None` pour éliminer valeurs None
     * **RETOUR DIRECT :** Si pas dictionnaire, retourne `return d` sans modification
   - RETOUR : Structure nettoyée - Dictionnaire sans valeurs None ou valeur inchangée
   - UTILITÉ : Nettoyage récursif données pour éviter erreurs de traitement avec valeurs None dans structures imbriquées

10. undo_last_move_1.txt (HybridBaccaratPredictor.undo_last_move_1 - Annulation dernier coup v1)
    - FONCTION : Version alternative d'annulation avec options avancées
    - UTILITÉ : Annulation avec paramètres configurables

11. _undo_last_move_unsafe.txt (HybridBaccaratPredictor._undo_last_move_unsafe - Annulation non sécurisée)
    - FONCTION : Annulation rapide sans toutes les validations de sécurité
    - UTILITÉ : Annulation optimisée pour cas d'usage spécifiques

12. _find_latest_state_file.txt (HybridBaccaratPredictor._find_latest_state_file - Recherche dernier fichier état)
    - Lignes 11124-11155 dans hbp.py (32 lignes)
    - FONCTION : Recherche et identifie le fichier d'état (.joblib ou .pkl) le plus récent dans répertoire spécifié
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * save_dir (str) - Répertoire de recherche des fichiers d'état
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION RÉPERTOIRE :** Vérifie `if not os.path.isdir(save_dir):` avec logging warning et retour `(None, None)` si inexistant
      * **INITIALISATION LISTE :** Crée `all_files = []` pour collecter fichiers candidats avec métadonnées
      * **PARCOURS FICHIERS :** Itère `for filename in os.listdir(save_dir):` pour examiner chaque fichier
      * **FILTRAGE EXTENSIONS :** Vérifie `ext_lower = os.path.splitext(filename)[1].lower()` puis `if os.path.isfile(filepath) and ext_lower in [".joblib", ".pkl"]:`
      * **EXTRACTION MÉTADONNÉES :** Pour chaque fichier valide, récupère `mod_time = os.path.getmtime(filepath)` et ajoute `(mod_time, filepath, ext_lower)` à la liste
      * **GESTION ERREURS MTIME :** Capture `OSError as e_mtime:` avec logging warning si impossible d'obtenir date modification
      * **GESTION ERREURS GLOBALES :** Capture `Exception as e:` avec logging error et `exc_info=True` pour erreurs listage répertoire
      * **VALIDATION RÉSULTATS :** Vérifie `if not all_files:` avec logging info et retour `(None, None)` si aucun fichier trouvé
      * **TRI CHRONOLOGIQUE :** Utilise `all_files.sort(key=lambda item: item[0], reverse=True)` pour trier par date modification décroissante
      * **SÉLECTION PLUS RÉCENT :** Extrait `latest_mtime, latest_path, latest_ext = all_files[0]` pour récupérer le plus récent
      * **LOGGING RÉSULTAT :** Enregistre `logger.info(f"Dernier fichier identifié: {os.path.basename(latest_path)} ({latest_ext})")`
    - RETOUR : Tuple[Optional[str], Optional[str]] - (chemin_fichier, extension) ou (None, None) si aucun trouvé
    - UTILITÉ : Localisation automatique dernière sauvegarde avec support .joblib/.pkl et tri chronologique robuste

13. _load_latest_state.txt (HybridBaccaratPredictor._load_latest_state - Chargement dernier état)
    - Lignes 10992-11122 dans hbp.py (131 lignes)
    - FONCTION : Charge automatiquement le dernier état (.joblib ou .pkl) avec stratégie de fallback et initialisation douce
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `save_dir = MODEL_SAVE_DIR`, `historical_txt_path = "historical_data.txt"`, `ui_available = self.is_ui_available()`, `state_loaded_successfully = False`
      * **RECHERCHE FICHIER :** Appelle `latest_path, latest_ext = self._find_latest_state_file(save_dir)` pour identifier dernier fichier
      * **CAS AUCUN FICHIER :** Si `latest_path is None`, log "Aucun état sauvegardé trouvé", appelle `self.init_ml_models(reset_weights=True)` et met à jour progression "Prêt (Nouvelle Session)"
      * **STRATÉGIE JOBLIB :** Si `latest_ext == ".joblib"` :
        - Vérifie existence `historical_data.txt` avec `if not os.path.exists(historical_txt_path):`
        - Charge historique avec `hist_load_ok_for_joblib = self._load_historical_txt(historical_txt_path)`
        - Si succès, appelle `self.load_trained_models(latest_path)` et met `state_loaded_successfully = True`
        - Met à jour progression "Chargement état: historique + joblib OK"
      * **STRATÉGIE FALLBACK PKL :** Si `not state_loaded_successfully` :
        - Détermine `target_pkl_path` soit depuis `latest_path` si `.pkl`, soit recherche nouveau `.pkl`
        - Réinitialise `self.loaded_historical = False`, `self.historical_games_at_startup_or_reset = 0`, `self.historical_data = []`
        - Appelle `self.load_trained_models(target_pkl_path)` et met progression "Chargement état: .pkl OK"
      * **CAS ÉCHEC TOTAL :** Si `not state_loaded_successfully` :
        - Log warning "Aucun état valide n'a pu être chargé"
        - Vérifie `needs_init_reset = (len(self.sequence) != 0 or self.loaded_historical or self.lgbm_base is None)`
        - Appelle `self.init_ml_models(reset_weights=True)` et `self.root.after(0, self._reset_session_display)`
        - Met progression "Pas de save : Session Vierge"
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Restauration automatique intelligente avec fallback .pkl et initialisation douce sans hard reset

14. _load_latest_state_1.txt (HybridBaccaratPredictor._load_latest_state_1 - Chargement dernier état v1)
    - FONCTION : Version alternative chargement avec options étendues
    - UTILITÉ : Chargement avec paramètres configurables

15. _load_selected_model.txt (HybridBaccaratPredictor._load_selected_model - Chargement modèle sélectionné)
    - FONCTION : Charge modèle spécifique sélectionné par utilisateur
    - UTILITÉ : Chargement ciblé de modèles sauvegardés

16. _save_model_metadata.txt (HybridBaccaratPredictor._save_model_metadata - Sauvegarde métadonnées modèle)
    - FONCTION : Sauvegarde métadonnées détaillées des modèles avec configuration
    - UTILITÉ : Traçabilité complète des modèles sauvegardés

17. _save_params_to_file.txt (HybridBaccaratPredictor._save_params_to_file - Sauvegarde paramètres fichier)
    - Lignes 2333-2357 dans hbp.py (25 lignes)
    - FONCTION : Sauvegarde paramètres optimisés dans fichier params.txt au format JSON avec validation et gestion d'erreurs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * params (Dict[str, Any]) - Dictionnaire des paramètres à sauvegarder
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PARAMÈTRES :** Vérifie `if not params or not isinstance(params, dict):` avec logging erreur si invalides
      * **CRÉATION FICHIER :** Ouvre `with open("params.txt", "w", encoding="utf-8") as f:` pour écriture UTF-8
      * **SÉRIALISATION JSON :** Utilise `json.dump(params, f, indent=4, sort_keys=True)` pour formatage lisible avec indentation et tri des clés
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Paramètres optimisés sauvegardés dans params.txt: {len(params)} paramètres.")` avec comptage
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde des paramètres dans params.txt: {e}", exc_info=True)`
      * **RETOUR STATUT :** Retourne `True` si succès, `False` si erreur
    - RETOUR : bool - True si sauvegarde réussie, False sinon
    - UTILITÉ : Persistance paramètres optimisés avec format JSON standardisé pour restauration et partage

18. _save_state_to_models_dir.txt (HybridBaccaratPredictor._save_state_to_models_dir - Sauvegarde état répertoire modèles)
    - FONCTION : Sauvegarde état complet dans répertoire modèles organisé
    - UTILITÉ : Organisation structurée des sauvegardes

19. _initialize_method_performance.txt (HybridBaccaratPredictor._initialize_method_performance - Initialisation performance méthodes)
    - FONCTION : Initialise structures de suivi performance des méthodes
    - UTILITÉ : Préparation système de monitoring performance

20. _get_color_for_intensity.txt (HybridBaccaratPredictor._get_color_for_intensity - Couleur selon intensité)
    - FONCTION : Calcule couleur d'affichage selon intensité valeur
    - UTILITÉ : Visualisation colorée des métriques dans interface

21. cleanup_and_show_message.txt (HybridBaccaratPredictor.cleanup_and_show_message - Nettoyage et message)
    - FONCTION : Nettoie ressources et affiche message utilisateur
    - UTILITÉ : Finalisation propre avec feedback utilisateur

22. cleanup_and_show_message_1.txt (HybridBaccaratPredictor.cleanup_and_show_message_1 - Nettoyage et message v1)
    - FONCTION : Version alternative nettoyage avec options étendues
    - UTILITÉ : Nettoyage configurable avec messages personnalisés

23. cleanup_and_show_message_2.txt (HybridBaccaratPredictor.cleanup_and_show_message_2 - Nettoyage et message v2)
    - FONCTION : Version avancée nettoyage avec gestion erreurs
    - UTILITÉ : Nettoyage robuste avec recovery automatique

24. cleanup_and_show_results.txt (HybridBaccaratPredictor.cleanup_and_show_results - Nettoyage et résultats)
    - FONCTION : Nettoie ressources et présente résultats finaux
    - UTILITÉ : Finalisation avec présentation résultats

25. apply_resource_config.txt (HybridBaccaratPredictor.apply_resource_config - Application configuration ressources)
    - FONCTION : Applique configuration des ressources système (CPU/GPU/mémoire)
    - UTILITÉ : Optimisation utilisation ressources selon configuration

26. reset_system.txt (HybridBaccaratPredictor.reset_system - Réinitialisation système)
    - FONCTION : Réinitialise complètement le système à l'état initial
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * keep_models (bool, optionnel) - Conserve modèles entraînés (défaut: False)
      * reset_ui (bool, optionnel) - Réinitialise interface utilisateur (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ARRÊT PROCESSUS :** Arrête tous processus en cours (entraînement, prédiction)
      * **NETTOYAGE MÉMOIRE :** Libère toute mémoire allouée aux modèles
      * **RÉINITIALISATION ÉTAT :** Remet tous attributs à valeurs initiales
      * **NETTOYAGE CACHE :** Vide tous caches et structures temporaires
      * **RÉINITIALISATION MODÈLES :** Recrée modèles vierges si demandé
      * **RESET INTERFACE :** Remet interface à état initial si activé
      * **VALIDATION FINALE :** Vérifie que système est dans état propre
    - RETOUR : None - Méthode de réinitialisation ne retourne rien
    - UTILITÉ : Permet réinitialisation propre et sécurisée avec deux niveaux selon besoins utilisateur
