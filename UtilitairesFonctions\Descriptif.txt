DESCRIPTIF DÉTAILLÉ DES MÉTHODES - UTILITAIRES FONCTIONS
================================================================================

Ce fichier contient la description détaillée des fonctions utilitaires
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : UTILITAIRES FONCTIONS
Fonctions utilitaires, helpers et méthodes de support pour le système.

TOTAL : 1 MÉTHODE ANALYSÉE

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES UTILITAIRES FONCTIONS
================================================================================

1. __init__.txt (HybridBaccaratPredictor.__init__ - Constructeur principal de la classe)
   - Lignes 541-779 dans hbp.py (239 lignes)
   - FONCTION : Initialise une instance de HybridBaccaratPredictor avec configuration complète des modèles ML, interface utilisateur et gestion des ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * root_or_config (Union[tk.Tk, PredictorConfig]) - Objet root Tkinter pour mode UI ou objet PredictorConfig pour mode Optuna
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OPTIMISATION MÉMOIRE :** Optimise l'utilisation mémoire PyTorch avant toute opération
     * **CONFIGURATION LOGGER :** Initialise le système de logging avec handlers console et fichier
     * **DÉTECTION MODE :** Détermine si c'est un mode UI (Tkinter) ou mode Optuna (sans UI)
     * **INITIALISATION ÉTAT :** Configure les attributs d'état (séquences, historique, cache LGBM)
     * **MODÈLES ML :** Initialise les placeholders pour LGBM, LSTM, optimiseurs et schedulers
     * **MODÈLE MARKOV :** Configure le modèle PersistentMarkov avec paramètres adaptatifs
     * **GESTION PERFORMANCE :** Initialise le suivi des performances et poids des méthodes
     * **VERROUS THREADING :** Crée les verrous pour accès concurrent sécurisé
     * **CONFIGURATION DEVICE :** Détecte et configure l'utilisation CPU/GPU
     * **INTERFACE UTILISATEUR :** Configure l'UI Tkinter si en mode interface
     * **CHARGEMENT AUTO :** Tente le chargement automatique de l'état précédent
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Point d'entrée principal pour créer une instance fonctionnelle du système de prédiction avec tous les composants initialisés
