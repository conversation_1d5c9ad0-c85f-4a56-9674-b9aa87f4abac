# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11124 à 11155
# Type: Méthode de la classe HybridBaccaratPredictor

    def _find_latest_state_file(self, save_dir: str) -> Tuple[Optional[str], Optional[str]]:
        """ Recherche le fichier d'état (.joblib ou .pkl) le plus récent. """
        logger.debug(f"Recherche dernier fichier état (.joblib/.pkl) dans: {save_dir}")
        if not os.path.isdir(save_dir):
            logger.warning(f"Dossier sauvegarde '{save_dir}' non trouvé.")
            return None, None

        all_files = []
        try:
            for filename in os.listdir(save_dir):
                filepath = os.path.join(save_dir, filename)
                ext_lower = os.path.splitext(filename)[1].lower()
                if os.path.isfile(filepath) and ext_lower in [".joblib", ".pkl"]:
                    try:
                        mod_time = os.path.getmtime(filepath)
                        all_files.append((mod_time, filepath, ext_lower))
                    except OSError as e_mtime:
                        logger.warning(f"Impossible obtenir mtime pour {filepath}: {e_mtime}")
        except Exception as e:
            logger.error(f"Erreur listage/accès dossier {save_dir}: {e}", exc_info=True)
            return None, None

        if not all_files:
            logger.info(f"Aucun fichier .joblib ou .pkl trouvé dans '{save_dir}'.")
            return None, None

        # Trier par date de modification (plus récent en premier)
        all_files.sort(key=lambda item: item[0], reverse=True)
        latest_mtime, latest_path, latest_ext = all_files[0]

        logger.info(f"Dernier fichier identifié: {os.path.basename(latest_path)} ({latest_ext})")
        return latest_path, latest_ext