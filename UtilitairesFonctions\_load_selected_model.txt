# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 3520 à 3556
# Type: Méthode de la classe HybridBaccaratPredictor

    def _load_selected_model(self, tree: ttk.Treeview) -> None:
        """
        Charge le modèle sélectionné dans le tableau de bord.

        Args:
            tree (ttk.Treeview): Widget Treeview contenant les modèles
        """
        selected_items = tree.selection()
        if not selected_items:
            messagebox.showinfo("Information", "Veuillez sélectionner un modèle dans la liste.")
            return

        # Récupérer le nom du fichier sélectionné
        item = selected_items[0]
        values = tree.item(item, 'values')
        if not values:
            return

        filename = values[0]
        model_path = os.path.join(os.getcwd(), "models", filename)

        if not os.path.exists(model_path):
            messagebox.showerror("Erreur", f"Le fichier {filename} n'existe pas.")
            return

        # Demander confirmation à l'utilisateur
        response = messagebox.askyesno(
            "Confirmation",
            f"Voulez-vous charger le modèle {filename}?\n\n"
            "Cela remplacera le modèle actuellement chargé."
        )

        if not response:
            return

        # Charger le modèle
        self.load_trained_models(model_path)