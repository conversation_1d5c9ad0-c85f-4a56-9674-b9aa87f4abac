# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1797 à 2019
# Type: Méthode de la classe HybridBaccaratPredictor

    def _perform_save(self, filepath: str = None) -> bool:
        """
        Logique interne de création du package de l'état actuel et sauvegarde via joblib.
        Sauvegarde l'état cohérent actuel (post re-training OU post mise à jour rapide).
        Vérifie que les modèles essentiels sont 'fit' avant sauvegarde.
        Ne sauvegarde PLUS la clé 'incremental_data'.
        CORRIGÉ: Utilisation des bons noms d'attributs config pour les fallbacks LSTM.
        MODIFIÉ: Fallback pour lstm_input_size aligné sur 6 (cohérent avec init_ml_models).
        AMÉLIORÉ: Génère automatiquement un nom de fichier informatif si filepath est None.
        """
        # Générer un nom de fichier informatif si aucun n'est fourni
        if filepath is None:
            # Extraire quelques hyperparamètres clés pour le nom du fichier
            lstm_hidden = getattr(self.config, 'lstm_hidden_dim', 0)
            lstm_layers = getattr(self.config, 'lstm_num_layers', 0)
            lgbm_trees = getattr(self.config, 'lgbm_n_estimators', 0)
            markov_order = getattr(self.config, 'max_markov_order', 0)

            # Ajouter un score de performance si disponible
            score_info = ""
            if hasattr(self, 'best_accuracy') and self.best_accuracy is not None:
                score_info = f"_acc{self.best_accuracy:.3f}"

            # Créer un nom de fichier informatif
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"model_lstm{lstm_hidden}x{lstm_layers}_lgbm{lgbm_trees}_markov{markov_order}{score_info}_{timestamp}.joblib"

            # Construire le chemin complet
            models_dir = os.path.join(os.getcwd(), "models")
            os.makedirs(models_dir, exist_ok=True)
            filepath = os.path.join(models_dir, filename)

            logger.info(f"Nom de fichier généré automatiquement: {filename}")

        logger.info(f"Préparation du package pour sauvegarde directe vers: {filepath}")
        logger.info(f"Préparation du package pour sauvegarde directe vers: {filepath}")
        locks_acquired = False
        try:
            # --- Verrous (Acquisition) ---
            logger.debug("_perform_save: Tentative acquisition verrous...")
            self.sequence_lock.acquire()
            self.model_lock.acquire()
            if self.markov and hasattr(self.markov, 'lock'): self.markov_lock = self.markov.lock
            else:
                 if not hasattr(self, '_fallback_markov_lock'): self._fallback_markov_lock = threading.RLock()
                 self.markov_lock = self._fallback_markov_lock
            self.markov_lock.acquire()
            self.weights_lock.acquire()
            locks_acquired = True
            logger.debug("_perform_save: Verrous acquis.")

            # --- Vérification Préalable des Modèles Essentiels ---
            missing_or_not_fitted = []
            models_to_check_fit = {
                'feature_scaler': self.feature_scaler,
                'lgbm_base': self.lgbm_base,
                'calibrated_lgbm': self.calibrated_lgbm,
                'lgbm_uncertainty': self.lgbm_uncertainty
            }
            for name, model in models_to_check_fit.items():
                if model is None:
                    missing_or_not_fitted.append(f"{name} (instance manquante)")
                else:
                    try:
                        if isinstance(model, StandardScaler):
                             if not hasattr(model, 'n_features_in_') or \
                                (not hasattr(model, 'mean_') and not hasattr(model, 'scale_')):
                                  missing_or_not_fitted.append(f"{name} (non 'fit')")
                        else:
                             check_is_fitted(model)
                    except NotFittedError:
                        missing_or_not_fitted.append(f"{name} (non 'fit')")
                    except AttributeError as ae:
                        logger.warning(f"Échec vérif 'fit' {name}: {ae}")
                        missing_or_not_fitted.append(f"{name} (vérif 'fit' échouée)")
                    except Exception as e_check:
                         logger.error(f"Erreur vérif 'fit' {name}: {e_check}", exc_info=True)
                         missing_or_not_fitted.append(f"{name} (erreur vérif 'fit')")

            if self.markov is None: missing_or_not_fitted.append("markov (instance manquante)")
            if not self.feature_names: missing_or_not_fitted.append("feature_names (liste vide)")
            if self.lstm is None: logger.debug("LSTM non présent. Sauvegarde sans état LSTM.")

            if missing_or_not_fitted:
                error_details = f"Sauvegarde impossible : Composants manquants/non entraînés : {', '.join(missing_or_not_fitted)}"
                logger.error(error_details)
                raise ValueError(error_details)

            # --- Construction du Package à sauvegarder ---
            logger.debug("Construction du dictionnaire du package pour sauvegarde...")
            package = {
                # Modèles ML et Scaler (fittés)
                'feature_scaler': self.feature_scaler,
                'lgbm_base': self.lgbm_base,
                'calibrated_lgbm': self.calibrated_lgbm,
                'lgbm_uncertainty': self.lgbm_uncertainty,

                # État Markov
                'markov_models': self.markov.export_models() if self.markov else None,

                # État LSTM (state_dict)
                'lstm_state': self.lstm.state_dict() if self.lstm else None,

                # Métadonnées et Configuration (pour recréation si besoin)
                'feature_names': self.feature_names[:],
                'config_details': {
                    'max_markov_order': self.config.max_markov_order,
                    'markov_smoothing': self.config.markov_smoothing,
                    'lstm_sequence_length': self.config.lstm_sequence_length,
                    'lstm_sequence_length_compat': self.config.lstm_sequence_length,  # Utiliser lstm_sequence_length au lieu de lstm_sequence_length_compat
                    'lstm_input_size': getattr(self.lstm, 'input_size', 6),
                    'lstm_hidden_dim': getattr(self.lstm, 'hidden_dim', self.config.lstm_hidden_dim),
                    'lstm_num_layers': getattr(self.lstm, 'num_layers', self.config.lstm_num_layers),
                    'dropout_prob': getattr(self.lstm.dropout, 'p', self.config.lstm_dropout) if self.lstm else self.config.lstm_dropout,
                    'lstm_bidirectional': getattr(self.lstm, 'bidirectional', self.config.lstm_bidirectional),
                    # Ajout de la phase d'optimisation pour garantir la cohérence entre entraînement et utilisation
                    'optimization_phase': getattr(self.config, 'optimization_phase', None),
                    # Sauvegarder tous les hyperparamètres importants pour garantir la cohérence
                    'hyperparameters': {
                        # Paramètres généraux
                        'min_confidence_for_recommendation': getattr(self.config, 'min_confidence_for_recommendation', 0.65),
                        'error_pattern_threshold': getattr(self.config, 'error_pattern_threshold', 0.5),
                        'transition_uncertainty_threshold': getattr(self.config, 'transition_uncertainty_threshold', 0.6),
                        'wait_optimizer_confidence_threshold': getattr(self.config, 'wait_optimizer_confidence_threshold', 0.5),
                        'wait_ratio_min_threshold': getattr(self.config, 'wait_ratio_min_threshold', 0.3),
                        'wait_ratio_max_threshold': getattr(self.config, 'wait_ratio_max_threshold', 0.7),

                        # Paramètres LSTM
                        'lstm_hidden_dim': self.config.lstm_hidden_dim,
                        'lstm_num_layers': self.config.lstm_num_layers,
                        'lstm_dropout': self.config.lstm_dropout,
                        'lstm_bidirectional': self.config.lstm_bidirectional,
                        'lstm_learning_rate': self.config.lstm_learning_rate,
                        'lstm_weight_decay': self.config.lstm_weight_decay,
                        'lstm_batch_size': self.config.lstm_batch_size,
                        'lstm_epochs': self.config.lstm_epochs,

                        # Paramètres LGBM
                        'lgbm_n_estimators': self.config.lgbm_n_estimators,
                        'lgbm_learning_rate': self.config.lgbm_learning_rate,
                        'lgbm_max_depth': self.config.lgbm_max_depth,
                        'lgbm_num_leaves': self.config.lgbm_num_leaves,
                        'lgbm_min_child_samples': self.config.lgbm_min_child_samples,
                        'lgbm_subsample': self.config.lgbm_subsample,
                        'lgbm_colsample_bytree': self.config.lgbm_colsample_bytree,
                        'lgbm_reg_alpha': self.config.lgbm_reg_alpha,
                        'lgbm_reg_lambda': self.config.lgbm_reg_lambda,

                        # Poids des modèles
                        'initial_weights': self.config.initial_weights,
                    }
                },

                # État de la Session / Hybridation
                'sequence': self.sequence[:],
                'prediction_history': copy.deepcopy(self.prediction_history),
                'weights': self.weights.copy(),
                'method_performance': copy.deepcopy(self.method_performance),
                'best_accuracy': self.best_accuracy,
                'best_weights': self.best_weights.copy(),
                'early_stopping_counter': self.early_stopping_counter,
                'last_incremental_update_index': self.last_incremental_update_index,

                # Temps de sauvegarde et d'entraînement
                'last_train_time': float(getattr(self, 'last_train_time', 0.0)),  # Ajout de la clé manquante
                'last_save_time': float(getattr(self, 'last_save_time', 0.0)),  # Ajout de la clé manquante

                # Métadonnées de Sauvegarde
                'save_timestamp': datetime.now().isoformat(),
                'predictor_version': "12.7", # Mettre à jour si la version change
            }
            logger.debug("Dictionnaire du package construit.")

            # Log de diagnostic pour la phase d'optimisation
            optimization_phase = package.get('config_details', {}).get('optimization_phase')
            if optimization_phase is not None:
                logger.info(f"Phase d'optimisation sauvegardée: {optimization_phase}")
            else:
                logger.debug("Aucune phase d'optimisation à sauvegarder (None)")

            # --- Sauvegarde Fichier via Joblib ---
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            logger.info(f"Sauvegarde Joblib vers {filepath} (compression=3)...")
            joblib.dump(package, filepath, compress=3)
            logger.info(f"Sauvegarde Joblib réussie vers {filepath}")

            # --- Sauvegarde des métadonnées dans un fichier JSON associé ---
            try:
                self._save_model_metadata(filepath, package)
                logger.info(f"Métadonnées du modèle sauvegardées avec succès")
            except Exception as e:
                logger.warning(f"Erreur lors de la sauvegarde des métadonnées: {e}")

            return True

        except ValueError as e_valid:
            logger.error(f"Erreur validation pré-sauvegarde: {e_valid}") # Log plus détaillé
            return False
        except (pickle.PicklingError, TypeError) as e_pickle:
            logger.error(f"Erreur sérialisation (pickle/deepcopy?) pré-sauvegarde: {e_pickle}", exc_info=True)
            if self.is_ui_available(): # Vérifier si UI existe
                 messagebox.showerror("Erreur Sérialisation", f"Erreur copie interne:\n{e_pickle}")
            return False
        except (joblib.externals.loky.process_executor.TerminatedWorkerError, IOError, OSError) as e_save:
            logger.error(f"Erreur écriture fichier vers {filepath}: {e_save}", exc_info=True)
            if self.is_ui_available(): # Vérifier si UI existe
                 messagebox.showerror("Erreur Sauvegarde Fichier", f"Erreur écriture fichier:\n{e_save}")
            return False
        except Exception as e:
             logger.error(f"Erreur inattendue pendant _perform_save vers {filepath}: {e}", exc_info=True)
             if self.is_ui_available(): # Vérifier si UI existe
                  messagebox.showerror("Erreur Inattendue Sauvegarde", f"Erreur imprévue:\n{e}")
             return False
        finally:
            if locks_acquired:
                try:
                    self.weights_lock.release()
                    self.markov_lock.release()
                    self.model_lock.release()
                    self.sequence_lock.release()
                    logger.debug("_perform_save: Verrous libérés (finally).")
                except RuntimeError as e_release:
                    logger.error(f"_perform_save: Erreur libération verrou: {e_release}")