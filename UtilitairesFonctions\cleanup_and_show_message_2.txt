# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13385 à 13412
# Type: Méthode de la classe HybridBaccaratPredictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

                    def cleanup_and_show_message():
                        # Nettoyage de la mémoire
                        gc.collect()
                        if torch.cuda.is_available():
                            try:
                                torch.cuda.empty_cache()
                            except Exception:
                                pass

                        # Forcer la terminaison des processus multiprocessing qui pourraient être encore actifs
                        try:
                            import multiprocessing
                            # Récupérer tous les processus actifs
                            active_children = multiprocessing.active_children()
                            if active_children:
                                logger.warning(f"Terminaison de {len(active_children)} processus multiprocessing actifs")
                                for process in active_children:
                                    try:
                                        process.terminate()
                                        process.join(timeout=1.0)  # Attendre 1 seconde maximum
                                    except Exception as e:
                                        logger.error(f"Erreur lors de la terminaison du processus {process.name}: {e}")
                        except Exception as e:
                            logger.error(f"Erreur lors de la terminaison des processus multiprocessing: {e}")

                        # Afficher le message d'erreur
                        display_error = error_msg if error_msg else "Aucun essai valide terminé ou erreur interne."
                        messagebox.showerror("Échec Optimisation multi-niveaux", f"L'optimisation a échoué ou n'a pas trouvé de résultat.\n{display_error[:500]}")