# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12614 à 12744
# Type: Méthode de la classe HybridBaccaratPredictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def undo_last_move(self) -> None:
        """Annule le dernier coup enregistré de manière thread-safe.
           MODIFIÉ: Ne vide PLUS le cache LGBM.
        """
        logger.debug("Tentative d'annulation du dernier coup...")
        ui_available = self.is_ui_available()

        # Prendre tous les verrous nécessaires pour modifier l'état de manière cohérente
        with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:
            if not self.sequence:
                logger.info("Annulation impossible: Séquence de jeu vide.")
                if ui_available: messagebox.showwarning("Annulation Impossible", "La séquence de jeu est vide.")
                return

            # Demander confirmation à l'utilisateur si UI dispo
            confirm_undo = False
            if ui_available:
                 confirm_undo = messagebox.askyesno("Confirmation", "Voulez-vous vraiment annuler la dernière manche enregistrée ?")
            else:
                 confirm_undo = True # Procéder sans confirmation si pas d'UI

            if not confirm_undo:
                logger.info("Annulation annulée par l'utilisateur.")
                return

            try:
                # --- Logique interne d'annulation (protégée par les verrous externes) ---
                # 1. Retirer de la séquence
                last_outcome = self.sequence.pop()
                logger.debug(f"Résultat '{last_outcome}' retiré de la séquence.")

                # 2. Retirer de l'historique de prédiction (la prédiction pour le coup N+1)
                removed_prediction = None
                if self.prediction_history:
                    removed_prediction = self.prediction_history.pop()
                    logger.debug("Dernière prédiction retirée de l'historique.")

                # 3. Décrémenter compteur de motif (si assez de données AVANT le pop)
                if len(self.sequence) >= 3: # Besoin de 3 coups restants + celui retiré
                    pattern_to_decrement = tuple(self.sequence[-3:] + [last_outcome])
                    dict_key = last_outcome
                    if dict_key in self.pattern_counts and pattern_to_decrement in self.pattern_counts[dict_key]:
                        self.pattern_counts[dict_key][pattern_to_decrement] -= 1
                        count_after = self.pattern_counts[dict_key][pattern_to_decrement]
                        logger.debug(f"Compteur décrémenté pour motif {pattern_to_decrement} -> {dict_key}. Nouveau compte: {count_after}")
                        if count_after <= 0:
                            del self.pattern_counts[dict_key][pattern_to_decrement]
                            logger.debug(f"Motif {pattern_to_decrement} supprimé car compteur <= 0.")
                    # else: logger.debug(f"Motif {pattern_to_decrement} non trouvé pour décrémentation.")

                # 4. Gérer Markov session: Simple reset+recalcul
                if self.markov and hasattr(self.markov, 'reset'):
                     self.markov.reset(reset_type='soft') # Reset session models
                     if self.sequence: # Recalculer si séquence non vide
                          self.markov.update_session(self.sequence)
                     logger.info("Modèles Markov de session réinitialisés et recalculés après annulation.")

                # 5. Vider le cache LGBM
                # >>>>>>>>>>>>> MODIFICATION: LIGNE SUPPRIMÉE <<<<<<<<<<<<<<
                # self.lgbm_cache = deque(maxlen=100) # Recréer un deque vide
                # logger.debug("Cache LGBM vidé après annulation.")
                # >>>>>>>>>>>>> FIN MODIFICATION <<<<<<<<<<<<<<<
                # Note: Vider le cache n'est pas strictement nécessaire ici, car
                # create_hybrid_features/predict sera appelé avec la séquence N-1
                # et une entrée de cache basée sur N est invalide, mais le supprimer
                # ne cause pas de problème et était dans la demande initiale. Ignorons-le.

                # 6. Décrémenter compteurs performance des METHOTES (complexe, impact faible si peu fréquent)
                # -> On pourrait essayer de le faire si removed_prediction existe et contient 'methods'
                if removed_prediction and 'methods' in removed_prediction:
                     prev_pred_details = removed_prediction['methods']
                     # Pour chaque méthode, décrémenter total et correct si besoin
                     for method, perf_data in self.method_performance.items():
                          if method in prev_pred_details and perf_data['total'] > 0:
                               method_pred = prev_pred_details[method]
                               pred_player = method_pred.get('player', 0.5)
                               pred_banker = method_pred.get('banker', 0.5)
                               # Convertir en minuscules pour assurer la cohérence
                               last_outcome_lower = last_outcome.lower()
                               method_was_correct = (pred_player > pred_banker and last_outcome_lower == 'player') or \
                                                    (pred_banker > pred_player and last_outcome_lower == 'banker')

                               perf_data['total'] -= 1
                               if method_was_correct:
                                    perf_data['correct'] -= 1
                               # Retirer la dernière valeur de accuracy_history
                               if perf_data.get('accuracy_history'): # Vérifier existence
                                    try:
                                        perf_data['accuracy_history'].pop() # pop enlève le dernier ajouté
                                        logger.debug(f" Stats méthode {method} potentiellement ajustées après undo.")
                                    except IndexError:
                                         logger.debug(f" Historique précision pour {method} déjà vide.")
                          # else: logger.debug(f" Méthode {method} non ajustée après undo (pas dans pred ou total=0).")


                # 7. Ajuster index pour mise à jour rapide
                # Si le coup annulé était après le dernier index de mise à jour rapide,
                # on peut simplement ramener l'index à sa valeur précédente (qui est maintenant len(sequence))
                # C'est le comportement implicite qu'on souhaite. Si on annule un coup AVANT l'index,
                # la situation est plus complexe. Solution simple: forcer last_incremental_update_index = 0
                # pour provoquer une mise à jour complète la prochaine fois.
                if len(self.sequence) < self.last_incremental_update_index:
                     logger.warning("Annulation d'un coup antérieur à la dernière MAJ rapide. Index MAJ rapide réinitialisé à 0.")
                     self.last_incremental_update_index = 0
                # Sinon, rien à faire, l'index pointe déjà sur len(sequence) implicitement

                # --- Fin logique interne ---

                # Refaire une prédiction pour l'état actuel (N-1) - Toujours sous verrous
                lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)

                # Récupérer la phase d'optimisation depuis la configuration
                optimization_phase = getattr(self.config, 'optimization_phase', None)

                # Appeler hybrid_prediction avec le paramètre optimization_phase
                current_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat, optimization_phase=optimization_phase)

                # Mettre à jour l'affichage (planifié via root.after si UI dispo)
                if ui_available:
                     # Utiliser copies pour éviter modifs concurrentes dans lambda
                     pred_copy = current_prediction.copy()
                     self.root.after(0, lambda p=pred_copy: self.lightweight_update_display(p))
                     self.root.after(10, self.update_display) # Mise à jour complète
                     if self.graph_visible: self.root.after(20, self.draw_trend_chart)

                logger.info(f"Dernière manche (résultat: {last_outcome}) annulée avec succès.")
                if ui_available: messagebox.showinfo("Annulation Réussie", "La dernière manche a été annulée.")

            except Exception as e:
                 logger.error(f"Erreur majeure lors de l'annulation du dernier coup: {e}", exc_info=True)
                 if ui_available: messagebox.showerror("Erreur Annulation", f"Une erreur s'est produite lors de l'annulation:\n{e}")