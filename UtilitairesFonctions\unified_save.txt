# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 5004 à 5053
# Type: Méthode de la classe HybridBaccaratPredictor

    def unified_save(self) -> None:
        """
        Sauvegarde manuelle de l'état actuel via une boîte de dialogue.
        Propose .joblib comme type par défaut, mais autorise aussi .pkl.
        Utilise _perform_save pour la logique de sauvegarde réelle.
        """
        if self.is_training or self.is_fast_updating:
            messagebox.showwarning("Action Impossible", "Veuillez arrêter tout processus ML avant de sauvegarder.")
            return

        save_dir = MODEL_SAVE_DIR # Utilise la constante globale
        initial_dir = save_dir if os.path.exists(save_dir) else os.getcwd()
        # Proposer un nom de fichier par défaut avec .joblib
        default_filename = f"predictor_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.joblib"

        # Définir les types de fichiers avec .joblib en premier
        file_types = [
            ("Predictor State (Joblib)", "*.joblib"),
            ("Predictor State (Pickle)", "*.pkl"),
            ("Tous les fichiers", "*.*")
        ]

        filepath = filedialog.asksaveasfilename(
            initialdir=initial_dir,
            initialfile=default_filename,
            filetypes=file_types,
            defaultextension=".joblib", # Extension suggérée si aucune n'est donnée
            title="Sauvegarder État Actuel"
        )

        if not filepath:
            logger.info("Sauvegarde manuelle annulée par l'utilisateur.")
            return

        # Vérifier explicitement et ajouter l'extension si manquante (robustesse)
        if not (filepath.lower().endswith(".joblib") or filepath.lower().endswith(".pkl")):
             # Si l'utilisateur n'a pas mis d'extension, utiliser .joblib
             logger.warning(f"Aucune extension reconnue (.joblib/.pkl) spécifiée, ajout de '.joblib' par défaut à: {filepath}")
             filepath += ".joblib"

        self._update_progress(10, f"Sauvegarde manuelle: {os.path.basename(filepath)}...")

        # Appel de la logique interne _perform_save
        save_success = self._perform_save(filepath)

        if save_success:
             self._update_progress(100, f"Sauvegarde terminée: {os.path.basename(filepath)}")
             messagebox.showinfo("Sauvegarde Réussie", f"L'état actuel a été sauvegardé avec succès dans:\n{filepath}")
        else:
             self._update_progress(0, "Erreur sauvegarde.")