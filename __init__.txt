# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 541 à 779
# Type: Méthode de la classe HybridBaccaratPredictor

    def __init__(self, root_or_config):
        # Optimiser l'utilisation de la mémoire par PyTorch AVANT toute autre opération
        # Note: Cette fonction peut afficher un message d'avertissement si PyTorch a déjà commencé des opérations parallèles
        # Ce message est normal et n'indique pas un problème
        try:
            optimize_pytorch_memory()
        except Exception as e:
            # Continuer l'exécution même en cas d'erreur
            # Nous utiliserons self.logger plus tard, donc utilisons logging directement ici
            logging.warning(f"Erreur lors de l'optimisation de la mémoire PyTorch: {e}")

        # Initialiser le logger après l'optimisation de la mémoire PyTorch
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.StreamHandler(),  # Affiche les logs dans la console
                logging.FileHandler("training.log")  # Sauvegarde les logs dans un fichier
            ]
        )

        # Déterminer si l'argument est un objet root (Tkinter) ou un objet config (PredictorConfig)
        if hasattr(root_or_config, 'winfo_exists'):
            # C'est un objet root Tkinter
            self.root = root_or_config
            self.root.title("HBP")
            self.config = PredictorConfig()
            self.auto_update_enabled = tk.BooleanVar(value=True)
            self.is_optuna_running = False
        else:
            # C'est un objet config (PredictorConfig) - mode Optuna
            self.root = None
            self.config = root_or_config
            self.auto_update_enabled = None
            self.is_optuna_running = True
            logger.debug("HybridBaccaratPredictor initialisé en mode Optuna (sans UI)")
        self.initialize_lgbm_cache(maxlen=100)
        self.optimizer = None
        self.current_optimizer_instance = None

        # Initialiser le flag pour indiquer si nous sommes en train de charger un modèle existant
        # Ce flag sera utilisé pour choisir la bonne longueur de séquence LSTM
        self._loading_existing_model = False
        # Attribut pour contrôler l'affichage des messages de débogage
        self.first_run = True

        # Initialisation des attributs pour le suivi des prédictions
        self._total_predictions = 0
        self._last_progress_update = 0
        self._last_progress_time = time.time()
        self._progress_update_interval = 1000  # Afficher la progression tous les 1000 prédictions

        logger.debug("Flag _loading_existing_model initialisé à False et first_run initialisé à True")
        logger.debug("Compteurs de prédictions initialisés (_total_predictions, _last_progress_update, _last_progress_time)")

        logger.info("Configuration (CPU Max, LSTM Bidir, EarlyStop Acc=5) chargée.")

        self.auto_update_enabled = tk.BooleanVar(value=False)
        self.logger.info("Auto-update désactivé par défaut.")
        self.setup_auto_update()


        # Initialisations état
        self.sequence: List[str] = []
        self.prediction_history: List[Dict] = []
        self.historical_data: List[List[str]] = []
        self.loaded_historical: bool = False
        self.lgbm_cache: deque = deque(maxlen=100)
        self.pattern_counts = {'player': Counter(), 'banker': Counter()}
        self.last_incremental_update_index: int = 0
        self.historical_games_at_startup_or_reset: int = 0

        # Initialisation du calculateur de confiance basé sur les séquences consécutives
        self.consecutive_confidence_calculator = ConsecutiveConfidenceCalculator(config=self.config)
        logger.info("État initial défini et calculateur de confiance consécutive initialisé avec la configuration.")

        # Fonction utilitaire pour vérifier si l'UI est disponible
        def is_ui_available(self):
            return hasattr(self, 'root') and self.root is not None and hasattr(self.root, 'winfo_exists') and self.root.winfo_exists()

        # Ajouter la méthode à la classe
        self.__class__.is_ui_available = is_ui_available

        # Ajout IMPORTANT : initialiser ui_available ici pour assurer qu'elle est définie avant utilisation
        ui_available = self.is_ui_available()

        # Placeholders modèles
        self.feature_names: List[str] = []
        self.feature_scaler: Optional[StandardScaler] = None
        self.lgbm_base: Optional[LGBMClassifier] = None
        self.lgbm_uncertainty: Optional[BaggingClassifier] = None
        self.calibrated_lgbm: Optional[CalibratedClassifierCV] = None
        self.lstm: Optional[EnhancedLSTMModel] = None
        self.optimizer: Optional[optim.Optimizer] = None
        self.scheduler: Optional[optim.lr_scheduler._LRScheduler] = None
        self.criterion = nn.CrossEntropyLoss()
        logger.info("Placeholders ML créés.")

        # Initialisation Markov (si activé)
        self.markov = None
        use_markov_model = getattr(self.config, 'use_markov_model', True)
        if use_markov_model:
            try:
                # Utiliser les paramètres de configuration pour initialiser le modèle Markov
                # Utiliser la valeur entière de max_markov_order
                max_order = getattr(self.config, 'max_markov_order', 2)

                # Convertir en entier si c'est un flottant
                if isinstance(max_order, float):
                    logger.warning(f"max_markov_order reçu comme flottant ({max_order}), conversion en entier.")
                    max_order = int(max_order)

                # Garantir une valeur entière entre 1 et 12
                max_order = max(1, min(12, max_order))

                smoothing = getattr(self.config, 'markov_smoothing', 0.15)
                self.markov = PersistentMarkov(max_order=max_order, smoothing=smoothing)
                logger.info(f"Modèle PersistentMarkov initialisé avec max_order={max_order} (entier), smoothing={smoothing}.")
            except Exception as e:
                logger.critical(f"ERREUR CRITIQUE lors de l'initialisation de PersistentMarkov: {e}", exc_info=True)
                if self.is_ui_available():
                    messagebox.showerror("Erreur Critique Markov", f"Impossible init Markov:\n{e}")
                raise # Relancer pour arrêter
        else:
            logger.info("Modèle PersistentMarkov désactivé (use_markov_model=False).")

        # Poids et Performance
        self.weights: Dict[str, float] = self.config.initial_weights.copy()
        self.method_performance: Dict[str, Dict] = {}
        self._initialize_method_performance() # Assurez-vous que cette méthode est définie
        self.best_accuracy: float = 0.5
        self.best_weights: Dict[str, float] = self.weights.copy()
        self.early_stopping_counter: int = 0
        logger.info("Poids et suivi de performance initialisés.")

        # Flags d'état
        self.is_training: bool = False; self.stop_training: bool = False
        self.graph_visible: bool = False; self.stats_visible: bool = False
        self.is_fast_updating: bool = False
        logger.info("Flags d'état initialisés.")

        # Verrous
        self.sequence_lock = threading.RLock(); self.model_lock = threading.RLock()
        # Gestion correcte du verrou Markov
        if hasattr(self.markov, 'lock') and self.markov.lock is not None:
             self.markov_lock = self.markov.lock
        else:
             if not hasattr(self, '_fallback_markov_lock'): self._fallback_markov_lock = threading.RLock()
             self.markov_lock = self._fallback_markov_lock; logger.warning("Utilisation verrou fallback Markov.")
        self.training_lock = threading.RLock(); self.weights_lock = threading.RLock()
        logger.info("Verrous créés.")

        # Contrôle de la verbosité des logs
        self.debug_verbose = getattr(self.config, 'debug_verbose', False)

        # Device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Utilisation device: {self.device.type.upper()}")

        # Variables UI Ressources (MODIFIÉES : Max RAM supprimée)
        default_cores = getattr(self.config, 'default_cpu_cores', 2)
        self.use_cpu = tk.BooleanVar(value=(self.device.type == 'cpu'))
        self.use_gpu = tk.BooleanVar(value=(self.device.type == 'cuda'))
        self.cpu_cores = tk.IntVar(value=default_cores)
        logger.info(f"Variables Tk UI créées (Cores par défaut: {default_cores}).")

        try:
            # Vérifier si nous sommes en mode UI ou en mode Optuna
            if self.root is not None:
                # Mode UI normal
                logger.debug("Avant _setup_ui_variables")
                self._setup_ui_variables()
                logger.debug("Après _setup_ui_variables")
                logger.debug("Avant init_ml_models")
                self.init_ml_models()
                logger.info("Après init_ml_models")
                logger.debug("Avant setup_ui")
                self.setup_ui()
                logger.info("Après setup_ui")
                self.root.update()
                logger.debug("Avant apply_resource_config initial")
                self.apply_resource_config()
                logger.info("Après apply_resource_config initial.")
            else:
                # Mode Optuna (sans UI)
                logger.debug("Mode Optuna: initialisation des modèles ML uniquement")
                self.init_ml_models()
                logger.info("Mode Optuna: modèles ML initialisés")

            # Vérifier si l'UI est disponible
            ui_available = self.is_ui_available()

            if ui_available:
                self._update_progress(0,"Début du processus de chargement")
                style = ttk.Style()
                style.configure("green.Horizontal.TProgressbar", foreground='green', background='green')
                self._update_progress(100," Initialisation UI Terminé(Démarrage auto en cours...)")
            else:
                logger.debug("Mode Optuna: pas d'UI disponible, étapes d'initialisation UI ignorées")

            auto_load_error = None
            try: logger.info("Tentative chargement auto..."); self._load_latest_state() # Assurez-vous que cette méthode est définie
            except Exception as e_load: logger.error(f"... Erreur chargement auto: {e_load}", exc_info=True); auto_load_error = e_load

            # Le chargement automatique des paramètres optimisés depuis params.txt a été désactivé
            # car il n'est pas nécessaire au démarrage du programme
            logger.info("Chargement automatique des paramètres optimisés désactivé.")


            # Vérifier à nouveau si l'UI est disponible
            ui_available = self.is_ui_available()

            if ui_available:
                logger.debug("Avant update_display initial")
                self.update_display() # Assurez-vous que cette méthode est définie
                logger.info("Après update_display initial.")
                logger.debug("Appel initial _update_weights_display()")
                self._update_weights_display() # Assurez-vous que cette méthode est définie
                logger.debug("Après appel initial _update_weights_display()")

                if hasattr(self, 'graph_frame') and self.graph_frame: self.graph_frame.pack_forget(); self.graph_visible = False;
                if hasattr(self, 'stats_frame') and self.stats_frame: self.stats_frame.pack_forget(); self.stats_visible = False;

                if hasattr(self, 'toggle_graph_button') and self.toggle_graph_button: self.toggle_graph_button.config(text="Afficher Graphique")
                if hasattr(self, 'toggle_stats_button') and self.toggle_stats_button: self.toggle_stats_button.config(text="Afficher Statistiques")

                logger.debug("Avant root.protocol")
                self.root.protocol("WM_DELETE_WINDOW", self.on_close) # Assurez-vous que on_close est définie
            else:
                logger.debug("Mode Optuna: pas d'UI disponible, étapes d'initialisation UI supplémentaires ignorées")
            logger.info("Initialisation __init__ terminée.")
            if auto_load_error and hasattr(self, 'root') and self.root is not None:
                self.root.after(200, lambda msg=f"Erreur chargement auto: {auto_load_error}": messagebox.showerror("Erreur Chargement Auto", msg))
        except Exception as e_init:
             logger.critical(f"ERREUR CRITIQUE PENDANT __init__: {e_init}", exc_info=True)
             if self.is_ui_available():
                 messagebox.showerror("Erreur Init", f"Erreur critique init:\n{e_init}")
             raise # Relancer pour arrêter