# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1423 à 1427
# Type: Méthode de la classe ConsecutiveConfidenceCalculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

                def __init__(self):
                    self.pattern_stats = defaultdict(lambda: {"total": 0, "success": 0, "consecutive_lengths": [], "max_consecutive": 0})
                    self.recent_recommendations = []
                    self.recent_outcomes = []
                    self.max_recent_history = getattr(config_ref, 'max_recent_history', 50)  # Nombre maximum de recommandations récentes à conserver