# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13941 à 13979
# Type: Méthode de la classe HybridBaccaratPredictor

    def _analyze_sequence_context(self, sequence: List[str]) -> float:
        """
        Analyse le contexte de la séquence pour adapter les poids des modèles.

        Args:
            sequence (List[str]): Séquence de résultats

        Returns:
            float: Facteur contextuel entre 0 et 1
                - Proche de 0: séquence stable, modèle global plus pertinent
                - Proche de 1: séquence volatile, modèle de session plus pertinent
        """
        if len(sequence) < 10:
            # Pas assez de données pour une analyse fiable
            return 0.5

        # 1. Calculer la volatilité récente (alternances dans les 10 derniers coups)
        recent_seq = sequence[-10:]
        alternances = 0
        for i in range(1, len(recent_seq)):
            if recent_seq[i] != recent_seq[i-1]:
                alternances += 1

        volatility = alternances / (len(recent_seq) - 1)

        # 2. Détecter les streaks récents
        current_streak = 1
        for i in range(len(sequence)-2, -1, -1):
            if sequence[i] == sequence[-1]:
                current_streak += 1
            else:
                break

        streak_factor = min(current_streak / 10, 1.0)  # Normaliser

        # 3. Combiner les facteurs (volatilité générale + importance du streak actuel)
        context_factor = (volatility * 0.7) + (streak_factor * 0.3)

        return context_factor