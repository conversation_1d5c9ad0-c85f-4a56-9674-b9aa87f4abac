# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13913 à 13939
# Type: Méthode de la classe HybridBaccaratPredictor

    def _calculate_decay_feature(self, sequence: List[str], target_outcome: str) -> float:
        """
        Calcule une feature avec decay pour donner plus de poids aux résultats récents.

        Args:
            sequence (List[str]): Séquence de résultats
            target_outcome (str): Résultat cible ('banker' ou 'player')

        Returns:
            float: Valeur de la feature avec decay
        """
        if not sequence:
            return 0.0

        decay_factor = self.config.decay_factor
        total_weight = 0
        weighted_sum = 0

        for i, outcome in enumerate(sequence):
            # Le poids augmente avec l'indice (plus récent = plus de poids)
            weight = decay_factor ** (len(sequence) - i - 1)
            total_weight += weight

            if outcome == target_outcome:
                weighted_sum += weight

        return weighted_sum / total_weight if total_weight > 0 else 0.0