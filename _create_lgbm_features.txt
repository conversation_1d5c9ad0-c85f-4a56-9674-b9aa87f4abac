# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13748 à 13801
# Type: Méthode de la classe HybridBaccaratPredictor

    def _create_lgbm_features(self, sequence: List[str]) -> List[float]:
        """
        Méthode interne pour créer les features LGBM.

        Args:
            sequence (List[str]): Séquence de résultats ('player', 'banker')

        Returns:
            List[float]: Liste des features pour LGBM
        """
        # Compteurs basiques
        banker_count = sum(1 for outcome in sequence if outcome == 'banker')
        player_count = sum(1 for outcome in sequence if outcome == 'player')

        # Calcul des streaks
        streak_counts = self._calculate_streaks(sequence)

        # Calcul des alternances
        alternate_info = self._calculate_alternates(sequence)

        # Decay features
        banker_decay = self._calculate_decay_feature(sequence, 'banker')
        player_decay = self._calculate_decay_feature(sequence, 'player')

        # Assembler toutes les features dans l'ordre attendu par le modèle
        features = [
            banker_count,
            player_count,
            banker_count / max(1, len(sequence)),  # Ratio banker
            player_count / max(1, len(sequence)),  # Ratio player
            streak_counts['banker_streaks'],
            streak_counts['player_streaks'],
            banker_decay,
            player_decay
        ]

        # Ajouter les streak lengths spécifiques (2-7)
        for length in range(2, 8):
            features.append(streak_counts[f'banker_streak_{length}'])
            features.append(streak_counts[f'player_streak_{length}'])

        # Ajouter les infos d'alternance
        features.extend([
            alternate_info['alternate_count_2'],
            alternate_info['alternate_count_3'],
            alternate_info['alternate_ratio'],
            streak_counts['max_banker_streak'],
            streak_counts['max_player_streak']
        ])

        # Les 3 features spécifiques à l'optimisation Optuna ont été supprimées
        # pour réduire le nombre de features de 28 à 25

        return features