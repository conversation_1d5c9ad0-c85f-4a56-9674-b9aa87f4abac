# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1429 à 1440
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

                def _extract_pattern_key(self, features_vector):
                    """Extrait une clé de pattern à partir du vecteur de features."""
                    # Utiliser les features les plus importantes pour créer une clé de pattern
                    # Arrondir les valeurs pour regrouper des patterns similaires
                    key_parts = []
                    for i, feature in enumerate(features_vector[:5]):  # Utiliser les 5 premières features
                        # Arrondir à 1 décimale pour les ratios, 0 décimale pour les autres
                        if i in [0, 1, 2, 3]:  # Ratios
                            key_parts.append(f"{i}:{round(feature, 1)}")
                        else:  # Autres features
                            key_parts.append(f"{i}:{round(feature)}")
                    return "|".join(key_parts)