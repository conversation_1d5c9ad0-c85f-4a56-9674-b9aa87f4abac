# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13167 à 13430
# Type: Méthode de la classe HybridBaccaratPredictor

    def _finalize_optuna_optimization(self, success: bool, best_params: Optional[Dict], duration: float, error_msg: Optional[str]):
        """
        Finalise l'optimisation multi-niveaux et applique les meilleurs paramètres trouvés.
        Cette méthode est appelée par le callback du OptunaThreadManager.

        Args:
            success: Indique si l'optimisation a réussi
            best_params: Meilleurs paramètres trouvés
            duration: Durée de l'optimisation en secondes
            error_msg: Message d'erreur éventuel

        Returns:
            None
        """
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        ui_available = self.is_ui_available()

        if ui_available:
            self.toggle_training_controls(enabled=True)

        with self.training_lock:
            self.is_training = False
            self.stop_training = False
            # Réinitialiser l'attribut is_optuna_running
            self.is_optuna_running = False
            # Réinitialiser les flags de log pour permettre l'affichage des messages lors de la prochaine session
            if hasattr(self, '_logged_sequence_length'):
                delattr(self, '_logged_sequence_length')
            if hasattr(self, '_logged_compat_sequence_length'):
                delattr(self, '_logged_compat_sequence_length')

        # Nettoyer les ressources du gestionnaire de thread
        if hasattr(self, 'optuna_thread_manager') and self.optuna_thread_manager:
            logger_instance.info("Nettoyage des ressources du gestionnaire de thread Optuna")
            # Le thread est déjà terminé à ce stade, pas besoin de l'arrêter explicitement

        optimizer_instance = getattr(self, 'current_optimizer_instance', None)
        self.current_optimizer_instance = None

        if not optimizer_instance:
            logger_instance.error("Impossible de récupérer l'instance OptunaOptimizer après l'exécution du thread.")

        final_progress_val = 0
        final_progress_msg = "Optimisation échouée"
        was_interrupted = (not success and error_msg == "Optimisation interrompue par l'utilisateur.")

        if best_params:
            is_different_from_default = False
            try:
                default_conf = PredictorConfig()
                current_config_dict = {k: getattr(self.config, k, None) for k in best_params if hasattr(self.config, k)}
                for k, v in best_params.items():
                    if k.startswith('weight_'): continue
                    if k in current_config_dict:
                        current_val = current_config_dict[k]
                        try:
                            if isinstance(current_val, bool): param_val_typed = bool(v)
                            elif isinstance(current_val, int): param_val_typed = int(float(v)) if isinstance(v, float) and float(v).is_integer() else int(v)
                            elif isinstance(current_val, float): param_val_typed = float(v)
                            else: param_val_typed = type(current_val)(v)
                            if current_val != param_val_typed:
                                is_different_from_default = True
                                break
                        except Exception:
                            is_different_from_default = True
                            break
                    else:
                        is_different_from_default = True
                        break
                if not is_different_from_default:
                    current_initial_weights = self.config.initial_weights
                    total_opt_weight = sum(best_params.get(f'weight_{k}', 0) for k in current_initial_weights)
                    if total_opt_weight > 1e-9:
                        normalized_opt_weights = {k: best_params.get(f'weight_{k}', 0) / total_opt_weight for k in current_initial_weights}
                        for k in current_initial_weights:
                            if abs(current_initial_weights[k] - normalized_opt_weights.get(k, 0)) > 1e-3:
                                is_different_from_default = True
                                break
                    elif len(current_initial_weights) > 0:
                         is_different_from_default = True

            except Exception as e_diff:
                logger_instance.warning(f"Erreur comparaison paramètres Optuna: {e_diff}")
                is_different_from_default = True

            if is_different_from_default:
                completion_type = "Interrompue" if was_interrupted else "Terminée"
                result_message = f"Optimisation multi-niveaux {completion_type} ({duration:.1f}s) !\nMeilleurs paramètres trouvés (parmi les essais effectués)."
                logger_instance.info(f"Optimisation multi-niveaux {completion_type}. Meilleurs params: {best_params}")
                final_progress_val = 100
                final_progress_msg = f"Optimisation {completion_type} ({duration:.1f}s)"

                if ui_available:
                    self.root.after(0, lambda p=final_progress_val, m=final_progress_msg: self._update_progress(p, m))

                    # Nettoyage explicite des processus et de la mémoire avant d'afficher la fenêtre de résultats
                    def cleanup_and_show_results():
                        # Nettoyage de la mémoire
                        gc.collect()
                        if torch.cuda.is_available():
                            try:
                                torch.cuda.empty_cache()
                            except Exception:
                                pass

                        # Forcer la terminaison des processus multiprocessing qui pourraient être encore actifs
                        try:
                            import multiprocessing
                            # Récupérer tous les processus actifs
                            active_children = multiprocessing.active_children()
                            if active_children:
                                logger.warning(f"Terminaison de {len(active_children)} processus multiprocessing actifs")
                                for process in active_children:
                                    try:
                                        process.terminate()
                                        process.join(timeout=1.0)  # Attendre 1 seconde maximum
                                    except Exception as e:
                                        logger.error(f"Erreur lors de la terminaison du processus {process.name}: {e}")
                        except Exception as e:
                            logger.error(f"Erreur lors de la terminaison des processus multiprocessing: {e}")

                        # Activer les contrôles et afficher la fenêtre de résultats
                        self.toggle_training_controls(enabled=True)
                        self._show_optuna_results_window(best_params)

                    # Utiliser after pour exécuter le nettoyage et afficher la fenêtre de résultats
                    self.root.after(100, cleanup_and_show_results)

                    # Afficher automatiquement les résultats d'optimisation
                    self.root.after(300, self.show_optimization_results)
            else:
                completion_type = "Interrompue" if was_interrupted else "Terminée"
                logger_instance.warning(f"Optimisation multi-niveaux {completion_type} mais sans amélioration trouvée par rapport aux paramètres actuels.")
                final_progress_val = 100
                final_progress_msg = f"Optimisation finie (sans amélioration)"
                if ui_available:
                    self.root.after(0, lambda p=final_progress_val, m=final_progress_msg: self._update_progress(p, m))

                    # Nettoyage explicite des processus et de la mémoire avant d'afficher le message
                    def cleanup_and_show_message():
                        # Nettoyage de la mémoire
                        gc.collect()
                        if torch.cuda.is_available():
                            try:
                                torch.cuda.empty_cache()
                            except Exception:
                                pass

                        # Forcer la terminaison des processus multiprocessing qui pourraient être encore actifs
                        try:
                            import multiprocessing
                            # Récupérer tous les processus actifs
                            active_children = multiprocessing.active_children()
                            if active_children:
                                logger.warning(f"Terminaison de {len(active_children)} processus multiprocessing actifs")
                                for process in active_children:
                                    try:
                                        process.terminate()
                                        process.join(timeout=1.0)  # Attendre 1 seconde maximum
                                    except Exception as e:
                                        logger.error(f"Erreur lors de la terminaison du processus {process.name}: {e}")
                        except Exception as e:
                            logger.error(f"Erreur lors de la terminaison des processus multiprocessing: {e}")

                        # Afficher le message
                        messagebox.showwarning("Optimisation multi-niveaux", f"Optimisation {completion_type} ({duration:.1f}s) sans trouver de meilleurs paramètres.")

                    # Utiliser after pour exécuter le nettoyage et afficher le message
                    self.root.after(100, cleanup_and_show_message)

                    # Afficher automatiquement les résultats d'optimisation même sans amélioration
                    self.root.after(300, self.show_optimization_results)
        else:
            final_progress_val = 0
            if was_interrupted:
                final_progress_msg = f"Optimisation Interrompue (0 essais valides)"
                logger_instance.warning(f"Finalisation Optimisation multi-niveaux: Interrompue avant de trouver des paramètres.")
                if ui_available:
                    self.root.after(0, lambda p=final_progress_val, m=final_progress_msg: self._update_progress(p, m))

                    # Nettoyage explicite des processus et de la mémoire avant d'afficher le message
                    def cleanup_and_show_message():
                        # Nettoyage de la mémoire
                        gc.collect()
                        if torch.cuda.is_available():
                            try:
                                torch.cuda.empty_cache()
                            except Exception:
                                pass

                        # Forcer la terminaison des processus multiprocessing qui pourraient être encore actifs
                        try:
                            import multiprocessing
                            # Récupérer tous les processus actifs
                            active_children = multiprocessing.active_children()
                            if active_children:
                                logger.warning(f"Terminaison de {len(active_children)} processus multiprocessing actifs")
                                for process in active_children:
                                    try:
                                        process.terminate()
                                        process.join(timeout=1.0)  # Attendre 1 seconde maximum
                                    except Exception as e:
                                        logger.error(f"Erreur lors de la terminaison du processus {process.name}: {e}")
                        except Exception as e:
                            logger.error(f"Erreur lors de la terminaison des processus multiprocessing: {e}")

                        # Afficher le message
                        messagebox.showwarning("Optimisation Interrompue", f"L'optimisation a été interrompue ({duration:.1f}s) avant qu'un essai valide ne soit terminé.")

                    # Utiliser after pour exécuter le nettoyage et afficher le message
                    self.root.after(100, cleanup_and_show_message)
            else:
                final_progress_msg = f"Échec Optimisation multi-niveaux ({duration:.1f}s)"
                logger_instance.error(f"Finalisation Optimisation multi-niveaux: Échec ou aucun résultat. Erreur: {error_msg}")
                if ui_available:
                    self.root.after(0, lambda p=final_progress_val, m=final_progress_msg: self._update_progress(p, m))

                    # Nettoyage explicite des processus et de la mémoire avant d'afficher le message
                    def cleanup_and_show_message():
                        # Nettoyage de la mémoire
                        gc.collect()
                        if torch.cuda.is_available():
                            try:
                                torch.cuda.empty_cache()
                            except Exception:
                                pass

                        # Forcer la terminaison des processus multiprocessing qui pourraient être encore actifs
                        try:
                            import multiprocessing
                            # Récupérer tous les processus actifs
                            active_children = multiprocessing.active_children()
                            if active_children:
                                logger.warning(f"Terminaison de {len(active_children)} processus multiprocessing actifs")
                                for process in active_children:
                                    try:
                                        process.terminate()
                                        process.join(timeout=1.0)  # Attendre 1 seconde maximum
                                    except Exception as e:
                                        logger.error(f"Erreur lors de la terminaison du processus {process.name}: {e}")
                        except Exception as e:
                            logger.error(f"Erreur lors de la terminaison des processus multiprocessing: {e}")

                        # Afficher le message d'erreur
                        display_error = error_msg if error_msg else "Aucun essai valide terminé ou erreur interne."
                        messagebox.showerror("Échec Optimisation multi-niveaux", f"L'optimisation a échoué ou n'a pas trouvé de résultat.\n{display_error[:500]}")

                    # Utiliser after pour exécuter le nettoyage et afficher le message
                    self.root.after(100, cleanup_and_show_message)

        # Réinitialiser la phase d'optimisation à None pour revenir en mode normal
        if hasattr(self.config, 'optimization_phase'):
            previous_phase = self.config.optimization_phase
            self.config.optimization_phase = None
            logger.info(f"Phase d'optimisation réinitialisée: {previous_phase} -> None (mode normal)")

        # Nettoyage de la mémoire
        gc.collect()
        if torch.cuda.is_available():
            try:
                torch.cuda.empty_cache()
            except Exception:
                pass
        logger.debug("_finalize_optuna_optimization: Nettoyage GC/CUDA effectué.")