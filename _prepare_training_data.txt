# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4137 à 4269
# Type: Méthode de la classe HybridBaccaratPredictor

    def _prepare_training_data(self, force_use_historical: bool = False,
                               max_games: Optional[int] = None,
                               sampling_fraction: Optional[float] = None
                               ) -> Tuple[
                                   Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray],
                                   Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray],
                                   Optional[List[List[str]]], Optional[List[int]] # Ajout List[int] pour origins
                               ]:
        """
        Prépare les données d'entraînement. Version 7 - Simplifiée et factorisée.
        MODIFIÉ : Simplification gestion erreurs, validations, et corrections config.
        """
        UI_UPDATE_INTERVAL = 5 # Pour ne pas spammer l'UI

        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        data_source_name = "historique chargé"
        ui_available = self.is_ui_available()

        # --- 1. Lecture Config et Validation ---
        min_target_idx = getattr(self.config, 'min_target_hand_index_training', 30)
        lstm_seq_len_cfg = getattr(self.config, 'lstm_sequence_length', 20)
        lstm_feat_count_cfg = getattr(self.config, 'lstm_input_size', 6) # **IMPORTANT: On accepte la valeur configurée!**
        # Utiliser lgbm_input_size de la configuration si disponible
        lgbm_feat_count_cfg = getattr(self.config, 'lgbm_input_size', 0)

        # Si lgbm_input_size n'est pas défini ou est 0, utiliser len(self.feature_names) comme fallback
        if lgbm_feat_count_cfg == 0:
            with self.model_lock:
                if hasattr(self, 'feature_names') and self.feature_names:
                    lgbm_feat_count_cfg = len(self.feature_names)

        # Validation critique du nombre de features LGBM
        if lgbm_feat_count_cfg == 0:
            logger_instance.error("_prepare_training_data: Nombre features LGBM (self.feature_names) est zéro ou non défini.")
            return (None, None, None, None, None, None, None, None)

        logger_instance.info(f"_prepare_training_data: Appel du Manager avec filtre min_target_hand_index = {min_target_idx}, LSTM feat count = {lstm_feat_count_cfg}")

        # --- 2. Préparation des Données (Historique, échantillonnage) ---
        with self.sequence_lock:
            if not self.loaded_historical or not self.historical_data:
                logger_instance.error(f"_prepare_training_data: Aucun {data_source_name} disponible.")
                return (None, None, None, None, None, None, None, None)
            original_historical_data = self.historical_data[:]
            num_original_games = len(original_historical_data)

        data_to_process, sampling_applied_info = self._apply_data_sampling(original_historical_data, max_games, sampling_fraction)
        logger_instance.info(sampling_applied_info)

        # --- 3. Instanciation et Appel du BaccaratSequenceManager ---
        if ui_available: self.root.after(0, lambda: self._update_progress(UI_UPDATE_INTERVAL, f"Génération via Manager ({len(data_to_process)} jeux)..."))

        if not hasattr(self, 'create_hybrid_features') or not callable(self.create_hybrid_features):
            logger_instance.critical("_prepare_training_data: Méthode 'create_hybrid_features' non trouvée ou non callable!")
            return (None, None, None, None, None, None, None, None)

        try:
            manager = BaccaratSequenceManager(
                sequence_length=lstm_seq_len_cfg,
                min_target_hand_index=min_target_idx,
                hybrid_feature_creator=self.create_hybrid_features,
                lgbm_feature_count=lgbm_feat_count_cfg,
                lstm_seq_len=lstm_seq_len_cfg,
                lstm_feature_count=lstm_feat_count_cfg, # UTILISER VALEUR CONFIGURÉE!
                parent_logger=logger_instance
            )
            manager_results = manager.prepare_data_for_model(data_to_process)
        except (ValueError, TypeError) as e_manager_init:
            logger_instance.error(f"Erreur initialisation BaccaratSequenceManager: {e_manager_init}", exc_info=True)
            if ui_available:
                messagebox.showerror("Erreur Préparation (Manager)", "Impossible d'initialiser le Manager.\nConsultez les logs.")
                self._update_progress(0, "Erreur Manager Init")
            return (None, None, None, None, None, None, None, None)
        except Exception as e_manager_prep:
            logger_instance.error(f"Erreur pendant manager.prepare_data_for_model: {e_manager_prep}", exc_info=True)
            if ui_available:
                messagebox.showerror("Erreur Préparation (Manager)", "Erreur pendant préparation données par le Manager.\nConsultez les logs.")
                self._update_progress(0, "Erreur Manager Prépa")
            return (None, None, None, None, None, None, None, None)

        # --- 4. Vérification et Unpack des Résultats du Manager ---
        if manager_results is None or len(manager_results) != 5 or manager_results[0] is None:
            logger_instance.error(f"Échec préparation données par BaccaratSequenceManager ({sampling_applied_info.split(':')[0].lower()}).")
            if ui_available:
                messagebox.showerror("Erreur Préparation (Manager)", "Impossible de préparer les données via le Manager.\nConsultez les logs.")
                self._update_progress(0, "Erreur Manager Data")
            return (None, None, None, None, None, None, None, None)

        X_lgbm_all, y_labels_all, X_lstm_all, list_of_all_prefix_sequences, list_of_all_origins = manager_results
        final_num_samples = len(y_labels_all)

        logger_instance.info(f"Manager a retourné {final_num_samples} échantillons filtrés/générés.")
        if ui_available: self.root.after(0, lambda: self._update_progress(UI_UPDATE_INTERVAL, "Calcul poids & Split..."))

        # --- 5. Validation Nombre Echantillons APRÈS FILTRAGE ---
        min_samples_config = self.config.min_training_samples
        if not force_use_historical and final_num_samples < min_samples_config:
            logger_instance.error(f"Pas assez d'échantillons valides retournés par Manager ({final_num_samples}). Min requis: {min_samples_config}.")
            if ui_available:
                messagebox.showerror("Données Insuffisantes (Post-Manager)", f"Pas assez d'échantillons ({final_num_samples}) retournés par le manager.\nMinimum requis: {min_samples_config}.")
                self._update_progress(0, f"Erreur: Manager data insuf.")
            return (None, None, None, None, None, None, None, None)

        # --- 6. Calcul des Poids et Split Train/Validation ---
        try:
            sample_weights_all = self._calculate_sample_weights(list_of_all_origins, final_num_samples)
            train_indices, val_indices = self._create_temporal_split(X_lgbm_all)

            # --- 7. Vérifications Finales et Retour ---
            (is_valid, message) = self._validate_data_shapes(X_lgbm_all, y_labels_all, X_lstm_all, sample_weights_all, list_of_all_prefix_sequences, list_of_all_origins, final_num_samples)
            if not is_valid:
                logger_instance.error(message)
                return (None, None, None, None, None, None, None, None)

            logger_instance.info(f"Préparation via Manager OK: {final_num_samples} éch. filtrés. Split: {len(train_indices)} train / {len(val_indices)} val.")
            logger_instance.info(f"  Shapes Finales: X_lgbm({X_lgbm_all.shape}), X_lstm({X_lstm_all.shape}), y({y_labels_all.shape}), w({sample_weights_all.shape})")
            logger_instance.info(f"  Nombre origines retourné: {len(list_of_all_origins)}")

            return (X_lgbm_all, y_labels_all, X_lstm_all, sample_weights_all, train_indices, val_indices, list_of_all_prefix_sequences, list_of_all_origins)

        except ValueError as ve:
            logger_instance.error(f"Erreur valeur pendant calcul poids/split post-manager: {ve}", exc_info=True)
            if ui_available:
              messagebox.showerror("Erreur Post-Manager", "Erreur pendant calcul des poids ou split.\nConsultez les logs.")
              self._update_progress(0, "Erreur Post-Manager")
            return (None, None, None, None, None, None, None, None)

        except Exception as e:
            logger_instance.error(f"Erreur inattendue pendant calcul poids/split post-manager: {e}", exc_info=True)
            if ui_available:
              messagebox.showerror("Erreur Post-Manager", "Erreur INATTENDUE pendant calcul des poids ou split.\nConsultez les logs.")
              self._update_progress(0, "Erreur Post-Manager Inatt.")
            return (None, None, None, None, None, None, None, None)