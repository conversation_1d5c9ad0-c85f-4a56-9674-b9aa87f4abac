# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 6716 à 6845
# Type: Méthode de la classe HybridBaccaratPredictor

    def _save_lgbm_training_plots(self, eval_result: Dict) -> None:
        """Crée et sauvegarde des graphiques des métriques d'entraînement LGBM."""
        try:
            # Forcer l'utilisation du backend non-interactif pour éviter les problèmes de thread
            import matplotlib
            matplotlib.use('Agg', force=True)
            import matplotlib.pyplot as plt

            # Créer un dossier pour les graphiques s'il n'existe pas
            plots_dir = os.path.join("logs", "training_plots")
            os.makedirs(plots_dir, exist_ok=True)

            # Nom de fichier basé sur la date et l'heure
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Vérifier si les résultats d'évaluation sont disponibles
            if not eval_result or not isinstance(eval_result, dict) or not eval_result:
                logger.warning("Aucun résultat d'évaluation disponible pour créer des graphiques LGBM.")
                return

            # Extraire les métriques
            metrics = list(eval_result.get('val', {}).keys())
            if not metrics:
                logger.warning("Aucune métrique trouvée dans les résultats d'évaluation LGBM.")
                return

            # Créer un graphique pour chaque métrique
            for metric in metrics:
                plt.figure(figsize=(10, 5))

                # Tracer les courbes d'entraînement et de validation
                if 'train' in eval_result and metric in eval_result['train']:
                    plt.plot(eval_result['train'][metric], label=f'Train {metric}')
                if 'val' in eval_result and metric in eval_result['val']:
                    plt.plot(eval_result['val'][metric], label=f'Validation {metric}')

                plt.title(f'Évolution de {metric} pendant l\'entraînement LGBM')
                plt.xlabel('Itération')
                plt.ylabel(metric)
                plt.legend()
                plt.grid(True)

                # Sauvegarder le graphique
                metric_plot_path = os.path.join(plots_dir, f"lgbm_{metric}_{timestamp}.png")
                plt.savefig(metric_plot_path)
                plt.close()

                logger.info(f"Graphique LGBM pour {metric} sauvegardé dans {metric_plot_path}")

            # Créer un graphique pour l'importance des caractéristiques si disponible
            if hasattr(self, 'lgbm_metrics') and 'feature_importance' in self.lgbm_metrics and hasattr(self, 'feature_names'):
                feature_importance = self.lgbm_metrics['feature_importance']
                if len(feature_importance) > 0 and len(self.feature_names) == len(feature_importance):
                    # Trier les caractéristiques par importance
                    indices = np.argsort(feature_importance)[::-1]
                    top_n = min(20, len(indices))  # Limiter à 20 caractéristiques pour la lisibilité

                    plt.figure(figsize=(12, 8))
                    plt.barh(range(top_n), feature_importance[indices[:top_n]], align='center')
                    plt.yticks(range(top_n), [self.feature_names[i] for i in indices[:top_n]])
                    plt.title('Importance des caractéristiques LGBM')
                    plt.xlabel('Importance')
                    plt.ylabel('Caractéristique')
                    plt.tight_layout()

                    # Sauvegarder le graphique
                    importance_plot_path = os.path.join(plots_dir, f"lgbm_feature_importance_{timestamp}.png")
                    plt.savefig(importance_plot_path)
                    plt.close()

                    logger.info(f"Graphique d'importance des caractéristiques LGBM sauvegardé dans {importance_plot_path}")

            # Créer un graphique pour la matrice de confusion si disponible
            if hasattr(self, 'lgbm_metrics') and 'confusion_matrix' in self.lgbm_metrics:
                cm = self.lgbm_metrics['confusion_matrix']

                plt.figure(figsize=(8, 6))
                plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
                plt.title('Matrice de confusion LGBM')
                plt.colorbar()

                classes = ['Banker', 'Player']
                tick_marks = np.arange(len(classes))
                plt.xticks(tick_marks, classes, rotation=45)
                plt.yticks(tick_marks, classes)

                # Ajouter les valeurs dans les cellules
                thresh = cm.max() / 2.
                for i in range(cm.shape[0]):
                    for j in range(cm.shape[1]):
                        plt.text(j, i, format(cm[i, j], 'd'),
                                horizontalalignment="center",
                                color="white" if cm[i, j] > thresh else "black")

                plt.tight_layout()
                plt.ylabel('Vrai label')
                plt.xlabel('Label prédit')

                # Sauvegarder le graphique
                cm_plot_path = os.path.join(plots_dir, f"lgbm_confusion_matrix_{timestamp}.png")
                plt.savefig(cm_plot_path)
                plt.close()

                logger.info(f"Graphique de matrice de confusion LGBM sauvegardé dans {cm_plot_path}")

            # Créer un graphique pour la courbe ROC si disponible
            if hasattr(self, 'lgbm_metrics') and 'auc_roc' in self.lgbm_metrics:
                auc_roc = self.lgbm_metrics['auc_roc']

                plt.figure(figsize=(8, 6))
                plt.plot([0, 1], [0, 1], 'k--')
                plt.xlim([0.0, 1.0])
                plt.ylim([0.0, 1.05])
                plt.xlabel('Taux de faux positifs')
                plt.ylabel('Taux de vrais positifs')
                plt.title(f'Courbe ROC (AUC = {auc_roc:.4f})')
                plt.legend(loc="lower right")
                plt.grid(True)

                # Sauvegarder le graphique
                roc_plot_path = os.path.join(plots_dir, f"lgbm_roc_curve_{timestamp}.png")
                plt.savefig(roc_plot_path)
                plt.close()

                logger.info(f"Graphique de courbe ROC LGBM sauvegardé dans {roc_plot_path}")

            logger.info(f"Tous les graphiques LGBM ont été sauvegardés dans {plots_dir}")

        except Exception as e:
            logger.error(f"Erreur lors de la création des graphiques LGBM: {e}", exc_info=True)