# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 6847 à 6953
# Type: Méthode de la classe HybridBaccaratPredictor

    def _save_lstm_training_plots(self) -> None:
        """Crée et sauvegarde des graphiques des métriques d'entraînement LSTM."""
        try:
            # Forcer l'utilisation du backend non-interactif pour éviter les problèmes de thread
            import matplotlib
            matplotlib.use('Agg', force=True)
            import matplotlib.pyplot as plt

            # Vérifier si les métriques LSTM sont disponibles
            if not hasattr(self, 'lstm_metrics') or not isinstance(self.lstm_metrics, dict) or not self.lstm_metrics:
                logger.warning("Aucune métrique LSTM disponible pour créer des graphiques.")
                return

            # Créer un dossier pour les graphiques s'il n'existe pas
            plots_dir = os.path.join("logs", "training_plots")
            os.makedirs(plots_dir, exist_ok=True)

            # Nom de fichier basé sur la date et l'heure
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Graphique des pertes
            if 'train_losses' in self.lstm_metrics:
                plt.figure(figsize=(10, 5))
                plt.plot(self.lstm_metrics['train_losses'], label='Train Loss')
                if 'val_losses' in self.lstm_metrics:
                    plt.plot(self.lstm_metrics['val_losses'], label='Validation Loss')
                plt.title('Évolution de la perte pendant l\'entraînement LSTM')
                plt.xlabel('Époque')
                plt.ylabel('Perte')
                plt.legend()
                plt.grid(True)
                loss_plot_path = os.path.join(plots_dir, f"lstm_loss_plot_{timestamp}.png")
                plt.savefig(loss_plot_path)
                plt.close()
                logger.info(f"Graphique des pertes LSTM sauvegardé dans {loss_plot_path}")

            # Graphique des exactitudes
            if 'train_accuracies' in self.lstm_metrics:
                plt.figure(figsize=(10, 5))
                plt.plot(self.lstm_metrics['train_accuracies'], label='Train Accuracy')
                if 'val_accuracies' in self.lstm_metrics:
                    plt.plot(self.lstm_metrics['val_accuracies'], label='Validation Accuracy')
                plt.title('Évolution de l\'exactitude pendant l\'entraînement LSTM')
                plt.xlabel('Époque')
                plt.ylabel('Exactitude')
                plt.legend()
                plt.grid(True)
                acc_plot_path = os.path.join(plots_dir, f"lstm_accuracy_plot_{timestamp}.png")
                plt.savefig(acc_plot_path)
                plt.close()
                logger.info(f"Graphique des exactitudes LSTM sauvegardé dans {acc_plot_path}")

            # Graphique de la matrice de confusion
            if 'confusion_matrix' in self.lstm_metrics:
                cm = self.lstm_metrics['confusion_matrix']

                plt.figure(figsize=(8, 6))
                plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
                plt.title('Matrice de confusion LSTM')
                plt.colorbar()

                classes = ['Banker', 'Player']
                tick_marks = np.arange(len(classes))
                plt.xticks(tick_marks, classes, rotation=45)
                plt.yticks(tick_marks, classes)

                # Ajouter les valeurs dans les cellules
                thresh = cm.max() / 2.
                for i in range(cm.shape[0]):
                    for j in range(cm.shape[1]):
                        plt.text(j, i, format(cm[i, j], 'd'),
                                horizontalalignment="center",
                                color="white" if cm[i, j] > thresh else "black")

                plt.tight_layout()
                plt.ylabel('Vrai label')
                plt.xlabel('Label prédit')

                cm_plot_path = os.path.join(plots_dir, f"lstm_confusion_matrix_{timestamp}.png")
                plt.savefig(cm_plot_path)
                plt.close()
                logger.info(f"Graphique de matrice de confusion LSTM sauvegardé dans {cm_plot_path}")

            # Graphique des métriques de classification
            if all(k in self.lstm_metrics for k in ['precision', 'recall', 'f1']):
                metrics = ['precision', 'recall', 'f1', 'accuracy']
                values = [self.lstm_metrics.get(m, 0) for m in metrics]

                plt.figure(figsize=(10, 6))
                plt.bar(metrics, values, color=['blue', 'green', 'red', 'purple'])
                plt.title('Métriques de classification LSTM')
                plt.ylim(0, 1)

                # Ajouter les valeurs au-dessus des barres
                for i, v in enumerate(values):
                    plt.text(i, v + 0.02, f'{v:.4f}', ha='center')

                plt.tight_layout()
                metrics_plot_path = os.path.join(plots_dir, f"lstm_metrics_{timestamp}.png")
                plt.savefig(metrics_plot_path)
                plt.close()
                logger.info(f"Graphique des métriques de classification LSTM sauvegardé dans {metrics_plot_path}")

            logger.info(f"Tous les graphiques LSTM ont été sauvegardés dans {plots_dir}")

        except Exception as e:
            logger.error(f"Erreur lors de la création des graphiques LSTM: {e}", exc_info=True)