# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7916 à 8004
# Type: Méthode de la classe HybridBaccaratPredictor

    def _show_feature_importance(self):
        """Affiche l'importance des caractéristiques dans l'onglet des graphiques."""
        # Effacer le contenu précédent
        for widget in self.plot_display_frame.winfo_children():
            widget.destroy()

        if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics or 'feature_importance' not in self.lgbm_metrics:
            ttk.Label(self.plot_display_frame, text="Aucune donnée d'importance des caractéristiques disponible").pack(pady=50)
            return

        if not hasattr(self, 'feature_names') or not self.feature_names:
            ttk.Label(self.plot_display_frame, text="Noms des caractéristiques non disponibles").pack(pady=50)
            return

        feature_importance = self.lgbm_metrics['feature_importance']
        if len(feature_importance) == 0 or len(self.feature_names) != len(feature_importance):
            ttk.Label(self.plot_display_frame, text="Données d'importance des caractéristiques invalides").pack(pady=50)
            return

        # Créer un canvas pour afficher le graphique
        canvas = tk.Canvas(self.plot_display_frame, bg='white')
        canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Obtenir les dimensions du canvas
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        # Si le canvas n'est pas encore rendu, utiliser des dimensions par défaut
        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width = 600
            canvas_height = 400

        # Définir les marges
        margin_left = 150
        margin_right = 20
        margin_top = 20
        margin_bottom = 20

        # Calculer les dimensions du graphique
        plot_width = canvas_width - margin_left - margin_right
        plot_height = canvas_height - margin_top - margin_bottom

        # Trier les caractéristiques par importance
        sorted_features = sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)

        # Limiter à 15 caractéristiques pour la lisibilité
        top_n = min(15, len(sorted_features))
        sorted_features = sorted_features[:top_n]

        # Dessiner les barres
        bar_height = plot_height / top_n
        max_importance = max(importance for _, importance in sorted_features)

        for i, (feature, importance) in enumerate(sorted_features):
            # Calculer les coordonnées de la barre
            x1 = margin_left
            y1 = margin_top + i * bar_height
            x2 = margin_left + (importance / max_importance) * plot_width
            y2 = y1 + bar_height * 0.8

            # Dessiner la barre
            canvas.create_rectangle(x1, y1, x2, y2, fill="blue")

            # Ajouter le nom de la caractéristique
            canvas.create_text(
                x1 - 5,
                y1 + bar_height * 0.4,
                text=feature,
                fill="black",
                anchor="e"
            )

            # Ajouter la valeur d'importance
            canvas.create_text(
                x2 + 5,
                y1 + bar_height * 0.4,
                text=f"{importance:.6f}",
                fill="black",
                anchor="w"
            )

        # Ajouter un titre
        canvas.create_text(
            canvas_width // 2,
            margin_top // 2,
            text="Importance des Caractéristiques LGBM",
            fill="black",
            font=("Arial", 12, "bold")
        )