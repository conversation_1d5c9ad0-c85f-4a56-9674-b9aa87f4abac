# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7549 à 7680
# Type: Méthode de la classe HybridBaccaratPredictor

    def _show_lgbm_learning_curves(self):
        """Affiche les courbes d'apprentissage LGBM dans l'onglet des graphiques."""
        # Effacer le contenu précédent
        for widget in self.plot_display_frame.winfo_children():
            widget.destroy()

        if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics or 'eval_result' not in self.lgbm_metrics:
            ttk.Label(self.plot_display_frame, text="Aucune donnée de courbe d'apprentissage LGBM disponible").pack(pady=50)
            return

        # Créer un canvas pour afficher les courbes
        canvas = tk.Canvas(self.plot_display_frame, bg='white')
        canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Obtenir les résultats d'évaluation
        eval_result = self.lgbm_metrics['eval_result']

        # Vérifier si les résultats d'évaluation sont disponibles
        if not eval_result or not isinstance(eval_result, dict) or not eval_result:
            ttk.Label(self.plot_display_frame, text="Aucun résultat d'évaluation disponible").pack(pady=50)
            return

        # Extraire les métriques
        metrics = []
        for dataset in eval_result:
            metrics.extend(list(eval_result[dataset].keys()))
        metrics = list(set(metrics))  # Supprimer les doublons

        if not metrics:
            ttk.Label(self.plot_display_frame, text="Aucune métrique trouvée dans les résultats d'évaluation").pack(pady=50)
            return

        # Obtenir les dimensions du canvas
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        # Si le canvas n'est pas encore rendu, utiliser des dimensions par défaut
        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width = 600
            canvas_height = 400

        # Définir les marges
        margin_left = 50
        margin_right = 20
        margin_top = 20
        margin_bottom = 50

        # Calculer les dimensions du graphique
        plot_width = canvas_width - margin_left - margin_right
        plot_height = canvas_height - margin_top - margin_bottom

        # Dessiner les axes
        canvas.create_line(
            margin_left, margin_top,
            margin_left, canvas_height - margin_bottom,
            fill="black", width=2
        )
        canvas.create_line(
            margin_left, canvas_height - margin_bottom,
            canvas_width - margin_right, canvas_height - margin_bottom,
            fill="black", width=2
        )

        # Dessiner les étiquettes des axes
        canvas.create_text(
            canvas_width // 2,
            canvas_height - margin_bottom // 2,
            text="Itération",
            fill="black"
        )
        canvas.create_text(
            margin_left // 2,
            canvas_height // 2,
            text="Valeur",
            fill="black",
            angle=90
        )

        # Couleurs pour les différentes métriques et datasets
        colors = {
            'train': {
                'binary_logloss': 'blue',
                'auc': 'green',
                'binary_error': 'red'
            },
            'val': {
                'binary_logloss': 'cyan',
                'auc': 'lime',
                'binary_error': 'magenta'
            }
        }

        # Dessiner les courbes pour chaque métrique et dataset
        legend_y = margin_top + 10
        legend_x = margin_left + plot_width - 150

        for dataset in eval_result:
            for metric in eval_result[dataset]:
                data = eval_result[dataset][metric]

                # Déterminer les valeurs min et max pour l'échelle
                min_val = min(data)
                max_val = max(data)

                # Ajouter une marge pour l'échelle
                val_range = max_val - min_val
                min_val = min_val - val_range * 0.1
                max_val = max_val + val_range * 0.1

                # Dessiner la courbe
                color = colors.get(dataset, {}).get(metric, 'black')
                self._draw_curve(
                    canvas,
                    data,
                    min_val, max_val,
                    margin_left, margin_top,
                    plot_width, plot_height,
                    color=color
                )

                # Ajouter à la légende
                canvas.create_line(
                    legend_x, legend_y, legend_x + 20, legend_y,
                    fill=color, width=2
                )
                canvas.create_text(
                    legend_x + 70, legend_y,
                    text=f"{dataset} {metric}",
                    fill=color,
                    anchor="w"
                )
                legend_y += 20