# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 5254 à 6714
# Type: Méthode de la classe HybridBaccaratPredictor

    def _train_models_async(self,
                            X_lgbm: np.n<PERSON><PERSON>, y_lgbm: np.ndarray,
                            X_lstm: np.ndarray, y_lstm: np.ndarray,
                            config_override: Optional[Dict[str, Any]] = None) -> None:
        # Importer les métriques nécessaires
        from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score, balanced_accuracy_score

        # Indiquer que nous sommes en phase d'entraînement pour réduire les logs d'avertissement
        self._is_training = True

        # Ajouter des logs pour déboguer l'entraînement
        logger.info(f"_train_models_async: Démarrage avec X_lgbm shape={X_lgbm.shape}, y_lgbm shape={y_lgbm.shape}")
        logger.info(f"_train_models_async: X_lstm shape={X_lstm.shape}, y_lstm shape={y_lstm.shape}")

        # Initialisation des logs d'entraînement
        logger.info("_train_models_async: Démarrage de l'entraînement...")

        start_time = time.time()
        train_summary = []
        success = False
        scaler_updated, lgbm_base_updated, lstm_trained = False, False, False
        calibrated_lgbm_updated, uncertainty_model_updated = False, False
        confidence_metrics_used = False  # Indicateur pour savoir si les métriques de confiance ont été utilisées

        effective_config = self.config
        # Utiliser le nombre d'époques LSTM défini dans la configuration
        lstm_epochs = getattr(self.config, 'lstm_epochs', 6)
        logger.info(f"Utilisation configuration: epochs LSTM={lstm_epochs}")

        try:
            ui_available = self.is_ui_available()
            if ui_available: self._safe_update_progress(5, "Démarrage Entraînement Complet...")

            n_features_lgbm = X_lgbm.shape[1]
            if self.feature_scaler is None or not isinstance(self.feature_scaler, StandardScaler):
                logger.warning("Feature Scaler manquant ou type incorrect, création d'un nouveau.")
                temp_scaler = StandardScaler()
            else:
                temp_scaler = clone(self.feature_scaler)
                logger.debug("Utilisation d'un clone du scaler existant pour l'entraînement.")

            if self.stop_training: raise InterruptedError("Arrêt demandé avant fit Scaler.")
            if ui_available: self._safe_update_progress(10, "Ajustement Scaler...")
            try:
                temp_scaler.fit(X_lgbm)
                with self.model_lock:
                    self.feature_scaler = temp_scaler
                    scaler_updated = True
                logger.info(f"StandardScaler ajusté ('fit').")
                train_summary.append(f"Scaler ({X_lgbm.shape[0]})")
            except ValueError as e_scaler_fit:
                logger.error(f"Erreur pendant StandardScaler fit: {e_scaler_fit}", exc_info=True)
                raise RuntimeError("Échec entraînement Scaler.") from e_scaler_fit

            if ui_available: self._safe_update_progress(15, "Mise à l'échelle données LGBM...")
            try:
                X_lgbm_scaled = self.feature_scaler.transform(X_lgbm)
            except NotFittedError:
                logger.error("Erreur: Tentative transform avec scaler non 'fit'.")
                raise RuntimeError("Scaler non 'fit' après la tentative d'entraînement.")
            except ValueError as e_scaler_transform:
                logger.error(f"Erreur StandardScaler transform: {e_scaler_transform}", exc_info=True)
                raise RuntimeError("Échec mise à l'échelle données LGBM.") from e_scaler_transform

            if self.stop_training: raise InterruptedError("Arrêt demandé avant entr. LGBM Base.")
            if ui_available: self._safe_update_progress(20, "Entraînement LGBM Base...")

            init_model_booster = None
            if self.lgbm_base and hasattr(self.lgbm_base, 'booster_'):
                 try:
                      check_is_fitted(self.lgbm_base)
                      init_model_booster = self.lgbm_base.booster_
                      logger.info("Utilisation du booster du modèle LGBM Base existant comme init_model.")
                 except NotFittedError:
                      logger.warning("Modèle LGBM Base existant non 'fit', init_model ne sera pas utilisé.")
                 except Exception as e_booster_check:
                      logger.error(f"Erreur accès booster LGBM existant: {e_booster_check}, init_model non utilisé.")

            # Utiliser tous les cœurs CPU disponibles pour LGBM
            # Utiliser le module psutil global, pas une variable locale
            import sys
            # Vérifier si psutil est disponible dans le module global
            if 'psutil' in sys.modules:
                max_logical_cores = sys.modules['psutil'].cpu_count(logical=True)
            else:
                max_logical_cores = 1
            logger.info(f"Nombre de cœurs logiques détectés: {max_logical_cores}")

            # Utiliser tous les cœurs disponibles pour maximiser la performance
            num_cpu_jobs = max(1, self.cpu_cores.get())
            # S'assurer de ne pas dépasser le nombre de cœurs logiques disponibles
            num_cpu_jobs = min(num_cpu_jobs, max_logical_cores)
            logger.info(f"Entraînement LGBM Base avec n_jobs={num_cpu_jobs} (utilisation de tous les cœurs disponibles)")

            lgbm_params_from_config = {}
            missing_lgbm_params = []
            lgbm_config_keys = [
                'lgbm_n_estimators', 'lgbm_learning_rate', 'lgbm_max_depth', 'lgbm_num_leaves',
                'lgbm_min_child_samples', 'lgbm_subsample', 'lgbm_colsample_bytree',
                'lgbm_reg_alpha', 'lgbm_reg_lambda'
            ]
            config_to_lgbm_map = {
                'lgbm_n_estimators': 'n_estimators',
                'lgbm_learning_rate': 'learning_rate',
                'lgbm_max_depth': 'max_depth',
                'lgbm_num_leaves': 'num_leaves',
                'lgbm_min_child_samples': 'min_child_samples',
                'lgbm_subsample': 'subsample',
                'lgbm_colsample_bytree': 'colsample_bytree',
                'lgbm_reg_alpha': 'reg_alpha',
                'lgbm_reg_lambda': 'reg_lambda'
            }

            for config_key in lgbm_config_keys:
                if hasattr(self.config, config_key):
                    lgbm_arg_name = config_to_lgbm_map.get(config_key, config_key)
                    lgbm_params_from_config[lgbm_arg_name] = getattr(self.config, config_key)
                else:
                    missing_lgbm_params.append(config_key)
                    logger.warning(f"Attribut de configuration LGBM '{config_key}' non trouvé dans PredictorConfig.")

            if not missing_lgbm_params:
                 logger.info(f"Paramètres LGBM chargés depuis PredictorConfig pour entraînement: {lgbm_params_from_config}")
            else:
                 logger.warning(f"Certains paramètres LGBM manquants dans config pour entraînement: {missing_lgbm_params}. LGBM utilisera ses défauts pour ceux-là.")

            # Préparation des poids d'échantillons basés sur les recommandations consécutives
            sample_weights_lgbm = None
            try:
                # Générer les positions de séquence pour les échantillons
                sequence_positions = np.arange(len(X_lgbm_scaled))

                # Calculer les poids d'échantillons focalisés sur les recommandations consécutives
                if len(X_lgbm_scaled) > 0 and hasattr(self, 'calculate_consecutive_focused_weights'):
                    logger.info("Calcul des poids d'échantillons focalisés sur les recommandations NON-WAIT valides consécutives...")
                    sample_weights_lgbm = self.calculate_consecutive_focused_weights(X_lgbm_scaled, y_lgbm, sequence_positions)
                    if sample_weights_lgbm is not None and len(sample_weights_lgbm) == len(X_lgbm_scaled):
                        logger.info(f"Poids d'échantillons calculés avec succès: min={np.min(sample_weights_lgbm):.4f}, max={np.max(sample_weights_lgbm):.4f}, mean={np.mean(sample_weights_lgbm):.4f}")
                        confidence_metrics_used = True
                    else:
                        logger.warning("Échec du calcul des poids d'échantillons ou longueur incorrecte. Utilisation de poids uniformes.")
                        sample_weights_lgbm = None
                # Fallback sur la méthode précédente si la nouvelle n'est pas disponible
                elif len(X_lgbm_scaled) > 0 and hasattr(self, 'calculate_sample_weights_from_metrics'):
                    logger.info("Calcul des poids d'échantillons basés sur les métriques de confiance et d'incertitude (fallback)...")
                    sample_weights_lgbm = self.calculate_sample_weights_from_metrics(X_lgbm_scaled)
                    if sample_weights_lgbm is not None and len(sample_weights_lgbm) == len(X_lgbm_scaled):
                        logger.info(f"Poids d'échantillons calculés avec succès (fallback): min={np.min(sample_weights_lgbm):.4f}, max={np.max(sample_weights_lgbm):.4f}, mean={np.mean(sample_weights_lgbm):.4f}")
                        confidence_metrics_used = True
                    else:
                        logger.warning("Échec du calcul des poids d'échantillons ou longueur incorrecte. Utilisation de poids uniformes.")
                        sample_weights_lgbm = None
            except Exception as e_weights:
                logger.error(f"Erreur lors du calcul des poids d'échantillons: {e_weights}", exc_info=True)
                sample_weights_lgbm = None

            temp_lgbm_base = LGBMClassifier(
                random_state=self.config.random_seed,
                n_jobs=num_cpu_jobs,
                **lgbm_params_from_config
            )

            try:
                # Diviser les données en ensembles d'entraînement et de validation
                X_train_lgbm, X_val_lgbm, y_train_lgbm, y_val_lgbm = train_test_split(
                    X_lgbm_scaled, y_lgbm, test_size=0.2, random_state=self.config.random_seed, stratify=y_lgbm
                )

                # Créer un callback pour suivre les métriques pendant l'entraînement
                eval_metrics = ['binary_logloss', 'auc', 'binary_error']
                eval_result = {}

                # Utiliser notre métrique personnalisée focalisée sur l'objectif 1
                # Utiliser la métrique isolée pour éviter les problèmes de pickling
                try:
                    from isolated_metrics import isolated_consecutive_focused_metric
                    logger.info("Utilisation de la métrique isolée 'isolated_consecutive_focused_metric' pour l'entraînement LGBM (objectif 1)")
                    eval_metrics = [isolated_consecutive_focused_metric]
                    # Ajouter un log pour indiquer que l'entraînement est focalisé sur l'objectif 1
                    logger.info("ENTRAÎNEMENT FOCALISÉ SUR L'OBJECTIF 1: Recommandations NON-WAIT valides consécutives")
                except ImportError:
                    logger.warning("Module isolated_metrics non disponible, utilisation de métriques standard")
                    eval_metrics = ['auc', 'binary_logloss']

                start_lgbm_base = time.time()
                # Utiliser les poids d'échantillons si disponibles
                if sample_weights_lgbm is not None:
                    # Diviser les poids d'échantillons selon la même stratégie que les données
                    sample_weights_train, sample_weights_val = train_test_split(
                        sample_weights_lgbm, test_size=0.2, random_state=self.config.random_seed, stratify=y_lgbm
                    )

                    # Vérifier que les dimensions correspondent
                    if len(sample_weights_train) != len(X_train_lgbm):
                        logger.warning(f"Dimensions incohérentes: sample_weights_train ({len(sample_weights_train)}) != X_train_lgbm ({len(X_train_lgbm)}). Utilisation de poids uniformes.")
                        sample_weights_train = None

                    # Utiliser des métriques standard pour éviter les problèmes de pickling
                    temp_lgbm_base.fit(
                        X_train_lgbm, y_train_lgbm,
                        sample_weight=sample_weights_train,  # Utiliser uniquement les poids de l'ensemble d'entraînement
                        eval_set=[(X_train_lgbm, y_train_lgbm), (X_val_lgbm, y_val_lgbm)],
                        eval_names=['train', 'val'],
                        eval_metric=['auc', 'binary_logloss'],  # Métriques standard sans référence à self
                        callbacks=[lgb.record_evaluation(eval_result)],
                        init_model=init_model_booster
                    )
                    logger.info("LGBM Base entraîné avec poids d'échantillons focalisés sur les recommandations consécutives.")
                else:
                    # Utiliser des métriques standard pour éviter les problèmes de pickling
                    temp_lgbm_base.fit(
                        X_train_lgbm, y_train_lgbm,
                        eval_set=[(X_train_lgbm, y_train_lgbm), (X_val_lgbm, y_val_lgbm)],
                        eval_names=['train', 'val'],
                        eval_metric=['auc', 'binary_logloss'],  # Métriques standard sans référence à self
                        callbacks=[lgb.record_evaluation(eval_result)],
                        init_model=init_model_booster
                    )
                    logger.info("LGBM Base entraîné avec poids uniformes (poids focalisés sur les recommandations consécutives non utilisés).")

                # Calculer et afficher des métriques supplémentaires
                y_pred_lgbm = temp_lgbm_base.predict(X_val_lgbm)
                y_pred_proba_lgbm = temp_lgbm_base.predict_proba(X_val_lgbm)[:, 1]

                # Matrice de confusion
                cm = confusion_matrix(y_val_lgbm, y_pred_lgbm)

                # Précision, rappel, F1-score avec zero_division=0 pour éviter les avertissements
                precision = precision_score(y_val_lgbm, y_pred_lgbm, zero_division=0)
                recall = recall_score(y_val_lgbm, y_pred_lgbm, zero_division=0)
                f1 = f1_score(y_val_lgbm, y_pred_lgbm, zero_division=0)

                # AUC-ROC
                auc_roc = roc_auc_score(y_val_lgbm, y_pred_proba_lgbm)

                # Importance des caractéristiques
                feature_importance = temp_lgbm_base.feature_importances_

                # Afficher les métriques
                logger.info(f"LGBM - Métriques de validation:")
                logger.info(f"  Matrice de confusion: {cm}")
                logger.info(f"  Précision: {precision:.4f}, Rappel: {recall:.4f}, F1-Score: {f1:.4f}")
                logger.info(f"  AUC-ROC: {auc_roc:.4f}")

                # Afficher les caractéristiques les plus importantes
                if hasattr(self, 'feature_names') and len(self.feature_names) > 0:
                    top_features = sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)[:5]
                    logger.info(f"  Top 5 caractéristiques importantes: {top_features}")

                # Sauvegarder les métriques pour affichage dans l'interface utilisateur
                self.lgbm_metrics = {
                    'confusion_matrix': cm,
                    'precision': precision,
                    'recall': recall,
                    'f1': f1,
                    'auc_roc': auc_roc,
                    'feature_importance': feature_importance,
                    'eval_result': eval_result
                }

                # Créer et sauvegarder des graphiques si matplotlib est disponible
                try:
                    self._save_lgbm_training_plots(eval_result)
                except Exception as e_plot:
                    logger.error(f"Erreur lors de la création des graphiques LGBM: {e_plot}", exc_info=True)

                with self.model_lock:
                    self.lgbm_base = temp_lgbm_base
                    lgbm_base_updated = True
                lgbm_base_time = time.time() - start_lgbm_base
                logger.info(f"Entraînement LGBM Base terminé ({lgbm_base_time:.2f}s).")
                train_summary.append(f"LGBM Base ({y_lgbm.shape[0]}){' avec métriques de confiance' if confidence_metrics_used else ''}")

                X_train_calib, X_val_calib, y_train_calib, y_val_calib = train_test_split(
                     X_lgbm_scaled, y_lgbm, test_size=0.3, random_state=self.config.random_seed, stratify=y_lgbm
                )
            except Exception as e_lgbm_base:
                logger.error(f"Erreur pendant entraînement LGBM Base: {e_lgbm_base}", exc_info=True)
                raise RuntimeError("Échec entraînement LGBM Base.") from e_lgbm_base

            if self.stop_training: raise InterruptedError("Arrêt demandé avant entr. LSTM.")
            if ui_available: self._safe_update_progress(40, f"Entraînement LSTM ({lstm_epochs} epochs)...")

            self.lstm, self.optimizer, self.scheduler = None, None, None
            lstm_input_size = X_lstm.shape[2]
            logger.info(f"Début entraînement LSTM... Input size: {lstm_input_size}")

            # Créer le modèle LSTM
            # S'assurer que les dimensions du modèle LSTM sont cohérentes
            # Si lstm_hidden_dim et lstm_hidden_size sont différents, les synchroniser
            if hasattr(self.config, 'lstm_hidden_dim') and hasattr(self.config, 'lstm_hidden_size'):
                if self.config.lstm_hidden_dim != self.config.lstm_hidden_size:
                    logger.warning(f"Synchronisation des dimensions LSTM: lstm_hidden_dim={self.config.lstm_hidden_dim}, lstm_hidden_size={self.config.lstm_hidden_size}")
                    # Utiliser lstm_hidden_dim comme valeur de référence
                    self.config.lstm_hidden_size = self.config.lstm_hidden_dim
                    logger.warning(f"Dimensions LSTM synchronisées: lstm_hidden_dim=lstm_hidden_size={self.config.lstm_hidden_dim}")

            # S'assurer que les dimensions du modèle LSTM sont cohérentes
            # Si lstm_hidden_dim et lstm_hidden_size sont différents, les synchroniser
            if hasattr(self.config, 'lstm_hidden_dim') and hasattr(self.config, 'lstm_hidden_size'):
                if self.config.lstm_hidden_dim != self.config.lstm_hidden_size:
                    logger.warning(f"Synchronisation des dimensions LSTM: lstm_hidden_dim={self.config.lstm_hidden_dim}, lstm_hidden_size={self.config.lstm_hidden_size}")
                    # Utiliser lstm_hidden_dim comme valeur de référence
                    self.config.lstm_hidden_size = self.config.lstm_hidden_dim
                    logger.warning(f"Dimensions LSTM synchronisées: lstm_hidden_dim=lstm_hidden_size={self.config.lstm_hidden_dim}")

            lstm = EnhancedLSTMModel(
                input_size=lstm_input_size,
                hidden_dim=self.config.lstm_hidden_dim,
                num_layers=self.config.lstm_num_layers,
                output_size=2,
                dropout_prob=self.config.lstm_dropout,
                bidirectional=self.config.lstm_bidirectional
            )

            # Optimiser le modèle LSTM pour une meilleure utilisation de la mémoire
            lstm = optimize_lstm_memory(lstm)

            # Déplacer le modèle sur le device approprié
            temp_lstm = lstm.to(self.device)

            # S'assurer que le modèle est en mode d'entraînement
            temp_lstm.train()

            # S'assurer que requires_grad est activé pour tous les paramètres
            for param in temp_lstm.parameters():
                param.requires_grad = True

            # Créer l'optimiseur après avoir configuré requires_grad
            # Utiliser le taux d'apprentissage configuré (déjà optimisé pour les manches cibles 31-60)
            learning_rate = self.config.lstm_learning_rate
            logger.info(f"Utilisation du taux d'apprentissage configuré: {learning_rate:.6f}")
            temp_optimizer = optim.AdamW(temp_lstm.parameters(), lr=learning_rate, weight_decay=self.config.lstm_weight_decay)

            # Utiliser la patience du scheduler configurée
            scheduler_patience = self.config.scheduler_patience
            logger.info(f"Utilisation de la patience de scheduler configurée: {scheduler_patience}")
            temp_scheduler = optim.lr_scheduler.ReduceLROnPlateau(temp_optimizer, mode='min', factor=self.config.scheduler_factor, patience=scheduler_patience)

            num_samples_lstm = X_lstm.shape[0]
            # Utiliser 80% des ressources disponibles sous Windows
            # Utiliser le module psutil global, ne pas le réimporter localement
            if psutil is not None:
                available_ram = psutil.virtual_memory().total
                target_ram_bytes = available_ram * 0.8  # 80% de la RAM disponible
                target_ram_gb = target_ram_bytes / (1024**3)
                logger.info(f"Utilisation de 80% de la RAM disponible ({target_ram_gb:.1f} GB) pour l'entraînement des modèles")
            else:
                # Valeur par défaut si psutil n'est pas disponible
                target_ram_bytes = 8 * (1024**3)  # 8 GB par défaut
                target_ram_gb = 8.0
                logger.info(f"Module psutil non disponible, utilisation d'une valeur par défaut de {target_ram_gb:.1f} GB pour l'entraînement des modèles")

            # Calculer la taille estimée d'un échantillon en mémoire avec une méthode plus robuste
            sample_ram_est = 0
            if num_samples_lstm > 0:
                # Pour X_lstm, prendre en compte toutes les dimensions d'un échantillon
                if len(X_lstm.shape) > 1:
                    # Si X_lstm est multidimensionnel, calculer la taille d'un échantillon complet
                    sample_ram_est = np.prod(X_lstm[0].shape) * X_lstm.itemsize
                else:
                    # Fallback sur la méthode simple
                    sample_ram_est = X_lstm.nbytes / num_samples_lstm

                # Ajouter un facteur de sécurité pour tenir compte des surcharges mémoire
                sample_ram_est = sample_ram_est * 10.0  # 1000% de marge de sécurité pour compenser la sous-estimation significative

            # Vérifier que la taille estimée est raisonnable (au moins 4 bytes)
            sample_ram_est = max(4, sample_ram_est)

            # Calculer la taille de batch optimale en utilisant 80% des ressources disponibles
            if sample_ram_est > 0:
                # Calculer la taille de batch optimale basée sur 80% des ressources disponibles
                dynamics_batch_size = int((target_ram_bytes * 0.8) / sample_ram_est)
                logger.info(f"Taille de batch calculée pour utiliser {target_ram_gb:.1f} GB (80% des ressources disponibles): {dynamics_batch_size}")

                # Vérifier si la taille calculée est raisonnable
                if dynamics_batch_size > 1024:
                    logger.warning(f"Taille de batch calculée anormalement grande ({dynamics_batch_size}), possible sous-estimation de la taille des échantillons")
                    # Limiter à une valeur plus raisonnable
                    dynamics_batch_size = 1024
                    logger.info(f"Taille de batch limitée à {dynamics_batch_size} pour éviter les problèmes de mémoire")
            else:
                # Fallback si on ne peut pas estimer la taille
                dynamics_batch_size = min(1024, self.config.batch_sizes['large'] * 4)  # Limiter à 1024
                logger.info(f"Impossible d'estimer la taille mémoire des échantillons, utilisation d'une taille de batch par défaut: {dynamics_batch_size}")

            # Limites de sécurité pour la taille du batch - optimisées pour équilibrer performance et rapidité
            # Limite supérieure absolue pour éviter les problèmes de mémoire
            max_batch_size = getattr(self.config, 'max_batch_size', 8192)  # Utiliser la valeur de config ou 8192 par défaut

            # Limite basée sur la configuration, mais plafonnée
            batch_size_multiplier = getattr(self.config, 'batch_size_multiplier', 8)
            config_based_limit = min(self.config.batch_sizes.get('large', 128) * batch_size_multiplier, max_batch_size)

            # Limite basée sur la taille du dataset (pas plus de X% du dataset)
            dataset_ratio = getattr(self.config, 'dataset_batch_ratio', 0.1)  # 10% par défaut
            dataset_based_limit = int(num_samples_lstm * dataset_ratio) if num_samples_lstm > 0 else max_batch_size

            # Appliquer toutes les limites
            dynamics_batch_size = min(dynamics_batch_size, config_based_limit, dataset_based_limit, max_batch_size)

            # Assurer une taille minimale pour la performance
            min_batch_size = getattr(self.config, 'min_batch_size', 512)  # Utiliser la valeur de config ou 512 par défaut
            dynamics_batch_size = max(min_batch_size, dynamics_batch_size)

            logger.info(f"Taille de batch finale pour l'entraînement LSTM après application des limites de sécurité: {dynamics_batch_size} échantillons")

            # Utiliser tous les cœurs disponibles pour un traitement parallèle maximal
            max_workers = self.cpu_cores.get()

            # Utiliser le nombre de workers défini dans la configuration
            num_workers = getattr(self.config, 'dataloader_num_workers', 6)  # Utiliser la valeur de config ou 6 par défaut

            # Utiliser persistent_workers selon la configuration
            persistent_workers = getattr(self.config, 'dataloader_persistent_workers', True)  # Utiliser la valeur de config ou True par défaut

            logger.info(f"Utilisation de {num_workers} workers pour le DataLoader (demandé explicitement)")

            # Activer le pin_memory uniquement si on utilise CUDA
            pin_memory_setting = (self.device.type == 'cuda')

            logger.info(f"DataLoader LSTM: Batch Size={dynamics_batch_size}, Num Workers={num_workers}, Pin Memory={pin_memory_setting}")

            # Préparation des poids d'échantillons pour LSTM basés sur les métriques de confiance et d'incertitude
            sample_weights_lstm = None
            try:
                # Générer les positions de séquence pour les échantillons
                sequence_positions = np.arange(len(X_lstm))

                # Calculer les poids d'échantillons pour LSTM si possible
                # Suppression de la vérification de self.lstm qui n'est pas encore initialisé à ce stade
                if hasattr(self, 'calculate_lstm_sample_weights'):
                    logger.info("Calcul des poids d'échantillons pour LSTM basés sur les métriques de confiance et positions de séquence...")
                    try:
                        sample_weights_lstm = self.calculate_lstm_sample_weights(X_lstm, y_lstm, sequence_positions)
                        if sample_weights_lstm is not None and len(sample_weights_lstm) == len(X_lstm):
                            logger.info(f"Poids d'échantillons LSTM calculés: min={np.min(sample_weights_lstm):.4f}, max={np.max(sample_weights_lstm):.4f}, mean={np.mean(sample_weights_lstm):.4f}")
                            confidence_metrics_used = True
                        else:
                            logger.warning("Échec du calcul des poids d'échantillons LSTM. Utilisation de poids uniformes.")
                            sample_weights_lstm = None
                    except Exception as e_weights_calc:
                        logger.warning(f"Erreur lors du calcul des poids d'échantillons LSTM: {e_weights_calc}. Utilisation de poids uniformes.")
                        sample_weights_lstm = None
            except Exception as e_lstm_weights:
                logger.error(f"Erreur lors du calcul des poids d'échantillons LSTM: {e_lstm_weights}", exc_info=True)
                sample_weights_lstm = None

            # Générer les positions de séquence pour les échantillons
            sequence_positions = np.arange(len(X_lstm))

            # Créer le dataset et le dataloader avec optimisations avancées
            if sample_weights_lstm is not None:
                # Utiliser un dataset pondéré avec positions de séquence
                dataset = BaccaratDataset(X_lstm, y_lstm, sample_weights_lstm, sequence_positions)
                # Utiliser un sampler pondéré pour donner plus d'importance aux échantillons avec des poids élevés
                from torch.utils.data import WeightedRandomSampler
                # Convertir les poids en probabilités de sélection
                sample_probs = sample_weights_lstm / np.sum(sample_weights_lstm)
                sampler = WeightedRandomSampler(
                    weights=sample_probs,
                    num_samples=len(X_lstm),  # Utiliser la taille du dataset, pas des poids
                    replacement=True
                )
                loader = DataLoader(
                    dataset,
                    batch_size=dynamics_batch_size,
                    sampler=sampler,  # Utiliser le sampler pondéré au lieu de shuffle=True
                    num_workers=num_workers,
                    pin_memory=pin_memory_setting,
                    persistent_workers=persistent_workers,
                    prefetch_factor=4 if num_workers > 0 else None,  # Utiliser un prefetch_factor modéré pour équilibrer performance et consommation mémoire
                    drop_last=False,
                    multiprocessing_context='spawn' if num_workers > 0 else None  # Utiliser 'spawn' pour une meilleure compatibilité
                )
                logger.info("DataLoader LSTM optimisé créé avec échantillonnage pondéré et positions de séquence.")
            else:
                # Utiliser un dataset standard avec positions de séquence
                dataset = BaccaratDataset(X_lstm, y_lstm, None, sequence_positions)
                loader = DataLoader(
                    dataset,
                    batch_size=dynamics_batch_size,
                    shuffle=True,
                    num_workers=num_workers,
                    pin_memory=pin_memory_setting,
                    persistent_workers=persistent_workers,
                    prefetch_factor=4 if num_workers > 0 else None,  # Utiliser un prefetch_factor modéré pour équilibrer performance et consommation mémoire
                    drop_last=False,
                    multiprocessing_context='spawn' if num_workers > 0 else None  # Utiliser 'spawn' pour une meilleure compatibilité
                )
                logger.info("DataLoader LSTM optimisé créé avec échantillonnage uniforme et positions de séquence.")

            # Fonction de perte personnalisée pour optimiser les recommandations consécutives
            if hasattr(self, 'consecutive_valid_recommendations_loss'):
                criterion = self.consecutive_valid_recommendations_loss
                logger.info("Utilisation de la fonction de perte 'consecutive_valid_recommendations_loss' pour LSTM.")
            # Fallback sur la fonction de perte avec prise en compte de l'incertitude
            elif confidence_metrics_used and hasattr(self, 'uncertainty_weighted_loss'):
                criterion = self.uncertainty_weighted_loss
                logger.info("Utilisation d'une fonction de perte pondérée par l'incertitude pour LSTM (fallback).")
            else:
                criterion = nn.CrossEntropyLoss()
                logger.info("Utilisation de la fonction de perte standard (CrossEntropyLoss) pour LSTM.")

            # Diviser les données en ensembles d'entraînement et de validation
            if len(X_lstm) > 10:  # S'assurer qu'il y a suffisamment de données pour la validation
                X_train_lstm, X_val_lstm, y_train_lstm, y_val_lstm = train_test_split(
                    X_lstm, y_lstm, test_size=0.2, random_state=self.config.random_seed, stratify=y_lstm
                )

                # Créer les datasets et dataloaders pour l'entraînement et la validation
                if sample_weights_lstm is not None:
                    # Diviser les poids d'échantillons selon la même stratégie que les données
                    sample_weights_train, sample_weights_val = train_test_split(
                        sample_weights_lstm, test_size=0.2, random_state=self.config.random_seed, stratify=y_lstm
                    )

                    # Générer les positions de séquence pour les ensembles d'entraînement et de validation
                    train_positions = np.arange(len(X_train_lstm))
                    val_positions = np.arange(len(X_val_lstm))

                    train_dataset = BaccaratDataset(X_train_lstm, y_train_lstm, sample_weights_train, train_positions)
                    val_dataset = BaccaratDataset(X_val_lstm, y_val_lstm, sample_weights_val, val_positions)

                    # Utiliser un sampler pondéré pour l'entraînement avec équilibrage des classes
                    from torch.utils.data import WeightedRandomSampler

                    # Calculer les poids pour équilibrer les classes
                    class_counts = np.bincount(y_train_lstm)
                    class_weights = 1.0 / class_counts

                    # Appliquer les poids de classe aux échantillons
                    weights_by_class = class_weights[y_train_lstm]

                    # Combiner avec les poids d'échantillons existants
                    if sample_weights_train is not None:
                        combined_weights = weights_by_class * sample_weights_train
                    else:
                        combined_weights = weights_by_class

                    # Normaliser les poids
                    sample_probs = combined_weights / np.sum(combined_weights)

                    # Créer le sampler avec les poids combinés
                    sampler = WeightedRandomSampler(
                        weights=sample_probs,
                        num_samples=len(sample_probs),
                        replacement=True
                    )

                    logger.info(f"Équilibrage des classes appliqué - Poids des classes: {class_weights}, Distribution des classes: {class_counts}")

                    train_loader = DataLoader(
                        train_dataset,
                        batch_size=dynamics_batch_size,
                        sampler=sampler,
                        num_workers=num_workers,
                        pin_memory=pin_memory_setting,
                        persistent_workers=persistent_workers,
                        prefetch_factor=4 if num_workers > 0 else None,  # Utiliser un prefetch_factor modéré pour équilibrer performance et consommation mémoire
                        multiprocessing_context='spawn' if num_workers > 0 else None  # Utiliser 'spawn' pour une meilleure compatibilité
                    )
                else:
                    # Générer les positions de séquence pour les ensembles d'entraînement et de validation
                    train_positions = np.arange(len(X_train_lstm))
                    val_positions = np.arange(len(X_val_lstm))

                    train_dataset = BaccaratDataset(X_train_lstm, y_train_lstm, None, train_positions)
                    val_dataset = BaccaratDataset(X_val_lstm, y_val_lstm, None, val_positions)

                    train_loader = DataLoader(
                        train_dataset,
                        batch_size=dynamics_batch_size,
                        shuffle=True,
                        num_workers=num_workers,
                        pin_memory=pin_memory_setting,
                        persistent_workers=persistent_workers,
                        prefetch_factor=4 if num_workers > 0 else None,  # Utiliser un prefetch_factor modéré pour équilibrer performance et consommation mémoire
                        multiprocessing_context='spawn' if num_workers > 0 else None  # Utiliser 'spawn' pour une meilleure compatibilité
                    )

                val_loader = DataLoader(
                    val_dataset,
                    batch_size=dynamics_batch_size * 8,  # Batch size encore plus grand pour la validation (8x au lieu de 4x)
                    shuffle=False,
                    num_workers=num_workers,
                    pin_memory=pin_memory_setting,
                    persistent_workers=persistent_workers,
                    prefetch_factor=4 if num_workers > 0 else None,  # Utiliser un prefetch_factor modéré pour équilibrer performance et consommation mémoire
                    drop_last=False,
                    multiprocessing_context='spawn' if num_workers > 0 else None  # Utiliser 'spawn' pour une meilleure compatibilité
                )

                logger.info(f"Données LSTM divisées en {len(X_train_lstm)} échantillons d'entraînement et {len(X_val_lstm)} échantillons de validation")
            else:
                # Si trop peu de données, utiliser toutes les données pour l'entraînement
                train_loader = loader
                val_loader = None
                logger.warning(f"Trop peu de données LSTM ({len(X_lstm)}) pour créer un ensemble de validation. Utilisation de toutes les données pour l'entraînement.")

            start_lstm = time.time()
            train_losses = []
            val_losses = []
            train_accuracies = []
            val_accuracies = []

            # Variables pour early stopping
            best_val_loss = float('inf')
            best_val_accuracy = 0.0
            best_model_state = None
            patience_counter = 0

            try:
                for epoch in range(lstm_epochs):
                    if self.stop_training: break

                    # --- Phase d'entraînement ---
                    temp_lstm.train()
                    running_loss = 0.0
                    correct_train = 0
                    total_train = 0
                    num_batches = len(train_loader)

                    for i, batch_data in enumerate(train_loader):
                        if self.stop_training: break

                        # Gérer les données du batch selon qu'on utilise des poids et/ou des positions de séquence
                        if len(batch_data) == 4:  # Données, cibles, poids et positions de séquence
                            data, target, weights, seq_positions = batch_data
                            data = data.to(self.device, non_blocking=pin_memory_setting)
                            target = target.to(self.device, non_blocking=pin_memory_setting)
                            weights = weights.to(self.device, non_blocking=pin_memory_setting)
                            seq_positions = seq_positions.to(self.device, non_blocking=pin_memory_setting)
                        elif len(batch_data) == 3:  # Données, cibles et positions de séquence (ou poids)
                            if sample_weights_lstm is not None:
                                data, target, weights = batch_data
                                seq_positions = None
                            else:
                                data, target, seq_positions = batch_data
                                weights = None
                            data = data.to(self.device, non_blocking=pin_memory_setting)
                            target = target.to(self.device, non_blocking=pin_memory_setting)
                            if weights is not None:
                                weights = weights.to(self.device, non_blocking=pin_memory_setting)
                            if seq_positions is not None:
                                seq_positions = seq_positions.to(self.device, non_blocking=pin_memory_setting)
                        else:  # Seulement données et cibles
                            data, target = batch_data
                            data = data.to(self.device, non_blocking=pin_memory_setting)
                            target = target.to(self.device, non_blocking=pin_memory_setting)
                            weights = None
                            seq_positions = None

                        temp_optimizer.zero_grad()
                        outputs = temp_lstm(data)

                        # Calculer la perte avec les paramètres disponibles
                        # Utiliser une fonction de perte personnalisée qui donne plus de poids aux échantillons des manches cibles (31-60)
                        if seq_positions is not None:
                            # Créer des poids pour chaque échantillon en fonction de sa position dans la séquence
                            # Les positions sont 0-indexées, donc ajouter 1 pour obtenir le numéro de manche
                            positions_1_indexed = seq_positions.cpu().numpy() + 1
                            target_round_min = getattr(self.config, 'target_round_min', 31)
                            target_round_max = getattr(self.config, 'target_round_max', 60)

                            # Créer un masque pour les manches cibles (31-60)
                            is_target_round = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)

                            # Créer des poids: utiliser le facteur de poids configuré pour les manches cibles
                            sample_weights = torch.ones_like(target, dtype=torch.float32)

                            # Utiliser le paramètre late_game_weight_factor de la configuration avec une valeur BEAUCOUP plus élevée
                            # Augmenter considérablement pour forcer le modèle à sortir du minimum local
                            target_weight_factor = getattr(self.config, 'late_game_weight_factor', 5.0)
                            # Multiplier par 3 pour donner beaucoup plus d'importance aux manches cibles
                            target_weight_factor = target_weight_factor * 3.0

                            # Appliquer une pondération exponentielle pour les manches cibles
                            # Plus on s'approche de la manche 60, plus le poids est important
                            is_target_round_tensor = torch.tensor(is_target_round, device=self.device)
                            target_positions = positions_1_indexed[is_target_round] - target_round_min
                            max_position = target_round_max - target_round_min

                            # Créer des poids exponentiels pour les manches cibles
                            # Formule: target_weight_factor * (1 + position/max_position)^3 - Exposant augmenté de 2 à 3
                            position_weights = torch.ones_like(target, dtype=torch.float32)

                            # Appliquer les poids de base pour toutes les manches cibles
                            position_weights[is_target_round_tensor] = target_weight_factor

                            # Pour chaque position dans les manches cibles, calculer un poids exponentiel
                            for i, pos in enumerate(target_positions):
                                # Trouver l'index correspondant dans le tenseur original
                                idx = np.where(is_target_round)[0][i]
                                # Calculer le poids exponentiel avec un exposant plus élevé (3 au lieu de 2)
                                relative_pos = pos / max_position
                                exponential_weight = target_weight_factor * (1 + relative_pos)**3

                                # Ajouter une perturbation aléatoire pour aider à sortir du minimum local
                                # Cette perturbation est proportionnelle au poids pour maintenir l'importance relative
                                random_factor = 1.0 + 0.2 * (torch.rand(1).item() - 0.5)  # Perturbation de ±10%
                                exponential_weight = exponential_weight * random_factor

                                # Appliquer le poids exponentiel
                                position_weights[idx] = exponential_weight

                            # Appliquer les poids finaux
                            sample_weights = position_weights

                            # Ne pas ajouter de log pour les poids afin d'éviter les logs répétitifs

                            # Utiliser CrossEntropyLoss avec les poids
                            loss_fn = nn.CrossEntropyLoss(weight=None, reduction='none')
                            element_wise_loss = loss_fn(outputs, target.long())

                            # Appliquer les poids aux pertes individuelles
                            weighted_loss = element_wise_loss * sample_weights.to(self.device)

                            # Calculer la perte moyenne
                            loss = weighted_loss.mean()

                            # Ne pas ajouter de log pour la perte afin d'éviter les logs répétitifs

                            # Convertir is_target_round en tenseur PyTorch avant d'utiliser torch.sum()
                            # Nous ne loggons plus cette information pour éviter les logs répétitifs
                            # is_target_round_tensor = torch.tensor(is_target_round, device=self.device)
                            # logger.debug(f"Perte pondérée appliquée: {torch.sum(is_target_round_tensor).item()} échantillons cibles sur {len(is_target_round)} avec poids {target_weight_factor}")
                        else:
                            # Fallback sur CrossEntropyLoss standard si les positions ne sont pas disponibles
                            loss = nn.CrossEntropyLoss()(outputs, target.long())

                        loss.backward()
                        if self.config.gradient_clip_norm > 0:
                             # Utiliser un gradient clipping plus permissif pour permettre des mises à jour plus importantes
                             # tout en évitant l'explosion des gradients
                             current_grad_norm = torch.nn.utils.clip_grad_norm_(
                                 temp_lstm.parameters(),
                                 max_norm=self.config.gradient_clip_norm
                             )
                             # Surveiller la norme du gradient pour détecter d'éventuels problèmes
                             # Ne logger que si la norme est vraiment élevée (>1.5x le seuil) pour réduire le bruit
                             if current_grad_norm > self.config.gradient_clip_norm * 1.5:
                                 logger.debug(f"Gradient norm très élevé: {current_grad_norm:.4f} (seuil: {self.config.gradient_clip_norm})")
                        temp_optimizer.step()
                        running_loss += loss.item()

                        # Calculer l'exactitude d'entraînement
                        _, predicted = torch.max(outputs.data, 1)

                        # DIAGNOSTIC: Vérifier les prédictions et les cibles pendant l'entraînement
                        if i % 50 == 0:  # Limiter la fréquence des logs pour éviter de surcharger
                            # Obtenir les probabilités
                            probs = F.softmax(outputs.data, dim=1)
                            # Extraire quelques exemples pour le diagnostic
                            sample_size = min(5, len(target))
                            sample_indices = torch.randint(0, len(target), (sample_size,))
                            sample_targets = target[sample_indices].cpu().numpy()
                            sample_preds = predicted[sample_indices].cpu().numpy()
                            sample_probs = probs[sample_indices].detach().cpu().numpy()

                            # Compter les occurrences de chaque classe dans le batch
                            unique_targets, target_counts = torch.unique(target, return_counts=True)
                            unique_preds, pred_counts = torch.unique(predicted, return_counts=True)

                            # Réduire la verbosité des logs de diagnostic pendant l'entraînement
                            # Ne logger que si un déséquilibre important est détecté dans les distributions
                            target_dist = dict(zip(unique_targets.cpu().numpy(), target_counts.cpu().numpy()))
                            pred_dist = dict(zip(unique_preds.cpu().numpy(), pred_counts.cpu().numpy()))

                            # Vérifier s'il y a un déséquilibre important dans les prédictions
                            has_imbalance = False
                            if len(pred_dist) > 1:
                                pred_values = list(pred_dist.values())
                                max_pred = max(pred_values)
                                min_pred = min(pred_values)
                                if max_pred > min_pred * 3:  # Déséquilibre de 3:1 ou plus
                                    has_imbalance = True

                            if has_imbalance:
                                logger.info(f"Déséquilibre détecté - Epoch {epoch+1}, Batch {i}")
                                logger.info(f"  Distribution des cibles: {target_dist}")
                                logger.info(f"  Distribution des prédictions: {pred_dist}")

                        total_train += target.size(0)
                        correct_train += (predicted == target).sum().item()

                        if ui_available and (i + 1) % 20 == 0:
                           progress_epoch = 40 + (epoch / lstm_epochs) * 30
                           progress_batch = ((i + 1) / num_batches) * (30 / lstm_epochs)
                           current_progress = progress_epoch + progress_batch
                           msg = f"LSTM Epoch {epoch+1}/{lstm_epochs}, Batch {i+1}/{num_batches}, Loss: {loss.item():.4f}"
                           self._safe_update_progress(current_progress, msg)

                    # Calculer les métriques d'entraînement moyennes
                    epoch_loss = running_loss / num_batches if num_batches > 0 else 0
                    epoch_accuracy = correct_train / total_train if total_train > 0 else 0
                    train_losses.append(epoch_loss)
                    train_accuracies.append(epoch_accuracy)

                    # --- Phase de validation ---
                    if val_loader is not None:
                        temp_lstm.eval()
                        val_loss = 0.0
                        correct_val = 0
                        total_val = 0
                        all_preds = []
                        all_targets = []

                        # Pas besoin de définir sample_weights_val ici, il est déjà accessible dans la portée

                        with torch.no_grad():
                            for batch_data in val_loader:
                                # Gérer les données du batch selon qu'on utilise des poids et/ou des positions de séquence
                                if len(batch_data) == 4:  # Données, cibles, poids et positions de séquence
                                    val_data, val_target, weights, seq_positions = batch_data
                                    val_data = val_data.to(self.device, non_blocking=pin_memory_setting)
                                    val_target = val_target.to(self.device, non_blocking=pin_memory_setting)
                                    weights = weights.to(self.device, non_blocking=pin_memory_setting)
                                    seq_positions = seq_positions.to(self.device, non_blocking=pin_memory_setting)
                                elif len(batch_data) == 3:  # Données, cibles et positions de séquence (ou poids)
                                    # Toujours traiter comme des positions de séquence, car les poids sont gérés par le DataLoader
                                    val_data, val_target, seq_positions = batch_data
                                    weights = None
                                    val_data = val_data.to(self.device, non_blocking=pin_memory_setting)
                                    val_target = val_target.to(self.device, non_blocking=pin_memory_setting)
                                    # weights est toujours None ici, donc pas besoin de vérifier
                                    # seq_positions est toujours non-None ici
                                    seq_positions = seq_positions.to(self.device, non_blocking=pin_memory_setting)
                                else:  # Seulement données et cibles
                                    val_data, val_target = batch_data
                                    val_data = val_data.to(self.device, non_blocking=pin_memory_setting)
                                    val_target = val_target.to(self.device, non_blocking=pin_memory_setting)
                                    weights = None
                                    seq_positions = None

                                val_outputs = temp_lstm(val_data)

                                # Calculer la perte avec les paramètres disponibles
                                # Utiliser une fonction de perte personnalisée qui donne plus de poids aux échantillons des manches cibles (31-60)
                                if seq_positions is not None:
                                    # Créer des poids pour chaque échantillon en fonction de sa position dans la séquence
                                    # Les positions sont 0-indexées, donc ajouter 1 pour obtenir le numéro de manche
                                    positions_1_indexed = seq_positions.cpu().numpy() + 1
                                    target_round_min = getattr(self.config, 'target_round_min', 31)
                                    target_round_max = getattr(self.config, 'target_round_max', 60)

                                    # Créer un masque pour les manches cibles (31-60)
                                    is_target_round = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)

                                    # Créer des poids: utiliser le facteur de poids configuré pour les manches cibles
                                    sample_weights = torch.ones_like(val_target, dtype=torch.float32)

                                    # Utiliser le paramètre late_game_weight_factor de la configuration avec une valeur BEAUCOUP plus élevée
                                    # Augmenter considérablement pour forcer le modèle à sortir du minimum local
                                    target_weight_factor = getattr(self.config, 'late_game_weight_factor', 5.0)
                                    # Multiplier par 3 pour donner beaucoup plus d'importance aux manches cibles
                                    target_weight_factor = target_weight_factor * 3.0

                                    # Appliquer une pondération exponentielle pour les manches cibles
                                    # Plus on s'approche de la manche 60, plus le poids est important
                                    is_target_round_tensor = torch.tensor(is_target_round, device=self.device)
                                    target_positions = positions_1_indexed[is_target_round] - target_round_min
                                    max_position = target_round_max - target_round_min

                                    # Créer des poids exponentiels pour les manches cibles
                                    # Formule: target_weight_factor * (1 + position/max_position)^3 - Exposant augmenté de 2 à 3
                                    position_weights = torch.ones_like(val_target, dtype=torch.float32)

                                    # Appliquer les poids de base pour toutes les manches cibles
                                    position_weights[is_target_round_tensor] = target_weight_factor

                                    # Pour chaque position dans les manches cibles, calculer un poids exponentiel
                                    for i, pos in enumerate(target_positions):
                                        # Trouver l'index correspondant dans le tenseur original
                                        idx = np.where(is_target_round)[0][i]
                                        # Calculer le poids exponentiel avec un exposant plus élevé (3 au lieu de 2)
                                        relative_pos = pos / max_position
                                        exponential_weight = target_weight_factor * (1 + relative_pos)**3

                                        # Ne pas ajouter de perturbation aléatoire pour avoir des résultats stables

                                        # Appliquer le poids exponentiel
                                        position_weights[idx] = exponential_weight

                                    # Appliquer les poids finaux
                                    sample_weights = position_weights

                                    # Utiliser CrossEntropyLoss avec les poids
                                    loss_fn = nn.CrossEntropyLoss(weight=None, reduction='none')
                                    element_wise_loss = loss_fn(val_outputs, val_target.long())

                                    # Appliquer les poids aux pertes individuelles
                                    weighted_loss = element_wise_loss * sample_weights.to(self.device)

                                    # Calculer la perte moyenne
                                    loss = weighted_loss.mean()
                                else:
                                    # Fallback sur CrossEntropyLoss standard si les positions ne sont pas disponibles
                                    loss = nn.CrossEntropyLoss()(val_outputs, val_target.long())

                                val_loss += loss.item()

                                _, predicted = torch.max(val_outputs.data, 1)
                                total_val += val_target.size(0)
                                correct_val += (predicted == val_target).sum().item()

                                all_preds.extend(predicted.cpu().numpy())
                                all_targets.extend(val_target.cpu().numpy())

                        # Calculer les métriques de validation moyennes
                        val_epoch_loss = val_loss / len(val_loader) if len(val_loader) > 0 else float('inf')
                        val_epoch_accuracy = correct_val / total_val if total_val > 0 else 0
                        val_losses.append(val_epoch_loss)
                        val_accuracies.append(val_epoch_accuracy)

                        # Calculer des métriques supplémentaires
                        if len(all_targets) > 0 and len(np.unique(all_targets)) > 1:
                            try:
                                val_cm = confusion_matrix(all_targets, all_preds)
                                # Ajouter le paramètre zero_division=0 pour éviter les avertissements
                                val_precision = precision_score(all_targets, all_preds, average='binary', zero_division=0)
                                val_recall = recall_score(all_targets, all_preds, average='binary', zero_division=0)
                                val_f1 = f1_score(all_targets, all_preds, average='binary', zero_division=0)

                                # Calculer les métriques spécifiques pour les objectifs 1 et 2
                                # Objectif 1: Recommandations NON-WAIT valides consécutives (manches 31-60)
                                # Objectif 2: Précision globale (score composite)

                                # Récupérer les positions de séquence directement depuis les données de validation
                                # Nous utilisons les positions de séquence qui ont été créées lors de la création du dataset
                                val_positions = val_positions if 'val_positions' in locals() else None

                                # Si val_positions n'est pas disponible, on le crée à partir des indices
                                if val_positions is None:
                                    val_positions = np.arange(len(all_targets))
                                    logger.info(f"Positions de séquence créées à partir des indices (len={len(val_positions)})")

                                # Calculer les métriques pour les manches cibles (31-60)
                                target_round_min = getattr(self.config, 'target_round_min', 31)
                                target_round_max = getattr(self.config, 'target_round_max', 60)

                                # Extraire les données pour les manches cibles (31-60) et faire des prédictions spécifiques
                                # pour ces manches à chaque époque, en utilisant le modèle actuel
                                if val_positions is not None:
                                    # Utiliser les positions réelles des manches ou une approximation
                                    positions_1_indexed = val_positions + 1

                                    # Vérifier si nous avons des positions dans la plage 31-60
                                    has_target_positions = np.any((positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max))
                                    if not has_target_positions:
                                        # Créer un masque qui sélectionne environ 30 positions (manches 31-60)
                                        total_positions = len(positions_1_indexed)
                                        if total_positions >= 60:
                                            # Si nous avons au moins 60 positions, prendre les positions 31-60
                                            start_idx = 30  # Position 31 (0-indexée)
                                            end_idx = 60    # Position 60 (0-indexée)
                                            target_mask = np.zeros(total_positions, dtype=bool)
                                            for i in range(start_idx, min(end_idx, total_positions)):
                                                target_mask[i] = True
                                        else:
                                            # Sinon, prendre la seconde moitié des positions
                                            half_point = total_positions // 2
                                            target_mask = np.zeros(total_positions, dtype=bool)
                                            for i in range(half_point, total_positions):
                                                target_mask[i] = True
                                    else:
                                        # Créer un masque pour les manches cibles (31-60)
                                        target_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)

                                    if np.any(target_mask):
                                        # Extraire les données pour les manches cibles
                                        target_data = X_val_lstm[target_mask]
                                        target_targets = y_val_lstm[target_mask]

                                        # Faire des prédictions spécifiques pour les manches cibles avec le modèle actuel
                                        # Utiliser le modèle à l'état actuel pour chaque époque
                                        # IMPORTANT: Ne pas utiliser torch.no_grad() ici pour permettre le calcul des gradients
                                        # et l'apprentissage spécifique sur les manches cibles

                                        # Convertir en tenseur et déplacer vers le device approprié
                                        target_data_tensor = torch.tensor(target_data, dtype=torch.float32).to(self.device)

                                        # Activer le calcul des gradients pour ces échantillons spécifiques
                                        # Cela permettra au modèle d'apprendre spécifiquement sur les manches cibles

                                        # Obtenir les prédictions du modèle actuel
                                        target_outputs = temp_lstm(target_data_tensor)

                                        # Obtenir les probabilités brutes (avant argmax)
                                        target_probs_raw = F.softmax(target_outputs, dim=1)

                                        # Utiliser les probabilités brutes sans perturbation
                                        # Cela donne une évaluation plus précise des performances réelles du modèle

                                        # Convertir en numpy pour les calculs de métriques
                                        # Utiliser detach() pour éviter l'erreur "Can't call numpy() on Tensor that requires grad"
                                        target_probs = target_probs_raw.detach().cpu().numpy()

                                        # Obtenir les prédictions finales à partir des probabilités brutes
                                        # Utiliser detach() pour éviter l'erreur "Can't call numpy() on Tensor that requires grad"
                                        # IMPORTANT: Utiliser un seuil de décision équilibré (0.5) pour éviter le biais vers une classe
                                        target_probs_np = target_probs_raw.detach().cpu().numpy()
                                        # Utiliser un seuil de 0.5 pour la décision (classe 1 si prob[1] > 0.5)
                                        target_preds = (target_probs_np[:, 1] > 0.5).astype(np.int64)

                                        # Log de diagnostic pour vérifier les probabilités et les prédictions
                                        # Utiliser le niveau INFO au lieu de CRITICAL pour éviter de déclencher des alertes
                                        # Réduire la fréquence des logs (seulement aux époques paires)
                                        if epoch % 2 == 0:  # Réduire la fréquence des logs
                                            logger.info(f"Évaluation - Epoch {epoch+1} - Exemples de probabilités: {target_probs_np[:3]}")
                                            logger.info(f"Évaluation - Epoch {epoch+1} - Exemples de prédictions: {target_preds[:3]}")

                                        # Ne pas calculer les métriques d'incertitude pour éviter les logs répétitifs

                                        # Calculer une perte spécifique pour les manches cibles
                                        # et la rétropropager pour améliorer le modèle sur ces manches
                                        target_targets_tensor = torch.tensor(target_targets, dtype=torch.long).to(self.device)

                                        # Utiliser CrossEntropyLoss pour calculer la perte
                                        target_loss = nn.CrossEntropyLoss()(target_outputs, target_targets_tensor)

                                        # Rétropropager la perte pour améliorer le modèle sur les manches cibles
                                        # Cela est crucial pour que le modèle apprenne spécifiquement sur ces manches
                                        target_loss.backward()

                                        # Mettre à jour les poids du modèle avec l'optimiseur
                                        # Cela applique les gradients calculés ci-dessus
                                        temp_optimizer.step()

                                        # Ne pas logger la perte pour éviter les logs répétitifs

                                        # Log détaillé pour déboguer
                                        logger.info(f"Prédictions cibles: {len(target_preds)} éléments")
                                        if len(target_preds) > 0:
                                            logger.info(f"Distribution des prédictions: {np.bincount(target_preds)}")
                                            logger.info(f"Distribution des cibles: {np.bincount(target_targets)}")

                                            # Ne pas générer de logs détaillés pour éviter les logs répétitifs

                                            # Stocker les prédictions actuelles pour la prochaine époque
                                            self.previous_target_preds = target_preds.copy()

                                        if len(target_preds) > 0 and len(np.unique(target_targets)) > 1:
                                            # Calculer l'exactitude équilibrée pour les manches cibles
                                            # Cela donne un poids égal à chaque classe, indépendamment de leur fréquence
                                            # Utiliser les métriques déjà importées au début de la fonction _train_models_async

                                            # Calculer plusieurs métriques pour une évaluation plus complète
                                            target_accuracy = balanced_accuracy_score(target_targets, target_preds)
                                            target_f1 = f1_score(target_targets, target_preds, average='weighted', zero_division=0)
                                            target_precision = precision_score(target_targets, target_preds, average='weighted', zero_division=0)
                                            target_recall = recall_score(target_targets, target_preds, average='weighted', zero_division=0)

                                            # Calculer le nombre de prédictions consécutives correctes
                                            consecutive_correct = 0
                                            max_consecutive_correct = 0
                                            current_streak = 0

                                            # Calculer également les erreurs consécutives pour pénaliser les longues séquences d'erreurs
                                            current_error_streak = 0
                                            max_consecutive_errors = 0
                                            error_streaks = []

                                            for i in range(len(target_preds)):
                                                if target_preds[i] == target_targets[i]:
                                                    # Prédiction correcte
                                                    current_streak += 1
                                                    max_consecutive_correct = max(max_consecutive_correct, current_streak)

                                                    # Réinitialiser le compteur d'erreurs consécutives
                                                    if current_error_streak > 0:
                                                        error_streaks.append(current_error_streak)
                                                        current_error_streak = 0
                                                else:
                                                    # Prédiction incorrecte
                                                    current_error_streak += 1
                                                    max_consecutive_errors = max(max_consecutive_errors, current_error_streak)
                                                    current_streak = 0

                                            # Ajouter la dernière séquence d'erreurs si elle existe
                                            if current_error_streak > 0:
                                                error_streaks.append(current_error_streak)

                                            # Calculer une pénalité pour les erreurs consécutives
                                            # Plus les séquences d'erreurs sont longues, plus la pénalité est importante (croissance cubique)
                                            consecutive_penalty_factor = getattr(self.config, 'consecutive_penalty_factor', 1.0)
                                            # Augmenter le facteur de pénalité pour les erreurs consécutives
                                            consecutive_penalty_factor = consecutive_penalty_factor * 2.0

                                            error_penalty = 0
                                            if error_streaks:
                                                # Pénalité cubique pour les longues séquences d'erreurs (exposant augmenté de 2 à 3)
                                                error_penalty = sum([streak**3 for streak in error_streaks]) / len(target_preds) * consecutive_penalty_factor

                                            # Combiner les métriques en un score unique avec beaucoup plus de poids sur les prédictions consécutives
                                            consecutive_weight = self.config.consecutive_focus_factor
                                            # Augmenter le poids des prédictions consécutives
                                            consecutive_weight = consecutive_weight * 2.0

                                            # Calculer l'accuracy composite en donnant beaucoup plus de poids aux prédictions consécutives
                                            # et en pénalisant fortement les erreurs consécutives
                                            # Réduire le poids de l'accuracy et du F1 pour augmenter l'importance des prédictions consécutives
                                            composite_accuracy = (target_accuracy * 0.2 +  # Réduit de 0.3 à 0.2
                                                              target_f1 * 0.2 +  # Réduit de 0.3 à 0.2
                                                              (max_consecutive_correct / len(target_preds)) * consecutive_weight * 0.6 -  # Augmenté de 0.4 à 0.6
                                                              error_penalty)

                                            # S'assurer que l'accuracy composite reste positive
                                            composite_accuracy = max(0.01, composite_accuracy)

                                            # Ne pas ajouter de perturbation aléatoire à l'accuracy composite
                                            # Cela masquerait le problème réel plutôt que de le résoudre
                                            # Garder l'accuracy composite telle quelle pour une évaluation précise
                                            pass

                                            # Suppression du log détaillé des métriques consécutives pour réduire le bruit

                                            # Afficher les métriques détaillées pour le débogage
                                            logger.info(f"Métriques cibles: Acc={target_accuracy:.4f}, F1={target_f1:.4f}, Prec={target_precision:.4f}, Rec={target_recall:.4f}, MaxConsec={max_consecutive_correct}")

                                            # Simplifier le calcul des prédictions consécutives
                                            correct_predictions = (target_preds == target_targets)

                                            # Calculer directement le nombre maximum de prédictions consécutives
                                            from itertools import groupby
                                            consecutive_groups = [(k, sum(1 for _ in g)) for k, g in groupby(correct_predictions)]
                                            max_consecutive = max([length for is_correct, length in consecutive_groups if is_correct], default=0)

                                            # Métriques pour l'objectif 1 (prédictions consécutives)
                                            objective1_metric = max_consecutive

                                            # Métriques pour l'objectif 2 (précision composite)
                                            objective2_metric = composite_accuracy
                                        else:
                                            objective1_metric = 0
                                            objective2_metric = 0
                                    else:
                                        objective1_metric = 0
                                        objective2_metric = 0
                                else:
                                    objective1_metric = 0
                                    objective2_metric = 0

                                # Sauvegarder les métriques pour affichage dans l'interface utilisateur
                                # Ajouter un timestamp pour s'assurer que les métriques sont bien mises à jour à chaque époque
                                current_timestamp = time.time()

                                # Ajouter des métriques détaillées pour les manches cibles
                                target_metrics = {
                                    'timestamp': current_timestamp,  # Timestamp pour vérifier les mises à jour
                                    'epoch': epoch + 1,  # Numéro de l'époque actuelle
                                    'target_preds': target_preds.tolist() if 'target_preds' in locals() else [],  # Prédictions pour les manches cibles
                                    'target_targets': target_targets.tolist() if 'target_targets' in locals() else [],  # Cibles pour les manches cibles
                                    'target_accuracy': target_accuracy if 'target_accuracy' in locals() else 0,
                                    'target_f1': target_f1 if 'target_f1' in locals() else 0,
                                    'target_precision': target_precision if 'target_precision' in locals() else 0,
                                    'target_recall': target_recall if 'target_recall' in locals() else 0,
                                    'max_consecutive': max_consecutive if 'max_consecutive' in locals() else 0
                                }

                                # Suppression du log de mise à jour des métriques cibles pour réduire le bruit

                                self.lstm_metrics = {
                                    'confusion_matrix': val_cm,
                                    'precision': val_precision,
                                    'recall': val_recall,
                                    'f1': val_f1,
                                    'accuracy': val_epoch_accuracy,
                                    'loss': val_epoch_loss,
                                    'train_losses': train_losses,
                                    'val_losses': val_losses,
                                    'train_accuracies': train_accuracies,
                                    'val_accuracies': val_accuracies,
                                    'objective1_metric': objective1_metric,
                                    'objective2_metric': objective2_metric,
                                    'target_metrics': target_metrics  # Ajouter les métriques détaillées
                                }

                                # Afficher les métriques
                                logger.info(f"LSTM Epoch {epoch+1}/{lstm_epochs}:")
                                logger.info(f"  Train Loss: {epoch_loss:.4f}, Train Accuracy: {epoch_accuracy:.4f}")
                                logger.info(f"  Val Loss: {val_epoch_loss:.4f}, Val Accuracy: {val_epoch_accuracy:.4f}")
                                logger.info(f"  Val Precision: {val_precision:.4f}, Val Recall: {val_recall:.4f}, Val F1: {val_f1:.4f}")
                                logger.info(f"  Val Confusion Matrix: {val_cm}")
                                logger.info(f"  Objectif 1 (Recommandations consécutives valides 31-60): {objective1_metric}")
                                logger.info(f"  Objectif 2 (Précision manches 31-60): {objective2_metric:.4f}")
                            except Exception as e_metrics:
                                logger.error(f"Erreur lors du calcul des métriques de validation LSTM: {e_metrics}", exc_info=True)

                        # Early stopping modifié pour prendre en compte les métriques des manches cibles
                        # Vérifier si nous avons des métriques cibles
                        has_target_metrics = 'objective1_metric' in locals() and 'objective2_metric' in locals()

                        if has_target_metrics:
                            # Calculer un score composite qui prend en compte à la fois la perte de validation
                            # et les métriques des manches cibles
                            # Donner plus de poids aux métriques cibles (70%) qu'à la perte de validation (30%)
                            composite_score = 0.3 * (-val_epoch_loss) + 0.7 * (objective1_metric + objective2_metric)

                            # Log pour suivre l'évolution du score composite
                            logger.info(f"Score composite pour early stopping: {composite_score:.4f} (ValLoss: {val_epoch_loss:.4f}, Obj1: {objective1_metric}, Obj2: {objective2_metric:.4f})")

                            # Comparer avec le meilleur score précédent
                            if not hasattr(self, 'best_composite_score') or composite_score > self.best_composite_score:
                                self.best_composite_score = composite_score
                                best_val_loss = val_epoch_loss
                                best_val_accuracy = val_epoch_accuracy
                                best_model_state = temp_lstm.state_dict().copy()
                                patience_counter = 0
                                logger.info(f"Nouveau meilleur score composite: {composite_score:.4f}")
                            else:
                                patience_counter += 1
                                logger.info(f"Pas d'amélioration du score composite. Patience: {patience_counter}/{self.config.lstm_early_stopping_patience}")
                        else:
                            # Fallback sur l'early stopping standard basé uniquement sur la perte de validation
                            if val_epoch_loss < best_val_loss:
                                best_val_loss = val_epoch_loss
                                best_val_accuracy = val_epoch_accuracy
                                best_model_state = temp_lstm.state_dict().copy()
                                patience_counter = 0
                            else:
                                patience_counter += 1

                        # Vérifier si la patience est épuisée
                        patience = self.config.lstm_early_stopping_patience
                        if patience_counter >= patience:
                            logger.info(f"Early stopping déclenché après {epoch+1} époques (patience: {patience})")
                            break
                    else:
                        # Pas de validation, utiliser les métriques d'entraînement
                        logger.info(f"LSTM Epoch {epoch+1}/{lstm_epochs}:")
                        logger.info(f"  Train Loss: {epoch_loss:.4f}, Train Accuracy: {epoch_accuracy:.4f}")

                        # Sauvegarder les métriques pour affichage dans l'interface utilisateur
                        self.lstm_metrics = {
                            'accuracy': epoch_accuracy,
                            'loss': epoch_loss,
                            'train_losses': train_losses,
                            'train_accuracies': train_accuracies
                        }

                    # Mise à jour du scheduler
                    if val_loader is not None:
                        temp_scheduler.step(val_epoch_loss)
                    else:
                        temp_scheduler.step(epoch_loss)

                    current_lr = temp_optimizer.param_groups[0]['lr']
                    logger.info(f"  Learning Rate: {current_lr:.6f}")

                    if self.stop_training: break

                # Utiliser le meilleur modèle si disponible
                if best_model_state is not None:
                    temp_lstm.load_state_dict(best_model_state)
                    logger.info(f"Utilisation du meilleur modèle LSTM (Val Loss: {best_val_loss:.4f}, Val Accuracy: {best_val_accuracy:.4f})")

                # Créer et sauvegarder des graphiques
                try:
                    self._save_lstm_training_plots()
                except Exception as e_plot:
                    logger.error(f"Erreur lors de la création des graphiques LSTM: {e_plot}", exc_info=True)

                if not self.stop_training:
                    with self.model_lock:
                        self.lstm = temp_lstm
                        self.optimizer = temp_optimizer
                        self.scheduler = temp_scheduler
                        lstm_trained = True
                    self.lstm.eval()

                    # Nettoyer la mémoire PyTorch après l'entraînement LSTM
                    cleanup_pytorch_memory()

                    lstm_time = time.time() - start_lstm
                    logger.info(f"Entraînement LSTM ({lstm_epochs} epochs) terminé ({lstm_time:.2f}s).")
                    train_summary.append(f"LSTM ({y_lstm.shape[0]}, {lstm_epochs} epochs)")
                else:
                    logger.warning(f"Entraînement LSTM ({lstm_epochs} epochs) interrompu.")

            except Exception as e_lstm:
                logger.error(f"Erreur pendant entraînement LSTM ({lstm_epochs} epochs): {e_lstm}", exc_info=True)

            if self.stop_training: raise InterruptedError("Arrêt demandé avant Calibration LGBM.")
            if ui_available: self._safe_update_progress(75, "Calibration LGBM...")

            if self.lgbm_base is None:
                 logger.error("Calibration impossible: Modèle LGBM Base non entraîné/disponible.")
            else:
                try:
                    calibration_method = getattr(self.config, 'calibration_method', 'isotonic')
                    temp_calibrated_lgbm = CalibratedClassifierCV(
                        estimator=self.lgbm_base,
                        method=calibration_method,
                        cv='prefit'
                    )
                    start_calib = time.time()
                    temp_calibrated_lgbm.fit(X_val_calib, y_val_calib)
                    with self.model_lock:
                        self.calibrated_lgbm = temp_calibrated_lgbm
                        calibrated_lgbm_updated = True
                    calib_time = time.time() - start_calib
                    logger.info(f"Calibration LGBM ({calibration_method}) terminée ({calib_time:.2f}s).")
                    train_summary.append(f"LGBM Calib ({calibration_method})")
                except AttributeError as ae:
                    logger.error(f"Erreur Attribut pendant calibration (lgbm_base 'fit'?): {ae}", exc_info=True)
                except Exception as e_calib:
                    logger.error(f"Erreur pendant calibration LGBM: {e_calib}", exc_info=True)

            if self.stop_training: raise InterruptedError("Arrêt demandé avant Modèle Incertitude.")
            if ui_available: self._safe_update_progress(85, "Entraînement Modèle Incertitude...")

            if self.lgbm_base is None:
                 logger.error("Entr. Incertitude impossible: Modèle LGBM Base non disponible.")
            else:
                try:
                    # Utiliser tous les cœurs CPU disponibles pour le modèle d'incertitude
                    num_cpu_jobs_bagging = num_cpu_jobs
                    logger.info(f"Entraînement Modèle Incertitude avec n_jobs={num_cpu_jobs_bagging} (tous les cœurs disponibles)")

                    # Configurer l'estimateur de base pour utiliser un seul cœur par estimateur
                    # car le parallélisme sera géré au niveau du BaggingClassifier
                    base_estimator_for_bagging = LGBMClassifier(
                        random_state=self.config.random_seed + 1,
                        n_jobs=1,  # Un seul cœur par estimateur de base
                        **lgbm_params_from_config
                    )
                    bagging_n_estimators = getattr(self.config, 'bagging_n_estimators', 20)

                    # Configurer le BaggingClassifier pour utiliser tous les cœurs CPU disponibles
                    temp_uncertainty_model = BaggingClassifier(
                        estimator=base_estimator_for_bagging,
                        n_estimators=bagging_n_estimators,
                        max_samples=getattr(self.config,'uncertainty_max_samples',1.0),
                        max_features=getattr(self.config,'uncertainty_max_features',1.0),
                        bootstrap=True,
                        bootstrap_features = (getattr(self.config,'uncertainty_max_features',1.0) < 1.0),
                        oob_score=False,
                        n_jobs=num_cpu_jobs_bagging,  # Utiliser tous les cœurs disponibles
                        random_state=self.config.random_seed + 2,
                        verbose=1  # Activer la verbosité pour voir la progression du parallélisme
                    )
                    start_uncert = time.time()
                    temp_uncertainty_model.fit(X_lgbm_scaled, y_lgbm)
                    with self.model_lock:
                        self.lgbm_uncertainty = temp_uncertainty_model
                        uncertainty_model_updated = True
                    uncert_time = time.time() - start_uncert
                    logger.info(f"Entraînement Modèle Incertitude (Bagging) terminé ({uncert_time:.2f}s).")
                    train_summary.append(f"Uncertainty ({bagging_n_estimators} est)")
                except Exception as e_uncert:
                    logger.error(f"Erreur pendant entraînement Modèle Incertitude: {e_uncert}", exc_info=True)

            # Entraîner le calculateur de confiance consécutive
            if ui_available: self._safe_update_progress(90, "Entraînement calculateur de confiance consécutive...")
            try:
                # Extraire les indices d'entraînement et de validation
                train_indices = np.arange(len(X_lgbm))
                val_indices = np.array([])

                # Diviser les données en ensembles d'entraînement et de validation
                if len(train_indices) > 20:  # Assez de données pour faire une division
                    train_indices, val_indices = train_test_split(
                        np.arange(len(X_lgbm)), test_size=0.2, random_state=self.config.random_seed
                    )

                # Note: La Phase 2 (calculateur de confiance consécutive) a été supprimée
                logger.info("Entraînement du calculateur de confiance consécutive désactivé (Phase 2 supprimée)")
                # Pas besoin d'ajouter un avertissement puisque c'est un comportement attendu
            except Exception as e_consecutive:
                logger.error(f"Erreur pendant l'entraînement du calculateur de confiance consécutive: {e_consecutive}", exc_info=True)

            success = scaler_updated and lgbm_base_updated
            logger.info("Fin des iterations")
            if ui_available: self._safe_update_progress(95, "Fin des iterations")
        except InterruptedError as e:
            logger.warning(f"Entraînement COMPLET interrompu: {e}")
            success = False
        except (ValueError, RuntimeError, AttributeError) as e_crit:
            logger.error(f"Erreur critique pendant entraînement COMPLET: {e_crit}", exc_info=True)
            success = False
        except Exception as e_major:
            logger.error(f"Erreur majeure inattendue pendant entraînement COMPLET: {e_major}", exc_info=True)
            success = False
        finally:
            models_were_updated = (scaler_updated or lgbm_base_updated or lstm_trained or
                                   calibrated_lgbm_updated or uncertainty_model_updated)
            if models_were_updated:
                with self.model_lock:
                    self.lgbm_cache = deque(maxlen=100)
                    logger.info("Cache LGBM vidé car modèles mis à jour durant entraînement complet.")
            else:
                logger.info("Cache LGBM NON vidé (entraînement complet échoué?).")

            with self.training_lock:
                 self.is_training = False
                 self.stop_training = False
            if hasattr(self, 'training_thread') and self.training_thread and self.training_thread.is_alive():
                 try: self.training_thread.join(timeout=5); logger.debug("Attente fin thread OK.")
                 except Exception as e_join: logger.warning(f"Erreur join thread: {e_join}")
            end_time = time.time()
            total_time = end_time - start_time
            try:
                 total_samples = len(y_lgbm)
                 if total_samples > 0:
                      confidence_metrics_info = "avec métriques de confiance" if confidence_metrics_used else "sans métriques de confiance"
                      logger.info(f"Résultats Entraînement: Succès={success}, DuréeTotale={total_time:.2f}s, {confidence_metrics_info}, Scaler={scaler_updated}, LGBM={lgbm_base_updated}, LSTM={lstm_trained}, Calib={calibrated_lgbm_updated}, Uncert={uncertainty_model_updated} [{', '.join(train_summary)}]")
                 else:
                      logger.warning("Entraînement ML sans échantillons (ML désactivé?).")
            except Exception as e_stats:
                 logger.error(f"Erreur statistique post-entraînement: {e_stats}")

            if ui_available:
                 current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                 train_message = f"Entraînement {'OK' if success else 'ÉCHOUÉ'} ({current_time})"

		 # --- Vérification et appel correct de stop button ---
                 if hasattr(self, "stop_train_button"):
                    self.root.after(0, lambda m=train_message: self.stop_train_button.config(text=m)) # <-- Utiliser stop_train_button
                 else:
                    logger.warning(" Le code tente d'appeler stop_train_button mais cet attribut n'existe pas")
                 # Fin vérification

                 self._safe_update_progress(100, "Entraînement terminé.")
                 # Utiliser root.after pour le délai, mais avec _safe_update_progress
                 self.root.after(1250, lambda: self._safe_update_progress(0, ""))
                 self.root.after(0, self._update_weights_display)

            if hasattr(self, 'main_queue') and self.main_queue:
                 try:
                      logger.info(f"Envoi signal fin d'entraînement (succès={success}) vers main_queue...")
                      self.main_queue.put({'type': 'training_done', 'success': success})
                      logger.debug("Signal fin d'entraînement envoyé vers main_queue.")
                 except Exception as e_queue:
                      logger.error(f"Erreur envoi signal fin d'entraînement (queue): {e_queue}", exc_info=True)
            else:
                 logger.warning("Aucune queue principale disponible. Annonce de fin d'entraînement ignorée.")

            if ui_available and success:
                logger.info("Tentative de sauvegarde automatique de l'état (post-entraînement)...")
                save_ok = self._save_state_to_models_dir()
                if not save_ok:
                    logger.error("Échec sauvegarde auto post-entraînement.")
                else:
                    logger.info("Succès sauvegarde auto après entraînement.")

            # Réinitialiser l'indicateur de phase d'entraînement
            self._is_training = False

            # Nettoyer la mémoire PyTorch à la fin de l'entraînement
            cleanup_pytorch_memory()
            logger.info("Nettoyage de la mémoire PyTorch effectué à la fin de l'entraînement.")

            # Appeler finalize_training pour finaliser l'entraînement et réactiver l'auto-update si nécessaire
            if hasattr(self, 'finalize_training'):
                try:
                    logger.info("Appel de finalize_training pour finaliser l'entraînement")
                    self.finalize_training(success, start_time, train_summary)
                except Exception as e_finalize:
                    logger.error(f"Erreur lors de l'appel à finalize_training: {e_finalize}", exc_info=True)