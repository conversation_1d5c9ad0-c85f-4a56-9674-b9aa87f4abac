# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11292 à 11332
# Type: Méthode de la classe HybridBaccaratPredictor

    def _undo_last_move_unsafe(self) -> None:
        """Logique interne d'annulation (doit être appelée avec les verrous acquis)."""
        # 1. Retirer le dernier résultat de la séquence
        if not self.sequence: return # Double sécurité
        last_outcome = self.sequence.pop()
        logger.debug(f"Résultat '{last_outcome}' retiré de la séquence.")

        # 2. Retirer la dernière prédiction de l'historique
        # (Corresponds à la prédiction pour le coup N+1 faite après le coup N qui est annulé)
        if self.prediction_history:
            removed_prediction = self.prediction_history.pop()
            logger.debug("Dernière prédiction retirée de l'historique.")
        else:
            removed_prediction = None

        # 3. Décrémenter les compteurs de motifs (si applicable)
        if len(self.sequence) >= 3: # Motif = 3 coups avant + le coup annulé
            pattern_to_decrement = tuple(self.sequence[-3:] + [last_outcome])
            dict_key = last_outcome # Clé du dictionnaire = résultat du coup annulé
            if dict_key in self.pattern_counts and pattern_to_decrement in self.pattern_counts[dict_key]:
                self.pattern_counts[dict_key][pattern_to_decrement] -= 1
                logger.debug(f"Compteur décrémenté pour motif {pattern_to_decrement} -> {dict_key}")
                # Optionnel: supprimer la clé si le compteur atteint 0
                if self.pattern_counts[dict_key][pattern_to_decrement] <= 0:
                    del self.pattern_counts[dict_key][pattern_to_decrement]
                    logger.debug(f"Motif {pattern_to_decrement} supprimé car compteur <= 0.")


        # 4. Correction des modèles Markov de session (plus complexe)
        # Il faudrait idéalement "rembobiner" l'état de session. C'est difficile
        # sans stocker l'historique des changements de compteurs.
        # Solution simple mais imprécise: Réinitialiser les compteurs de session
        # et les recalculer à partir de la séquence N-1.
        self.markov.reset(reset_type='soft') # Réinitialise session_models
        if self.sequence: # Recalculer si la séquence n'est pas vide
             self.markov.update_session(self.sequence) # Met à jour avec la séquence N-1
        logger.info("Modèles Markov de session réinitialisés et recalculés après annulation.")

        # 5. Invalider/Vider le cache LGBM
        self.lgbm_cache = []
        logger.debug("Cache LGBM vidé après annulation.")