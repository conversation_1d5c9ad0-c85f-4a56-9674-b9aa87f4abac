# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7473 à 7524
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_combined_metrics(self):
        """Met à jour les métriques combinées dans le tableau de bord."""
        # Mettre à jour les métriques d'incertitude
        if hasattr(self, 'prediction_history') and self.prediction_history:
            last_prediction = self.prediction_history[-1]
            if 'confidence_metrics' in last_prediction:
                metrics = last_prediction['confidence_metrics']
                if 'epistemic_uncertainty' in metrics:
                    self.combined_metric_vars['epistemic_uncertainty'].set(f"Incertitude Épistémique: {metrics['epistemic_uncertainty']:.4f}")
                if 'aleatoric_uncertainty' in metrics:
                    self.combined_metric_vars['aleatoric_uncertainty'].set(f"Incertitude Aléatoire: {metrics['aleatoric_uncertainty']:.4f}")
                if 'context_sensitivity' in metrics:
                    self.combined_metric_vars['context_sensitivity'].set(f"Sensibilité Contextuelle: {metrics['context_sensitivity']:.4f}")
                if 'total_uncertainty' in metrics:
                    self.combined_metric_vars['total_uncertainty'].set(f"Incertitude Totale: {metrics['total_uncertainty']:.4f}")

        # Mettre à jour les poids des modèles
        if hasattr(self, 'config'):
            self.weights_vars['markov_weight'].set(f"Poids Markov: {self.config.weight_markov:.2f}")
            self.weights_vars['lgbm_weight'].set(f"Poids LGBM: {self.config.weight_lgbm:.2f}")
            self.weights_vars['lstm_weight'].set(f"Poids LSTM: {self.config.weight_lstm:.2f}")

        # Mettre à jour les métriques de performance combinées
        if hasattr(self, 'prediction_history') and len(self.prediction_history) > 0:
            # Calculer l'exactitude combinée
            correct_predictions = 0
            total_predictions = 0
            recommendations = 0
            total_confidence = 0

            for pred in self.prediction_history:
                if 'actual_outcome' in pred and 'combined_prediction' in pred:
                    total_predictions += 1
                    if pred['actual_outcome'] == pred['combined_prediction']:
                        correct_predictions += 1

                if 'recommendation' in pred and pred['recommendation'] is not None:
                    recommendations += 1

                if 'combined_confidence' in pred:
                    total_confidence += pred['combined_confidence']

            if total_predictions > 0:
                accuracy = correct_predictions / total_predictions
                self.combined_performance_vars['combined_accuracy'].set(f"Exactitude Combinée: {accuracy:.4f}")

            if len(self.prediction_history) > 0:
                recommendation_rate = recommendations / len(self.prediction_history)
                self.combined_performance_vars['recommendation_rate'].set(f"Taux de Recommandation: {recommendation_rate:.4f}")

                avg_confidence = total_confidence / len(self.prediction_history)
                self.combined_performance_vars['average_confidence'].set(f"Confiance Moyenne: {avg_confidence:.4f}")