# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13517 à 13568
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_dependent_configs(self):
        """Met à jour les configurations dépendantes (Threads, etc.)
           basées sur self.target_cpu_cores et self.device."""
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))

        num_threads = self.target_cpu_cores # Utilise les cœurs validés

        # 1. Mettre à jour le nombre de threads pour PyTorch
        try:
            if self.device.type == 'cpu':
                torch.set_num_threads(num_threads)
                logger_instance.info(f"PyTorch (CPU) nombre de threads mis à jour : {num_threads}")
            # else: # GPU peut gérer différemment, souvent laisser par défaut
            #    logger_instance.debug("Device est GPU, nombre de threads CPU Torch non modifié.")
        except Exception as e:
            logger_instance.error(f"Erreur lors de la configuration des threads Torch : {e}")

        # 2. Mettre à jour le nombre de jobs/threads pour LightGBM dans la config
        #    (Cela sera utilisé lors du prochain entraînement/init de LGBM)
        try:
            if hasattr(self.config, 'lgbm_params') and isinstance(self.config.lgbm_params, dict):
                # Mettre à jour n_jobs dans les paramètres LGBM
                original_n_jobs = self.config.lgbm_params.get('n_jobs', -1)
                new_n_jobs = num_threads if num_threads > 0 else -1 # LGBM utilise -1 pour tous les cœurs
                self.config.lgbm_params['n_jobs'] = new_n_jobs

                # # Optionnel: Mettre aussi à jour thread_count ou num_threads si présent
                # original_num_threads = self.config.lgbm_params.get('num_threads', -1)
                # self.config.lgbm_params['num_threads'] = new_n_jobs

                if original_n_jobs != new_n_jobs:
                     logger_instance.info(f"LightGBM n_jobs (dans config) mis à jour : {new_n_jobs}")
                else:
                     logger_instance.debug(f"LightGBM n_jobs (dans config) déjà à {new_n_jobs}.")

            elif hasattr(self.config, 'n_jobs'): # Si la config a un n_jobs global
                 original_n_jobs = self.config.n_jobs
                 new_n_jobs = num_threads if num_threads > 0 else -1
                 self.config.n_jobs = new_n_jobs
                 if original_n_jobs != new_n_jobs:
                     logger_instance.info(f"Config n_jobs mis à jour : {new_n_jobs}")
                 else:
                      logger_instance.debug(f"Config n_jobs déjà à {new_n_jobs}.")
            else:
                 logger_instance.warning("Impossible de trouver où définir n_jobs pour LGBM dans self.config.")

        except Exception as e:
            logger_instance.error(f"Erreur lors de la mise à jour de n_jobs pour LGBM dans la config : {e}")

        # Ajouter ici d'autres mises à jour si nécessaires (ex: configurer Dask, Ray, etc. si utilisés)

        logger_instance.debug("Mise à jour des configurations dépendantes terminée.")