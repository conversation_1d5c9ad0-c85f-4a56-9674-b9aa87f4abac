# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10769 à 10776
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_pattern_counts(self, last_outcome: str) -> None:
        """Met à jour les compteurs de motifs basés sur les 4 derniers coups.
           DOIT être appelée avec self.sequence_lock déjà acquis."""
        if len(self.sequence) >= 4:
             pattern = tuple(self.sequence[-4:]) # Le motif INCLUT le dernier coup
             dict_key = last_outcome # Clé = résultat du dernier coup
             if dict_key in self.pattern_counts:
                self.pattern_counts[dict_key][pattern] += 1