# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4680 à 4709
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_progress(self, value: int, message: str) -> None:
        """Met à jour la barre de progression de manière thread-safe."""
        # Fonction pour exécuter dans le thread principal de l'UI
        def update_ui():
             # Valider la valeur
             value_clamped = max(0, min(100, int(value)))
             if hasattr(self, 'progress_var') and self.progress_var:
                 self.progress_var.set(value_clamped)
             if hasattr(self, 'progress_label_var') and self.progress_label_var:
                 # Tronquer les messages trop longs
                 message_display = message[:100] + '...' if len(message) > 100 else message
                 self.progress_label_var.set(message_display)
             # Mettre à jour l'UI si la fenêtre existe toujours
             if self.is_ui_available():
                 self.root.update_idletasks()

        # Si on est déjà dans le thread principal, appeler directement
        # Sinon, utiliser `after` pour planifier l'exécution dans le thread principal
        try:
            # Simple check: if Tkinter method directly accessible on root, likely we are main thread.
            # This is not foolproof, but avoids checking thread identity directly.
            # Vérifier si l'UI est disponible
            self.is_ui_available() # Simple call to check if direct access possible
            update_ui()
        except tk.TclError: # Probablement appelé depuis un autre thread
            if self.is_ui_available():
                self.root.after(0, update_ui)
        except Exception as e:
            # Log l'erreur mais ne pas crasher l'application
            logger.error(f"Erreur inattendue dans _update_progress: {e}", exc_info=False) # exc_info=False pour éviter spam logs