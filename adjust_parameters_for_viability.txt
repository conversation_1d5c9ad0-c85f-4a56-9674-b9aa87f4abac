# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13432 à 13515
# Type: Méthode de la classe HybridBaccaratPredictor

    def adjust_parameters_for_viability(self, has_wait_in_target, has_non_wait_in_target):
        """
        Ajuste les paramètres pour garantir qu'au moins une recommandation WAIT et une NON-WAIT
        soient faites entre les manches 31 et 60, tout en maximisant le nombre de recommandations
        NON-WAIT valides.

        Args:
            has_wait_in_target (bool): Indique si au moins une recommandation WAIT a été faite
            has_non_wait_in_target (bool): Indique si au moins une recommandation NON-WAIT a été faite

        Returns:
            bool: True si des ajustements ont été effectués, False sinon
        """
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        adjustments_made = False

        # Si aucune recommandation WAIT n'a été faite
        if not has_wait_in_target:
            adjustments_made = True

            # Augmenter le seuil de confiance pour avoir plus de WAIT, mais de manière minimale
            # pour ne pas trop réduire les recommandations NON-WAIT
            current_min_confidence = getattr(self.config, 'min_confidence_for_recommendation', 0.6)
            # Ajustement plus modéré pour favoriser les NON-WAIT
            self.config.min_confidence_for_recommendation = min(0.85, current_min_confidence + 0.03)

            # Augmenter le ratio minimum de WAIT, mais le maintenir bas
            current_wait_ratio_min = getattr(self.config, 'wait_ratio_min_threshold', 0.2)
            # Ajustement minimal pour satisfaire la condition tout en favorisant les NON-WAIT
            self.config.wait_ratio_min_threshold = min(0.3, current_wait_ratio_min + 0.05)

            # Réduire le seuil de détection des patterns d'erreur
            current_error_threshold = getattr(self.config, 'error_pattern_threshold', 0.6)
            self.config.error_pattern_threshold = max(0.4, current_error_threshold - 0.05)

            # Réduire le seuil d'incertitude de transition
            current_transition_threshold = getattr(self.config, 'transition_uncertainty_threshold', 0.6)
            self.config.transition_uncertainty_threshold = max(0.4, current_transition_threshold - 0.05)

            # Augmenter le seuil de confiance pour le WaitPlacementOptimizer
            current_confidence_threshold = getattr(self.config, 'wait_optimizer_confidence_threshold', 0.7)
            self.config.wait_optimizer_confidence_threshold = min(0.85, current_confidence_threshold + 0.05)

            logger_instance.warning("=" * 80)
            logger_instance.warning("OBJECTIF PRINCIPAL: Maximiser les recommandations NON-WAIT VALIDES")
            logger_instance.warning("Les WAIT stratégiques sont essentiels pour augmenter le taux de validité des NON-WAIT")
            logger_instance.warning("=" * 80)
            logger_instance.warning("Paramètres ajustés pour ajouter des WAIT stratégiques:")
            logger_instance.warning(f"  min_confidence_for_recommendation: {self.config.min_confidence_for_recommendation:.2f}")
            logger_instance.warning(f"  wait_ratio_min_threshold: {self.config.wait_ratio_min_threshold:.2f}")
            logger_instance.warning(f"  error_pattern_threshold: {self.config.error_pattern_threshold:.2f}")
            logger_instance.warning(f"  transition_uncertainty_threshold: {self.config.transition_uncertainty_threshold:.2f}")
            logger_instance.warning(f"  wait_optimizer_confidence_threshold: {self.config.wait_optimizer_confidence_threshold:.2f}")

        # Si aucune recommandation NON-WAIT n'a été faite
        if not has_non_wait_in_target:
            adjustments_made = True

            # Réduire significativement le seuil de confiance pour avoir plus de NON-WAIT
            current_min_confidence = getattr(self.config, 'min_confidence_for_recommendation', 0.6)
            # Réduction plus agressive pour favoriser les NON-WAIT
            self.config.min_confidence_for_recommendation = max(0.3, current_min_confidence - 0.1)

            # Réduire significativement le ratio maximum de WAIT
            current_wait_ratio_max = getattr(self.config, 'wait_ratio_max_threshold', 0.7)
            # Réduction plus agressive pour favoriser les NON-WAIT
            self.config.wait_ratio_max_threshold = max(0.2, current_wait_ratio_max - 0.15)

            # Augmenter significativement le seuil d'incertitude
            current_uncertainty_threshold = getattr(self.config, 'uncertainty_threshold', 0.4)
            self.config.uncertainty_threshold = min(0.8, current_uncertainty_threshold + 0.15)

            # Réduire significativement le seuil de confiance pour le WaitPlacementOptimizer
            current_confidence_threshold = getattr(self.config, 'wait_optimizer_confidence_threshold', 0.7)
            self.config.wait_optimizer_confidence_threshold = max(0.4, current_confidence_threshold - 0.15)

            logger_instance.warning("Paramètres ajustés pour favoriser fortement les recommandations NON-WAIT:")
            logger_instance.warning(f"  min_confidence_for_recommendation: {self.config.min_confidence_for_recommendation:.2f}")
            logger_instance.warning(f"  wait_ratio_max_threshold: {self.config.wait_ratio_max_threshold:.2f}")
            logger_instance.warning(f"  uncertainty_threshold: {self.config.uncertainty_threshold:.2f}")
            logger_instance.warning(f"  wait_optimizer_confidence_threshold: {self.config.wait_optimizer_confidence_threshold:.2f}")
            logger_instance.warning("OBJECTIF PRINCIPAL: Maximiser les recommandations NON-WAIT valides")

        return adjustments_made