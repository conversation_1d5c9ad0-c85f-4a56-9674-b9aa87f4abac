DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ANCIENNES CLASSES
================================================================================

Ce fichier contient la description détaillée des définitions de classes
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : ANCIENNES CLASSES
Définitions complètes des classes du système.

TOTAL : 2 CLASSES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
DÉFINITIONS DE CLASSES
================================================================================

1. class_HybridBaccaratPredictor.txt (Classe HybridBaccaratPredictor - Classe principale du système)
   - FONCTION : Définition complète de la classe principale du système de prédiction
   - UTILITÉ : Architecture centrale intégrant tous les composants ML et interface

2. class_ConsecutiveConfidenceCalculator.txt (Classe ConsecutiveConfidenceCalculator - Calculateur confiance consécutive)
   - FONCTION : Classe spécialisée pour calcul de confiance sur manches consécutives
   - UTILITÉ : Gestion sophistiquée de la confiance pour plages de manches spécifiques
