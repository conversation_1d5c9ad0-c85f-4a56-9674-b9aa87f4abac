DESCRIPTIF DÉTAILLÉ DES MÉTHODES - SYSTÈME ML
================================================================================

Ce fichier contient la description détaillée des classes complètes du système
ML, organisées dans le dossier spécial anciennesclasses.

STRUCTURE DU SYSTÈME (basée sur l'analyse architecturale) :
- **anciennesclasses** : Classes complètes extraites du système ML

TOTAL : 6 CLASSES ANALYSÉES

Dernière mise à jour: [DATE] - Création plateforme maintenance

================================================================================
SECTION : ANCIENNESCLASSES (6 CLASSES)
================================================================================

Description des classes complètes du système ML.

1. class_AdvancedLSTM.txt (AdvancedLSTM - CLASSE LSTM AVANCÉE COMPLÈTE)
   - Lignes 62-146 dans utils.py (85 lignes)
   - FONCTION : Classe LSTM avancée complète avec attention, dropout et classification
   - UTILITÉ : Architecture LSTM sophistiquée pour classification séquences avec mécanismes attention

2. class_AttentionLayer.txt (AttentionLayer - CLASSE COUCHE ATTENTION COMPLÈTE)
   - Lignes 43-60 dans utils.py (18 lignes)
   - FONCTION : Classe couche attention complète pour pondération séquences LSTM
   - UTILITÉ : Mécanisme attention pour focus éléments importants séquences

3. class_FocalLoss.txt (FocalLoss - CLASSE FOCAL LOSS COMPLÈTE)
   - Lignes 320-364 dans utils.py (45 lignes)
   - FONCTION : Classe Focal Loss complète pour gestion exemples difficiles
   - UTILITÉ : Fonction perte spécialisée pour datasets déséquilibrés avec focus exemples difficiles

4. class_ConsecutiveConfidenceCalculator.txt (ConsecutiveConfidenceCalculator - CLASSE CALCULATEUR CONFIANCE CONSÉCUTIVE)
   - Lignes 434-2027 dans utils.py (1594 lignes)
   - FONCTION : Classe principale pour calcul de confiance basé sur patterns historiques et séquences consécutives
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * config (optionnel) - Configuration initiale pour paramètres par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Configure paramètres par défaut (similarity_threshold=0.8, max_similar_patterns=50)
     * **STOCKAGE DONNÉES :** Initialise historical_data, recent_data, training_data pour patterns
     * **PARAMÈTRES ADAPTATIFS :** target_round_min=31, target_round_max=60 pour focus manches cibles
     * **FACTEURS CONFIANCE :** occurrence_factor, consecutive_factor, sequence_bonus configurables
     * **SEUILS WAIT :** wait_threshold_base, wait_threshold_adaptive pour recommandations WAIT
     * **MÉTRIQUES PERFORMANCE :** Suivi success_rate, consecutive_length moyens
   - RETOUR : None (constructeur)
   - UTILITÉ : Architecture centrale pour analyse patterns et calcul confiance avec optimisations objectif 1

5. class_WaitPlacementOptimizer.txt (WaitPlacementOptimizer - CLASSE OPTIMISEUR PLACEMENT WAIT)
   - Lignes 3328-3816 dans utils.py (489 lignes)
   - FONCTION : Optimise placement stratégique des recommandations WAIT pour maximiser performance globale
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * config (optionnel) - Configuration pour paramètres optimisation et seuils adaptatifs
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION SEUILS :** Configure error_pattern_threshold=0.3, transition_uncertainty_threshold=0.25
     * **PARAMÈTRES RATIO :** wait_ratio_min=0.05, wait_ratio_max=0.15 pour équilibrage optimal
     * **FACTEURS PONDÉRATION :** error_weight=0.7, transition_weight=0.3, consecutive_priority_factor=2.0
     * **STOCKAGE PATTERNS :** error_patterns, transition_patterns pour analyse historique
     * **HISTORIQUES :** outcome_history, wait_efficiency_history pour tendances récentes
     * **MÉTRIQUES PERFORMANCE :** current_wait_ratio, recent_history_window=20 pour adaptation
     * **SEUILS ADAPTATIFS :** wait_efficiency_threshold=0.6, min_pattern_occurrences=3
   - RETOUR : None (constructeur)
   - UTILITÉ : Architecture centrale optimisation WAIT avec équilibrage performance/prudence et focus séquences consécutives

6. class_WaitPlacementOptimizer_1.txt (WaitPlacementOptimizer - CLASSE OPTIMISEUR WAIT - DOUBLON 1)
   - Lignes 3823-4307 dans utils.py (485 lignes)
   - FONCTION : Classe optimiseur placement WAIT version alternative avec architecture similaire
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * config (optionnel) - Configuration pour paramètres optimisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ARCHITECTURE IDENTIQUE :** Même structure que classe principale WaitPlacementOptimizer
     * **PLAGE LIGNES DIFFÉRENTE :** Lignes 3823-4307 vs 3328-3816 pour version principale
     * **MÉTHODES SIMILAIRES :** should_wait, update, _adapt_thresholds, _create_pattern_key
     * **PARAMÈTRES IDENTIQUES :** error_pattern_threshold, transition_uncertainty_threshold, wait_ratio_min/max
   - RETOUR : None (constructeur)
   - UTILITÉ : Version alternative optimiseur WAIT avec même fonctionnalités
