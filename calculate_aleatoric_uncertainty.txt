# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8646 à 8667
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_aleatoric_uncertainty(self, prob: float) -> float:
        """
        Calcule l'incertitude aléatoire (incertitude inhérente) basée sur
        l'entropie de la prédiction.

        Args:
            prob (float): Probabilité prédite pour une classe

        Returns:
            float: Score d'incertitude aléatoire entre 0 et 1
        """
        # Utiliser un epsilon depuis la configuration pour éviter les divisions par zéro
        epsilon = getattr(self.config, 'epsilon_value', 1e-9)

        # Assurer que prob est dans [epsilon, 1-epsilon] pour éviter log(0)
        prob_safe = max(epsilon, min(1.0 - epsilon, prob))

        # Calculer l'entropie binaire normalisée
        entropy = -(prob_safe * np.log2(prob_safe) + (1.0 - prob_safe) * np.log2(1.0 - prob_safe))

        # L'entropie binaire max est 1.0, donc pas besoin de normalisation supplémentaire
        return entropy