# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4386 à 4547
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_uncertainty(self, features: Optional[List[float]], predicted_class: Optional[int] = None) -> float:
        """
        Calcule un score d'incertitude basé sur la variance des prédictions
        des estimateurs du BaggingClassifier LGBM (self.lgbm_uncertainty).
        UTILISE LES FEATURES COMPLÈTES POUR CHAQUE ESTIMATEUR (contournement).
        Retourne 0.5 en cas d'erreur ou si le calcul est impossible.

        Args:
            features: Liste des features d'entrée
            predicted_class: Classe prédite (0 = PLAYER, 1 = BANKER), optionnel

        Returns:
            float: Score d'incertitude entre 0 et 1
        """
        # Suppression des logs de débogage pour éviter les messages répétitifs
        # pendant l'optimisation Optuna

        # Vérifications initiales
        if features is None:
             logger.warning("calculate_uncertainty: Features d'entrée manquantes (None). Retour valeur par défaut")
             return self.config.UNCERTAINTY_DEFAULT_VALUE
        if len(features) != len(self.feature_names): # Comparer à la longueur attendue
             logger.error(f"calculate_uncertainty: Incohérence du nombre de features d'entrée. Attendu {len(self.feature_names)}, reçu {len(features)}. Retour valeur par défaut")
             return self.config.UNCERTAINTY_DEFAULT_VALUE

        # Vérifier si le modèle LGBM est initialisé
        if not hasattr(self, 'lgbm_uncertainty') or self.lgbm_uncertainty is None:
            # Tenter d'initialiser le modèle SEULEMENT si nous ne sommes pas déjà en train d'initialiser
            if not hasattr(self, '_initializing_models') or not self._initializing_models:
                try:
                    if hasattr(self, 'init_ml_models'):
                        logger.info("Tentative d'initialisation du modèle LGBM depuis calculate_uncertainty...")
                        # Définir le drapeau d'initialisation pour éviter la récursion
                        self._initializing_models = True
                        self.init_ml_models()
                        logger.info("Modèle LGBM initialisé avec succès dans calculate_uncertainty.")
                        # Réinitialiser le drapeau
                        self._initializing_models = False
                except Exception as e:
                    # Réinitialiser le drapeau en cas d'erreur
                    self._initializing_models = False
                    logger.error(f"Échec de l'initialisation du modèle LGBM dans calculate_uncertainty: {e}")
                    return self.config.UNCERTAINTY_DEFAULT_VALUE
            else:
                logger.warning("Initialisation des modèles déjà en cours. Évitement de la récursion dans calculate_uncertainty.")
                return self.config.UNCERTAINTY_DEFAULT_VALUE

        with self.model_lock: # Nécessaire pour accéder à lgbm_uncertainty et feature_scaler
            if self.lgbm_uncertainty is None:
                # Suppression du log de débogage pour éviter les messages répétitifs
                return self.config.UNCERTAINTY_DEFAULT_VALUE
            if not hasattr(self.lgbm_uncertainty, 'estimators_') or not self.lgbm_uncertainty.estimators_:
                logger.warning("calculate_uncertainty: lgbm_uncertainty n'a pas d'estimateurs valides. Retour valeur par défaut")
                return self.config.UNCERTAINTY_DEFAULT_VALUE
            if self.feature_scaler is None:
                 logger.error("calculate_uncertainty: feature_scaler est None. Retour valeur par défaut")
                 return self.config.UNCERTAINTY_DEFAULT_VALUE

            # Log pour confirmer qu'on n'utilise pas estimator_features_
            if hasattr(self.lgbm_uncertainty, 'estimator_features_') and getattr(self.lgbm_uncertainty, 'max_features', 1.0) < 1.0:
                logger.warning("   -> Attribut 'estimator_features_' trouvé mais IGNORÉ par cette version de calculate_uncertainty.")
            # else: logger.debug("   -> Attribut 'estimator_features_' non trouvé ou max_features=1.0.")

            try:
                # 1. Préparer les features (array NumPy)
                features_array = np.array(features, dtype=np.float32).reshape(1, -1)

                # 2. Mise à l'échelle
                # Vérifier si scaler est 'fit' avant transform
                if not hasattr(self.feature_scaler, 'n_features_in_'):
                     logger.warning("calculate_uncertainty: feature_scaler non ajusté ('fit'). Retour valeur par défaut")
                     return self.config.UNCERTAINTY_DEFAULT_VALUE
                try:
                     features_scaled_array = self.feature_scaler.transform(features_array)
                     logger.debug(f"calculate_uncertainty: Input array shape après scaling: {features_scaled_array.shape}")
                except NotFittedError:
                     logger.error("calculate_uncertainty: feature_scaler.transform a échoué (NotFittedError). Retour valeur par défaut")
                     return self.config.UNCERTAINTY_DEFAULT_VALUE
                except ValueError as ve_scale:
                     logger.error(f"calculate_uncertainty: feature_scaler.transform a échoué (ValueError - prob. nbre features {features_array.shape[1]} vs scaler {self.feature_scaler.n_features_in_}?): {ve_scale}. Retour valeur par défaut")
                     return self.config.UNCERTAINTY_DEFAULT_VALUE

                # 3. Obtenir les prédictions de chaque estimateur en utilisant les features COMPLÈTES
                estimator_probas_banker = []
                num_estimators = len(self.lgbm_uncertainty.estimators_)
                if num_estimators == 0: # Sécurité supplémentaire
                     logger.warning("calculate_uncertainty: lgbm_uncertainty.estimators_ est vide. Retour valeur par défaut")
                     return self.config.UNCERTAINTY_DEFAULT_VALUE

                # Suppression du log de débogage pour éviter les messages répétitifs
                # logger.debug(f"calculate_uncertainty: Boucle sur {num_estimators} estimateurs (utilisant TOUTES les features)...")
                classes_banker_index_map = {} # Cache pour l'index de la classe Banker

                for i, estimator in enumerate(self.lgbm_uncertainty.estimators_):
                    try:
                        # --- Utiliser features_scaled_array directement ---
                        estimator_input_array = features_scaled_array
                        # logger.debug(f"  Estimator #{i}: Input shape pour predict_proba: {estimator_input_array.shape}")
                        # -------------------------------------------------

                        # Prédiction des probabilités
                        proba = estimator.predict_proba(estimator_input_array)[0]

                        # Trouver l'index de la classe 'banker' (qui est 1 dans les données) - Optimisé avec cache
                        estimator_id = id(estimator) # Utiliser ID pour le cache
                        if estimator_id not in classes_banker_index_map:
                            banker_index = 1 # Défaut (1 correspond à Banker dans les indices 0-based)
                            if hasattr(estimator, 'classes_'):
                                classes = list(estimator.classes_)
                                if len(classes) == 2:
                                    try: banker_index = classes.index(1)  # 1 correspond à Banker dans les données
                                    except ValueError: logger.warning(f"  Estimator #{i}: Classe 1 (Banker) non trouvée dans classes_: {classes}. Utilisation index 1 par défaut.")
                                else: logger.warning(f"  Estimator #{i}: Nombre inattendu de classes: {classes}. Utilisation index 1 par défaut.")
                            else: logger.warning(f"  Estimator #{i}: Attribut 'classes_' manquant. Utilisation index 1 par défaut.")
                            classes_banker_index_map[estimator_id] = banker_index
                        else:
                             banker_index = classes_banker_index_map[estimator_id]

                        # Assurer que banker_index est valide pour proba
                        if banker_index < len(proba):
                            estimator_probas_banker.append(proba[banker_index])
                        else:
                             logger.error(f"Index Banker ({banker_index}) hors limites pour probas (len={len(proba)}) estimateur #{i}. Ignoré.")


                    except NotFittedError:
                         logger.error(f"calculate_uncertainty: Estimateur bagging #{i} n'était pas 'fit'. Retour valeur par défaut")
                         return self.config.UNCERTAINTY_DEFAULT_VALUE
                    except ValueError as ve:
                         # Peut arriver si le nbre de features attendu par l'estimateur interne diffère (peu probable avec max_features=1.0)
                         logger.error(f"Erreur ValueError predict_proba sur estimateur bagging #{i} (input shape {estimator_input_array.shape}): {ve}. Retour valeur par défaut", exc_info=True)
                         return self.config.UNCERTAINTY_DEFAULT_VALUE
                    except Exception as e_inner:
                        logger.error(f"Erreur interne predict_proba sur estimateur bagging #{i}: {e_inner}. Retour valeur par défaut", exc_info=True)
                        return self.config.UNCERTAINTY_DEFAULT_VALUE

                if not estimator_probas_banker:
                     logger.error("calculate_uncertainty: Aucune prédiction valide obtenue des estimateurs. Retour valeur par défaut")
                     return self.config.UNCERTAINTY_DEFAULT_VALUE

                # 4. Calculer la variance des probabilités pour 'Banker'
                variance = np.var(np.array(estimator_probas_banker))

                # 5. Normaliser et Clipper ( variance max = 0.25 (entre 0 et 1), *4 pour mapper sur [0, 1])
                # Utiliser les constantes définies dans config.py, mais réduire le facteur de normalisation
                # Réduire le facteur de normalisation pour obtenir des valeurs d'incertitude plus réalistes
                uncertainty_normalization_factor = self.config.UNCERTAINTY_VARIANCE_NORMALIZATION_FACTOR / 2.0
                logger.warning(f"Facteur de normalisation d'incertitude réduit: {uncertainty_normalization_factor} (original: {self.config.UNCERTAINTY_VARIANCE_NORMALIZATION_FACTOR})")

                uncertainty_score = np.clip(
                    variance * uncertainty_normalization_factor,
                    self.config.UNCERTAINTY_MIN_CLIP,
                    self.config.UNCERTAINTY_MAX_CLIP
                )
                # Suppression du log de débogage pour éviter les messages répétitifs
                # logger.debug(f"calculate_uncertainty: Probas Banker: {np.round(estimator_probas_banker, 3)}. Variance={variance:.6f} (raw: {variance:.4e}), Score Incertitude={uncertainty_score:.6f}")

                return float(uncertainty_score)

            except Exception as e:
                logger.error(f"Erreur majeure inattendue dans calculate_uncertainty: {e}", exc_info=True)
                return self.config.UNCERTAINTY_DEFAULT_VALUE