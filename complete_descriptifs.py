# -*- coding: utf-8 -*-
"""
Script pour compléter tous les fichiers Descriptif.txt des sous-dossiers
en copiant les descriptifs correspondants depuis le Descriptif.txt principal
"""
import os
import re

def complete_descriptifs():
    base_dir = r"C:\Users\<USER>\Desktop\travail\OptimisationOptuna"
    main_descriptif = os.path.join(base_dir, "Descriptif.txt")
    
    print("🔍 COMPLETION DES FICHIERS DESCRIPTIF.TXT DES SOUS-DOSSIERS")
    print("=" * 70)
    
    # Lire le Descriptif.txt principal
    with open(main_descriptif, 'r', encoding='utf-8') as f:
        main_content = f.read()
    
    # Extraire toutes les méthodes du descriptif principal
    main_methods = {}
    current_method = None
    current_content = []
    
    for line in main_content.split('\n'):
        if re.match(r'^\d+\. ', line):
            # Sauvegarder la méthode précédente
            if current_method:
                main_methods[current_method] = '\n'.join(current_content)
            
            # Extraire le nom du fichier de la nouvelle méthode
            match = re.search(r'(\w+\.txt)', line)
            if match:
                current_method = match.group(1)
                current_content = [line]
            else:
                current_method = None
                current_content = []
        else:
            if current_method:
                current_content.append(line)
    
    # Sauvegarder la dernière méthode
    if current_method:
        main_methods[current_method] = '\n'.join(current_content)
    
    print(f"📊 Méthodes extraites du descriptif principal: {len(main_methods)}")
    
    # Parcourir tous les sous-dossiers
    for subdir in os.listdir(base_dir):
        subdir_path = os.path.join(base_dir, subdir)
        if os.path.isdir(subdir_path) and subdir != base_dir:
            descriptif_path = os.path.join(subdir_path, "Descriptif.txt")
            
            print(f"\n📁 Traitement du sous-dossier: {subdir}")
            
            # Lister les fichiers .txt du sous-dossier (sauf Descriptif.txt)
            txt_files = []
            for file in os.listdir(subdir_path):
                if file.endswith('.txt') and file != 'Descriptif.txt':
                    txt_files.append(file)
            
            txt_files.sort()
            print(f"   📄 Fichiers trouvés: {len(txt_files)}")
            
            # Créer le contenu du Descriptif.txt pour ce sous-dossier
            descriptif_content = []
            descriptif_content.append(f"DESCRIPTIF DÉTAILLÉ DES MÉTHODES - {subdir.upper()}")
            descriptif_content.append("=" * (40 + len(subdir)))
            descriptif_content.append("")
            descriptif_content.append(f"Ce fichier contient la description détaillée de toutes les méthodes")
            descriptif_content.append(f"présentes dans le sous-dossier {subdir}.")
            descriptif_content.append("")
            
            # Ajouter chaque méthode avec sa description
            method_number = 1
            found_methods = 0
            missing_methods = 0
            
            for txt_file in txt_files:
                if txt_file in main_methods:
                    # Remplacer le numéro par le numéro séquentiel du sous-dossier
                    method_desc = main_methods[txt_file]
                    # Remplacer le numéro au début
                    method_desc = re.sub(r'^\d+\.', f'{method_number}.', method_desc)
                    descriptif_content.append(method_desc)
                    descriptif_content.append("")
                    found_methods += 1
                else:
                    # Méthode non trouvée dans le descriptif principal
                    descriptif_content.append(f"{method_number}. {txt_file}")
                    descriptif_content.append("   - FONCTION : [DESCRIPTION NON TROUVÉE DANS LE DESCRIPTIF PRINCIPAL]")
                    descriptif_content.append("   - PARAMÈTRES : [À COMPLÉTER]")
                    descriptif_content.append("   - FONCTIONNEMENT DÉTAILLÉ : [À COMPLÉTER]")
                    descriptif_content.append("   - RETOUR : [À COMPLÉTER]")
                    descriptif_content.append("   - UTILITÉ : [À COMPLÉTER]")
                    descriptif_content.append("")
                    missing_methods += 1
                
                method_number += 1
            
            # Ajouter le total à la fin
            descriptif_content.append(f"TOTAL : {len(txt_files)} méthodes analysées et documentées")
            descriptif_content.append(f"- Méthodes trouvées dans descriptif principal: {found_methods}")
            descriptif_content.append(f"- Méthodes manquantes: {missing_methods}")
            
            # Écrire le fichier Descriptif.txt du sous-dossier
            with open(descriptif_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(descriptif_content))
            
            print(f"   ✅ Descriptif.txt créé: {found_methods} trouvées, {missing_methods} manquantes")
    
    print(f"\n✅ COMPLETION TERMINÉE POUR TOUS LES SOUS-DOSSIERS")

if __name__ == "__main__":
    complete_descriptifs()
    print("✅ TERMINÉ")
