# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1460 à 1484
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

                def get_confidence_adjustment(self):
                    """Calcule l'ajustement de confiance basé sur les performances récentes."""
                    if not self.recent_recommendations or not self.recent_outcomes:
                        return 0.0  # Pas d'ajustement par défaut

                    # Calculer le taux de succès des recommandations NON-WAIT récentes
                    valid_count = 0
                    total_nonwait = 0

                    for i, rec in enumerate(self.recent_recommendations):
                        if rec != 'wait':
                            total_nonwait += 1
                            if i < len(self.recent_outcomes) and rec == self.recent_outcomes[i]:
                                valid_count += 1

                    if total_nonwait == 0:
                        return 0.0

                    success_rate = valid_count / total_nonwait

                    # Ajuster la confiance en fonction du taux de succès
                    # Plus le taux de succès est élevé, plus l'ajustement est positif
                    confidence_adjustment_min = getattr(config_ref, 'confidence_adjustment_min', 0.5)
                    confidence_adjustment_max = getattr(config_ref, 'confidence_adjustment_max', 2.0)
                    return (success_rate - confidence_adjustment_min) * confidence_adjustment_max  # Plage: -1.0 à 1.0