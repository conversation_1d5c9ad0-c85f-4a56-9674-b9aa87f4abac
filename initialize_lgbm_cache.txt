# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11734 à 11740
# Type: Méthode de la classe HybridBaccaratPredictor

    def initialize_lgbm_cache(self, maxlen: int = None) -> None:
        # Utiliser la valeur de maxlen si fournie, sinon récupérer depuis la configuration
        if maxlen is None:
            maxlen = getattr(self.config, 'lgbm_cache_maxlen', 100)
        self.lgbm_cache = deque(maxlen=maxlen)
        self.lgbm_cache_dict = {}  # Dictionnaire pour accès rapide
        logger.info(f"Initialisation du cache LGBM avec une taille maximale de {maxlen}.")