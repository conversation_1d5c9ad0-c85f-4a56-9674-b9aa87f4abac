# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13595 à 13603
# Type: Méthode de la classe OptimizationStatsCollector
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, trial_id: Optional[int] = None):
        """
        Initialise le collecteur de statistiques.

        Args:
            trial_id: Identifiant de l'essai Optuna (optionnel)
        """
        self.trial_id = trial_id
        self.reset()