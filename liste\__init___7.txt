# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13322 à 13328
# Type: Méthode de la classe StandardCrossEntropyLoss
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, weight=None, size_average=None, ignore_index=-100,
                 reduce=None, reduction='mean', label_smoothing=0.0):
        super(StandardCrossEntropyLoss, self).__init__()
        self.standard_loss = nn.CrossEntropyLoss(
            weight=weight, size_average=size_average, ignore_index=ignore_index,
            reduce=reduce, reduction=reduction, label_smoothing=label_smoothing
        )