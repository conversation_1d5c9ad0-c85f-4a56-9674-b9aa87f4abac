# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 15103 à 15302
# Type: Méthode de la classe MetaOptimizer

    def _analyze_optimization_results(self, study, params_names=None, n_trials=None):
        """
        Analyse les résultats d'une optimisation Optuna et génère des statistiques détaillées.
        Cette méthode fournit des informations sur les paramètres optimaux, leur importance,
        et les tendances observées pendant l'optimisation.

        Args:
            study: Étude Optuna terminée
            params_names: Liste des noms de paramètres à analyser (si None, utilise tous les paramètres)
            n_trials: Nombre d'essais à analyser (si None, utilise tous les essais)

        Returns:
            dict: Dictionnaire contenant les résultats de l'analyse
        """
        import numpy as np
        import pandas as pd
        import optuna
        import time

        # Vérifier que l'étude contient des essais
        if len(study.trials) == 0:
            logger.warning("Aucun essai à analyser")
            return {
                'status': 'error',
                'message': 'Aucun essai à analyser',
                'timestamp': time.time()
            }

        # Limiter le nombre d'essais si demandé
        if n_trials is not None and n_trials > 0:
            trials = study.trials[:n_trials]
        else:
            trials = study.trials

        # Filtrer les essais terminés
        completed_trials = [t for t in trials if t.state == optuna.trial.TrialState.COMPLETE]

        if len(completed_trials) == 0:
            logger.warning("Aucun essai terminé à analyser")
            return {
                'status': 'error',
                'message': 'Aucun essai terminé à analyser',
                'timestamp': time.time()
            }

        # Extraire les paramètres et les valeurs objectives
        params_values = []
        objective_values = []

        for trial in completed_trials:
            params_values.append(trial.params)
            objective_values.append(trial.value)

        # Convertir en DataFrame pour faciliter l'analyse
        df = pd.DataFrame(params_values)
        df['objective'] = objective_values

        # Filtrer les paramètres si une liste est fournie
        if params_names is not None:
            param_columns = [col for col in df.columns if col in params_names]
        else:
            param_columns = [col for col in df.columns if col != 'objective']

        # Calculer les statistiques de base
        stats = {
            'status': 'success',
            'timestamp': time.time(),
            'n_trials': len(trials),
            'n_completed_trials': len(completed_trials),
            'best_value': study.best_value,
            'best_params': study.best_params,
            'param_stats': {},
            'correlations': {},
            'importance': {}
        }

        # Calculer les statistiques pour chaque paramètre
        for param in param_columns:
            # Vérifier si le paramètre est numérique
            if pd.api.types.is_numeric_dtype(df[param]):
                stats['param_stats'][param] = {
                    'min': float(df[param].min()),
                    'max': float(df[param].max()),
                    'mean': float(df[param].mean()),
                    'median': float(df[param].median()),
                    'std': float(df[param].std()),
                    'type': 'numeric'
                }

                # Calculer la corrélation avec l'objectif
                corr = df[param].corr(df['objective'])
                stats['correlations'][param] = float(corr) if not np.isnan(corr) else 0.0
            else:
                # Paramètre catégoriel
                value_counts = df[param].value_counts().to_dict()
                stats['param_stats'][param] = {
                    'unique_values': len(value_counts),
                    'value_counts': {str(k): int(v) for k, v in value_counts.items()},
                    'type': 'categorical'
                }

                # Calculer la performance moyenne par valeur
                performance_by_value = {}
                for value in df[param].unique():
                    mean_objective = df[df[param] == value]['objective'].mean()
                    performance_by_value[str(value)] = float(mean_objective)

                stats['param_stats'][param]['performance_by_value'] = performance_by_value

        # Calculer l'importance des paramètres
        try:
            # Utiliser la méthode d'importance des features d'Optuna si disponible
            if hasattr(optuna, 'importance'):
                importance = optuna.importance.get_param_importances(study)
                stats['importance'] = {k: float(v) for k, v in importance.items()}
            else:
                # Utiliser notre propre méthode
                stats['importance'] = self._get_feature_importance(study, params_names)
        except Exception as e:
            logger.warning(f"Erreur lors du calcul de l'importance des paramètres: {e}")
            stats['importance'] = {}

        # Analyser les tendances au fil des essais
        try:
            # Créer un DataFrame avec l'historique des essais
            history_df = pd.DataFrame({
                'trial_number': [t.number for t in completed_trials],
                'objective': [t.value for t in completed_trials]
            })

            # Calculer la moyenne mobile pour détecter les tendances
            window_size = min(10, len(history_df) // 2) if len(history_df) > 10 else 2
            if window_size > 1:
                history_df['moving_avg'] = history_df['objective'].rolling(window=window_size).mean()

                # Calculer la pente de la moyenne mobile
                if len(history_df) > window_size:
                    x = history_df['trial_number'].values[-10:]
                    y = history_df['moving_avg'].dropna().values[-10:]
                    if len(x) == len(y) and len(x) > 1:
                        slope = np.polyfit(x, y, 1)[0]
                        stats['trend'] = {
                            'slope': float(slope),
                            'improving': bool(slope < 0) if study.direction == optuna.study.StudyDirection.MINIMIZE else bool(slope > 0),
                            'window_size': window_size
                        }
        except Exception as e:
            logger.warning(f"Erreur lors de l'analyse des tendances: {e}")

        # Analyser la convergence
        try:
            # Calculer la différence entre les meilleurs résultats consécutifs
            best_values = []
            current_best = float('inf') if study.direction == optuna.study.StudyDirection.MINIMIZE else float('-inf')

            for trial in sorted(completed_trials, key=lambda t: t.number):
                if study.direction == optuna.study.StudyDirection.MINIMIZE:
                    if trial.value < current_best:
                        current_best = trial.value
                else:
                    if trial.value > current_best:
                        current_best = trial.value
                best_values.append(current_best)

            # Calculer les améliorations relatives
            if len(best_values) > 1:
                improvements = []
                for i in range(1, len(best_values)):
                    rel_improvement = abs(best_values[i] - best_values[i-1]) / (abs(best_values[i-1]) if best_values[i-1] != 0 else 1)
                    improvements.append(rel_improvement)

                # Calculer la moyenne des dernières améliorations
                last_n = min(10, len(improvements))
                recent_improvement_avg = sum(improvements[-last_n:]) / last_n if last_n > 0 else 0

                stats['convergence'] = {
                    'recent_improvement_avg': float(recent_improvement_avg),
                    'converged': bool(recent_improvement_avg < 0.01),  # Considérer comme convergé si l'amélioration moyenne est < 1%
                    'n_trials_since_best': len(completed_trials) - study.best_trial.number - 1
                }
        except Exception as e:
            logger.warning(f"Erreur lors de l'analyse de la convergence: {e}")

        # Afficher un résumé des résultats
        logger.warning(f"Analyse terminée: {len(completed_trials)} essais analysés")
        logger.warning(f"Meilleure valeur: {study.best_value}")
        logger.warning(f"Meilleurs paramètres: {study.best_params}")

        if 'importance' in stats and stats['importance']:
            logger.warning("Importance des paramètres:")
            for param, importance in sorted(stats['importance'].items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.warning(f"  {param}: {importance:.4f}")

        if 'convergence' in stats:
            if stats['convergence'].get('converged', False):
                logger.warning("L'optimisation semble avoir convergé")
            else:
                logger.warning("L'optimisation ne semble pas avoir encore convergé")

        return stats