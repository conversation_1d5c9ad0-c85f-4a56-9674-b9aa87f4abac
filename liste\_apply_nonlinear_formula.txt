# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 12285 à 12393
# Type: Méthode de la classe OptunaOptimizer

    def _apply_nonlinear_formula(self, param_type, original_value, scale_factor, data_complexity=None, resource_constraints=None):
        """
        Applique une formule non linéaire adaptée au type de paramètre.

        Args:
            param_type: Type de paramètre ('subsample', 'min_child_samples', etc.)
            original_value: Valeur originale du paramètre
            scale_factor: Facteur d'échelle
            data_complexity: Indicateur de complexité des données (optionnel)
            resource_constraints: Indicateur de contraintes de ressources (optionnel)

        Returns:
            Valeur adaptée du paramètre
        """
        import math

        # Valeurs par défaut si non fournies
        if data_complexity is None:
            data_complexity = 1.0  # Complexité moyenne
        if resource_constraints is None:
            resource_constraints = 1.0  # Contraintes moyennes

        # Paramètres qui diminuent quand la taille des données augmente
        decreasing_params = {
            'lgbm_subsample': {
                'formula': lambda x, sf: max(0.3, x * math.exp(-0.05 * sf)),
                'min_val': 0.3,
                'max_val': 1.0
            },
            'lgbm_learning_rate': {
                'formula': lambda x, sf: max(0.001, x / math.sqrt(sf)),
                'min_val': 0.001,
                'max_val': 0.5
            },
            'lstm_learning_rate': {
                'formula': lambda x, sf: max(0.0001, x / (1 + 0.2 * math.log(sf + 1))),
                'min_val': 0.0001,
                'max_val': 0.01
            },
            'lstm_batch_size': {
                'formula': lambda x, sf: max(8, int(x / math.sqrt(sf))),
                'min_val': 8,
                'max_val': 256
            },
            'lstm_epochs': {
                'formula': lambda x, sf: max(2, int(x / math.log(sf + 1))),
                'min_val': 2,
                'max_val': 20
            },
            'markov_smoothing': {
                'formula': lambda x, sf: max(0.001, x / math.pow(sf, 0.3)),
                'min_val': 0.001,
                'max_val': 0.5
            },
            'markov_batch_size': {
                'formula': lambda x, sf: max(10, int(x / math.sqrt(sf))),
                'min_val': 10,
                'max_val': 1000
            }
        }

        # Paramètres qui augmentent quand la taille des données augmente
        increasing_params = {
            'lgbm_min_child_samples': {
                'formula': lambda x, sf: min(1000, int(x * math.sqrt(sf))),
                'min_val': 5,
                'max_val': 1000
            },
            'lgbm_num_iterations': {
                'formula': lambda x, sf: min(2000, int(x * math.log(sf + 1) / math.log(11))),
                'min_val': 50,
                'max_val': 2000
            },
            'markov_depth': {
                'formula': lambda x, sf: min(10, int(x + math.log(sf + 1))),
                'min_val': 3,
                'max_val': 10
            }
        }

        # Appliquer la formule appropriée
        if param_type in decreasing_params:
            formula_info = decreasing_params[param_type]
            formula = formula_info['formula']
            min_val = formula_info['min_val']
            max_val = formula_info['max_val']

            # Appliquer la formule avec ajustement pour la complexité des données et les contraintes de ressources
            adjusted_value = formula(original_value, scale_factor * data_complexity / resource_constraints)

            # Limiter la valeur aux bornes définies
            return max(min_val, min(adjusted_value, max_val))

        elif param_type in increasing_params:
            formula_info = increasing_params[param_type]
            formula = formula_info['formula']
            min_val = formula_info['min_val']
            max_val = formula_info['max_val']

            # Appliquer la formule avec ajustement pour la complexité des données et les contraintes de ressources
            adjusted_value = formula(original_value, scale_factor * data_complexity * resource_constraints)

            # Limiter la valeur aux bornes définies
            return max(min_val, min(adjusted_value, max_val))

        else:
            # Pour les paramètres non reconnus, retourner la valeur originale
            logger.warning(f"Pas de formule non linéaire définie pour le paramètre '{param_type}'. Valeur inchangée.")
            return original_value