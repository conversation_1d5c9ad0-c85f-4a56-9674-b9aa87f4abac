# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13127 à 13221
# Type: Méthode de la classe OptunaOptimizer

    def _apply_optimal_batch_parameters(self, config, phase, for_full_training=False):
        """
        Applique les paramètres de batch optimaux à la configuration
        en fonction de la phase d'optimisation.

        Args:
            config: Configuration à mettre à jour
            phase: Phase d'optimisation ('phase0', 'phase1', 'phase2', 'phase3')
            for_full_training: Si True, adapte les paramètres pour un entraînement complet sur 100% des données

        Returns:
            Configuration mise à jour avec les paramètres optimaux
        """
        if not hasattr(self, 'optimal_batch_params'):
            # Si les paramètres optimaux n'ont pas été calculés, utiliser les valeurs par défaut
            logger.warning("Paramètres de batch optimaux non disponibles, utilisation des valeurs par défaut")
            return config

        # Copier la configuration pour ne pas modifier l'original
        updated_config = config.clone()

        # Adapter les paramètres pour l'entraînement complet si demandé
        params = self.optimal_batch_params
        if for_full_training:
            params = self.adapt_parameters_for_full_training(self.optimal_batch_params)

        # Appliquer les paramètres LGBM spécifiques à la phase si disponibles
        if phase in params and 'lgbm_subsample' in params[phase]:
            updated_config.lgbm_subsample = params[phase]['lgbm_subsample']
        else:
            updated_config.lgbm_subsample = params['lgbm_subsample']

        if phase in params and 'lgbm_min_child_samples' in params[phase]:
            updated_config.lgbm_min_child_samples = params[phase]['lgbm_min_child_samples']
        else:
            updated_config.lgbm_min_child_samples = params['lgbm_min_child_samples']

        # Appliquer les paramètres LGBM avancés
        if hasattr(updated_config, 'lgbm_num_iterations'):
            updated_config.lgbm_num_iterations = params['lgbm_num_iterations']
        if hasattr(updated_config, 'lgbm_learning_rate'):
            updated_config.lgbm_learning_rate = params['lgbm_learning_rate']

        # Appliquer la taille de batch LGBM spécifique à la phase si disponible
        if phase in params and 'lgbm_batch_size' in params[phase] and hasattr(updated_config, 'lgbm_batch_size'):
            updated_config.lgbm_batch_size = params[phase]['lgbm_batch_size']

        # Appliquer les paramètres LSTM en fonction de la phase
        if phase in ['phase2', 'phase3'] or phase.startswith('level2') or phase.startswith('level3'):
            # Phases avec LSTM activé
            if phase in params and 'lstm_batch_size' in params[phase]:
                updated_config.lstm_batch_size = params[phase]['lstm_batch_size']
            else:
                updated_config.lstm_batch_size = params['lstm_batch_size']

            # Appliquer le nombre d'époques optimal si la phase est la phase finale
            if phase == 'phase3' or phase.startswith('level3'):
                updated_config.lstm_epochs = params['lstm_epochs']
            elif phase == 'phase2' or phase.startswith('level2'):
                # Pour la phase 2, utiliser la moitié des époques optimales
                updated_config.lstm_epochs = max(1, params['lstm_epochs'] // 2)

            # Appliquer le learning rate optimal si disponible
            if hasattr(updated_config, 'lstm_learning_rate'):
                updated_config.lstm_learning_rate = params['lstm_learning_rate']

        # Appliquer les paramètres spécifiques à la phase Markov
        if phase == 'markov' or phase.startswith('markov_'):
            # S'assurer que Markov est activé pour cette phase
            updated_config.use_markov_model = True

            # Appliquer les paramètres de batch Markov spécifiques
            if 'markov_batch_size' in params:
                updated_config.markov_batch_size = params['markov_batch_size']

        # Appliquer les paramètres Markov pour toutes les phases
        if hasattr(updated_config, 'max_markov_order'):
            updated_config.max_markov_order = params.get('max_markov_order', params.get('markov_depth', 3))
        if hasattr(updated_config, 'markov_smoothing'):
            updated_config.markov_smoothing = params['markov_smoothing']
        if hasattr(updated_config, 'markov_batch_size'):
            updated_config.markov_batch_size = params['markov_batch_size']
        if hasattr(updated_config, 'markov_global_weight'):
            updated_config.markov_global_weight = params.get('markov_global_weight', getattr(updated_config, 'markov_global_weight', 0.7))
        if hasattr(updated_config, 'markov_context_weight'):
            updated_config.markov_context_weight = params.get('markov_context_weight', getattr(updated_config, 'markov_context_weight', 0.8))
        if hasattr(updated_config, 'markov_decay_factor'):
            updated_config.markov_decay_factor = params.get('markov_decay_factor', getattr(updated_config, 'markov_decay_factor', 0.95))

        # Journaliser les paramètres appliqués
        if for_full_training:
            logger.info(f"Paramètres adaptés pour l'entraînement complet appliqués pour la phase {phase}")
        else:
            logger.info(f"Paramètres de batch optimaux appliqués pour la phase {phase}")
        return updated_config