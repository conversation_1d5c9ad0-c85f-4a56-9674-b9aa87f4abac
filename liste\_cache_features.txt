# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 6211 à 6241
# Type: Méthode de la classe OptunaOptimizer

    def _cache_features(self, indices, X_lgbm, X_lstm, y=None):
        """
        Met en cache les features pour un ensemble d'indices donné.

        Args:
            indices: Indices des échantillons
            X_lgbm: Features LGBM
            X_lstm: Features LSTM
            y: Labels (optionnel)

        Returns:
            str: Clé de cache
        """
        if not hasattr(self, '_advanced_data_cache'):
            self._initialize_advanced_data_cache()

        # Vérifier si le cache est activé
        if not getattr(self.config, 'use_advanced_cache', True):
            return None

        # Générer une clé de cache
        cache_key = self._get_cache_key(indices=indices)

        # Mettre en cache les features
        self._advanced_data_cache['feature_cache'][cache_key] = (X_lgbm, X_lstm, y)
        logger.warning(f"Features mises en cache pour {len(indices)} échantillons (clé: {cache_key[:8]}...)")

        # Nettoyer le cache si nécessaire
        self._cleanup_cache_if_needed()

        return cache_key