# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 5144 à 5170
# Type: Méthode de la classe OptunaOptimizer

    def _convert_indices_to_sequences(self, sequences, indices):
        """
        Convertit des indices en séquences complètes.

        Args:
            sequences: Liste des séquences d'origine
            indices: Indices des séquences à extraire

        Returns:
            List: Liste des séquences correspondant aux indices
        """
        # Vérifier que les indices sont valides
        if indices is None:
            logger.warning("Indices None fournis à _convert_indices_to_sequences, retour de toutes les séquences")
            return sequences

        # Convertir les indices en liste si nécessaire
        if isinstance(indices, np.ndarray):
            indices = indices.tolist()

        # Vérifier que tous les indices sont dans les limites
        valid_indices = [i for i in indices if 0 <= i < len(sequences)]
        if len(valid_indices) < len(indices):
            logger.warning(f"{len(indices) - len(valid_indices)} indices hors limites ignorés")

        # Extraire les séquences correspondant aux indices
        return [sequences[i] for i in valid_indices]