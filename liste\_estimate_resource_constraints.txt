# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 12446 à 12486
# Type: Méthode de la classe OptunaOptimizer

    def _estimate_resource_constraints(self):
        """
        Estime les contraintes de ressources en fonction des ressources disponibles.

        Returns:
            float: Indicateur de contraintes de ressources (1.0 = contraintes moyennes)
        """
        import psutil
        import torch

        try:
            # Récupérer les informations sur les ressources
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            cpu_cores = psutil.cpu_count(logical=False) or psutil.cpu_count()
            has_gpu = torch.cuda.is_available()

            # Calculer les contraintes de ressources
            # Plus la valeur est élevée, plus les ressources sont limitées
            memory_constraint = 1.0
            if available_memory_gb < 4:
                memory_constraint = 2.0
            elif available_memory_gb > 16:
                memory_constraint = 0.5

            cpu_constraint = 1.0
            if cpu_cores < 4:
                cpu_constraint = 2.0
            elif cpu_cores > 8:
                cpu_constraint = 0.5

            gpu_constraint = 1.0 if has_gpu else 2.0

            # Combiner les contraintes
            resource_constraint = (memory_constraint + cpu_constraint + gpu_constraint) / 3

            logger.info(f"Contraintes de ressources estimées: {resource_constraint:.2f}")
            return resource_constraint

        except Exception as e:
            logger.error(f"Erreur lors de l'estimation des contraintes de ressources: {e}")
            return 1.0