# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 1273 à 1430
# Type: Méthode de la classe OptunaOptimizer

    def _evaluate_config_robustness(self, config, n_runs=5, subset_percentage=0.8, noise_level=0.05,
                                   use_cross_validation=True, n_folds=3, random_state=42, **kwargs):
        """
        Évalue la robustesse d'une configuration en la testant dans différentes conditions.
        Cette méthode permet de vérifier si une configuration est stable face à des variations
        dans les données ou des perturbations aléatoires.

        Args:
            config: Configuration à évaluer
            n_runs: Nombre d'exécutions pour l'évaluation de robustesse
            subset_percentage: Pourcentage des données à utiliser pour chaque exécution
            noise_level: Niveau de bruit à ajouter aux paramètres (en pourcentage)
            use_cross_validation: Utiliser la validation croisée pour chaque exécution
            n_folds: Nombre de plis pour la validation croisée
            random_state: Graine aléatoire pour la reproductibilité
            **kwargs: Arguments supplémentaires pour l'évaluation

        Returns:
            Tuple[float, Dict]: Score moyen de robustesse et métriques détaillées
        """
        import numpy as np
        import random
        import copy
        import time

        # Définir la graine aléatoire pour la reproductibilité
        np.random.seed(random_state)
        random.seed(random_state)

        # Charger les données prétraitées
        if not hasattr(self, '_preprocessed_data') or 'all_sequences' not in self._preprocessed_data:
            logger.warning("Données prétraitées non disponibles pour l'évaluation de robustesse")
            return 0.0, {'error': 'Données prétraitées non disponibles'}

        all_sequences = self._preprocessed_data['all_sequences']
        total_sequences = len(all_sequences)

        # Vérifier qu'il y a suffisamment de données
        if total_sequences < 100:
            logger.warning(f"Pas assez de données pour l'évaluation de robustesse (minimum 100 requis, {total_sequences} disponibles)")
            return 0.0, {'error': 'Pas assez de données'}

        # Initialiser les résultats
        all_scores = []
        all_metrics = []
        start_time = time.time()

        logger.warning(f"Évaluation de la robustesse avec {n_runs} exécutions (subset={subset_percentage*100:.0f}%, noise={noise_level*100:.1f}%)")

        for run in range(n_runs):
            run_start_time = time.time()
            logger.warning(f"Exécution {run+1}/{n_runs}")

            # 1. Créer un sous-ensemble aléatoire des données
            subset_size = int(total_sequences * subset_percentage)
            subset_indices = random.sample(range(total_sequences), subset_size)

            # 2. Ajouter du bruit aux paramètres numériques
            noisy_config = copy.deepcopy(config)
            for key, value in noisy_config.items():
                if isinstance(value, (int, float)):
                    # Ajouter un bruit gaussien
                    noise = np.random.normal(0, noise_level * abs(value) if value != 0 else noise_level)

                    if isinstance(value, int):
                        # Arrondir à l'entier le plus proche
                        noisy_value = int(round(value + noise))
                        # Assurer que la valeur reste positive si l'originale l'était
                        if value > 0 and noisy_value <= 0:
                            noisy_value = 1
                    else:
                        noisy_value = value + noise
                        # Assurer que la valeur reste positive si l'originale l'était
                        if value > 0 and noisy_value <= 0:
                            noisy_value = value * 0.1  # 10% de la valeur originale

                    noisy_config[key] = noisy_value

            # 3. Évaluer la configuration bruitée
            if use_cross_validation:
                # Utiliser la validation croisée pour une évaluation plus robuste
                score, metrics = self._evaluate_with_cross_validation(
                    noisy_config,
                    subset_indices=subset_indices,
                    n_folds=n_folds,
                    cv_type='stratified',
                    random_state=random_state + run,  # Différente graine pour chaque exécution
                    **kwargs
                )
            else:
                # Diviser manuellement en ensembles d'entraînement et de validation
                split_idx = int(len(subset_indices) * 0.8)
                train_indices = subset_indices[:split_idx]
                val_indices = subset_indices[split_idx:]

                score, metrics = self._evaluate_config(
                    noisy_config,
                    subset_indices=train_indices,
                    validation_indices=val_indices,
                    **kwargs
                )

            # 4. Enregistrer les résultats
            all_scores.append(score)
            all_metrics.append(metrics)

            run_time = time.time() - run_start_time
            logger.warning(f"Exécution {run+1} terminée en {run_time:.2f}s, score: {score:.4f}")

        # Calculer les statistiques de robustesse
        if not all_scores:
            logger.warning("Aucun score valide obtenu lors de l'évaluation de robustesse")
            return 0.0, {'error': 'Aucun score valide'}

        # Calculer le score moyen et l'écart-type
        mean_score = np.mean(all_scores)
        std_score = np.std(all_scores)
        cv_score = std_score / mean_score if mean_score != 0 else float('inf')  # Coefficient de variation

        # Calculer le score de robustesse (plus il est bas, plus la configuration est robuste)
        # Formule: 1 - coefficient de variation normalisé (limité à [0, 1])
        robustness_score = 1.0 - min(1.0, cv_score)

        # Agréger les métriques
        robustness_metrics = {
            'mean_score': float(mean_score),
            'std_score': float(std_score),
            'cv_score': float(cv_score),
            'robustness_score': float(robustness_score),
            'min_score': float(min(all_scores)),
            'max_score': float(max(all_scores)),
            'score_range': float(max(all_scores) - min(all_scores)),
            'n_runs': n_runs,
            'subset_percentage': subset_percentage,
            'noise_level': noise_level,
            'use_cross_validation': use_cross_validation,
            'n_folds': n_folds if use_cross_validation else 0,
            'evaluation_time': time.time() - start_time
        }

        # Agréger les métriques de toutes les exécutions
        if all_metrics:
            # Identifier toutes les clés de métriques
            all_keys = set()
            for metrics in all_metrics:
                all_keys.update(metrics.keys())

            # Calculer la moyenne et l'écart-type pour chaque métrique
            for key in all_keys:
                values = [m.get(key, 0.0) for m in all_metrics]
                if values:
                    robustness_metrics[f'mean_{key}'] = float(np.mean(values))
                    if len(values) > 1:
                        robustness_metrics[f'std_{key}'] = float(np.std(values))

        logger.warning(f"Évaluation de robustesse terminée: score={robustness_score:.4f}, mean={mean_score:.4f}, std={std_score:.4f}")

        return robustness_score, robustness_metrics