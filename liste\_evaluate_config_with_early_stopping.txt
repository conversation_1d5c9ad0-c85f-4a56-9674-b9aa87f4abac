# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 10701 à 11068
# Type: Méthode de la classe OptunaOptimizer

    def _evaluate_config_with_early_stopping(self, config, **kwargs):
        """
        Évalue une configuration avec arrêt précoce basé sur les performances intermédiaires.
        Utilise un pruning adaptatif basé sur l'historique des essais pour une meilleure efficacité.

        Cette méthode permet d'économiser des ressources computationnelles en arrêtant
        l'évaluation des configurations peu prometteuses avant d'utiliser toutes les données.

        Args:
            config: Configuration à évaluer
            **kwargs: Arguments supplémentaires pour l'évaluation
                - is_viability_check: Indique si c'est une vérification de viabilité
                - force_lstm_training: Force l'entraînement LSTM même si is_viability_check est True
                - force_markov_training: Force l'utilisation de Markov même si is_viability_check est True
                - subset_indices: Indices à utiliser pour l'évaluation
                - enable_cv: Activer la validation croisée
                - n_folds: Nombre de plis pour la validation croisée
                - trial: Essai Optuna actuel (pour le pruning)

        Returns:
            Tuple[float, Dict]: Score et dictionnaire de métriques
        """
        # Récupérer l'essai Optuna actuel pour le pruning
        trial = kwargs.get('trial', None)

        # Initialiser l'historique des performances si nécessaire
        if not hasattr(self, 'performance_history'):
            self.performance_history = {
                'segment_performances': {},  # Performances par segment
                'best_scores': [],           # Meilleurs scores observés
                'trial_count': 0             # Nombre d'essais évalués
            }

        # Incrémenter le compteur d'essais
        self.performance_history['trial_count'] += 1
        trial_count = self.performance_history['trial_count']

        # Définir des seuils d'arrêt précoce de base à partir de la configuration
        base_early_stop_thresholds = {
            'min_non_wait_accuracy': getattr(config, 'min_non_wait_accuracy', 0.30),  # Arrêter si précision NON-WAIT < seuil
            'min_wait_accuracy': getattr(config, 'min_wait_accuracy', 0.15),      # Arrêter si précision WAIT < seuil
            'min_recommendation_rate': getattr(config, 'min_recommendation_rate', 0.02), # Arrêter si taux de recommandation < seuil
            'max_wait_ratio': getattr(config, 'max_wait_ratio', 0.998),        # Arrêter si ratio WAIT > seuil
            'min_wait_ratio': getattr(config, 'min_wait_ratio', 0.002)         # Arrêter si ratio WAIT < seuil
        }

        # Utiliser les segments optimisés s'ils existent, sinon calculer des segments par défaut
        subset_indices = kwargs.get('subset_indices', self.train_indices)

        # Vérifier que les indices sont valides et non vides
        if subset_indices is None or len(subset_indices) == 0:
            logger.error("Erreur: subset_indices est None ou vide dans _evaluate_config_with_early_stopping")
            # Retourner une valeur par défaut pour éviter les erreurs
            return 0.0, {'early_stopping': True, 'error': 'Indices vides'}

        total_data = len(subset_indices)

        if hasattr(self, 'evaluation_segments') and self.evaluation_segments:
            segments = self.evaluation_segments
            logger.info(f"Utilisation des segments optimisés: {segments}")
        else:
            segments = [
                max(1, min(total_data, int(0.25 * total_data))),  # 25% des données chargées (au moins 1, au plus total_data)
                max(2, min(total_data, int(0.50 * total_data))),  # 50% des données chargées (au moins 2, au plus total_data)
                max(3, min(total_data, int(0.75 * total_data))),  # 75% des données chargées (au moins 3, au plus total_data)
                total_data                                        # 100% des données chargées
            ]
            logger.info(f"Utilisation des segments par défaut: {segments}")

        # Évaluer progressivement sur des segments croissants
        for i, segment_size in enumerate(segments):
            # Calculer le pourcentage de progression
            segment_percentage = segment_size / total_data if total_data > 0 else 0.0
            segment_key = f"segment_{i+1}"

            # Ajuster les seuils d'arrêt précoce en fonction de l'historique des performances
            # et de la progression de l'évaluation
            early_stop_thresholds = self._calculate_adaptive_pruning_thresholds(
                base_early_stop_thresholds,
                segment_key,
                segment_percentage,
                trial_count
            )

            # Sélectionner un sous-ensemble aléatoire des données
            subset = kwargs.get('subset_indices', self.train_indices)

            # S'assurer que segment_size ne dépasse pas la taille de subset
            actual_segment_size = min(segment_size, len(subset))

            if actual_segment_size < segment_size:
                logger.warning(f"Segment size ajusté de {segment_size} à {actual_segment_size} car subset_indices contient seulement {len(subset)} éléments")
                logger.warning(f"Note: Ce segment sera utilisé pour l'arrêt précoce, mais l'évaluation finale se fera sur {len(subset)} séquences")
                segment_size = actual_segment_size

            # Utiliser tous les indices si segment_size est égal à la taille de subset
            if segment_size == len(subset):
                segment_indices = np.array(subset)
            else:
                segment_indices = np.random.choice(
                    subset,
                    size=segment_size,
                    replace=False
                )

            # Vérifier si la validation croisée est activée
            if kwargs.get('enable_cv', False):
                # Utiliser la validation croisée temporelle
                score, metrics = self._perform_temporal_cross_validation(
                    config,
                    segment_indices,
                    n_folds=kwargs.get('n_folds', 5),
                    **kwargs
                )
                result = {
                    'score': score,
                    'non_wait_accuracy': metrics.get('precision_non_wait', 0.0),
                    'wait_accuracy': metrics.get('precision_wait', 0.0),
                    'wait_ratio': metrics.get('wait_ratio', 0.0),
                    'recommendation_rate': metrics.get('recommendation_rate', 0.0),
                    'metrics': metrics
                }
            else:
                # Évaluer sur ce segment
                result = self._evaluate_config_segment(config, segment_indices, **kwargs)

            # Vérifier si result est None ou si ses clés sont None
            if result is None:
                logger.error("Résultat None retourné par _evaluate_config_segment")
                # Retourner une valeur par défaut
                return 0.0, {'error': 'Résultat None'}

            # Extraire les métriques avec des valeurs par défaut sécurisées
            non_wait_accuracy = result.get('non_wait_accuracy', 0.0)
            wait_accuracy = result.get('wait_accuracy', 0.0)
            recommendation_rate = result.get('recommendation_rate', 0.0)
            wait_ratio = result.get('wait_ratio', 0.0)
            score = result.get('score', 0.0)

            # Enregistrer les performances pour ce segment
            if segment_key not in self.performance_history['segment_performances']:
                self.performance_history['segment_performances'][segment_key] = {
                    'non_wait_accuracies': [],
                    'wait_accuracies': [],
                    'recommendation_rates': [],
                    'wait_ratios': [],
                    'scores': []
                }

            # Limiter la taille de l'historique pour éviter une consommation excessive de mémoire
            max_history_size = 50
            history = self.performance_history['segment_performances'][segment_key]

            if len(history['non_wait_accuracies']) >= max_history_size:
                history['non_wait_accuracies'].pop(0)
                history['wait_accuracies'].pop(0)
                history['recommendation_rates'].pop(0)
                history['wait_ratios'].pop(0)
                history['scores'].pop(0)

            history['non_wait_accuracies'].append(non_wait_accuracy)
            history['wait_accuracies'].append(wait_accuracy)
            history['recommendation_rates'].append(recommendation_rate)
            history['wait_ratios'].append(wait_ratio)
            history['scores'].append(score)

            # Utiliser le pruning Optuna si disponible
            if trial is not None and hasattr(trial, 'report') and hasattr(trial, 'should_prune'):
                # Rapporter la valeur intermédiaire à Optuna
                trial.report(score, i)

                # Vérifier si l'essai doit être arrêté selon Optuna
                if trial.should_prune():
                    logger.warning(f"Pruning Optuna activé pour l'essai {trial.number} au segment {i+1}/{len(segments)}")

                    # Créer un dictionnaire de métriques pour le retour
                    enhanced_metrics = result.get('metrics', {}).copy()
                    enhanced_metrics.update({
                        'early_stopping': True,
                        'pruned_by_optuna': True,
                        'segment_size': segment_size,
                        'total_data': total_data,
                        'segment_ratio': segment_percentage,
                        'non_wait_accuracy': non_wait_accuracy,
                        'wait_accuracy': wait_accuracy,
                        'wait_ratio': wait_ratio,
                        'recommendation_rate': recommendation_rate
                    })

                    # Lever l'exception de pruning d'Optuna
                    raise optuna.exceptions.TrialPruned()

            # Vérifier les critères d'arrêt précoce
            if non_wait_accuracy < early_stop_thresholds['min_non_wait_accuracy'] or \
               wait_accuracy < early_stop_thresholds['min_wait_accuracy'] or \
               recommendation_rate < early_stop_thresholds['min_recommendation_rate'] or \
               wait_ratio > early_stop_thresholds['max_wait_ratio'] or \
               wait_ratio < early_stop_thresholds['min_wait_ratio']:

                # Ajouter un malus au score pour les configurations arrêtées tôt
                # Vérifier que total_data n'est pas zéro pour éviter la division par zéro
                if total_data > 0:
                    # Malus progressif: plus l'arrêt est précoce, plus le malus est important
                    malus_factor = 0.8 - (0.2 * (1 - segment_percentage))
                    result['score'] *= segment_percentage * malus_factor
                    segment_ratio = segment_percentage
                else:
                    result['score'] *= 0.1  # Malus important si total_data est zéro
                    segment_ratio = 0.0
                    logger.error("total_data est zéro dans _evaluate_config_with_early_stopping")

                # Journaliser l'arrêt précoce
                if total_data > 0:
                    # Calculer le pourcentage correctement (limité à 100%)
                    percentage = min(100.0, (segment_size / total_data) * 100)
                    logger.warning(f"Arrêt précoce après évaluation sur {segment_size}/{total_data} séquences ({percentage:.1f}%)")
                else:
                    logger.warning(f"Arrêt précoce après évaluation sur {segment_size} séquences (total_data=0)")

                # Récupérer les compteurs de WAIT et NON-WAIT pour ce segment
                wait_count_segment = result.get('metrics', {}).get('wait_count', 0)
                non_wait_count_segment = result.get('metrics', {}).get('non_wait_count', 0)
                total_count_segment = wait_count_segment + non_wait_count_segment

                # Calculer le ratio WAIT pour ce segment spécifique
                segment_wait_ratio = wait_count_segment / total_count_segment if total_count_segment > 0 else 0.0

                logger.warning(f"Métriques: NON-WAIT={non_wait_accuracy:.4f}, WAIT={wait_accuracy:.4f}, Ratio WAIT={segment_wait_ratio:.4f} ({wait_count_segment}/{total_count_segment})")
                logger.warning(f"Seuils: min_non_wait={early_stop_thresholds['min_non_wait_accuracy']:.4f}, min_wait={early_stop_thresholds['min_wait_accuracy']:.4f}")

                # Retourner un tuple (score, metrics) pour compatibilité avec objective_precision
                # Mais inclure toutes les métriques détaillées dans le dictionnaire metrics
                enhanced_metrics = result.get('metrics', {}).copy()

                # Calculer segment_ratio de manière sécurisée (limité à 100%)
                segment_ratio = min(1.0, segment_size / total_data) if total_data > 0 else 0.0

                enhanced_metrics.update({
                    'early_stopping': True,
                    'segment_size': segment_size,
                    'total_data': total_data,
                    'segment_ratio': segment_ratio,
                    'non_wait_accuracy': non_wait_accuracy,
                    'wait_accuracy': wait_accuracy,
                    'wait_ratio': wait_ratio,
                    'recommendation_rate': recommendation_rate,
                    'thresholds': early_stop_thresholds
                })

                # Vérifier si score est None avant de le retourner
                score = result.get('score')
                if score is None:
                    logger.error("Score None dans le résultat d'arrêt précoce")
                    return 0.0, enhanced_metrics

                return score, enhanced_metrics

        # Si on arrive ici, c'est que la configuration a passé tous les segments
        # Évaluer sur 100% des données pour le score final
        if kwargs.get('enable_cv', False):
            # Utiliser la validation croisée temporelle pour l'évaluation finale
            score, metrics = self._perform_temporal_cross_validation(
                config,
                kwargs.get('subset_indices', self.train_indices),
                n_folds=kwargs.get('n_folds', 5),
                **kwargs
            )
            result = {
                'score': score,
                'non_wait_accuracy': metrics.get('precision_non_wait', 0.0),
                'wait_accuracy': metrics.get('precision_wait', 0.0),
                'wait_ratio': metrics.get('wait_ratio', 0.0),
                'recommendation_rate': metrics.get('recommendation_rate', 0.0),
                'metrics': metrics
            }

            # Ajouter des informations sur la validation croisée dans les métriques
            result['metrics']['cv_enabled'] = True
            result['metrics']['cv_folds'] = kwargs.get('n_folds', 3)

            logger.warning(f"Évaluation avec validation croisée temporelle: score={score:.4f}, "
                          f"précision NON-WAIT={metrics.get('precision_non_wait', 0.0):.4f}, "
                          f"précision WAIT={metrics.get('precision_wait', 0.0):.4f}")
        else:
            # Évaluer sur ce segment sans validation croisée
            result = self._evaluate_config_segment(config, kwargs.get('subset_indices', self.train_indices), **kwargs)

        # Vérifier si result est None
        if result is None:
            logger.error("Résultat None retourné par _evaluate_config_segment pour l'évaluation finale")
            # Retourner une valeur par défaut
            return 0.0, {'error': 'Résultat final None'}

        # Extraire le score pour l'historique
        score = result.get('score')
        if score is not None:
            # Mettre à jour la liste des meilleurs scores
            self.performance_history['best_scores'].append(score)
            # Garder seulement les 20 meilleurs scores
            self.performance_history['best_scores'] = sorted(self.performance_history['best_scores'], reverse=True)[:20]

        # Vérifier si metrics est None
        if result.get('metrics') is None:
            logger.error("Métriques None dans le résultat final")
            # Créer un dictionnaire de métriques par défaut
            enhanced_metrics = {
                'early_stopping': False,
                'segment_size': total_data,
                'total_data': total_data,
                'segment_ratio': 1.0,
                'non_wait_accuracy': result.get('non_wait_accuracy', 0.0),
                'wait_accuracy': result.get('wait_accuracy', 0.0),
                'wait_ratio': result.get('wait_ratio', 0.0),
                'recommendation_rate': result.get('recommendation_rate', 0.0),
                'error': 'Métriques None dans le résultat final'
            }
        else:
            # Retourner un tuple (score, metrics) pour compatibilité avec objective_precision
            # Mais inclure toutes les métriques détaillées dans le dictionnaire metrics
            enhanced_metrics = result['metrics'].copy()

            # Récupérer les métriques importantes
            wait_ratio = result.get('wait_ratio', 0.0)
            non_wait_accuracy = result.get('non_wait_accuracy', 0.0)
            wait_accuracy = result.get('wait_accuracy', 0.0)
            recommendation_rate = result.get('recommendation_rate', 0.0)

            # Vérifier si un ratio WAIT optimisé est défini dans la configuration
            optimized_wait_ratio = getattr(config, 'target_wait_ratio', None)

            # Si un ratio WAIT optimisé est défini, vérifier l'écart avec le ratio réel
            if optimized_wait_ratio is not None:
                wait_ratio_gap = abs(wait_ratio - optimized_wait_ratio)

                # Journaliser l'écart
                logger.warning(f"Ratio WAIT réel: {wait_ratio:.4f}, Ratio WAIT optimisé: {optimized_wait_ratio:.4f}, Écart: {wait_ratio_gap:.4f}")

                # Ajouter l'écart aux métriques
                enhanced_metrics['wait_ratio_gap'] = wait_ratio_gap

                # Appliquer une pénalité au score si l'écart est trop important
                if wait_ratio_gap > 0.1:
                    logger.warning(f"Écart important entre le ratio WAIT réel ({wait_ratio:.4f}) et le ratio WAIT optimisé ({optimized_wait_ratio:.4f})")
                    # Réduire le score proportionnellement à l'écart (max 50% de pénalité)
                    penalty_factor = 1.0 - min(0.5, wait_ratio_gap)
                    score *= penalty_factor
                    logger.warning(f"Pénalité appliquée au score: facteur {penalty_factor:.4f}")

                    # Ajouter la pénalité aux métriques
                    enhanced_metrics['wait_ratio_penalty'] = 1.0 - penalty_factor

            enhanced_metrics.update({
                'early_stopping': False,
                'segment_size': total_data,
                'total_data': total_data,
                'segment_ratio': 1.0,
                'non_wait_accuracy': non_wait_accuracy,
                'wait_accuracy': wait_accuracy,
                'wait_ratio': wait_ratio,
                'recommendation_rate': recommendation_rate
            })

        # Vérifier si score est None avant de le retourner
        if score is None:
            logger.error("Score None dans le résultat final")
            return 0.0, enhanced_metrics

        return score, enhanced_metrics