# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 2021 à 2187
# Type: Méthode de la classe OptunaOptimizer

    def _export_study_to_dataframe(self, study, include_system_attrs=False, include_internal_params=False,
                                  include_datetime=True, output_file=None, format='csv'):
        """
        Exporte les résultats d'une étude Optuna vers un DataFrame pandas.
        Cette méthode permet de faciliter l'analyse des résultats d'optimisation
        en les convertissant dans un format tabulaire.

        Args:
            study: Étude Optuna terminée
            include_system_attrs: Inclure les attributs système dans l'export
            include_internal_params: Inclure les paramètres internes dans l'export
            include_datetime: Inclure les horodatages dans l'export
            output_file: Chemin du fichier de sortie (si None, retourne uniquement le DataFrame)
            format: Format de sortie ('csv', 'excel', 'json', 'pickle')

        Returns:
            pandas.DataFrame: DataFrame contenant les résultats de l'étude
        """
        import pandas as pd
        import numpy as np
        import os
        import optuna
        import json
        from datetime import datetime

        # Vérifier que l'étude contient des essais
        if len(study.trials) == 0:
            logger.warning("Aucun essai à exporter")
            return pd.DataFrame()

        # Utiliser la méthode intégrée d'Optuna pour obtenir le DataFrame de base
        try:
            df = study.trials_dataframe(include_system_attrs=include_system_attrs)
        except Exception as e:
            logger.warning(f"Erreur lors de la conversion en DataFrame: {e}")
            # Créer manuellement le DataFrame
            data = []
            for trial in study.trials:
                row = {
                    'number': trial.number,
                    'value': trial.value,
                    'state': trial.state.name,
                }

                # Ajouter les paramètres
                for param_name, param_value in trial.params.items():
                    row[f'params_{param_name}'] = param_value

                # Ajouter les attributs utilisateur
                for attr_name, attr_value in trial.user_attrs.items():
                    row[f'user_attrs_{attr_name}'] = attr_value

                # Ajouter les attributs système si demandé
                if include_system_attrs:
                    for attr_name, attr_value in trial.system_attrs.items():
                        row[f'system_attrs_{attr_name}'] = attr_value

                data.append(row)

            df = pd.DataFrame(data)

        # Ajouter des colonnes supplémentaires
        if include_datetime and 'datetime_start' not in df.columns:
            # Ajouter les horodatages
            datetime_start = []
            datetime_complete = []
            duration_seconds = []

            for trial in study.trials:
                # Horodatage de début
                if hasattr(trial, 'datetime_start') and trial.datetime_start:
                    start = trial.datetime_start
                    datetime_start.append(start)
                else:
                    datetime_start.append(None)

                # Horodatage de fin
                if hasattr(trial, 'datetime_complete') and trial.datetime_complete:
                    complete = trial.datetime_complete
                    datetime_complete.append(complete)

                    # Calculer la durée
                    if start:
                        duration = (complete - start).total_seconds()
                        duration_seconds.append(duration)
                    else:
                        duration_seconds.append(None)
                else:
                    datetime_complete.append(None)
                    duration_seconds.append(None)

            # Ajouter les colonnes au DataFrame
            df['datetime_start'] = datetime_start
            df['datetime_complete'] = datetime_complete
            df['duration_seconds'] = duration_seconds

        # Ajouter une colonne indiquant si l'essai est le meilleur
        df['is_best'] = df['number'] == study.best_trial.number

        # Ajouter une colonne avec le rang de l'essai
        if 'value' in df.columns:
            if study.direction == optuna.study.StudyDirection.MINIMIZE:
                df['rank'] = df['value'].rank(method='min')
            else:
                df['rank'] = (-df['value']).rank(method='min')

        # Filtrer les paramètres internes si demandé
        if not include_internal_params:
            # Identifier les colonnes de paramètres internes (commençant par '_')
            internal_param_cols = [col for col in df.columns if col.startswith('params__')]
            if internal_param_cols:
                df = df.drop(columns=internal_param_cols)

        # Ajouter des métadonnées
        df.attrs['study_name'] = study.study_name
        df.attrs['direction'] = study.direction.name
        df.attrs['best_value'] = study.best_value
        df.attrs['best_trial'] = study.best_trial.number
        df.attrs['n_trials'] = len(study.trials)
        df.attrs['export_datetime'] = datetime.now().isoformat()

        # Sauvegarder le DataFrame si un fichier de sortie est spécifié
        if output_file:
            # Créer le répertoire parent si nécessaire
            os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)

            # Sauvegarder selon le format demandé
            if format.lower() == 'csv':
                df.to_csv(output_file, index=False)
                logger.warning(f"DataFrame sauvegardé au format CSV: {output_file}")

            elif format.lower() == 'excel':
                df.to_excel(output_file, index=False)
                logger.warning(f"DataFrame sauvegardé au format Excel: {output_file}")

            elif format.lower() == 'json':
                # Convertir les types non sérialisables
                df_json = df.copy()
                for col in df_json.columns:
                    if df_json[col].dtype == 'datetime64[ns]':
                        df_json[col] = df_json[col].astype(str)

                # Ajouter les métadonnées
                metadata = {k: str(v) if not isinstance(v, (int, float, bool, str)) else v
                           for k, v in df.attrs.items()}

                # Créer un dictionnaire avec les données et les métadonnées
                data_dict = {
                    'metadata': metadata,
                    'data': df_json.to_dict(orient='records')
                }

                # Sauvegarder au format JSON
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data_dict, f, indent=2)

                logger.warning(f"DataFrame sauvegardé au format JSON: {output_file}")

            elif format.lower() == 'pickle':
                df.to_pickle(output_file)
                logger.warning(f"DataFrame sauvegardé au format Pickle: {output_file}")

            else:
                logger.warning(f"Format non reconnu: {format}. Sauvegarde au format CSV par défaut.")
                df.to_csv(output_file, index=False)

        return df