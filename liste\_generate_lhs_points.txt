# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 5520 à 5552
# Type: Méthode de la classe OptunaOptimizer

    def _generate_lhs_points(self, n_dims, n_samples):
        """
        Génère des points selon la méthode Latin Hypercube Sampling.

        Args:
            n_dims: Nombre de dimensions
            n_samples: Nombre d'échantillons à générer

        Returns:
            numpy.ndarray: Points LHS générés
        """
        import numpy as np

        # Créer une grille régulière pour chaque dimension
        result = np.zeros((n_samples, n_dims))

        for i in range(n_dims):
            # Créer des segments équidistants
            segments = np.linspace(0, 1, n_samples + 1)

            # Prendre un point aléatoire dans chaque segment
            points = np.random.uniform(segments[:-1], segments[1:])

            # Mélanger les points pour cette dimension
            np.random.shuffle(points)

            # Ajouter à la matrice de résultats
            result[:, i] = points

        # Optimiser la distribution des points
        result = self._transform_lhs_points(result)

        return result