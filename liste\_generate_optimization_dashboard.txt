# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 2189 à 2535
# Type: Méthode de la classe OptunaOptimizer

    def _generate_optimization_dashboard(self, study, output_dir=None, include_plots=True,
                                        include_trials_table=True, include_best_params=True,
                                        include_importance=True, include_correlations=True):
        """
        Génère un tableau de bord interactif pour visualiser les résultats d'optimisation.
        Cette méthode crée une page HTML interactive avec des graphiques, tableaux et analyses.

        Args:
            study: Étude Optuna terminée
            output_dir: Répertoire de sortie (si None, utilise un répertoire temporaire)
            include_plots: Inclure des graphiques interactifs
            include_trials_table: Inclure un tableau des essais
            include_best_params: Inclure les meilleurs paramètres
            include_importance: Inclure l'importance des paramètres
            include_correlations: Inclure les corrélations entre paramètres

        Returns:
            str: Chemin du fichier HTML généré
        """
        import os
        import tempfile
        import json
        import pandas as pd
        import numpy as np
        import optuna
        from datetime import datetime
        import shutil

        # Créer le répertoire de sortie si nécessaire
        if output_dir is None:
            output_dir = os.path.join(tempfile.gettempdir(), f"optuna_dashboard_{int(datetime.now().timestamp())}")

        os.makedirs(output_dir, exist_ok=True)

        # Créer un répertoire pour les ressources
        assets_dir = os.path.join(output_dir, "assets")
        os.makedirs(assets_dir, exist_ok=True)

        # Exporter les données de l'étude
        df = self._export_study_to_dataframe(study, include_datetime=True)

        # Créer le fichier de données JSON
        data_file = os.path.join(assets_dir, "study_data.json")

        # Convertir les types non sérialisables
        df_json = df.copy()
        for col in df_json.columns:
            if df_json[col].dtype == 'datetime64[ns]':
                df_json[col] = df_json[col].astype(str)

        # Créer un dictionnaire avec les métadonnées et les données
        data_dict = {
            'metadata': {
                'study_name': study.study_name,
                'direction': study.direction.name,
                'best_value': float(study.best_value),
                'best_trial': int(study.best_trial.number),
                'n_trials': len(study.trials),
                'n_completed_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]),
                'export_datetime': datetime.now().isoformat()
            },
            'best_params': study.best_params,
            'trials': df_json.to_dict(orient='records')
        }

        # Calculer l'importance des paramètres si demandé
        if include_importance:
            try:
                importances = optuna.importance.get_param_importances(study)
                data_dict['importance'] = {k: float(v) for k, v in importances.items()}
            except Exception as e:
                logger.warning(f"Impossible de calculer l'importance des paramètres: {e}")

        # Calculer les corrélations entre paramètres si demandé
        if include_correlations:
            try:
                # Extraire les colonnes de paramètres
                param_cols = [col for col in df.columns if col.startswith('params_')]

                # Calculer la matrice de corrélation
                corr_matrix = df[param_cols + ['value']].corr()

                # Convertir en dictionnaire
                correlations = {}
                for col1 in corr_matrix.columns:
                    correlations[col1] = {}
                    for col2 in corr_matrix.columns:
                        val = corr_matrix.loc[col1, col2]
                        if not np.isnan(val):
                            correlations[col1][col2] = float(val)

                data_dict['correlations'] = correlations
            except Exception as e:
                logger.warning(f"Impossible de calculer les corrélations: {e}")

        # Sauvegarder les données au format JSON
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(data_dict, f, indent=2)

        # Générer des graphiques statiques si demandé
        if include_plots:
            plots_dir = os.path.join(assets_dir, "plots")
            os.makedirs(plots_dir, exist_ok=True)

            # Générer les graphiques
            try:
                plot_results = self._plot_optimization_history(
                    study,
                    plot_type='all',
                    output_file=os.path.join(plots_dir, "plot"),
                    show_plot=False,
                    figsize=(10, 6)
                )

                # Ajouter les chemins des graphiques aux données
                data_dict['plots'] = [{'name': p['name'], 'path': os.path.relpath(p['path'], output_dir).replace('\\', '/')}
                                     for p in plot_results.get('plots_generated', [])]

                # Mettre à jour le fichier JSON avec les chemins des graphiques
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(data_dict, f, indent=2)
            except Exception as e:
                logger.warning(f"Erreur lors de la génération des graphiques: {e}")

        # Générer le fichier HTML
        html_file = os.path.join(output_dir, "dashboard.html")

        # Créer le contenu HTML
        html_content = []
        html_content.append("<!DOCTYPE html>")
        html_content.append("<html lang='fr'>")
        html_content.append("<head>")
        html_content.append("    <meta charset='UTF-8'>")
        html_content.append("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>")
        html_content.append(f"    <title>Tableau de bord d'optimisation - {study.study_name}</title>")

        # Ajouter des styles CSS
        html_content.append("    <style>")
        html_content.append("        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }")
        html_content.append("        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }")
        html_content.append("        .header { background-color: #2c3e50; color: white; padding: 20px; margin-bottom: 20px; }")
        html_content.append("        .card { background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 20px; padding: 20px; }")
        html_content.append("        .card h2 { margin-top: 0; color: #2c3e50; }")
        html_content.append("        .grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }")
        html_content.append("        .metric { text-align: center; padding: 15px; }")
        html_content.append("        .metric .value { font-size: 24px; font-weight: bold; margin: 10px 0; }")
        html_content.append("        .metric .label { font-size: 14px; color: #666; }")
        html_content.append("        table { width: 100%; border-collapse: collapse; }")
        html_content.append("        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }")
        html_content.append("        th { background-color: #f2f2f2; }")
        html_content.append("        tr:hover { background-color: #f5f5f5; }")
        html_content.append("        .plot-container { margin-bottom: 20px; }")
        html_content.append("        .plot-container img { max-width: 100%; height: auto; border: 1px solid #ddd; }")
        html_content.append("        .tabs { display: flex; margin-bottom: 20px; }")
        html_content.append("        .tab { padding: 10px 20px; cursor: pointer; background-color: #f2f2f2; border: 1px solid #ddd; border-bottom: none; }")
        html_content.append("        .tab.active { background-color: white; }")
        html_content.append("        .tab-content { display: none; }")
        html_content.append("        .tab-content.active { display: block; }")
        html_content.append("        .good { color: green; }")
        html_content.append("        .bad { color: red; }")
        html_content.append("        .neutral { color: orange; }")
        html_content.append("    </style>")
        html_content.append("</head>")
        html_content.append("<body>")

        # En-tête
        html_content.append("    <div class='header'>")
        html_content.append(f"        <h1>Tableau de bord d'optimisation - {study.study_name}</h1>")
        html_content.append(f"        <p>Direction: {study.direction.name}, Essais: {len(study.trials)}, Meilleure valeur: {study.best_value:.4f}</p>")
        html_content.append("    </div>")

        html_content.append("    <div class='container'>")

        # Métriques principales
        html_content.append("        <div class='card'>")
        html_content.append("            <h2>Métriques principales</h2>")
        html_content.append("            <div class='grid'>")
        html_content.append("                <div class='metric'>")
        html_content.append("                    <div class='label'>Meilleure valeur</div>")
        html_content.append(f"                    <div class='value'>{study.best_value:.4f}</div>")
        html_content.append("                </div>")
        html_content.append("                <div class='metric'>")
        html_content.append("                    <div class='label'>Nombre d'essais</div>")
        html_content.append(f"                    <div class='value'>{len(study.trials)}</div>")
        html_content.append("                </div>")
        html_content.append("                <div class='metric'>")
        html_content.append("                    <div class='label'>Essais terminés</div>")
        completed_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        html_content.append(f"                    <div class='value'>{completed_trials}</div>")
        html_content.append("                </div>")
        html_content.append("                <div class='metric'>")
        html_content.append("                    <div class='label'>Meilleur essai</div>")
        html_content.append(f"                    <div class='value'>#{study.best_trial.number}</div>")
        html_content.append("                </div>")
        html_content.append("            </div>")
        html_content.append("        </div>")

        # Meilleurs paramètres
        if include_best_params:
            html_content.append("        <div class='card'>")
            html_content.append("            <h2>Meilleurs paramètres</h2>")
            html_content.append("            <table>")
            html_content.append("                <tr><th>Paramètre</th><th>Valeur</th></tr>")
            for param_name, param_value in study.best_params.items():
                html_content.append(f"                <tr><td>{param_name}</td><td>{param_value}</td></tr>")
            html_content.append("            </table>")
            html_content.append("        </div>")

        # Importance des paramètres
        if include_importance:
            try:
                importances = optuna.importance.get_param_importances(study)
                html_content.append("        <div class='card'>")
                html_content.append("            <h2>Importance des paramètres</h2>")
                html_content.append("            <table>")
                html_content.append("                <tr><th>Paramètre</th><th>Importance</th></tr>")
                for param_name, importance in sorted(importances.items(), key=lambda x: x[1], reverse=True):
                    html_content.append(f"                <tr><td>{param_name}</td><td>{importance:.4f}</td></tr>")
                html_content.append("            </table>")
                html_content.append("        </div>")
            except Exception as e:
                logger.warning(f"Impossible d'inclure l'importance des paramètres: {e}")

        # Graphiques
        if include_plots and 'plots' in data_dict:
            html_content.append("        <div class='card'>")
            html_content.append("            <h2>Graphiques</h2>")
            html_content.append("            <div class='tabs'>")
            for i, plot in enumerate(data_dict['plots']):
                active = ' active' if i == 0 else ''
                html_content.append(f"                <div class='tab{active}' onclick='showTab(\"plot{i}\")'>{plot['name']}</div>")
            html_content.append("            </div>")
            for i, plot in enumerate(data_dict['plots']):
                active = ' active' if i == 0 else ''
                html_content.append(f"            <div id='plot{i}' class='tab-content{active}'>")
                html_content.append(f"                <div class='plot-container'>")
                html_content.append(f"                    <img src='{plot['path']}' alt='{plot['name']}'>")
                html_content.append("                </div>")
                html_content.append("            </div>")
            html_content.append("        </div>")

        # Tableau des essais
        if include_trials_table:
            html_content.append("        <div class='card'>")
            html_content.append("            <h2>Tableau des essais</h2>")
            html_content.append("            <div style='overflow-x: auto;'>")
            html_content.append("                <table>")
            html_content.append("                    <tr>")
            html_content.append("                        <th>Essai</th>")
            html_content.append("                        <th>Valeur</th>")
            html_content.append("                        <th>État</th>")

            # Ajouter les en-têtes pour les paramètres
            param_names = set()
            for trial in study.trials:
                param_names.update(trial.params.keys())

            for param_name in sorted(param_names):
                html_content.append(f"                        <th>{param_name}</th>")

            html_content.append("                    </tr>")

            # Ajouter les lignes pour chaque essai
            for trial in sorted(study.trials, key=lambda t: t.number):
                is_best = trial.number == study.best_trial.number
                row_class = " class='best-trial'" if is_best else ""

                html_content.append(f"                    <tr{row_class}>")
                html_content.append(f"                        <td>{trial.number}</td>")

                # Valeur avec classe CSS selon la performance
                if trial.value is not None:
                    value_class = ""
                    if is_best:
                        value_class = " class='good'"
                    html_content.append(f"                        <td{value_class}>{trial.value:.4f}</td>")
                else:
                    html_content.append("                        <td>-</td>")

                # État
                state_class = ""
                if trial.state == optuna.trial.TrialState.COMPLETE:
                    state_class = " class='good'"
                elif trial.state == optuna.trial.TrialState.FAIL or trial.state == optuna.trial.TrialState.PRUNED:
                    state_class = " class='bad'"
                html_content.append(f"                        <td{state_class}>{trial.state.name}</td>")

                # Paramètres
                for param_name in sorted(param_names):
                    if param_name in trial.params:
                        html_content.append(f"                        <td>{trial.params[param_name]}</td>")
                    else:
                        html_content.append("                        <td>-</td>")

                html_content.append("                    </tr>")

            html_content.append("                </table>")
            html_content.append("            </div>")
            html_content.append("        </div>")

        # Pied de page
        html_content.append("        <div class='card'>")
        html_content.append("            <p style='text-align: center; color: #666;'>")
        html_content.append(f"                Généré le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} par OptunaOptimizer")
        html_content.append("            </p>")
        html_content.append("        </div>")

        html_content.append("    </div>")

        # JavaScript pour les onglets
        html_content.append("    <script>")
        html_content.append("        function showTab(tabId) {")
        html_content.append("            // Cacher tous les contenus d'onglets")
        html_content.append("            var tabContents = document.getElementsByClassName('tab-content');")
        html_content.append("            for (var i = 0; i < tabContents.length; i++) {")
        html_content.append("                tabContents[i].classList.remove('active');")
        html_content.append("            }")
        html_content.append("            ")
        html_content.append("            // Désactiver tous les onglets")
        html_content.append("            var tabs = document.getElementsByClassName('tab');")
        html_content.append("            for (var i = 0; i < tabs.length; i++) {")
        html_content.append("                tabs[i].classList.remove('active');")
        html_content.append("            }")
        html_content.append("            ")
        html_content.append("            // Activer l'onglet sélectionné")
        html_content.append("            document.getElementById(tabId).classList.add('active');")
        html_content.append("            ")
        html_content.append("            // Activer le bouton d'onglet correspondant")
        html_content.append("            var tabButtons = document.getElementsByClassName('tab');")
        html_content.append("            for (var i = 0; i < tabButtons.length; i++) {")
        html_content.append("                if (tabButtons[i].getAttribute('onclick').includes(tabId)) {")
        html_content.append("                    tabButtons[i].classList.add('active');")
        html_content.append("                }")
        html_content.append("            }")
        html_content.append("        }")
        html_content.append("    </script>")

        html_content.append("</body>")
        html_content.append("</html>")

        # Écrire le fichier HTML
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(html_content))

        logger.warning(f"Tableau de bord généré: {html_file}")

        return html_file