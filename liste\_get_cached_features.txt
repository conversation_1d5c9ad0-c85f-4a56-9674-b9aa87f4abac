# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 6243 à 6272
# Type: Méthode de la classe OptunaOptimizer

    def _get_cached_features(self, indices):
        """
        Récupère les features en cache pour un ensemble d'indices donné.

        Args:
            indices: Indices des échantillons

        Returns:
            tuple: Features (X_lgbm, X_lstm, y) ou None si non trouvées
        """
        if not hasattr(self, '_advanced_data_cache'):
            self._initialize_advanced_data_cache()

        # Vérifier si le cache est activé
        if not getattr(self.config, 'use_advanced_cache', True):
            self._advanced_data_cache['cache_misses'] += 1
            return None

        # Générer une clé de cache
        cache_key = self._get_cache_key(indices=indices)

        # Récupérer les features du cache
        if cache_key in self._advanced_data_cache['feature_cache']:
            self._advanced_data_cache['cache_hits'] += 1
            logger.warning(f"Utilisation des features en cache pour {len(indices)} échantillons (clé: {cache_key[:8]}...)")
            return self._advanced_data_cache['feature_cache'][cache_key]

        # Features non trouvées dans le cache
        self._advanced_data_cache['cache_misses'] += 1
        return None