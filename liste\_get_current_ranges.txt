# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 659 à 735
# Type: Méthode de la classe DynamicRangeAdjuster

    def _get_current_ranges(self) -> Dict[str, Tuple[str, float, float]]:
        """
        Extrait les plages actuelles depuis config.py.

        Returns:
            Dict: Les plages actuelles {param_name: (param_type, low, high)}
        """
        ranges = {}

        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 1. Format standard: 'param_name': ('type', low, high) sans options
            pattern1 = r"['\"]([^'\"]+)['\"]:\s*\(['\"]([^'\"]+)['\"],\s*([0-9.]+),\s*([0-9.]+)\)"
            matches1 = re.findall(pattern1, content)

            for match in matches1:
                param_name, param_type, low, high = match

                # Convertir en int si nécessaire
                if param_type == 'int':
                    ranges[param_name] = (param_type, int(float(low)), int(float(high)))
                else:
                    ranges[param_name] = (param_type, float(low), float(high))

            # 1b. Format avec options: 'param_name': ('type', low, high, {'log': True})
            pattern1b = r"['\"]([^'\"]+)['\"]:\s*\(['\"]([^'\"]+)['\"],\s*([0-9.]+),\s*([0-9.]+),\s*(\{[^}]*\})\)"
            matches1b = re.findall(pattern1b, content)

            for match in matches1b:
                param_name, param_type, low, high, options = match

                # Convertir en int si nécessaire
                if param_type == 'int':
                    ranges[param_name] = (param_type, int(float(low)), int(float(high)))
                else:
                    ranges[param_name] = (param_type, float(low), float(high))

            # 2. Format pour les paramètres catégoriels: 'param_name': ('categorical', [val1, val2, ...])
            pattern2 = r"['\"]([^'\"]+)['\"]:\s*\(['\"]categorical['\"],\s*\[(.*?)\]\)"
            matches2 = re.findall(pattern2, content)

            for match in matches2:
                param_name, categories_str = match

                # Analyser la liste des catégories
                try:
                    # Remplacer les valeurs True/False par leur équivalent Python
                    categories_str = categories_str.replace("'True'", "True").replace("'False'", "False")
                    categories_str = categories_str.replace('"True"', "True").replace('"False"', "False")

                    # Évaluer la liste des catégories
                    categories = eval(f"[{categories_str}]")
                    ranges[param_name] = ('categorical', categories)
                except Exception as e:
                    logger.error(f"Erreur lors de l'analyse des catégories pour {param_name}: {e}")

            # 3. Format pour les paramètres booléens: self.param_name = True/False
            pattern3 = r"self\.([a-zA-Z0-9_]+)\s*=\s*(True|False)"
            matches3 = re.findall(pattern3, content)

            for match in matches3:
                param_name, value = match

                # Ajouter seulement si le paramètre n'est pas déjà dans ranges
                if param_name not in ranges:
                    ranges[param_name] = ('categorical', [True, False])

            logger.info(f"Plages extraites de config.py: {len(ranges)} paramètres trouvés")

        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des plages depuis config.py: {e}")
            import traceback
            logger.error(traceback.format_exc())

        return ranges