# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 6021 à 6092
# Type: Méthode de la classe OptunaOptimizer

    def _initialize_advanced_data_cache(self):
        """
        Initialise le cache avancé pour les données prétraitées.
        Ce cache permet de stocker et réutiliser les données prétraitées entre les différentes phases d'optimisation.
        Implémente des stratégies avancées de gestion du cache pour optimiser l'utilisation de la mémoire.
        """
        import os
        import pickle
        import hashlib
        import time
        import numpy as np
        import gc

        # Forcer la collecte des objets non référencés avant d'initialiser le cache
        gc.collect()

        # Créer un dictionnaire pour stocker les données prétraitées avec une structure avancée
        if not hasattr(self, '_advanced_data_cache'):
            self._advanced_data_cache = {
                'preprocessed_data': {},  # Données prétraitées par taille d'échantillon
                'feature_cache': {},      # Features précalculées par ensemble d'indices
                'validation_cache': {},   # Résultats de validation par configuration
                'model_cache': {},        # Modèles entraînés par configuration
                'prediction_cache': {},   # Prédictions par modèle et ensemble de données
                'importance_cache': {},   # Importances des features par modèle
                'phase_data': {           # Données spécifiques à chaque phase
                    'phase0': {},
                    'phase1': {},
                    'phase2': {},
                    'phase3': {},
                    'markov': {}
                },
                'cache_hits': 0,          # Nombre de fois où le cache a été utilisé
                'cache_misses': 0,        # Nombre de fois où le cache n'a pas pu être utilisé
                'last_cleanup': time.time(),  # Dernière fois que le cache a été nettoyé
                'memory_usage': {},       # Utilisation de la mémoire par composant du cache
                'cache_config': {         # Configuration du cache
                    'max_size_mb': 1024,  # Taille maximale du cache en MB (1 GB par défaut)
                    'cleanup_interval': 300,  # Intervalle de nettoyage en secondes (5 minutes)
                    'max_items_per_category': 100,  # Nombre maximum d'éléments par catégorie
                    'ttl': 3600,  # Durée de vie des éléments en secondes (1 heure)
                    'compression_level': 1,  # Niveau de compression (0-9, 0=pas de compression)
                    'priority_phases': ['phase3', 'markov']  # Phases prioritaires à conserver en cache
                }
            }

            # Détecter la mémoire disponible pour ajuster la taille du cache
            try:
                import psutil
                memory = psutil.virtual_memory()
                available_gb = memory.available / (1024**3)

                # Ajuster la taille maximale du cache en fonction de la mémoire disponible
                if available_gb > 16:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 4096  # 4 GB
                    logger.warning(f"Grande mémoire détectée ({available_gb:.1f} GB), taille du cache augmentée à 4 GB")
                elif available_gb > 8:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 2048  # 2 GB
                    logger.warning(f"Mémoire moyenne détectée ({available_gb:.1f} GB), taille du cache augmentée à 2 GB")
                elif available_gb > 4:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 1024  # 1 GB
                    logger.warning(f"Mémoire limitée détectée ({available_gb:.1f} GB), taille du cache standard à 1 GB")
                else:
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 512  # 512 MB
                    logger.warning(f"Mémoire faible détectée ({available_gb:.1f} GB), taille du cache réduite à 512 MB")
            except:
                logger.warning("Impossible de détecter la mémoire disponible, utilisation des paramètres par défaut")

            logger.warning("Cache avancé initialisé avec gestion optimisée de la mémoire")

            # Planifier un premier nettoyage du cache
            self._schedule_cache_cleanup()