# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 3080 à 3328
# Type: Méthode de la classe OptunaOptimizer

    def _integrate_with_mlflow(self, study, model=None, experiment_name=None, run_name=None,
                             log_params=True, log_metrics=True, log_artifacts=True,
                             register_model=False, model_name=None):
        """
        Intègre les résultats d'optimisation avec MLflow pour le suivi des expériences.
        Cette méthode permet de journaliser les paramètres, métriques et artefacts
        dans MLflow pour faciliter la comparaison et la reproductibilité.

        Args:
            study: Étude Optuna terminée
            model: Modèle entraîné à journaliser (optionnel)
            experiment_name: Nom de l'expérience MLflow
            run_name: Nom de l'exécution MLflow
            log_params: Journaliser les paramètres
            log_metrics: Journaliser les métriques
            log_artifacts: Journaliser les artefacts
            register_model: Enregistrer le modèle dans le registre MLflow
            model_name: Nom du modèle dans le registre

        Returns:
            str: URI de l'exécution MLflow
        """
        try:
            import mlflow
            import tempfile
            import os
            import json
            import numpy as np
            import pandas as pd
            from datetime import datetime

            # Configurer l'expérience MLflow
            if experiment_name is None:
                experiment_name = f"OptunaOptimizer_{study.study_name}"

            mlflow.set_experiment(experiment_name)

            # Démarrer une nouvelle exécution
            with mlflow.start_run(run_name=run_name) as run:
                run_id = run.info.run_id

                # Journaliser les paramètres
                if log_params:
                    # Journaliser les meilleurs paramètres
                    for param_name, param_value in study.best_params.items():
                        mlflow.log_param(f"best_{param_name}", param_value)

                    # Journaliser les métadonnées de l'étude
                    mlflow.log_param("study_name", study.study_name)
                    mlflow.log_param("direction", study.direction.name)
                    mlflow.log_param("n_trials", len(study.trials))
                    mlflow.log_param("n_completed_trials", len([t for t in study.trials if t.state.name == "COMPLETE"]))
                    mlflow.log_param("best_trial_number", study.best_trial.number)

                    # Journaliser les paramètres de configuration de l'optimiseur
                    if hasattr(self, 'config'):
                        config_dict = {}
                        for key, value in vars(self.config).items():
                            if not key.startswith('_') and not callable(value):
                                # Convertir les valeurs non sérialisables en chaînes
                                if isinstance(value, (int, float, bool, str, list, dict)):
                                    config_dict[key] = value
                                else:
                                    config_dict[key] = str(value)

                        # Journaliser les paramètres de configuration
                        for key, value in config_dict.items():
                            if isinstance(value, (int, float, bool, str)):
                                mlflow.log_param(f"config_{key}", value)

                # Journaliser les métriques
                if log_metrics:
                    # Journaliser la meilleure valeur objective
                    mlflow.log_metric("best_value", study.best_value)

                    # Journaliser les métriques supplémentaires du meilleur essai
                    for key, value in study.best_trial.user_attrs.items():
                        if isinstance(value, (int, float)):
                            mlflow.log_metric(f"best_{key}", value)

                    # Calculer et journaliser des statistiques sur les essais
                    values = [t.value for t in study.trials if t.value is not None]
                    if values:
                        mlflow.log_metric("mean_value", np.mean(values))
                        mlflow.log_metric("median_value", np.median(values))
                        mlflow.log_metric("std_value", np.std(values))
                        mlflow.log_metric("min_value", np.min(values))
                        mlflow.log_metric("max_value", np.max(values))

                    # Calculer et journaliser l'importance des paramètres
                    try:
                        import optuna
                        importances = optuna.importance.get_param_importances(study)
                        for param_name, importance in importances.items():
                            mlflow.log_metric(f"importance_{param_name}", importance)
                    except Exception as e:
                        logger.warning(f"Impossible de calculer l'importance des paramètres: {e}")

                # Journaliser les artefacts
                if log_artifacts:
                    # Créer un répertoire temporaire pour les artefacts
                    with tempfile.TemporaryDirectory() as tmp_dir:
                        # Exporter l'étude au format JSON
                        study_info = {
                            "study_name": study.study_name,
                            "direction": study.direction.name,
                            "best_value": study.best_value,
                            "best_params": study.best_params,
                            "best_trial_number": study.best_trial.number,
                            "n_trials": len(study.trials),
                            "datetime": datetime.now().isoformat()
                        }

                        # Ajouter les informations sur tous les essais
                        trials_info = []
                        for trial in study.trials:
                            trial_info = {
                                "number": trial.number,
                                "state": trial.state.name,
                                "value": trial.value,
                                "params": trial.params,
                                "datetime_start": trial.datetime_start.isoformat() if trial.datetime_start else None,
                                "datetime_complete": trial.datetime_complete.isoformat() if trial.datetime_complete else None
                            }
                            trials_info.append(trial_info)

                        study_info["trials"] = trials_info

                        # Sauvegarder les informations de l'étude
                        study_file = os.path.join(tmp_dir, "study_info.json")
                        with open(study_file, "w") as f:
                            json.dump(study_info, f, indent=2)

                        # Journaliser le fichier JSON
                        mlflow.log_artifact(study_file)

                        # Exporter les données de l'étude au format CSV
                        try:
                            df = self._export_study_to_dataframe(study)
                            csv_file = os.path.join(tmp_dir, "study_data.csv")
                            df.to_csv(csv_file, index=False)
                            mlflow.log_artifact(csv_file)
                        except Exception as e:
                            logger.warning(f"Impossible d'exporter les données de l'étude: {e}")

                        # Générer et journaliser des visualisations
                        try:
                            # Générer des graphiques
                            plots_dir = os.path.join(tmp_dir, "plots")
                            os.makedirs(plots_dir, exist_ok=True)

                            plot_results = self._plot_optimization_history(
                                study,
                                plot_type='all',
                                output_file=os.path.join(plots_dir, "plot"),
                                show_plot=False
                            )

                            # Journaliser le répertoire des graphiques
                            if os.path.exists(plots_dir) and os.listdir(plots_dir):
                                mlflow.log_artifacts(plots_dir, "plots")
                        except Exception as e:
                            logger.warning(f"Impossible de générer les visualisations: {e}")

                        # Générer et journaliser un rapport d'évaluation
                        try:
                            report_file = os.path.join(tmp_dir, "evaluation_report.md")

                            # Créer un dictionnaire de résultats d'évaluation factice
                            evaluation_results = {
                                "score": study.best_value,
                                "best_trial": study.best_trial.number,
                                "n_trials": len(study.trials)
                            }

                            # Ajouter les métriques du meilleur essai
                            for key, value in study.best_trial.user_attrs.items():
                                if isinstance(value, (int, float, str, bool)):
                                    evaluation_results[key] = value

                            # Générer le rapport
                            report = self._generate_evaluation_report(
                                study.best_params,
                                evaluation_results,
                                output_format='markdown',
                                output_file=report_file
                            )

                            # Journaliser le rapport
                            if os.path.exists(report_file):
                                mlflow.log_artifact(report_file)
                        except Exception as e:
                            logger.warning(f"Impossible de générer le rapport d'évaluation: {e}")

                # Journaliser et enregistrer le modèle
                if model is not None:
                    try:
                        # Déterminer le type de modèle
                        model_type = type(model).__module__.split('.')[0]

                        if model_type == 'sklearn':
                            # Modèle scikit-learn
                            mlflow.sklearn.log_model(model, "model")

                            # Enregistrer le modèle dans le registre si demandé
                            if register_model and model_name:
                                mlflow.register_model(f"runs:/{run_id}/model", model_name)

                        elif model_type == 'torch':
                            # Modèle PyTorch
                            mlflow.pytorch.log_model(model, "model")

                            # Enregistrer le modèle dans le registre si demandé
                            if register_model and model_name:
                                mlflow.register_model(f"runs:/{run_id}/model", model_name)

                        elif model_type == 'tensorflow' or model_type == 'keras':
                            # Modèle TensorFlow/Keras
                            mlflow.tensorflow.log_model(model, "model")

                            # Enregistrer le modèle dans le registre si demandé
                            if register_model and model_name:
                                mlflow.register_model(f"runs:/{run_id}/model", model_name)

                        else:
                            # Modèle générique (pickle)
                            mlflow.pyfunc.log_model(
                                artifact_path="model",
                                python_model=model,
                                code_path=None
                            )

                            # Enregistrer le modèle dans le registre si demandé
                            if register_model and model_name:
                                mlflow.register_model(f"runs:/{run_id}/model", model_name)

                    except Exception as e:
                        logger.warning(f"Impossible de journaliser le modèle: {e}")

                # Retourner l'URI de l'exécution
                return f"runs:/{run_id}"

        except ImportError as e:
            logger.warning(f"Impossible d'intégrer avec MLflow: {e}")
            logger.warning("Installez MLflow: pip install mlflow")
            return None
        except Exception as e:
            logger.warning(f"Erreur lors de l'intégration avec MLflow: {e}")
            return None