# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 7496 à 7741
# Type: Méthode de la classe OptunaOptimizer

    def _load_all_historical_data(self, sample_percentage=0.10, is_viability_check=False, use_stratified=True, use_parallel=True, max_cache_size_gb=5, force_sequential=False):
        """
        Charge les lignes du fichier historical_data.txt et conserve la séquence complète (1-60)
        tout en identifiant clairement les manches 31-60 pour l'évaluation.
        Version optimisée avec mise en cache avancée et traitement parallèle amélioré.
        Limite toujours les données à sample_percentage (10% par défaut) du fichier.

        Args:
            sample_percentage (float): Pourcentage des données à échantillonner (10% par défaut).
            is_viability_check (bool): Indique si c'est une vérification de viabilité (ignoré, toujours 10%).
            use_stratified (bool): Utiliser l'échantillonnage stratifié au lieu de l'échantillonnage aléatoire.
            use_parallel (bool): Utiliser le traitement parallèle pour accélérer le chargement.
            max_cache_size_gb (float): Taille maximale du cache en Go.
            force_sequential (bool): Forcer le traitement séquentiel même si use_parallel est True.
                Utile en cas de problèmes avec le multiprocessing.

        Returns:
            List[Dict]: Liste de dictionnaires contenant:
                - 'full_sequence': Séquence complète (manches 1-60) pour le contexte
                - 'target_sequence': Séquence cible (manches 31-60) pour l'évaluation
                - 'sequence_data': Liste de dictionnaires pour les manches 31-60
        """
        # Importer les modules nécessaires
        import os
        import random
        import numpy as np
        import time
        import sys
        import multiprocessing
        from concurrent.futures import ProcessPoolExecutor, as_completed

        # Forcer sample_percentage à 0.10 (10%) si différent ou non spécifié
        if sample_percentage is None or sample_percentage != 0.10:
            sample_percentage = 0.10
            logger.warning("Forçage de sample_percentage à 10% pour garantir l'utilisation de 10% des données")

        # Vérifier si le fichier est très grand et adapter la stratégie de chargement
        file_size_mb = os.path.getsize(os.path.join(os.getcwd(), "historical_data.txt")) / (1024 * 1024)
        logger.warning(f"Taille du fichier historical_data.txt: {file_size_mb:.2f} MB")

        # Vérifier si les données sont déjà en cache
        if hasattr(self, '_cached_historical_data') and self._cached_historical_data is not None:
            all_data = self._cached_historical_data
            logger.warning(f"Utilisation des données en cache ({len(all_data)} séquences)")

            # Si un échantillonnage est demandé, l'appliquer sur les données en cache
            if sample_percentage is not None and 0.0 < sample_percentage < 1.0:
                sample_size = int(len(all_data) * sample_percentage)
                if sample_size > 0:
                    if use_stratified:
                        # Utiliser l'échantillonnage stratifié
                        # Utiliser l'échantillonnage stratifié pour obtenir les indices
                        sampled_indices = self._stratified_sampling(all_data, sample_size)
                        # Convertir les indices en séquences complètes
                        sampled_data = self._convert_indices_to_sequences(all_data, sampled_indices)
                    else:
                        # Utiliser l'échantillonnage aléatoire simple
                        sampled_data = random.sample(all_data, sample_size)

                    logger.warning(f"Échantillonnage de {sample_size} séquences ({sample_percentage*100:.1f}%) sur {len(all_data)} en cache")
                    logger.warning(f"Méthode d'échantillonnage: {'stratifié' if use_stratified else 'aléatoire simple'}")
                    return sampled_data

            return all_data

        # Chemin du fichier historical_data.txt
        file_path = os.path.join(os.getcwd(), "historical_data.txt")

        if not os.path.exists(file_path):
            logger.error(f"Fichier historical_data.txt non trouvé à l'emplacement: {file_path}")
            return []

        # Vérifier la taille du fichier
        file_size_bytes = os.path.getsize(file_path)
        file_size_gb = file_size_bytes / (1024 ** 3)

        # Utiliser un attribut de classe pour suivre si ce message a déjà été affiché
        if not hasattr(self.__class__, '_file_size_logged') or not self.__class__._file_size_logged:
            logger.warning(f"Taille du fichier historical_data.txt: {file_size_bytes:,} octets ({file_size_gb:.2f} Go)")
            self.__class__._file_size_logged = True

        # Vérifier si le fichier est trop volumineux pour être chargé entièrement en mémoire
        if file_size_gb > max_cache_size_gb:
            if not hasattr(self.__class__, '_streaming_mode_logged') or not self.__class__._streaming_mode_logged:
                logger.warning(f"Le fichier est plus grand que la taille maximale du cache ({max_cache_size_gb} Go). Utilisation du mode streaming.")
                self.__class__._streaming_mode_logged = True
            # Utiliser le mode streaming pour les fichiers volumineux
            return self._load_historical_data_streaming(file_path, sample_percentage, use_stratified)

        all_data = []

        try:
            # Mesurer le temps de chargement
            start_time = time.time()

            with open(file_path, "r") as f:
                lines = f.readlines()

            logger.warning(f"Chargement de {len(lines)} lignes depuis historical_data.txt")

            # Utiliser la méthode de classe _process_line pour traiter les lignes

            # Traitement parallèle ou séquentiel selon les paramètres
            if use_parallel and not force_sequential:
                # Déterminer le nombre de cœurs à utiliser (8 cœurs demandés)
                num_cores = min(8, multiprocessing.cpu_count())

                # Utiliser un attribut de classe pour suivre si ce message a déjà été affiché
                if not hasattr(self.__class__, '_parallel_mode_logged') or not self.__class__._parallel_mode_logged:
                    logger.warning(f"Traitement parallèle activé avec {num_cores} cœurs")
                    self.__class__._parallel_mode_logged = True

                # Créer une liste d'entrées pour le traitement parallèle
                line_inputs = [(i, line) for i, line in enumerate(lines)]

                # Traiter les lignes en parallèle avec gestion des erreurs
                try:
                    with ProcessPoolExecutor(max_workers=num_cores) as executor:
                        # Soumettre les tâches en utilisant la méthode statique pour éviter les problèmes de sérialisation
                        futures = [executor.submit(OptunaOptimizer._process_line_static, line_info) for line_info in line_inputs]

                        # Initialiser le compteur de progression
                        total_lines = len(lines)
                        progress_step = max(1, total_lines // 10)
                        completed = 0
                        valid_sequences = 0

                        # Collecter les résultats au fur et à mesure qu'ils sont terminés
                        for future in as_completed(futures):
                            completed += 1
                            try:
                                result = future.result()

                                # Afficher la progression
                                if completed % progress_step == 0 or completed == total_lines:
                                    progress_percent = completed / total_lines * 100
                                    logger.warning(f"Progression: {progress_percent:.1f}% ({completed}/{total_lines} lignes traitées)")

                                # Ajouter le résultat s'il est valide
                                if result is not None:
                                    all_data.append(result)
                                    valid_sequences += 1
                            except Exception as e:
                                logger.error(f"Erreur lors du traitement parallèle d'une ligne: {e}")
                                # Continuer avec les autres lignes
                except Exception as e:
                    logger.error(f"Erreur critique lors du traitement parallèle: {e}")
                    logger.warning("Basculement en mode séquentiel suite à une erreur dans le traitement parallèle")

                    # Réinitialiser les données et basculer en mode séquentiel
                    all_data = []

                    # Traiter chaque ligne séquentiellement
                    total_lines = len(lines)
                    progress_step = max(1, total_lines // 10)
                    valid_sequences = 0

                    for line_idx, line in enumerate(lines):
                        # Afficher la progression
                        if (line_idx + 1) % progress_step == 0 or line_idx + 1 == total_lines:
                            progress_percent = (line_idx + 1) / total_lines * 100
                            logger.warning(f"Progression (mode séquentiel): {progress_percent:.1f}% ({line_idx + 1}/{total_lines} lignes traitées)")

                        # Traiter la ligne
                        result = OptunaOptimizer._process_line_static((line_idx, line))

                        # Ajouter le résultat s'il est valide
                        if result is not None:
                            all_data.append(result)
                            valid_sequences += 1
            else:
                # Traitement séquentiel
                if not hasattr(self.__class__, '_sequential_mode_logged') or not self.__class__._sequential_mode_logged:
                    logger.warning("Traitement séquentiel activé")
                    self.__class__._sequential_mode_logged = True

                # Initialiser le compteur de progression
                total_lines = len(lines)
                progress_step = max(1, total_lines // 10)
                valid_sequences = 0

                # Traiter chaque ligne séquentiellement
                for line_idx, line in enumerate(lines):
                    # Afficher la progression
                    if (line_idx + 1) % progress_step == 0 or line_idx + 1 == total_lines:
                        progress_percent = (line_idx + 1) / total_lines * 100
                        logger.warning(f"Progression: {progress_percent:.1f}% ({line_idx + 1}/{total_lines} lignes traitées)")

                    # Traiter la ligne
                    result = self._process_line((line_idx, line))

                    # Ajouter le résultat s'il est valide
                    if result is not None:
                        all_data.append(result)
                        valid_sequences += 1

            # Calculer le temps de traitement
            elapsed_time = time.time() - start_time
            logger.warning(f"Chargement terminé en {elapsed_time:.2f} secondes: {valid_sequences}/{total_lines} séquences valides extraites")

            # Vérifier si le cache est activé
            if max_cache_size_gb > 0:
                # Estimer la taille en mémoire des données
                estimated_size_bytes = sys.getsizeof(all_data)
                for item in all_data:
                    estimated_size_bytes += sys.getsizeof(item)
                    for key, value in item.items():
                        estimated_size_bytes += sys.getsizeof(key) + sys.getsizeof(value)

                estimated_size_gb = estimated_size_bytes / (1024 ** 3)
                logger.warning(f"Taille estimée des données en mémoire: {estimated_size_bytes:,} octets ({estimated_size_gb:.2f} Go)")

                # Mettre en cache les données si elles ne dépassent pas la taille maximale
                if estimated_size_gb <= max_cache_size_gb:
                    self._cached_historical_data = all_data
                    logger.warning(f"Données mises en cache ({len(all_data)} séquences)")
                else:
                    logger.warning(f"Les données dépassent la taille maximale du cache ({max_cache_size_gb} Go). Cache désactivé.")
            else:
                logger.warning("Cache désactivé (max_cache_size_gb=0)")

            # Si un échantillonnage est demandé, l'appliquer
            if sample_percentage is not None and 0.0 < sample_percentage < 1.0:
                sample_size = int(len(all_data) * sample_percentage)
                if sample_size > 0:
                    if use_stratified:
                        # Utiliser l'échantillonnage stratifié
                        # Utiliser l'échantillonnage stratifié pour obtenir les indices
                        sampled_indices = self._stratified_sampling(all_data, sample_size)
                        # Convertir les indices en séquences complètes
                        sampled_data = self._convert_indices_to_sequences(all_data, sampled_indices)
                    else:
                        # Utiliser l'échantillonnage aléatoire simple
                        sampled_data = random.sample(all_data, sample_size)

                    logger.warning(f"Échantillonnage de {sample_size} séquences ({sample_percentage*100:.1f}%) sur {len(all_data)}")
                    logger.warning(f"Méthode d'échantillonnage: {'stratifié' if use_stratified else 'aléatoire simple'}")
                    return sampled_data

            return all_data

        except Exception as e:
            logger.error(f"Erreur lors du chargement du fichier historical_data.txt: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []