# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 7743 à 7859
# Type: Méthode de la classe OptunaOptimizer

    def _load_historical_data_streaming(self, file_path, sample_percentage=None, use_stratified=True):
        """
        Charge les données du fichier historical_data.txt en mode streaming pour économiser la mémoire.

        Args:
            file_path: Chemin du fichier historical_data.txt
            sample_percentage: Pourcentage des données à échantillonner
            use_stratified: Utiliser l'échantillonnage stratifié

        Returns:
            List[Dict]: Liste de dictionnaires contenant les données des séquences
        """
        import random
        import os

        try:
            # Compter le nombre total de lignes dans le fichier
            total_lines = 0
            with open(file_path, "r") as f:
                for _ in f:
                    total_lines += 1

            logger.warning(f"Mode streaming: {total_lines} lignes détectées dans le fichier")

            # Calculer le nombre de lignes à échantillonner
            if sample_percentage is not None and 0.0 < sample_percentage < 1.0:
                sample_size = int(total_lines * sample_percentage)
                logger.warning(f"Échantillonnage de {sample_size} lignes ({sample_percentage*100:.1f}%) en mode streaming")

                # Sélectionner aléatoirement les indices des lignes à traiter
                if sample_size < total_lines:
                    selected_indices = set(random.sample(range(total_lines), sample_size))
                else:
                    selected_indices = set(range(total_lines))
            else:
                # Traiter toutes les lignes
                selected_indices = set(range(total_lines))
                logger.warning("Traitement de toutes les lignes en mode streaming")

            # Traiter uniquement les lignes sélectionnées
            all_data = []
            valid_sequences = 0

            with open(file_path, "r") as f:
                for line_idx, line in enumerate(f):
                    # Vérifier si cette ligne doit être traitée
                    if line_idx in selected_indices:
                        # Ignorer les lignes vides ou les commentaires
                        if not line.strip() or line.strip().startswith("#"):
                            continue

                        try:
                            # Format attendu: séquence de résultats (P/B)
                            full_sequence = line.strip()

                            # Vérifier si la séquence est assez longue
                            if len(full_sequence) >= 60:
                                # Extraire les manches 1 à 30 pour l'entraînement
                                training_sequence = full_sequence[:30]

                                # Extraire les manches 31 à 60 pour l'évaluation
                                target_sequence = full_sequence[30:60]

                                # Convertir la séquence cible en liste de dictionnaires pour faciliter le traitement
                                sequence_data = []
                                for i, outcome in enumerate(target_sequence):
                                    sequence_data.append({
                                        'round_num': i + 31,  # Manche 31 à 60
                                        'outcome': 'PLAYER' if outcome == 'P' else 'BANKER',
                                        'line_idx': line_idx
                                    })

                                # Convertir les séquences en format standard (lowercase)
                                training_sequence_std = []
                                for outcome in training_sequence:  # Manches 1-30
                                    training_sequence_std.append('player' if outcome == 'P' else 'banker')

                                target_sequence_std = []
                                for outcome in target_sequence:  # Manches 31-60
                                    target_sequence_std.append('player' if outcome == 'P' else 'banker')

                                # Ajouter les données à la liste
                                all_data.append({
                                    'training_sequence': training_sequence_std,  # Séquence d'entraînement (manches 1-30)
                                    'target_sequence': target_sequence_std,      # Séquence cible (manches 31-60)
                                    'sequence_data': sequence_data               # Données formatées pour l'évaluation
                                })
                                valid_sequences += 1

                                # Afficher la progression tous les 1000 séquences
                                if valid_sequences % 1000 == 0:
                                    logger.warning(f"Mode streaming: {valid_sequences} séquences valides traitées")

                        except Exception as e:
                            logger.error(f"Erreur lors du traitement de la ligne {line_idx+1} en mode streaming: {e}")

            logger.warning(f"Mode streaming terminé: {valid_sequences} séquences valides extraites")

            # Si un échantillonnage stratifié est demandé, l'appliquer sur les données déjà échantillonnées
            if use_stratified and sample_percentage is not None and 0.0 < sample_percentage < 1.0:
                # Calculer la taille de l'échantillon stratifié (même taille que l'échantillon aléatoire)
                stratified_size = len(all_data)

                # Appliquer l'échantillonnage stratifié
                # Utiliser l'échantillonnage stratifié pour obtenir les indices
                sampled_indices = self._stratified_sampling(all_data, stratified_size)
                # Convertir les indices en séquences complètes
                all_data = self._convert_indices_to_sequences(all_data, sampled_indices)
                logger.warning(f"Échantillonnage stratifié appliqué en mode streaming: {len(all_data)} séquences")

            return all_data

        except Exception as e:
            logger.error(f"Erreur lors du chargement en mode streaming: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []