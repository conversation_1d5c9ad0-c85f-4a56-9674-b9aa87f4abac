# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 6881 à 6998
# Type: Méthode de la classe OptunaOptimizer

    def _optimize_memory_for_full_dataset(self):
        """
        Optimise la mémoire spécifiquement pour l'utilisation de l'ensemble de données complet.
        Implémente des stratégies avancées pour réduire l'empreinte mémoire lors de l'utilisation
        de 100% des données, en particulier pour les grandes séquences et les modèles LSTM.

        Cette méthode est appelée avant de traiter l'ensemble de données complet pour s'assurer
        que les ressources mémoire sont suffisantes.
        """
        import gc
        import sys
        import numpy as np
        import torch
        import os

        logger.warning("Optimisation avancée de la mémoire pour l'ensemble de données complet...")

        # 1. Forcer plusieurs cycles de collecte des objets non référencés
        gc.collect()
        gc.collect()

        # 2. Détecter les ressources disponibles
        try:
            import psutil

            # Mémoire système
            system_memory = psutil.virtual_memory()
            total_gb = system_memory.total / (1024**3)
            available_gb = system_memory.available / (1024**3)
            used_percent = system_memory.percent

            logger.warning(f"Mémoire système: {available_gb:.2f} GB disponible sur {total_gb:.2f} GB total ({used_percent}% utilisé)")

            # Alerte si la mémoire est critique
            if available_gb < 2:
                logger.warning("ALERTE: Mémoire système critique! Risque d'erreur OOM (Out Of Memory)")
                logger.warning("Activation des optimisations mémoire agressives")

                # Stratégies d'optimisation agressives pour mémoire très limitée
                if hasattr(self, '_advanced_data_cache'):
                    # Réduire drastiquement la taille du cache
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 256  # 256 MB seulement
                    logger.warning("Taille du cache réduite à 256 MB")

                    # Vider les caches non essentiels
                    for cache_key in ['feature_cache', 'prediction_cache', 'importance_cache']:
                        if cache_key in self._advanced_data_cache:
                            self._advanced_data_cache[cache_key] = {}
                            logger.warning(f"Cache '{cache_key}' vidé")

            elif available_gb < 4:
                logger.warning("ATTENTION: Mémoire système limitée, activation des optimisations mémoire")

                # Stratégies d'optimisation pour mémoire limitée
                if hasattr(self, '_advanced_data_cache'):
                    # Réduire la taille du cache
                    self._advanced_data_cache['cache_config']['max_size_mb'] = 512  # 512 MB
                    logger.warning("Taille du cache réduite à 512 MB")
        except ImportError:
            logger.warning("Module psutil non disponible, impossible de détecter les ressources mémoire")

        # 3. Optimiser les tableaux NumPy en mémoire
        for attr_name in dir(self):
            if attr_name.startswith('_'):
                continue

            try:
                attr = getattr(self, attr_name)
                if isinstance(attr, np.ndarray) and attr.size > 1000000:  # Tableaux de plus de 1M d'éléments
                    # Vérifier si on peut convertir en type plus compact
                    if attr.dtype == np.float64:
                        setattr(self, attr_name, attr.astype(np.float32))
                        logger.warning(f"Tableau NumPy {attr_name} converti de float64 à float32")
                    elif attr.dtype == np.float32 and np.max(np.abs(attr)) < 65504:
                        setattr(self, attr_name, attr.astype(np.float16))
                        logger.warning(f"Tableau NumPy {attr_name} converti de float32 à float16")
            except:
                pass

        # 4. Optimiser les tenseurs PyTorch
        if torch.cuda.is_available():
            # Libérer le cache CUDA
            torch.cuda.empty_cache()
            logger.warning("Cache CUDA vidé")

            # Définir une stratégie d'allocation mémoire plus conservatrice
            if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                try:
                    # Limiter l'utilisation de la mémoire GPU à 80%
                    torch.cuda.set_per_process_memory_fraction(0.8)
                    logger.warning("Utilisation de la mémoire GPU limitée à 80%")
                except:
                    pass

        # 5. Optimiser les structures de données internes
        if hasattr(self, '_preprocessed_data') and isinstance(self._preprocessed_data, dict):
            # Convertir les listes en tableaux NumPy pour réduire l'empreinte mémoire
            for key, value in self._preprocessed_data.items():
                if isinstance(value, list) and len(value) > 1000:
                    try:
                        # Tenter de convertir en tableau NumPy
                        self._preprocessed_data[key] = np.array(value)
                        logger.warning(f"Liste '{key}' convertie en tableau NumPy")
                    except:
                        pass

        # 6. Compactage de la mémoire (si possible)
        try:
            import ctypes
            ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
            logger.warning("Mémoire système compactée")
        except:
            pass

        # 7. Forcer une dernière collecte
        gc.collect()

        logger.warning("Optimisation de la mémoire pour l'ensemble de données complet terminée")