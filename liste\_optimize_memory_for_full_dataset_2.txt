# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 11333 à 11476
# Type: Méthode de la classe OptunaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _optimize_memory_for_full_dataset(self):
        """
        Optimise l'utilisation de la mémoire pour les grands ensembles de données.

        Cette méthode réduit l'empreinte mémoire des données pour permettre
        l'utilisation de 100% des données chargées dans toutes les phases.
        Implémente des stratégies avancées de gestion de la mémoire pour les grands ensembles.
        """
        logger.warning("Optimisation avancée de la mémoire pour l'utilisation de 100% des données chargées")

        # 1. Conversion des types de données pour réduire l'empreinte mémoire
        if hasattr(self, 'X_lgbm_full') and self.X_lgbm_full is not None:
            # Convertir en float16 pour réduire la mémoire de 75%
            original_size = self.X_lgbm_full.nbytes / (1024 * 1024)

            # Vérifier si les données peuvent être converties en float16 sans perte significative
            # Calculer la plage des valeurs
            data_range = np.max(np.abs(self.X_lgbm_full))

            if data_range < 65504:  # Limite supérieure de float16
                # Conversion sûre en float16
                self.X_lgbm_full = self.X_lgbm_full.astype(np.float16)
                logger.warning("Conversion en float16 pour économie maximale de mémoire")
            else:
                # Conversion en float32 si float16 n'est pas adapté
                self.X_lgbm_full = self.X_lgbm_full.astype(np.float32)
                logger.warning("Conversion en float32 (float16 non adapté pour cette plage de données)")

            new_size = self.X_lgbm_full.nbytes / (1024 * 1024)
            logger.warning(f"X_lgbm_full converti: {original_size:.1f} MB -> {new_size:.1f} MB (réduction de {(1 - new_size/original_size)*100:.1f}%)")

        if hasattr(self, 'X_lstm_full') and self.X_lstm_full is not None:
            original_size = self.X_lstm_full.nbytes / (1024 * 1024)

            # Vérifier si les données peuvent être converties en float16 sans perte significative
            data_range = np.max(np.abs(self.X_lstm_full))

            if data_range < 65504:  # Limite supérieure de float16
                # Conversion sûre en float16
                self.X_lstm_full = self.X_lstm_full.astype(np.float16)
                logger.warning("Conversion en float16 pour économie maximale de mémoire")
            else:
                # Conversion en float32 si float16 n'est pas adapté
                self.X_lstm_full = self.X_lstm_full.astype(np.float32)
                logger.warning("Conversion en float32 (float16 non adapté pour cette plage de données)")

            new_size = self.X_lstm_full.nbytes / (1024 * 1024)
            logger.warning(f"X_lstm_full converti: {original_size:.1f} MB -> {new_size:.1f} MB (réduction de {(1 - new_size/original_size)*100:.1f}%)")

        # 2. Compression des données pour les grands ensembles
        if hasattr(self, '_preprocessed_data_cache') and self._preprocessed_data_cache is not None:
            # Utiliser des indices au lieu de séquences complètes pour économiser de la mémoire
            for phase in ['phase0', 'phase1', 'phase2']:
                phase_key = f'{phase}_sequences'
                if phase_key in self._preprocessed_data_cache:
                    # Supprimer les séquences complètes et ne garder que les indices
                    del self._preprocessed_data_cache[phase_key]
                    logger.warning(f"Séquences {phase} supprimées pour économiser de la mémoire, utilisation des indices uniquement")

        # 3. Libération proactive de la mémoire
        import gc
        # Forcer deux cycles de collecte pour s'assurer que toute la mémoire est libérée
        gc.collect()
        gc.collect()

        # 4. Définition d'un générateur adaptatif pour le traitement par lots
        def adaptive_data_generator(X, y, initial_batch_size=1000):
            """
            Générateur adaptatif pour traiter les données par lots.
            Ajuste automatiquement la taille des lots en fonction de la mémoire disponible.
            """
            n_samples = X.shape[0]
            indices = np.arange(n_samples)
            np.random.shuffle(indices)

            # Détecter la mémoire disponible
            try:
                import psutil
                mem = psutil.virtual_memory()
                available_gb = mem.available / (1024**3)

                # Ajuster la taille des lots en fonction de la mémoire disponible
                if available_gb < 2:
                    batch_size = min(100, initial_batch_size)
                    logger.warning(f"Mémoire faible ({available_gb:.1f} GB), taille de batch réduite à {batch_size}")
                elif available_gb < 4:
                    batch_size = min(500, initial_batch_size)
                    logger.warning(f"Mémoire limitée ({available_gb:.1f} GB), taille de batch réduite à {batch_size}")
                elif available_gb < 8:
                    batch_size = min(1000, initial_batch_size)
                    logger.warning(f"Mémoire moyenne ({available_gb:.1f} GB), taille de batch standard à {batch_size}")
                else:
                    batch_size = min(2000, initial_batch_size)
                    logger.warning(f"Mémoire abondante ({available_gb:.1f} GB), taille de batch augmentée à {batch_size}")
            except:
                batch_size = initial_batch_size
                logger.warning(f"Impossible de détecter la mémoire, utilisation de la taille de batch par défaut: {batch_size}")

            for start_idx in range(0, n_samples, batch_size):
                end_idx = min(start_idx + batch_size, n_samples)
                batch_indices = indices[start_idx:end_idx]
                yield X[batch_indices], y[batch_indices]

        # Stocker le générateur adaptatif pour une utilisation ultérieure
        self.data_generator = adaptive_data_generator

        # 5. Optimisation de la mémoire PyTorch
        if torch.cuda.is_available():
            # Libérer le cache CUDA
            torch.cuda.empty_cache()
            logger.warning("Cache CUDA vidé")

            # Configurer PyTorch pour libérer la mémoire immédiatement
            try:
                torch.cuda.set_per_process_memory_fraction(0.8)  # Utiliser 80% de la mémoire GPU max
                logger.warning("Limite d'utilisation de la mémoire GPU fixée à 80%")
            except:
                logger.warning("Impossible de définir la limite d'utilisation de la mémoire GPU")

        # 6. Rapport détaillé sur l'utilisation de la mémoire
        try:
            import psutil
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_usage = memory_info.rss / (1024 * 1024 * 1024)  # En GB

            # Mémoire système
            system_memory = psutil.virtual_memory()
            total_memory = system_memory.total / (1024 * 1024 * 1024)  # En GB
            available_memory = system_memory.available / (1024 * 1024 * 1024)  # En GB

            logger.warning(f"Utilisation mémoire du processus: {memory_usage:.2f} GB")
            logger.warning(f"Mémoire système: {available_memory:.2f} GB disponible sur {total_memory:.2f} GB total")

            # Alerte si la mémoire est critique
            if available_memory < 2:
                logger.warning("ALERTE: Mémoire système critique! Risque d'erreur OOM (Out Of Memory)")
            elif available_memory < 4:
                logger.warning("ATTENTION: Mémoire système limitée, performances potentiellement réduites")

        except ImportError:
            logger.warning("Module psutil non disponible, impossible de mesurer l'utilisation mémoire")

        logger.warning("Optimisation avancée de la mémoire terminée")