# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 5812 à 5895
# Type: Méthode de la classe OptunaOptimizer

    def _perform_temporal_cross_validation(self, config, subset_indices, n_folds=5, **kwargs):
        """
        Effectue une validation croisée temporelle qui respecte l'ordre chronologique des données.

        Args:
            config: Configuration à évaluer
            subset_indices: Indices à utiliser pour la validation croisée
            n_folds: Nombre de plis pour la validation croisée
            **kwargs: Arguments supplémentaires

        Returns:
            Tuple[float, Dict]: Score moyen et métriques moyennes
        """
        # Trier les indices par ordre chronologique
        # Nous supposons que les indices plus élevés correspondent à des données plus récentes
        sorted_indices = sorted(subset_indices)

        # Diviser les indices en n_folds plis consécutifs
        fold_size = len(sorted_indices) // n_folds
        folds = []
        for i in range(n_folds):
            start_idx = i * fold_size
            end_idx = (i + 1) * fold_size if i < n_folds - 1 else len(sorted_indices)
            folds.append(sorted_indices[start_idx:end_idx])

        # Effectuer la validation croisée temporelle
        scores = []
        all_metrics = []

        for i in range(1, n_folds):
            # Utiliser les plis 0 à i-1 pour l'entraînement
            train_indices = []
            for j in range(i):
                train_indices.extend(folds[j])

            # Utiliser le pli i pour la validation
            val_indices = folds[i]

            # Évaluer la configuration sur ce split temporel
            score, metrics = self._evaluate_config(
                config,
                subset_indices=train_indices,
                validation_indices=val_indices,
                **kwargs
            )

            scores.append(score)
            all_metrics.append(metrics)

        # Calculer le score moyen et les métriques moyennes
        avg_score = sum(scores) / len(scores) if scores else 0.0

        # Agréger les métriques
        avg_metrics = {}
        if all_metrics:
            for key in all_metrics[0].keys():
                avg_metrics[key] = sum(m.get(key, 0.0) for m in all_metrics) / len(all_metrics)

        return avg_score, avg_metrics

        # Sélectionner les échantillons de chaque groupe
        result = []
        for ratio, group in groups.items():
            if len(group) <= samples_per_group:
                result.extend(group)
            else:
                result.extend(random.sample(group, samples_per_group))

        # Si nous n'avons pas assez d'échantillons, ajouter des échantillons supplémentaires
        remaining = max_samples - len(result)
        if remaining > 0:
            # Créer une liste de toutes les séquences non sélectionnées
            all_selected = set(id(seq) for seq in result)
            remaining_seqs = [seq for seq in sequences if id(seq) not in all_selected]

            if remaining_seqs:
                # Ajouter des séquences supplémentaires aléatoirement
                additional = random.sample(remaining_seqs, min(remaining, len(remaining_seqs)))
                result.extend(additional)

        logger.warning(f"Échantillonnage stratifié: {len(result)} séquences sélectionnées à partir de {len(sequences)} séquences")
        logger.warning(f"Distribution des ratios P/B: {len(groups)} groupes différents")

        return result