# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 5053 à 5064
# Type: Méthode de la classe OptunaOptimizer

    def _process_line(self, line_info):
        """
        Traite une ligne du fichier historical_data.txt.
        Cette méthode est un wrapper autour de la méthode statique _process_line_static.

        Args:
            line_info: Tuple (line_idx, line) contenant l'indice de la ligne et son contenu

        Returns:
            Dict or None: Dictionnaire contenant les données de la séquence, ou None si la ligne est invalide
        """
        return self._process_line_static(line_info)