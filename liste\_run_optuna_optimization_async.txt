# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 13034 à 13165
# Type: Méthode de la classe HybridBaccaratPredictor

    def _run_optuna_optimization_async(self, training_data_package, n_trials, advanced_options=None):
        """
        Exécute l'optimisation Optuna de manière asynchrone avec la stratégie multi-niveaux.

        Args:
            training_data_package: Package de données pour l'optimisation
            n_trials: Nombre d'essais à effectuer
            advanced_options: Options avancées pour l'optimisation multi-niveaux

        Returns:
            None
        """
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        ui_available = self.is_ui_available()
        start_time = time.time()
        best_params = None
        success = False
        error_message = None
        optimizer_instance = None

        try:
            X_lgbm_all, y_all, X_lstm_all, _, \
            train_indices_from_prep, val_indices_from_prep, _, _ = training_data_package

            if ui_available: self.root.after(0, lambda: self._update_progress(25, "Configuration Optuna multi-niveaux..."))

            # IMPORTANT: Réinitialiser les variables de classe d'OptunaOptimizer
            # pour s'assurer que chaque nouvelle optimisation commence proprement
            # Importation conditionnelle pour éviter l'importation circulaire
            try:
                from optuna_optimizer import OptunaOptimizer
                # Réinitialiser le compteur d'essais viables optimisés
                OptunaOptimizer.optimized_viable_trials_count = 0
                # Réinitialiser le compteur total de tentatives
                OptunaOptimizer.total_attempts_made = 0
                logger_instance.warning("=" * 80)
                logger_instance.warning("RÉINITIALISATION DES COMPTEURS OPTUNA POUR UNE NOUVELLE OPTIMISATION")
                logger_instance.warning(f"optimized_viable_trials_count = {OptunaOptimizer.optimized_viable_trials_count}")
                logger_instance.warning(f"total_attempts_made = {OptunaOptimizer.total_attempts_made}")
                logger_instance.warning("=" * 80)

                optimizer_instance = OptunaOptimizer(self.config)

                # Transmettre les options avancées à l'optimiseur
                if advanced_options:
                    optimizer_instance.advanced_options = advanced_options

                    # Définir les ressources disponibles
                    optimizer_instance.cpu_count = advanced_options.get('cpu_count', 8)
                    optimizer_instance.ram_gb = advanced_options.get('ram_gb', 28)
                    optimizer_instance.batch_size = advanced_options.get('batch_size', 1024)

                    logger_instance.warning("=" * 80)
                    logger_instance.warning("OPTIONS AVANCÉES POUR L'OPTIMISATION MULTI-NIVEAUX")
                    for key, value in advanced_options.items():
                        logger_instance.warning(f"{key}: {value}")
                    logger_instance.warning("=" * 80)
            except ImportError as e:
                logger_instance.error(f"Erreur lors de l'importation d'OptunaOptimizer: {e}")
                raise ImportError(f"Impossible d'importer OptunaOptimizer: {e}")

            # Définir la référence au prédicateur pour permettre l'échantillonnage des données historiques
            optimizer_instance.predictor_ref = self
            self.current_optimizer_instance = optimizer_instance

            # Vérifier que les données historiques sont disponibles
            if hasattr(self, 'historical_data') and self.historical_data:
                logger_instance.warning(f"Données historiques disponibles pour échantillonnage: {len(self.historical_data)} séquences")
            else:
                logger_instance.warning("Aucune donnée historique disponible pour échantillonnage")

            # Assigner les données et indices pour l'optimisation
            optimizer_instance.X_lgbm_full = X_lgbm_all
            optimizer_instance.y_full = y_all
            optimizer_instance.X_lstm_full = X_lstm_all
            optimizer_instance.train_indices = train_indices_from_prep
            optimizer_instance.val_indices = val_indices_from_prep
            logger_instance.info("Données/Indices assignés à OptunaOptimizer multi-niveaux (Thread Async).")

            # Ajouter des logs très visibles pour l'optimisation multi-niveaux
            logger_instance.warning("=" * 80)
            logger_instance.warning("OPTIMISATION MULTI-NIVEAUX AVEC STRATÉGIE ADAPTÉE POUR CPU")
            logger_instance.warning("Niveau 0: Exploration préliminaire rapide (15-20% du temps)")
            logger_instance.warning("Niveau 1: Exploration ciblée avec LGBM complet (25-30% du temps)")
            logger_instance.warning("Niveau 2: Optimisation progressive avec LSTM (35-40% du temps)")
            logger_instance.warning("Niveau 3: Optimisation fine avec modèles complets (15-20% du temps)")
            logger_instance.warning("=" * 80)

            if ui_available: self.root.after(0, lambda: self._update_progress(30, f"Optimisation multi-niveaux ({n_trials} essais) en cours..."))
            logger_instance.info(f"Appel optimizer_instance.optimize({n_trials}) depuis thread async...")

            params_found = optimizer_instance.optimize(n_trials=n_trials)

            was_interrupted = getattr(self, 'stop_training', False)
            if was_interrupted:
                logger_instance.warning("Optimisation multi-niveaux (Thread) interrompue par utilisateur.")
                error_message = "Optimisation interrompue par l'utilisateur."
                success = False
                best_params = params_found
                if not best_params:
                    logger_instance.warning("Interruption avant la fin d'un essai valide.")
            else:
                success = True
                best_params = params_found
                if not best_params:
                    logger_instance.warning("Optimisation multi-niveaux (Thread) terminée mais aucun/meilleur paramètre trouvé.")

        except ImportError as ie :
            logger_instance.critical(f"Erreur import Optuna V3 (thread): {ie}", exc_info=True);
            error_message = f"Dépendance Optuna manquante:\n{ie}"
            success = False
            best_params = None
        except (ValueError, RuntimeError) as ve:
            logger_instance.error(f"Erreur Valeur/Runtime Optuna V3 (thread): {ve}", exc_info=True);
            error_message = f"Erreur configuration Optuna:\n{ve}"
            success = False
            best_params = None
        except Exception as e:
            logger_instance.error(f"Erreur inattendue Optuna V3 (thread): {e}", exc_info=True);
            error_message = f"Erreur Optuna inattendue:\n{e}"
            success = False
            best_params = None
        finally:
            duration = time.time() - start_time
            completion_status = "Terminée normalement" if success else "Interrompue" if error_message == "Optimisation interrompue par l'utilisateur." else "Échouée"
            logger_instance.info(f"Optimisation Optuna (Thread) {completion_status}. Durée: {duration:.1f}s. Params trouvés: {'Oui' if best_params else 'Non'}")
            if ui_available:
                self.root.after(0, self._finalize_optuna_optimization,
                                success, best_params, duration, error_message)
            else:
                if best_params:
                    logger_instance.info(f"(UI indispo) Meilleurs Params Trouvés: {best_params}")