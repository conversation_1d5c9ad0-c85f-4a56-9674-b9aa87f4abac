# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 5445 à 5518
# Type: Méthode de la classe OptunaOptimizer

    def _simple_lhs(self, features, n_samples):
        """
        Implémentation simplifiée de Latin Hypercube Sampling.

        Args:
            features: Tableau numpy de caractéristiques (n_samples, n_features)
            n_samples: Nombre d'échantillons à sélectionner

        Returns:
            list: Indices des échantillons sélectionnés
        """
        import numpy as np

        n_points, n_dims = features.shape

        # Normaliser les features pour éviter les problèmes numériques
        try:
            features_normalized = self._normalize_features(features)
            logger.warning(f"Features normalisées pour le calcul des distances (shape: {features_normalized.shape})")
        except Exception as e:
            logger.warning(f"Erreur lors de la normalisation des features: {e}")
            features_normalized = features

        # Générer des points LHS
        lhs_points = self._generate_lhs_points(n_dims, n_samples)

        # Trouver les indices des séquences les plus proches des points LHS
        selected_indices = []
        features_copy = features_normalized.copy()

        for point in lhs_points:
            # Calculer la distance euclidienne entre le point et toutes les caractéristiques
            # Utiliser une méthode robuste pour éviter les dépassements numériques
            try:
                # Méthode 1: Utiliser np.linalg.norm qui est plus stable numériquement
                distances = np.array([np.linalg.norm(f - point) for f in features_copy])
            except Exception as e:
                logger.warning(f"Erreur lors du calcul des distances avec np.linalg.norm dans _simple_lhs: {e}")

                try:
                    # Méthode 2: Calculer la distance manuellement avec une protection contre les dépassements
                    # Limiter les différences pour éviter les dépassements lors de l'élévation au carré
                    diff = np.clip(features_copy - point, -1e6, 1e6)
                    distances = np.sqrt(np.sum(diff * diff, axis=1))
                except Exception as e:
                    logger.warning(f"Erreur lors du calcul manuel des distances dans _simple_lhs: {e}")

                    # Méthode 3: Fallback - utiliser une distance approximative
                    # Utiliser la somme des valeurs absolues des différences (distance de Manhattan)
                    distances = np.sum(np.abs(features_copy - point), axis=1)

            # Trouver l'indice de la séquence la plus proche
            # Vérifier si distances contient des valeurs valides
            if np.all(np.isinf(distances)):
                # Toutes les distances sont infinies, choisir un indice aléatoire parmi ceux non sélectionnés
                remaining_indices = [i for i in range(n_points) if i not in selected_indices]
                if remaining_indices:
                    closest_idx = np.random.choice(remaining_indices)
                else:
                    # Aucun indice restant, sortir de la boucle
                    break
            else:
                closest_idx = np.argmin(distances)

            # Ajouter l'indice à la liste des sélectionnés
            selected_indices.append(closest_idx)

            # Mettre à jour les distances pour éviter de sélectionner la même séquence
            # Utiliser une grande valeur finie au lieu de l'infini pour éviter les problèmes de conversion
            # Vérifier le type de données pour éviter les erreurs de cast
            max_value = np.finfo(features_copy.dtype).max
            features_copy[closest_idx] = np.ones(n_dims) * max_value

        return selected_indices