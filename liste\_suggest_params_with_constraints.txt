# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 14920 à 15101
# Type: Méthode de la classe MetaOptimizer

    def _suggest_params_with_constraints(self, trial, search_space, constraints=None):
        """
        Suggère des paramètres pour un essai Optuna en respectant des contraintes.
        Cette méthode permet de définir des contraintes entre les paramètres pour
        éviter les combinaisons invalides ou sous-optimales.

        Args:
            trial: Essai Optuna en cours
            search_space: Espace de recherche des paramètres
            constraints: Liste de contraintes à respecter (fonctions ou dictionnaires)

        Returns:
            dict: Paramètres suggérés respectant les contraintes
        """
        import random

        # Initialiser le dictionnaire de paramètres
        params = {}

        # Vérifier si l'espace de recherche est valide
        if not search_space or not isinstance(search_space, dict):
            logger.warning("Espace de recherche invalide ou vide")
            return params

        # Initialiser les contraintes si non fournies
        if constraints is None:
            constraints = []

        # Fonction pour vérifier si les paramètres respectent les contraintes
        def check_constraints(current_params):
            # Si aucun paramètre n'est encore défini, les contraintes sont respectées
            if not current_params:
                return True

            for constraint in constraints:
                if callable(constraint):
                    # Contrainte sous forme de fonction
                    if not constraint(current_params):
                        return False
                elif isinstance(constraint, dict):
                    # Contrainte sous forme de dictionnaire
                    if 'type' not in constraint:
                        continue

                    if constraint['type'] == 'range_dependency':
                        # Contrainte de dépendance de plage
                        param1 = constraint.get('param1')
                        param2 = constraint.get('param2')
                        relation = constraint.get('relation', '>')

                        if param1 in current_params and param2 in current_params:
                            val1 = current_params[param1]
                            val2 = current_params[param2]

                            if relation == '>' and not (val1 > val2):
                                return False
                            elif relation == '>=' and not (val1 >= val2):
                                return False
                            elif relation == '<' and not (val1 < val2):
                                return False
                            elif relation == '<=' and not (val1 <= val2):
                                return False
                            elif relation == '==' and not (val1 == val2):
                                return False
                            elif relation == '!=' and not (val1 != val2):
                                return False

                    elif constraint['type'] == 'conditional':
                        # Contrainte conditionnelle
                        condition_param = constraint.get('if_param')
                        condition_value = constraint.get('if_value')
                        then_param = constraint.get('then_param')
                        then_value = constraint.get('then_value')

                        if condition_param in current_params and current_params[condition_param] == condition_value:
                            if then_param in current_params and current_params[then_param] != then_value:
                                return False

                    elif constraint['type'] == 'sum_constraint':
                        # Contrainte de somme
                        params_to_sum = constraint.get('params', [])
                        target_sum = constraint.get('target_sum')
                        tolerance = constraint.get('tolerance', 0.0)

                        if all(p in current_params for p in params_to_sum):
                            current_sum = sum(current_params[p] for p in params_to_sum)
                            if abs(current_sum - target_sum) > tolerance:
                                return False

            return True

        # Trier les paramètres pour respecter les dépendances
        param_order = list(search_space.keys())

        # Réorganiser l'ordre des paramètres en fonction des contraintes
        for constraint in constraints:
            if isinstance(constraint, dict) and constraint.get('type') == 'conditional':
                # Placer le paramètre conditionnel avant le paramètre dépendant
                if_param = constraint.get('if_param')
                then_param = constraint.get('then_param')

                if if_param in param_order and then_param in param_order:
                    # S'assurer que if_param est avant then_param
                    if param_order.index(if_param) > param_order.index(then_param):
                        param_order.remove(if_param)
                        param_order.insert(0, if_param)

        # Suggérer les paramètres dans l'ordre défini
        max_attempts = 100  # Limite de tentatives pour éviter les boucles infinies

        for param_name in param_order:
            param_spec = search_space[param_name]

            # Extraire le type et les arguments du paramètre
            if isinstance(param_spec, (list, tuple)) and len(param_spec) >= 1:
                param_type = param_spec[0]
                param_args = param_spec[1:]
            elif isinstance(param_spec, dict) and 'type' in param_spec:
                param_type = param_spec['type']
                if param_type == 'int' or param_type == 'float':
                    param_args = [param_spec.get('low', 0), param_spec.get('high', 1)]
                elif param_type == 'categorical':
                    param_args = [param_spec.get('choices', [])]
                else:
                    logger.warning(f"Type de paramètre inconnu: {param_type} pour {param_name}")
                    continue
            else:
                logger.warning(f"Format d'espace de recherche non reconnu pour {param_name}: {param_spec}")
                continue

            # Suggérer le paramètre avec plusieurs tentatives si nécessaire
            attempts = 0
            while attempts < max_attempts:
                # Suggérer une valeur pour le paramètre
                if param_type == 'int':
                    low, high = param_args[0], param_args[1]
                    params[param_name] = trial.suggest_int(param_name, low, high)
                elif param_type == 'float':
                    low, high = param_args[0], param_args[1]
                    log_scale = param_spec.get('log_scale', False) if isinstance(param_spec, dict) else False
                    if log_scale:
                        params[param_name] = trial.suggest_float(param_name, low, high, log=True)
                    else:
                        params[param_name] = trial.suggest_float(param_name, low, high)
                elif param_type == 'categorical':
                    choices = param_args[0]
                    params[param_name] = trial.suggest_categorical(param_name, choices)
                else:
                    logger.warning(f"Type de paramètre non pris en charge: {param_type}")
                    break

                # Vérifier si les contraintes sont respectées
                if check_constraints(params):
                    break

                # Si les contraintes ne sont pas respectées, réessayer
                attempts += 1

                # Si trop de tentatives, utiliser une valeur aléatoire qui respecte les contraintes
                if attempts >= max_attempts - 1:
                    logger.warning(f"Impossible de trouver une valeur valide pour {param_name} après {attempts} tentatives")

                    # Générer une valeur aléatoire qui respecte les contraintes
                    if param_type == 'int':
                        low, high = param_args[0], param_args[1]
                        params[param_name] = random.randint(low, high)
                    elif param_type == 'float':
                        low, high = param_args[0], param_args[1]
                        params[param_name] = random.uniform(low, high)
                    elif param_type == 'categorical':
                        choices = param_args[0]
                        params[param_name] = random.choice(choices)

                    # Forcer la valeur dans le trial
                    trial._suggest(param_name, params[param_name])
                    break

        # Valider les paramètres finaux
        if not check_constraints(params):
            logger.warning("Les paramètres suggérés ne respectent pas toutes les contraintes")

        return params