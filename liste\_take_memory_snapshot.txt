# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 6699 à 6879
# Type: Méthode de la classe OptunaOptimizer

    def _take_memory_snapshot(self, label):
        """
        Prend un instantané détaillé de la mémoire et affiche les différences.
        Implémente des mécanismes avancés de surveillance de la mémoire.

        Args:
            label: Étiquette pour l'instantané

        Returns:
            dict: Informations détaillées sur l'utilisation de la mémoire
        """
        import time
        import os
        import sys
        import gc

        # Stocker les résultats dans un dictionnaire
        memory_info = {
            'label': label,
            'timestamp': time.time(),
            'details': {}
        }

        # 1. Utiliser tracemalloc si disponible
        try:
            import tracemalloc

            if not tracemalloc.is_tracing():
                # Démarrer le traçage si ce n'est pas déjà fait
                tracemalloc.start()
                self._memory_profiling_start_time = time.time()
                logger.warning("Profilage de la mémoire démarré")

            # Prendre un instantané
            snapshot = tracemalloc.take_snapshot()

            # Afficher les statistiques
            top_stats = snapshot.statistics('lineno')

            logger.warning(f"Instantané de mémoire détaillé ({label}):")

            # Stocker les 20 plus gros blocs
            top_blocks = []
            for stat in top_stats[:20]:
                block_info = {
                    'count': stat.count,
                    'size_kb': stat.size / 1024,
                    'source': stat.traceback.format()[0]
                }
                top_blocks.append(block_info)
                logger.warning(f"{stat.count} blocs: {stat.size / 1024:.1f} KiB - {stat.traceback.format()[0]}")

            # Afficher la mémoire totale tracée
            total_size = sum(stat.size for stat in top_stats)
            logger.warning(f"Mémoire totale tracée: {total_size / (1024 * 1024):.2f} MiB")

            # Stocker les informations tracemalloc
            memory_info['details']['tracemalloc'] = {
                'top_blocks': top_blocks,
                'total_size_mb': total_size / (1024 * 1024)
            }

            # Comparer avec l'instantané précédent si disponible
            if hasattr(self, '_previous_memory_snapshot'):
                prev_snapshot = self._previous_memory_snapshot
                comparison = snapshot.compare_to(prev_snapshot, 'lineno')

                logger.warning(f"Différences depuis l'instantané précédent ({self._previous_memory_label}):")
                for stat in comparison[:10]:
                    logger.warning(f"{stat.size_diff / 1024:.1f} KiB: {stat.traceback.format()[0]}")

                # Stocker les différences
                memory_info['details']['comparison'] = {
                    'previous_label': self._previous_memory_label,
                    'differences': [
                        {'size_diff_kb': stat.size_diff / 1024, 'source': stat.traceback.format()[0]}
                        for stat in comparison[:10]
                    ]
                }

            # Sauvegarder l'instantané actuel pour comparaison future
            self._previous_memory_snapshot = snapshot
            self._previous_memory_label = label

        except Exception as e:
            logger.warning(f"Erreur lors de l'utilisation de tracemalloc: {e}")

        # 2. Utiliser psutil pour des informations système détaillées
        try:
            import psutil

            # Informations sur le processus
            process = psutil.Process(os.getpid())
            proc_info = process.memory_info()

            # Mémoire du processus
            rss_mb = proc_info.rss / (1024 * 1024)
            vms_mb = proc_info.vms / (1024 * 1024)

            # Mémoire système
            system_memory = psutil.virtual_memory()
            total_gb = system_memory.total / (1024**3)
            available_gb = system_memory.available / (1024**3)
            used_percent = system_memory.percent

            logger.warning(f"Mémoire du processus: {rss_mb:.2f} MiB (RSS), {vms_mb:.2f} MiB (VMS)")
            logger.warning(f"Mémoire système: {available_gb:.2f} GB disponible sur {total_gb:.2f} GB total ({used_percent}% utilisé)")

            # Stocker les informations psutil
            memory_info['details']['psutil'] = {
                'process': {
                    'rss_mb': rss_mb,
                    'vms_mb': vms_mb
                },
                'system': {
                    'total_gb': total_gb,
                    'available_gb': available_gb,
                    'used_percent': used_percent
                }
            }

            # Alerte si la mémoire est critique
            if available_gb < 2:
                logger.warning("ALERTE: Mémoire système critique! Risque d'erreur OOM (Out Of Memory)")
                memory_info['details']['alert'] = "Mémoire critique"
            elif available_gb < 4:
                logger.warning("ATTENTION: Mémoire système limitée, performances potentiellement réduites")
                memory_info['details']['alert'] = "Mémoire limitée"

        except ImportError:
            logger.warning("Module psutil non disponible, informations système limitées")

        # 3. Informations sur les objets Python
        try:
            # Forcer la collecte des objets non référencés
            gc.collect()

            # Obtenir des statistiques sur les objets
            objects_count = {}
            for obj in gc.get_objects():
                obj_type = type(obj).__name__
                if obj_type in objects_count:
                    objects_count[obj_type] += 1
                else:
                    objects_count[obj_type] = 1

            # Trier par nombre d'objets
            sorted_objects = sorted(objects_count.items(), key=lambda x: x[1], reverse=True)

            logger.warning("Top 10 des types d'objets en mémoire:")
            for obj_type, count in sorted_objects[:10]:
                logger.warning(f"{obj_type}: {count} instances")

            # Stocker les informations sur les objets
            memory_info['details']['objects'] = {
                'top_types': [
                    {'type': obj_type, 'count': count}
                    for obj_type, count in sorted_objects[:10]
                ]
            }

        except:
            logger.warning("Impossible d'obtenir des statistiques sur les objets Python")

        # 4. Afficher le temps écoulé depuis le début du profilage
        if hasattr(self, '_memory_profiling_start_time'):
            elapsed_time = time.time() - self._memory_profiling_start_time
            logger.warning(f"Temps écoulé depuis le début du profilage: {elapsed_time:.2f} secondes")
            memory_info['details']['elapsed_time'] = elapsed_time

        # 5. Stocker l'instantané dans l'historique
        if not hasattr(self, '_memory_snapshots_history'):
            self._memory_snapshots_history = []

        self._memory_snapshots_history.append(memory_info)

        # Limiter l'historique à 10 instantanés pour éviter de consommer trop de mémoire
        if len(self._memory_snapshots_history) > 10:
            self._memory_snapshots_history.pop(0)

        return memory_info