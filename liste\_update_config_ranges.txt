# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 483 à 652
# Type: Méthode de la classe DynamicRangeAdjuster

    def _update_config_ranges(self, new_ranges: Dict[str, Tuple[str, float, float]]) -> Tuple[bool, List[str]]:
        """
        Met à jour les plages dans config.py et dans l'espace de recherche en mémoire.

        Args:
            new_ranges: Les nouvelles plages à appliquer {param_name: (param_type, new_low, new_high)}

        Returns:
            Tuple[bool, List[str]]:
                - True si la mise à jour a réussi, False sinon
                - Liste des paramètres qui n'ont pas pu être ajustés
        """
        try:
            # Liste pour suivre les paramètres qui n'ont pas pu être ajustés
            unadjusted_params = []

            # 1. Mise à jour du fichier config.py
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()

            original_content = config_content  # Sauvegarder le contenu original

            # Dictionnaire pour suivre les paramètres qui ont été ajustés dans le fichier
            adjusted_in_file = {param_name: False for param_name in new_ranges.keys()}

            for param_name, (param_type, new_low, new_high) in new_ranges.items():
                # Construire le motif de recherche pour ce paramètre
                # Différents motifs possibles selon la syntaxe dans config.py
                patterns = [
                    # Format: 'param_name': ('type', low, high)
                    rf"['\"]({re.escape(param_name)})['\"]:\s*\(['\"]({param_type})['\"],\s*([0-9.]+),\s*([0-9.]+)\)",
                    # Format: 'param_name': ('type', low, high, {'log': True})
                    rf"['\"]({re.escape(param_name)})['\"]:\s*\(['\"]({param_type})['\"],\s*([0-9.]+),\s*([0-9.]+),\s*(\{{[^}}]*\}})\)",
                    # Format: 'param_name': ('categorical', [val1, val2, ...])
                    rf"['\"]({re.escape(param_name)})['\"]:\s*\(['\"]categorical['\"]\s*,\s*\[(.*?)\]\)",
                    # Format: self.param_name = value
                    rf"self\.({re.escape(param_name)})\s*=\s*([0-9.]+)"
                ]

                param_adjusted = False
                for pattern in patterns:
                    matches = re.findall(pattern, config_content)
                    if matches:
                        for match in matches:
                            if len(match) >= 5:  # Format: 'param_name': ('type', low, high, {'log': True})
                                matched_name, matched_type, matched_low, matched_high, options = match
                                if matched_name == param_name and matched_type == param_type:
                                    old_str = f"'{param_name}': ('{param_type}', {matched_low}, {matched_high}, {options})"
                                    new_str = f"'{param_name}': ('{param_type}', {new_low}, {new_high}, {options}),  # Plage ajustée automatiquement"
                                    config_content = config_content.replace(old_str, new_str)
                                    logger.info(f"Plage mise à jour dans config.py: {old_str} -> {new_str}")
                                    param_adjusted = True
                                    adjusted_in_file[param_name] = True
                            elif len(match) >= 4:  # Format: 'param_name': ('type', low, high)
                                matched_name, matched_type, matched_low, matched_high = match
                                if matched_name == param_name and matched_type == param_type:
                                    old_str = f"'{param_name}': ('{param_type}', {matched_low}, {matched_high})"
                                    new_str = f"'{param_name}': ('{param_type}', {new_low}, {new_high}),  # Plage ajustée automatiquement"
                                    config_content = config_content.replace(old_str, new_str)
                                    logger.info(f"Plage mise à jour dans config.py: {old_str} -> {new_str}")
                                    param_adjusted = True
                                    adjusted_in_file[param_name] = True
                            elif len(match) >= 2:
                                if pattern.find("categorical") > -1:  # Format: 'param_name': ('categorical', [val1, val2, ...])
                                    matched_name, categories_str = match
                                    if matched_name == param_name and param_type == 'categorical' and isinstance(new_low, list):
                                        # Convertir la liste en chaîne pour la comparaison
                                        new_categories_str = ", ".join([str(val) if not isinstance(val, str) else f"'{val}'" for val in new_low])
                                        old_str = f"'{param_name}': ('categorical', [{categories_str}])"
                                        new_str = f"'{param_name}': ('categorical', [{new_categories_str}]),  # Liste ajustée automatiquement"
                                        config_content = config_content.replace(old_str, new_str)
                                        logger.info(f"Liste catégorielle mise à jour dans config.py: {old_str} -> {new_str}")
                                        param_adjusted = True
                                        adjusted_in_file[param_name] = True
                                else:  # Format: self.param_name = value
                                    matched_name, matched_value = match
                                    if matched_name == param_name:
                                        old_str = f"self.{param_name} = {matched_value}"
                                        new_str = f"self.{param_name} = {new_low}  # Valeur ajustée automatiquement - plage: [{new_low}, {new_high}]"
                                        config_content = config_content.replace(old_str, new_str)
                                        logger.info(f"Valeur mise à jour dans config.py: {old_str} -> {new_str}")
                                        param_adjusted = True
                                        adjusted_in_file[param_name] = True

                # Si le paramètre n'a pas été ajusté dans le fichier, l'ajouter à la liste des paramètres non ajustés
                if not param_adjusted:
                    unadjusted_params.append(param_name)
                else:
                    # Stocker la plage ajustée
                    self.adjusted_ranges[param_name] = (param_type, new_low, new_high)

            # Vérifier si des modifications ont été apportées
            if config_content != original_content:
                # Écrire le contenu mis à jour
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    f.write(config_content)
                logger.warning(f"Fichier config.py mis à jour avec les nouvelles plages")
            else:
                logger.warning(f"Aucune modification apportée à config.py (les motifs de recherche n'ont pas trouvé de correspondance)")

            # Journaliser les paramètres qui n'ont pas pu être ajustés
            if unadjusted_params:
                logger.warning(f"Les paramètres suivants n'ont pas pu être ajustés dans config.py: {unadjusted_params}")

            # 2. Mise à jour de l'espace de recherche en mémoire
            try:
                # Importer le module config
                import sys
                import importlib

                # Recharger le module config pour prendre en compte les modifications
                if 'config' in sys.modules:
                    importlib.reload(sys.modules['config'])
                    import config

                    # Vérifier si optuna_search_space existe dans le module config
                    if hasattr(config, 'optuna_search_space'):
                        # Mettre à jour l'espace de recherche en mémoire
                        for param_name, (param_type, new_low, new_high) in new_ranges.items():
                            if param_name in config.optuna_search_space:
                                # Récupérer les options supplémentaires si elles existent
                                current_param_config = config.optuna_search_space[param_name]
                                options = {}
                                if len(current_param_config) > 3:
                                    options = current_param_config[3]

                                # Mettre à jour la plage
                                if param_type == 'categorical' and isinstance(new_low, list):
                                    # Cas spécial pour les paramètres catégoriels
                                    config.optuna_search_space[param_name] = (param_type, new_low)
                                else:
                                    # Cas standard pour les paramètres numériques
                                    if options:
                                        config.optuna_search_space[param_name] = (param_type, new_low, new_high, options)
                                    else:
                                        config.optuna_search_space[param_name] = (param_type, new_low, new_high)

                                logger.info(f"Espace de recherche mis à jour en mémoire pour {param_name}: {config.optuna_search_space[param_name]}")

                                # Marquer le paramètre comme ajusté même s'il n'a pas été ajusté dans le fichier
                                if param_name in unadjusted_params:
                                    unadjusted_params.remove(param_name)
                                    self.adjusted_ranges[param_name] = (param_type, new_low, new_high)
                            else:
                                # Si le paramètre n'existe pas dans l'espace de recherche en mémoire, l'ajouter à la liste des paramètres non ajustés
                                if param_name not in unadjusted_params:
                                    unadjusted_params.append(param_name)

                        logger.warning(f"Espace de recherche mis à jour en mémoire avec {len(new_ranges) - len(unadjusted_params)} paramètres")
                    else:
                        logger.warning("optuna_search_space n'existe pas dans le module config")
                else:
                    logger.warning("Module config non trouvé dans sys.modules")
            except Exception as e:
                logger.error(f"Erreur lors de la mise à jour de l'espace de recherche en mémoire: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # Ne pas échouer complètement si la mise à jour en mémoire échoue

            # Journaliser à nouveau les paramètres qui n'ont pas pu être ajustés après la mise à jour en mémoire
            if unadjusted_params:
                logger.warning(f"Les paramètres suivants n'ont pas pu être ajustés ni dans config.py ni en mémoire: {unadjusted_params}")

            return True, unadjusted_params

        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de config.py: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False, list(new_ranges.keys())