# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 863 à 893
# Type: Méthode de la classe DynamicRangeAdjuster

    def _update_relative_search_space(self, search_space, new_ranges):
        """
        Met à jour un espace de recherche de type RelativeSearchSpace.

        Args:
            search_space: L'espace de recherche à mettre à jour
            new_ranges: Les nouvelles plages à appliquer
        """
        try:
            # Récupérer l'espace de recherche de base
            if hasattr(search_space, 'base_search_space'):
                base_space = search_space.base_search_space
                # Mettre à jour l'espace de recherche de base
                if hasattr(base_space, 'get') and callable(base_space.get):
                    for param_name, (param_type, new_low, new_high) in new_ranges.items():
                        if param_name in base_space:
                            # Mettre à jour la distribution pour ce paramètre
                            if param_type == 'float':
                                base_space[param_name] = optuna.distributions.FloatDistribution(low=new_low, high=new_high)
                            elif param_type == 'int':
                                base_space[param_name] = optuna.distributions.IntDistribution(low=int(new_low), high=int(new_high))
                            elif param_type == 'categorical' and isinstance(new_low, list):
                                base_space[param_name] = optuna.distributions.CategoricalDistribution(choices=new_low)

                logger.info(f"Espace de recherche RelativeSearchSpace mis à jour avec {len(new_ranges)} paramètres")
            else:
                logger.warning("Structure interne du RelativeSearchSpace non reconnue")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour du RelativeSearchSpace: {e}")
            import traceback
            logger.error(traceback.format_exc())