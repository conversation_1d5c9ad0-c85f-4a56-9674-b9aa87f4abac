# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 11399 à 11434
# Type: Méthode de la classe OptunaOptimizer

        def adaptive_data_generator(X, y, initial_batch_size=1000):
            """
            Générateur adaptatif pour traiter les données par lots.
            Ajuste automatiquement la taille des lots en fonction de la mémoire disponible.
            """
            n_samples = X.shape[0]
            indices = np.arange(n_samples)
            np.random.shuffle(indices)

            # Détecter la mémoire disponible
            try:
                import psutil
                mem = psutil.virtual_memory()
                available_gb = mem.available / (1024**3)

                # Ajuster la taille des lots en fonction de la mémoire disponible
                if available_gb < 2:
                    batch_size = min(100, initial_batch_size)
                    logger.warning(f"Mémoire faible ({available_gb:.1f} GB), taille de batch réduite à {batch_size}")
                elif available_gb < 4:
                    batch_size = min(500, initial_batch_size)
                    logger.warning(f"Mémoire limitée ({available_gb:.1f} GB), taille de batch réduite à {batch_size}")
                elif available_gb < 8:
                    batch_size = min(1000, initial_batch_size)
                    logger.warning(f"Mémoire moyenne ({available_gb:.1f} GB), taille de batch standard à {batch_size}")
                else:
                    batch_size = min(2000, initial_batch_size)
                    logger.warning(f"Mémoire abondante ({available_gb:.1f} GB), taille de batch augmentée à {batch_size}")
            except:
                batch_size = initial_batch_size
                logger.warning(f"Impossible de détecter la mémoire, utilisation de la taille de batch par défaut: {batch_size}")

            for start_idx in range(0, n_samples, batch_size):
                end_idx = min(start_idx + batch_size, n_samples)
                batch_indices = indices[start_idx:end_idx]
                yield X[batch_indices], y[batch_indices]