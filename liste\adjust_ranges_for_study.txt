# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 266 à 406
# Type: Méthode de la classe DynamicRangeAdjuster

    def adjust_ranges_for_study(self, study: optuna.study.Study) -> Dict[str, Tuple[str, float, float]]:
        """
        Ajuste les plages pour une étude donnée en fonction des meilleurs essais.

        Args:
            study: L'étude Optuna pour laquelle ajuster les plages

        Returns:
            Dict: Les plages ajustées {param_name: (param_type, low, high)}
        """
        # Vérifier si l'intervalle minimum entre les ajustements est respecté
        current_time = time.time()
        if current_time - self.last_adjustment_time < self.adjustment_interval:
            logger.debug(f"Ajustement des plages ignoré - intervalle minimum non atteint ({self.adjustment_interval}s)")
            return {}

        # Libérer le verrou de l'étude précédente
        self.study_lock = None

        # Obtenir les meilleurs essais
        best_trials = study.get_trials(states=[optuna.trial.TrialState.COMPLETE])
        if not best_trials:
            logger.info("Aucun essai terminé trouvé dans l'étude. Aucun ajustement nécessaire.")
            return {}

        # Trier les essais par valeur (du meilleur au pire)
        best_trials.sort(key=lambda t: t.value if t.value is not None else float('-inf'),
                         reverse=(study.direction == optuna.study.StudyDirection.MAXIMIZE))

        # Prendre les 10 meilleurs essais au maximum
        best_trials = best_trials[:10]

        # Analyser les paramètres des meilleurs essais
        out_of_range_params = self._identify_out_of_range_params(best_trials)

        if out_of_range_params:
            logger.warning(f"Paramètres hors plage détectés: {list(out_of_range_params.keys())}")

            # Sauvegarder les plages originales si ce n'est pas déjà fait
            if not self.original_ranges:
                self._backup_original_ranges()

            # Ajuster les plages dans config.py
            success, unadjusted_params = self._update_config_ranges(out_of_range_params)

            if not success:
                logger.error("Échec de la mise à jour des plages dans config.py. Aucun ajustement appliqué.")
                return {}

            # Avertir si certains paramètres n'ont pas pu être ajustés
            if unadjusted_params:
                logger.warning(f"ATTENTION: Les paramètres suivants n'ont pas pu être ajustés: {unadjusted_params}")

            # Mettre à jour le timestamp du dernier ajustement
            self.last_adjustment_time = current_time

            # Verrouiller les plages pour cette étude
            self.study_lock = study.study_name

            # Journaliser les ajustements
            logger.warning(f"Plages ajustées pour l'étude {study.study_name}: {self.adjusted_ranges}")

            # Ajouter à l'historique
            self.adjustment_history.append({
                'timestamp': datetime.now().isoformat(),
                'study_name': study.study_name,
                'adjustments': out_of_range_params.copy()
            })

            # Mettre à jour l'espace de recherche dans l'étude en cours
            try:
                # Récupérer le sampler de l'étude
                sampler = study.sampler

                # Vérifier si le sampler a un attribut pour l'espace de recherche
                # Utiliser getattr avec une valeur par défaut pour éviter les erreurs d'attribut
                search_space = getattr(sampler, '_search_space', None)

                if search_space is not None:
                    # Vérifier le type de l'espace de recherche
                    if hasattr(search_space, 'get') and callable(search_space.get):
                        # Cas où _search_space est un dictionnaire ou un objet similaire avec une méthode get()
                        for param_name, (param_type, new_low, new_high) in out_of_range_params.items():
                            # Vérifier si le paramètre existe dans l'espace de recherche
                            if search_space.get(param_name) is not None:
                                # Mettre à jour la distribution pour ce paramètre
                                if param_type == 'float':
                                    search_space[param_name] = optuna.distributions.FloatDistribution(low=new_low, high=new_high)
                                elif param_type == 'int':
                                    search_space[param_name] = optuna.distributions.IntDistribution(low=int(new_low), high=int(new_high))
                                elif param_type == 'categorical' and isinstance(new_low, list):
                                    search_space[param_name] = optuna.distributions.CategoricalDistribution(choices=new_low)

                                logger.info(f"Espace de recherche mis à jour dans le sampler pour {param_name}")

                        logger.warning(f"Espace de recherche mis à jour dans le sampler avec {len(out_of_range_params)} paramètres")
                    elif (isinstance(search_space, optuna.search_space.intersection.IntersectionSearchSpace) or
                          hasattr(search_space, 'intersection_search_space') and callable(search_space.intersection_search_space)):
                        # Cas où _search_space est un objet IntersectionSearchSpace
                        logger.info("Détection d'un IntersectionSearchSpace - Utilisation de la méthode spécifique pour la mise à jour")
                        self._update_intersection_search_space(search_space, out_of_range_params)
                    elif (hasattr(search_space, 'group_search_space') and callable(search_space.group_search_space)):
                        # Cas où _search_space est un objet GroupSearchSpace
                        logger.info("Détection d'un GroupSearchSpace - Utilisation de la méthode spécifique pour la mise à jour")
                        self._update_group_search_space(search_space, out_of_range_params)
                    elif (hasattr(search_space, 'relative_search_space') and callable(search_space.relative_search_space)):
                        # Cas où _search_space est un objet RelativeSearchSpace
                        logger.info("Détection d'un RelativeSearchSpace - Utilisation de la méthode spécifique pour la mise à jour")
                        self._update_relative_search_space(search_space, out_of_range_params)
                    else:
                        # Type d'espace de recherche non reconnu
                        logger.warning(f"Type d'espace de recherche non pris en charge: {type(search_space)}")
                        logger.info("Les nouveaux essais utiliseront l'espace de recherche mis à jour via le rechargement de config.py")
                        # Tenter de recharger l'espace de recherche à partir de config.py
                        self._reload_search_space_from_config(study)
                else:
                    # Vérifier si le sampler a une méthode pour obtenir ou définir l'espace de recherche
                    if hasattr(sampler, 'get_search_space') and callable(getattr(sampler, 'get_search_space')):
                        logger.info("Utilisation de la méthode get_search_space du sampler pour obtenir l'espace de recherche")
                        try:
                            # Tenter d'obtenir l'espace de recherche via la méthode publique
                            search_space = sampler.get_search_space(study)
                            # Mettre à jour l'espace de recherche si possible
                            # (code similaire à ci-dessus)
                        except Exception as e:
                            logger.warning(f"Impossible d'obtenir l'espace de recherche via get_search_space: {e}")
                    else:
                        logger.info("Le sampler n'a pas d'attribut _search_space ni de méthode get_search_space")
                        logger.info("Utilisation de la méthode alternative via config.py pour mettre à jour l'espace de recherche")
                        # Tenter de recharger l'espace de recherche à partir de config.py
                        self._reload_search_space_from_config(study)
            except Exception as e:
                logger.error(f"Erreur lors de la mise à jour de l'espace de recherche dans le sampler: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # Ne pas échouer complètement si la mise à jour du sampler échoue

            return self.adjusted_ranges
        else:
            logger.info("Aucun paramètre hors plage détecté. Aucun ajustement nécessaire.")
            return {}