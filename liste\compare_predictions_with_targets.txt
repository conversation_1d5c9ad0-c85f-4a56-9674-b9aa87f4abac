# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13372 à 13397
# Type: Méthode

def compare_predictions_with_targets(predictions, targets):
    """
    Compare les prédictions avec les cibles, en utilisant directement les indices 0-based.

    Args:
        predictions: Tenseur de prédictions (indices 0-based, 0 ou 1)
        targets: Tenseur de cibles (indices 0-based, 0 ou 1)

    Returns:
        torch.Tensor: Tenseur booléen indiquant les prédictions correctes
    """
    # Vérifier que les prédictions et les cibles sont bien des indices 0-based
    if predictions.min() < 0 or predictions.max() > 1:
        logger.warning(
            f"ATTENTION: Indices invalides détectés dans les prédictions: min={predictions.min().item()}, max={predictions.max().item()}. "
            f"Doivent être 0 (Player) ou 1 (Banker)."
        )

    if targets.min() < 0 or targets.max() > 1:
        logger.warning(
            f"ATTENTION: Indices invalides détectés dans les cibles: min={targets.min().item()}, max={targets.max().item()}. "
            f"Doivent être 0 (Player) ou 1 (Banker)."
        )

    # Comparer directement les prédictions et les cibles (tous deux en indices 0-based)
    return predictions.eq(targets)