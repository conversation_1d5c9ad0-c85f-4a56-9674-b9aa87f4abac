# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 12725 à 12848
# Type: Méthode de la classe OptunaOptimizer

    def create_dynamic_adaptation_callbacks(self, adapted_params):
        """
        Crée des callbacks pour l'adaptation dynamique des hyperparamètres pendant l'entraînement.

        Ces callbacks permettent d'ajuster certains hyperparamètres (comme le learning rate)
        pendant l'entraînement en fonction des performances observées.

        Args:
            adapted_params: Paramètres adaptés initiaux

        Returns:
            Dict: Callbacks pour l'adaptation dynamique
        """
        import numpy as np

        # Définir les callbacks pour différents modèles
        callbacks = {
            'lgbm': {},
            'lstm': {},
            'markov': {}
        }

        # 1. Callback pour LGBM: ajustement du learning rate
        def lgbm_lr_callback(env):
            """Callback pour ajuster le learning rate de LGBM pendant l'entraînement."""
            # Récupérer l'itération actuelle
            iteration = env.iteration

            # Récupérer les métriques d'évaluation
            eval_result = env.evaluation_result_list

            # Récupérer le learning rate actuel
            current_lr = env.params.get('learning_rate', 0.1)

            # Ajuster le learning rate en fonction des performances
            if iteration > 0 and iteration % 10 == 0:
                # Vérifier si les performances stagnent
                if len(env.evaluation_result_list) > 0:
                    current_score = eval_result[0][2]

                    # Réduire le learning rate si les performances stagnent
                    if hasattr(env, 'best_score') and current_score <= env.best_score:
                        env.params['learning_rate'] = current_lr * 0.9
                        print(f"LGBM: Réduction du learning rate à {env.params['learning_rate']}")
                    else:
                        env.best_score = current_score

        callbacks['lgbm']['learning_rate'] = lgbm_lr_callback

        # 2. Callback pour LSTM: ajustement du learning rate et de la taille de batch
        class LSTMDynamicCallback:
            def __init__(self, initial_lr, initial_batch_size):
                self.initial_lr = initial_lr
                self.current_lr = initial_lr
                self.initial_batch_size = initial_batch_size
                self.current_batch_size = initial_batch_size
                self.best_loss = float('inf')
                self.patience = 0
                self.max_patience = 3

            def on_epoch_end(self, epoch, logs=None):
                """Appelé à la fin de chaque époque."""
                if logs is None:
                    logs = {}

                current_loss = logs.get('val_loss', logs.get('loss', float('inf')))

                # Vérifier si les performances s'améliorent
                if current_loss < self.best_loss:
                    self.best_loss = current_loss
                    self.patience = 0
                else:
                    self.patience += 1

                # Ajuster les hyperparamètres si nécessaire
                if self.patience >= self.max_patience:
                    # Réduire le learning rate
                    self.current_lr *= 0.5
                    print(f"LSTM: Réduction du learning rate à {self.current_lr}")

                    # Réinitialiser la patience
                    self.patience = 0

                    # Mettre à jour le learning rate du modèle
                    import tensorflow as tf
                    tf.keras.backend.set_value(self.model.optimizer.lr, self.current_lr)

        # Créer l'instance du callback LSTM
        lstm_lr = adapted_params.get('lstm_learning_rate', 0.001)
        lstm_batch_size = adapted_params.get('lstm_batch_size', 32)
        callbacks['lstm']['dynamic'] = LSTMDynamicCallback(lstm_lr, lstm_batch_size)

        # 3. Callback pour Markov: ajustement du lissage
        class MarkovDynamicAdapter:
            def __init__(self, initial_smoothing):
                self.initial_smoothing = initial_smoothing
                self.current_smoothing = initial_smoothing
                self.performance_history = []

            def update(self, performance):
                """Met à jour le lissage en fonction des performances."""
                self.performance_history.append(performance)

                # Calculer la tendance des performances
                if len(self.performance_history) >= 3:
                    recent_trend = np.mean(np.diff(self.performance_history[-3:]))

                    # Ajuster le lissage en fonction de la tendance
                    if recent_trend < 0:  # Performances en baisse
                        # Augmenter le lissage pour plus de stabilité
                        self.current_smoothing = min(0.5, self.current_smoothing * 1.2)
                        print(f"Markov: Augmentation du lissage à {self.current_smoothing}")
                    elif recent_trend > 0:  # Performances en hausse
                        # Réduire le lissage pour plus de réactivité
                        self.current_smoothing = max(0.001, self.current_smoothing * 0.8)
                        print(f"Markov: Réduction du lissage à {self.current_smoothing}")

                return self.current_smoothing

        # Créer l'instance de l'adaptateur Markov
        markov_smoothing = adapted_params.get('markov_smoothing', 0.1)
        callbacks['markov']['smoothing'] = MarkovDynamicAdapter(markov_smoothing)

        return callbacks