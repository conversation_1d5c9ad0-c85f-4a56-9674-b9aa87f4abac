# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 737 à 796
# Type: Méthode de la classe DynamicRangeAdjuster

    def finalize_adjustments(self, save_to_config: bool = True) -> Dict[str, Tuple[str, float, float]]:
        """
        Finalise les ajustements de plages à la fin de l'optimisation.

        Args:
            save_to_config: Si True, sauvegarde définitivement les plages ajustées dans config.py

        Returns:
            Dict: Les plages finales ajustées
        """
        if not self.adjusted_ranges:
            logger.info("Aucun ajustement de plage à finaliser.")
            return {}

        # Journaliser les ajustements finaux en un seul bloc
        logger.warning("=" * 80)
        logger.warning("FINALISATION DES AJUSTEMENTS DE PLAGES")

        # Construire un message unique avec tous les ajustements
        adjustments_message = "Les plages suivantes ont été ajustées pour inclure les valeurs optimales:\n"

        for param_name, (param_type, new_low, new_high) in self.adjusted_ranges.items():
            original = self.original_ranges.get(param_name, ("inconnu", "inconnu", "inconnu"))
            adjustments_message += f"  {param_name}: [{original[1]}, {original[2]}] -> [{new_low}, {new_high}]\n"

        # Afficher le message complet en une seule fois
        logger.warning(adjustments_message)

        if save_to_config:
            # Créer une sauvegarde de config.py avant modification finale
            backup_path = f"{self.config_path}.bak.{int(time.time())}"
            shutil.copy2(self.config_path, backup_path)
            logger.warning(f"Sauvegarde de config.py créée: {backup_path}")

            # Ajouter un commentaire dans config.py indiquant que les plages ont été ajustées
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                comment = "\n# Les plages suivantes ont été automatiquement ajustées par DynamicRangeAdjuster\n"
                comment += "# pour inclure les valeurs optimales trouvées lors de l'optimisation Optuna.\n"
                comment += f"# Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

                for param_name, (param_type, new_low, new_high) in self.adjusted_ranges.items():
                    comment += f"# {param_name}: [{new_low}, {new_high}]\n"

                with open(self.config_path, 'w', encoding='utf-8') as f:
                    f.write(comment + content)

                logger.warning(f"Les plages ajustées ont été sauvegardées définitivement dans {self.config_path}")

            except Exception as e:
                logger.error(f"Erreur lors de la finalisation des ajustements dans config.py: {e}")
                import traceback
                logger.error(traceback.format_exc())

        # Réinitialiser le verrou d'étude mais conserver les plages ajustées
        self.study_lock = None

        return self.adjusted_ranges