# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13399 à 13415
# Type: Méthode

def get_accuracy(predictions, targets):
    """
    Calcule la précision en comparant les prédictions avec les cibles.
    Utilise directement les indices 0-based.

    Args:
        predictions: Tenseur de prédictions (indices 0-based, 0 ou 1)
        targets: Tenseur de cibles (indices 0-based, 0 ou 1)

    Returns:
        float: Précision (entre 0 et 1)
    """
    # Comparer directement les prédictions et les cibles
    correct = compare_predictions_with_targets(predictions, targets).sum().item()
    total = targets.size(0)

    return correct / total if total > 0 else 0.0