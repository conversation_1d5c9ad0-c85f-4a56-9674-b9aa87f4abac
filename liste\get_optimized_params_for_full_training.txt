# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 3881 à 3935
# Type: Méthode de la classe OptunaOptimizer

    def get_optimized_params_for_full_training(self, use_progressive_validation=True, use_empirical_validation=True):
        """
        Retourne les paramètres optimisés adaptés pour un entraînement complet sur 100% des données.

        Cette méthode adapte les hyperparamètres optimisés sur 10% des données pour qu'ils soient
        optimaux pour un entraînement sur 100% des données, puis les affine avec une validation progressive
        et une validation empirique.

        Args:
            use_progressive_validation: Si True, utilise la validation progressive pour affiner les paramètres
            use_empirical_validation: Si True, valide empiriquement l'efficacité de l'adaptation

        Returns:
            Dict: Paramètres optimisés adaptés pour l'entraînement complet
        """
        if not hasattr(self, 'optimal_batch_params'):
            logger.warning("Aucun paramètre optimisé disponible. Exécutez d'abord l'optimisation.")
            return None

        # Sauvegarder les paramètres originaux
        original_params = self.optimal_batch_params.copy()

        # Adapter les paramètres pour l'entraînement complet
        adapted_params = self.adapt_parameters_for_full_training(self.optimal_batch_params)

        # Affiner les paramètres avec validation progressive si demandé
        if use_progressive_validation:
            adapted_params = self.validate_progressive_adaptation(adapted_params)

        # Valider empiriquement l'efficacité de l'adaptation si demandé
        if use_empirical_validation:
            adapted_params = self.validate_adaptation_empirically(adapted_params, original_params)

        # Créer une configuration avec les paramètres adaptés
        adapted_config = self.config.clone()

        # Appliquer les paramètres adaptés à la configuration
        for param_name, param_value in adapted_params.items():
            if not param_name.startswith('_') and hasattr(adapted_config, param_name):
                setattr(adapted_config, param_name, param_value)

        # S'assurer que Markov est activé dans la configuration finale
        adapted_config.use_markov_model = True
        logger.warning("Markov activé dans la configuration finale pour l'entraînement complet")

        # S'assurer que les paramètres Markov sont correctement définis
        if hasattr(self, 'optimal_batch_params') and 'phase_markov' in self.optimal_batch_params:
            markov_params = self.optimal_batch_params['phase_markov']
            for param_name, param_value in markov_params.items():
                if param_name.startswith('markov_') or param_name == 'max_markov_order':
                    setattr(adapted_config, param_name, param_value)
                    logger.warning(f"Paramètre Markov appliqué: {param_name}={param_value}")

        # Retourner les paramètres adaptés sous forme de dictionnaire
        return {k: v for k, v in adapted_config.__dict__.items() if not k.startswith('_')}