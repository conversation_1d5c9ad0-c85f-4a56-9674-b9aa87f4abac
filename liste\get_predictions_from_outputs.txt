# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13419 à 13432
# Type: Méthode

def get_predictions_from_outputs(outputs):
    """
    Convertit les sorties du modèle en prédictions avec indices 0-based.

    Args:
        outputs: Sorties du modèle (logits)

    Returns:
        torch.Tensor: Prédictions (indices 0-based, 0 ou 1)
    """
    # Obtenir directement les indices des valeurs maximales (0-based)
    _, predicted = torch.max(outputs, 1)

    return predicted