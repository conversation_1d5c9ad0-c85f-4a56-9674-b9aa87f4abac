# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 6308 à 6321
# Type: Méthode de la classe OptunaOptimizer

        def get_size(obj, seen=None):
            """Calculer la taille récursive d'un objet"""
            size = sys.getsizeof(obj)
            if seen is None:
                seen = set()
            obj_id = id(obj)
            if obj_id in seen:
                return 0
            seen.add(obj_id)
            if isinstance(obj, dict):
                size += sum(get_size(k, seen) + get_size(v, seen) for k, v in obj.items())
            elif isinstance(obj, (list, tuple, set, frozenset)):
                size += sum(get_size(i, seen) for i in obj)
            return size