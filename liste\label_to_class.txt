# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13434 à 13445
# Type: Méthode

def label_to_class(label):
    """
    Convertit une étiquette en indice de classe.

    Args:
        label: Étiquette ('player', 'banker', 'PLAYER', 'BANKER')

    Returns:
        int: Indice de classe correspondant (0 ou 1)
    """
    label = label.lower()
    return 0 if label == "player" else 1