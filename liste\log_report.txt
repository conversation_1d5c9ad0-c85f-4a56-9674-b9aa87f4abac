# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13871 à 13880
# Type: Méthode de la classe OptimizationStatsCollector

    def log_report(self, level=logging.WARNING):
        """
        Génère et journalise le rapport récapitulatif.

        Args:
            level: Niveau de journalisation (par défaut: WARNING)
        """
        report = self.generate_report()
        for line in report.split("\n"):
            logger.log(level, line)