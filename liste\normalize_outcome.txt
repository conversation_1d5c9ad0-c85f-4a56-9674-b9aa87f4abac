# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 10230 à 10244
# Type: Méthode de la classe OptunaOptimizer

                    def normalize_outcome(value):
                        value = value.strip().upper()
                        # Vérifier si la valeur est déjà valide
                        if value in ["BANKER", "PLAYER"]:
                            return value
                        # Vérifier les variations de "PLAYER"
                        elif any(pattern in value for pattern in ["PLAY", "P ", " P", "JOUEUR"]):
                            return "PLAYER"
                        # Vérifier les variations de "BANKER"
                        elif any(pattern in value for pattern in ["BANK", "B ", " B", "BANQUIER"]):
                            return "BANKER"
                        # Valeur par défaut
                        else:
                            logger.warning(f"  ALERTE VÉRIFICATION: Valeur invalide: '{value}', correction en 'BANKER'")
                            return "BANKER"