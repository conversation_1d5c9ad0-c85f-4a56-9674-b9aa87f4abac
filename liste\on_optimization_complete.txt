# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13526 à 13531
# Type: Méthode de la classe OptunaThreadManager

        def on_optimization_complete(result):
            duration = time.time() - self.start_time
            logger.info("Optimisation terminée avec succès")
            self.result_queue.put(("success", result, duration))
            if self.callback:
                self.callback(result, duration)