# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13534 à 13539
# Type: Méthode de la classe OptunaThreadManager

        def on_optimization_error(error):
            duration = time.time() - self.start_time
            logger.error(f"Erreur lors de l'optimisation: {error}")
            self.result_queue.put(("error", str(error), duration))
            if self.error_callback:
                self.error_callback(str(error), None, duration)