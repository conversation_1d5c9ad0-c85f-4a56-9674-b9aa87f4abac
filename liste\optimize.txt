# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 3459 à 3526
# Type: Méthode de la classe OptunaOptimizer

    def optimize(self, n_trials=None):
        """
        Point d'entrée principal pour l'optimisation séquentielle.

        Args:
            n_trials: Nombre d'essais à effectuer (si None, utilise la valeur de la configuration)

        Returns:
            Dict: Meilleurs paramètres trouvés
        """
        # Vérifier que les données sont disponibles
        if self.X_lgbm_full is None or self.y_full is None or self.X_lstm_full is None:
            logger.error("Données manquantes pour l'optimisation")
            return self.config.__dict__

        # Calculer le nombre total d'essais
        total_trials = n_trials if n_trials is not None else self.max_trials

        # Afficher les paramètres d'optimisation
        logger.warning("=" * 80)
        logger.warning("STRATÉGIE D'OPTIMISATION SÉQUENTIELLE")
        logger.warning(f"Ressources: {self.cpu_count} cœurs CPU, {self.ram_gb} GB RAM")
        logger.warning("=" * 80)

        # Sauvegarder le nombre d'époques LSTM original
        original_lstm_epochs = self.config.lstm_epochs

        # Définir les paramètres pour chaque phase selon la nouvelle stratégie
        # Phase 0: 100 essais (augmenté de 20 à 100 pour une exploration plus large)
        n_trials_level0 = 100

        # Phase 1, 2, 3 et Markov: 2 essais en parallèle chacune
        n_trials_level1 = 2
        n_trials_markov = 2  # Définir explicitement n_trials_markov=2 pour la phase Markov
        n_trials_level2 = 2
        n_trials_level3 = 2

        # Définir le nombre de workers pour chaque phase
        level0_jobs = min(6, self.cpu_count)  # Utiliser jusqu'à 6 cœurs pour la phase 0
        level1_jobs = 2  # 2 essais en parallèle pour la phase 1
        level2_jobs = 2  # 2 essais en parallèle pour la phase 2
        level3_jobs = 2  # 2 essais en parallèle pour la phase 3

        logger.warning("=" * 80)
        logger.warning("RÉPARTITION DES ESSAIS PAR PHASE")
        logger.warning(f"Phase 0 (Exploration préliminaire): {n_trials_level0} essais ({level0_jobs} workers)")
        logger.warning(f"Phase 1 (LGBM complet): {n_trials_level1} essais ({level1_jobs} workers)")
        logger.warning(f"Phase Markov (Markov activé): {n_trials_markov} essais ({level1_jobs} workers)")
        logger.warning(f"Phase 2 (LSTM activé - 1 époque): {n_trials_level2} essais ({level2_jobs} workers)")
        logger.warning(f"Phase 3 (LSTM complet): {n_trials_level3} essais ({level3_jobs} workers)")
        logger.warning("=" * 80)

        # Vérifier si une fonction d'arrêt a été fournie
        if hasattr(self, 'stop_requested') and callable(self.stop_requested):
            logger.warning("Fonction d'arrêt détectée - l'optimisation pourra être interrompue sur demande")
        else:
            # Définir une fonction d'arrêt par défaut qui renvoie toujours False
            self.stop_requested = lambda: False
            logger.warning("Aucune fonction d'arrêt fournie - l'optimisation ne pourra pas être interrompue")

        # Implémenter la stratégie d'optimisation séquentielle
        return self.launch_sequential_optimization(
            n_trials_level0=n_trials_level0,
            n_trials_level1=n_trials_level1,
            n_trials_markov=2,  # Définir explicitement n_trials_markov=2 pour limiter le nombre d'essais
            n_trials_level2=n_trials_level2,
            n_trials_level3=n_trials_level3
        )