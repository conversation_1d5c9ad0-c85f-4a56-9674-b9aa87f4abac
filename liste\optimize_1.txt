# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13553 à 13563
# Type: Méthode de la classe OptimizerAdapter
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

            def optimize(self, stop_event=None):
                # Ajouter un attribut pour indiquer que l'optimisation doit s'arrêter si demandé
                self.optimizer_instance.stop_requested = lambda: stop_event.is_set() if stop_event else False

                # Mettre à jour la progression initiale
                if self.progress_callback:
                    self.progress_callback(0, "Démarrage de l'optimisation...")

                # Exécuter l'optimisation
                logger.info(f"Début de l'optimisation avec {self.n_trials} essais")
                return self.optimizer_instance.optimize(n_trials=self.n_trials)