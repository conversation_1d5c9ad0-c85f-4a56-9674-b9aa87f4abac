# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 9446 à 9456
# Type: Méthode de la classe OptunaOptimizer

                def prepare_train_data():
                    # Préparer les données d'entraînement - Création directe des tenseurs sans ajustement
                    X_lstm_train_tensor = torch.tensor(X_lstm_train, dtype=torch.float32)
                    y_train_tensor = torch.tensor(y_train, dtype=torch.long)  # Contient directement 1 et 2

                    # Journaliser les classes uniques pour information
                    unique_train_classes = np.unique(y_train)
                    logger.info(f"Classes uniques dans y_train: {unique_train_classes}")
                    logger.info(f"Forme de X_lstm_train: {X_lstm_train.shape}, y_train: {y_train.shape}")

                    return X_lstm_train_tensor, y_train_tensor