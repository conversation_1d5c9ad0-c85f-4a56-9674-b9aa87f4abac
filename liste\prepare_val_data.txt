# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 9459 à 9469
# Type: Méthode de la classe OptunaOptimizer

                def prepare_val_data():
                    # Préparer les données de validation - Création directe des tenseurs sans ajustement
                    X_lstm_val_tensor = torch.tensor(X_lstm_val, dtype=torch.float32)
                    y_val_tensor = torch.tensor(y_val, dtype=torch.long)  # Contient directement 1 et 2

                    # Journaliser les classes uniques pour information
                    unique_val_classes = np.unique(y_val)
                    logger.info(f"Classes uniques dans y_val: {unique_val_classes}")
                    logger.info(f"Forme de X_lstm_val: {X_lstm_val.shape}, y_val: {y_val.shape}")

                    return X_lstm_val_tensor, y_val_tensor