# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13485 à 13498
# Type: Méthode de la classe OptunaThreadManager

    def request_stop(self):
        """
        Demande l'arrêt de l'optimisation en cours.

        Returns:
            bool: True si la demande d'arrêt a été envoyée, False sinon
        """
        if self.is_optimization_running():
            logger.warning("Demande d'arrêt de l'optimisation en cours...")
            self.stop_requested = True
            if self.threaded_optimizer:
                return self.threaded_optimizer.stop()
            return True
        return False