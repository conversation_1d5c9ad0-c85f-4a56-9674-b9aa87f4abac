# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13605 à 13637
# Type: Méthode de la classe OptimizationStatsCollector

    def reset(self):
        """Réinitialise toutes les statistiques collectées."""
        # Statistiques générales
        self.total_predictions = 0
        self.wait_predictions = 0
        self.non_wait_predictions = 0

        # Statistiques détaillées
        self.decision_thresholds = []
        self.confidence_values = []
        self.uncertainty_values = []
        self.wait_ratios = []
        self.consecutive_wait_counts = []
        self.player_probabilities = []
        self.banker_probabilities = []
        self.recommendation_scores = []

        # Statistiques par phase
        self.phase_stats = defaultdict(lambda: {
            'count': 0,
            'wait_count': 0,
            'non_wait_count': 0,
            'decision_thresholds': [],
            'confidence_values': [],
            'uncertainty_values': [],
            'recommendation_scores': []
        })

        # Compteurs pour les alertes de diagnostic
        self.diagnostic_alerts = defaultdict(int)

        # Flag pour indiquer si le rapport a déjà été généré
        self.report_generated = False