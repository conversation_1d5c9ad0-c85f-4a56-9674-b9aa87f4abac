# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 12995 à 13002
# Type: Méthode de la classe HybridBaccaratPredictor

            def success_callback(best_params, duration):
                if ui_available:
                    self.root.after(0, lambda: self._finalize_optuna_optimization(
                        True, best_params, duration, None
                    ))
                else:
                    logger_instance.info(f"Optimisation terminée avec succès en {duration:.2f} secondes")
                    logger_instance.info(f"Meilleurs paramètres: {best_params}")