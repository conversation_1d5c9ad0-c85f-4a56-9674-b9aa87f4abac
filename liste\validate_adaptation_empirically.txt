# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 3701 à 3879
# Type: Méthode de la classe OptunaOptimizer

    def validate_adaptation_empirically(self, adapted_params, original_params, validation_size=0.3, num_trials=3):
        """
        Valide empiriquement l'efficacité de l'adaptation en comparant les performances
        des paramètres adaptés et non adaptés sur un sous-ensemble représentatif.

        Args:
            adapted_params: Paramètres adaptés pour l'entraînement complet
            original_params: Paramètres originaux optimisés sur 10% des données
            validation_size: Proportion des données complètes à utiliser pour la validation
            num_trials: Nombre d'essais pour chaque configuration

        Returns:
            Dict: Résultats de la validation empirique
        """
        import os
        import numpy as np
        import time
        from datetime import datetime

        logger.warning("=" * 80)
        logger.warning("VALIDATION EMPIRIQUE DE L'ADAPTATION")
        logger.warning("=" * 80)

        # Vérifier si le fichier historical_data.txt existe
        historical_data_path = os.path.join(os.getcwd(), "historical_data.txt")
        if not os.path.exists(historical_data_path):
            logger.warning(f"Fichier {historical_data_path} non trouvé. Validation empirique impossible.")
            return adapted_params

        try:
            # Compter le nombre total de lignes dans le fichier
            with open(historical_data_path, 'r') as f:
                total_lines = sum(1 for _ in f)

            # Calculer le nombre de lignes à utiliser pour la validation
            validation_lines = int(total_lines * validation_size)

            logger.warning(f"Validation empirique sur {validation_lines} lignes ({validation_size*100:.1f}% du total)")

            # Sélectionner aléatoirement les lignes pour la validation
            validation_indices = np.random.choice(total_lines, size=validation_lines, replace=False)
            validation_indices = set(validation_indices)

            # Charger et prétraiter les données de validation
            X_lgbm_validation = []
            X_lstm_validation = []
            y_validation = []

            # Charger les données de validation
            with open(historical_data_path, 'r') as f:
                for i, line in enumerate(f):
                    if i in validation_indices:
                        # Prétraiter la ligne
                        X_lgbm_line, X_lstm_line, y_line = self._preprocess_line(line)

                        # Accumuler les données prétraitées
                        X_lgbm_validation.append(X_lgbm_line)
                        X_lstm_validation.append(X_lstm_line)
                        y_validation.append(y_line)

            # Convertir en arrays NumPy
            X_lgbm_validation = np.array(X_lgbm_validation)
            X_lstm_validation = np.array(X_lstm_validation)
            y_validation = np.array(y_validation)

            logger.warning(f"Données de validation chargées: {len(y_validation)} échantillons")

            # Créer les configurations pour les paramètres adaptés et originaux
            adapted_config = self.config.clone()
            original_config = self.config.clone()

            # Appliquer les paramètres adaptés et originaux
            for param_name, param_value in adapted_params.items():
                if not param_name.startswith('_') and hasattr(adapted_config, param_name):
                    setattr(adapted_config, param_name, param_value)

            for param_name, param_value in original_params.items():
                if not param_name.startswith('_') and hasattr(original_config, param_name):
                    setattr(original_config, param_name, param_value)

            # Évaluer les deux configurations plusieurs fois
            adapted_scores = []
            original_scores = []
            adapted_metrics = []
            original_metrics = []

            for trial in range(num_trials):
                logger.warning(f"Essai {trial+1}/{num_trials}")

                # Évaluer les paramètres adaptés
                logger.warning("Évaluation des paramètres adaptés...")
                adapted_score, adapted_metric = self._evaluate_config(adapted_config,
                                                                     X_lgbm=X_lgbm_validation,
                                                                     X_lstm=X_lstm_validation,
                                                                     y=y_validation)
                adapted_scores.append(adapted_score)
                adapted_metrics.append(adapted_metric)

                # Évaluer les paramètres originaux
                logger.warning("Évaluation des paramètres originaux...")
                original_score, original_metric = self._evaluate_config(original_config,
                                                                       X_lgbm=X_lgbm_validation,
                                                                       X_lstm=X_lstm_validation,
                                                                       y=y_validation)
                original_scores.append(original_score)
                original_metrics.append(original_metric)

            # Calculer les moyennes et écarts-types
            adapted_mean = np.mean(adapted_scores)
            adapted_std = np.std(adapted_scores)
            original_mean = np.mean(original_scores)
            original_std = np.std(original_scores)

            # Calculer l'amélioration relative
            improvement = (adapted_mean - original_mean) / abs(original_mean) * 100

            # Journaliser les résultats
            logger.warning(f"Résultats de la validation empirique:")
            logger.warning(f"  - Paramètres adaptés: score moyen = {adapted_mean:.4f} ± {adapted_std:.4f}")
            logger.warning(f"  - Paramètres originaux: score moyen = {original_mean:.4f} ± {original_std:.4f}")
            logger.warning(f"  - Amélioration relative: {improvement:.2f}%")

            # Décider si l'adaptation est efficace
            is_effective = adapted_mean > original_mean

            if is_effective:
                logger.warning("L'adaptation est efficace! Les paramètres adaptés seront utilisés.")

                # Ajouter les résultats de la validation empirique aux méta-informations
                if '_meta_info' not in adapted_params:
                    adapted_params['_meta_info'] = {}

                adapted_params['_meta_info']['empirical_validation'] = {
                    'adapted_mean': float(adapted_mean),
                    'adapted_std': float(adapted_std),
                    'original_mean': float(original_mean),
                    'original_std': float(original_std),
                    'improvement': float(improvement),
                    'validation_size': validation_size,
                    'num_trials': num_trials,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }

                return adapted_params
            else:
                logger.warning("L'adaptation n'est pas efficace! Les paramètres originaux seront utilisés avec des ajustements mineurs.")

                # Utiliser les paramètres originaux avec des ajustements mineurs pour la taille des données
                adjusted_params = original_params.copy()

                # Ajuster uniquement les paramètres critiques liés à la taille des données
                if 'lgbm_min_child_samples' in adjusted_params:
                    adjusted_params['lgbm_min_child_samples'] = int(adjusted_params['lgbm_min_child_samples'] * 2)

                if 'lstm_batch_size' in adjusted_params:
                    adjusted_params['lstm_batch_size'] = max(8, int(adjusted_params['lstm_batch_size'] * 0.5))

                # Ajouter les résultats de la validation empirique aux méta-informations
                if '_meta_info' not in adjusted_params:
                    adjusted_params['_meta_info'] = {}

                adjusted_params['_meta_info']['empirical_validation'] = {
                    'adapted_mean': float(adapted_mean),
                    'adapted_std': float(adapted_std),
                    'original_mean': float(original_mean),
                    'original_std': float(original_std),
                    'improvement': float(improvement),
                    'validation_size': validation_size,
                    'num_trials': num_trials,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'used_original': True
                }

                return adjusted_params

        except Exception as e:
            logger.error(f"Erreur lors de la validation empirique: {e}")
            logger.warning("Utilisation des paramètres adaptés sans validation empirique.")
            return adapted_params