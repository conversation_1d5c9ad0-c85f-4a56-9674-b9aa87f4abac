# TÂCHE : ANALYSE EXHAUSTIVE ET DOCUMENTATION DÉTAILLÉE DE MÉTHODES SYSTÈME ML
- Note : Si tu trouves des doublons tu dois les garder et mentionner qu'ils sont des doublons ou des doublons de doublons etc.
- Tout ce que tu écris dans le fichier descriptif.txt tu le copieras dans le fichier descriptif.txt correspondant d'un des sous-dossier à chaque fois que nécessaire
- Une fois que tu as fait l'analyse profonde et détaillée de chaque fichier texte tu dois le déplacer dans le bon dossier/sous-dossier.
- Puis t'assurer que le descriptif est bien écrit dans le fichier descriptif.txt correspondant et dans le fichier descriptif.txt principal.
- Tu dois copier et coller le descriptif du fichier descriptif.txt principal au fichier descriptif.txt du sous-dossier correspondant à chaque fois.

# CONTEXTE
- Tu dois analyser un système [TYPE_SYSTÈME] dont les méthodes ont été extraites en fichiers texte individuels.
- Le répertoire de travail contient :
  * Un fichier Python principal ([NOM_FICHIER_PRINCIPAL].py)
  * Tous les fichiers .txt des méthodes extraites (dans le même répertoire)
- Ces fichiers doivent être intégralement lus et organisés dans des sous-dossiers catégoriels que tu créeras.
- Chaque fichier texte contient une méthode et porte le nom de cette méthode.
- IMPORTANT : Chaque fichier texte contient dans ses 4 PREMIÈRES LIGNES des informations cruciales sur la classe d'origine et le fichier source.

# RÉPERTOIRE DE TRAVAIL
- Le répertoire de travail VSCode contient déjà tous les fichiers nécessaires :
  * [NOM_FICHIER_PRINCIPAL].py (fichier source principal)
  * Tous les fichiers .txt des méthodes extraites
- Aucun dossier externe à spécifier, tout est dans le répertoire courant

# OBJECTIF PRINCIPAL
- Créer une plateforme de maintenance précise et efficace du code contenu dans [NOM_FICHIER_PRINCIPAL].py
- Comprendre l'architecture globale du système à travers l'analyse des classes et méthodes
- Organiser tous les fichiers .txt du répertoire courant dans une structure catégorielle
- Créer un fichier Descriptif.txt principal et des fichiers descriptifs dans chaque sous-dossier

# INSTRUCTIONS OBLIGATOIRES

## PHASE 1 : EXPLORATION ET COMPRÉHENSION GLOBALE

### Lecture des Métadonnées
- Pour CHAQUE fichier .txt, lis OBLIGATOIREMENT les 4 PREMIÈRES LIGNES
- Ces lignes contiennent : classe d'origine, fichier source, numéros de lignes, contexte
- Format typique :
  ```
  # Classe: NomClasse
  # Fichier: nom_fichier.py
  # Lignes: X-Y
  # Contexte: Description courte
  ```


### Échantillonnage Représentatif
- Sélectionne 15-20 fichiers représentatifs couvrant différentes classes
- Assure-toi d'avoir au moins 2-3 méthodes par classe principale identifiée
- Lis intégralement ces fichiers pour comprendre :
* Architecture globale du système
* Relations entre classes
* Domaines fonctionnels principaux
* Patterns de nommage et organisation

### Analyse Architecturale
- Identifie toutes les classes présentes dans le système
- Comprends le rôle de chaque classe dans l'architecture globale
- Détermine les domaines fonctionnels principaux
- Identifie les dépendances et interactions entre classes

## PHASE 2 : CRÉATION STRUCTURE CATÉGORIELLE

### Définition des Catégories
Basé sur ton analyse architecturale, crée 6-10 catégories logiques :
- **Par domaine fonctionnel** (ex: Optimisation, Évaluation, Cache)
- **Par responsabilité** (ex: Analyse, Configuration, Utilitaires)
- **Par niveau d'abstraction** (ex: Classes principales, Callbacks, Internes)

### Exemples de catégories typiques :
- **AnalyseResultats** : Méthodes d'analyse, rapports, métriques
- **ClassesPrincipales** : Définitions de classes et constructeurs
- **GestionRessources** : Cache, mémoire, optimisations système
- **MethodesOptimisation** : Algorithmes d'optimisation et stratégies
- **UtilitairesInternes** : Fonctions d'aide et outils internes
- **ConfigurationEtudes** : Configuration et paramétrage
- **CallbacksGestionnaires** : Callbacks et gestionnaires d'événements
- **MethodesEvaluation** : Validation et évaluation de performance

### Création Structure Dossiers
```
RÉPERTOIRE_COURANT/
├── [NOM_FICHIER_PRINCIPAL].py (fichier source déjà présent)
├── fichier1.txt, fichier2.txt, ... (méthodes à organiser)
├── Descriptif.txt (documentation maître - À CRÉER)
├── [Catégorie1]/
│   └── Descriptif.txt (À CRÉER)
├── [Catégorie2]/
│   └── Descriptif.txt (À CRÉER)
└── [CatégorieN]/
    └── Descriptif.txt (À CRÉER)
```

## PHASE 3 : ANALYSE DÉTAILLÉE SYSTÉMATIQUE

### Lecture Exhaustive
- Lis intégralement chaque fichier texte du répertoire de travail courant
- Utilise TOUJOURS le paramètre -Raw pour lecture complète
- Ne saute AUCUN fichier, traite 100% des fichiers .txt présents
- Lis chaque méthode autant de fois que nécessaire pour compréhension complète

### Analyse Détaillée Requise
Pour chaque méthode, documente :
- **FONCTION :** Description précise du rôle de la méthode
- **PARAMÈTRES :** Liste complète avec types et descriptions
- **FONCTIONNEMENT DÉTAILLÉ :** Analyse ligne par ligne des étapes principales
- **RETOUR :** Type et description de ce qui est retourné
- **UTILITÉ :** Contexte d'utilisation dans le système global
- **CLASSE D'ORIGINE :** Extraite des 4 premières lignes
- **NUMÉROS DE LIGNES :** Position exacte dans le fichier source

## PHASE 4 : DOCUMENTATION ET ORGANISATION

### Structure Descriptif.txt Principal
```
DESCRIPTIF DÉTAILLÉ DES MÉTHODES - [TYPE_SYSTÈME]
================================================================================

Ce fichier contient la description détaillée de toutes les méthodes du système
[TYPE_SYSTÈME], organisées par sections fonctionnelles.

STRUCTURE DU SYSTÈME (basée sur l'analyse architecturale) :
- **[Catégorie1]** : Description du domaine fonctionnel
- **[Catégorie2]** : Description du domaine fonctionnel
- **[CatégorieN]** : Description du domaine fonctionnel

TOTAL : [X] MÉTHODES ANALYSÉES

Dernière mise à jour: [DATE] - Création plateforme maintenance

================================================================================
SECTION 1 : [CATÉGORIE1] ([X] MÉTHODES)
================================================================================

Description du domaine fonctionnel de cette catégorie.

1. nom_fichier.txt (Classe.méthode - DESCRIPTION COURTE)
   - Lignes X-Y dans [NOM_FICHIER_PRINCIPAL].py (Z lignes)
   - FONCTION : Description précise du rôle de la méthode
   - PARAMÈTRES :
     * self - Instance de la classe
     * param1 (type) - Description paramètre 1
     * param2 (type, optionnel) - Description paramètre 2
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ÉTAPE 1 :** Description première étape avec détails techniques
     * **ÉTAPE 2 :** Description deuxième étape avec logique
     * **VALIDATION :** Vérifications et contrôles effectués
     * **TRAITEMENT :** Algorithme principal et calculs
     * **FINALISATION :** Préparation résultat et nettoyage
   - RETOUR : Type - Description de ce qui est retourné
   - UTILITÉ : Contexte d'utilisation dans le système global


2. [méthode suivante avec même format détaillé...]
```


### Documentation Sous-Dossiers
- **Format identique** au Descriptif.txt principal
- **Contenu filtré** : uniquement les méthodes de cette catégorie
- **Structure complète** : en-tête, description catégorie, méthodes détaillées
- **Numérotation continue** : 1, 2, 3... pour les méthodes de la catégorie
- **Même niveau de détail** : 20-30 lignes minimum par méthode

### Classification et Déplacement
- Détermine catégorie appropriée pour chaque méthode basée sur :
  * Classe d'origine
  * Fonctionnalité
  * Domaine d'application
  * Niveau d'abstraction
- Déplace chaque fichier .txt dans son sous-dossier approprié
- Met à jour les fichiers Descriptif.txt correspondants

## PHASE 5 : CRITÈRES DE QUALITÉ ET VÉRIFICATION

### Critères de Qualité Obligatoires
- **AUCUNE description courte** (ex: "Calcule la précision" ❌)
- **AUCUNE mention "FICHIER NON TROUVÉ"** sans descriptif détaillé
- **Descriptifs détaillés UNIQUEMENT** (minimum 20-30 lignes par méthode)
- **Analyse fonctionnelle complète** de chaque méthode avec étapes détaillées
- **Métadonnées complètes** (classe, lignes, fichier source)
- **Format standardisé** : FONCTION, PARAMÈTRES, FONCTIONNEMENT DÉTAILLÉ, RETOUR, UTILITÉ
- **Gestion doublons** : Conserver et mentionner "DOUBLON X" dans titre
- **Numéros de lignes précis** : Format "Lignes X-Y dans fichier.py (Z lignes)"
- **Paramètres structurés** : Format "* param (type) - Description"
- **Fonctionnement par étapes** : Format "* **ÉTAPE :** Description détaillée"

### Vérification et Correction
- Vérifie systématiquement chaque fichier Descriptif.txt créé
- Identifie toute description superficielle ou incomplète
- Corrige immédiatement en créant des descriptifs détaillés complets
- Continue jusqu'à 100% de descriptifs détaillés sans exception

### Gestion des Cas Spéciaux
- Si métadonnées manquantes : utilise codebase-retrieval pour analyse
- Si fichier source illisible : crée descriptif basé sur logique fonctionnelle
- Si méthode mal classée : déplace dans le bon dossier
- Si doublons : conserve et mentionne "DOUBLON X" dans description

## PHASE 6 : VALIDATION FINALE

### Critères de Complétion
La tâche est terminée à 100% UNIQUEMENT quand :
- ✅ Exploration représentative effectuée (15-20 fichiers analysés)
- ✅ Architecture globale comprise et documentée
- ✅ Structure catégorielle créée (6-10 sous-dossiers)
- ✅ Tous les fichiers .txt ont été lus intégralement
- ✅ Chaque méthode a un descriptif détaillé complet (20-30 lignes minimum)
- ✅ Métadonnées extraites (classe, lignes, fichier) pour chaque méthode
- ✅ Descriptif.txt principal contient TOUTES les méthodes
- ✅ Chaque sous-dossier a son Descriptif.txt avec méthodes correspondantes
- ✅ Tous les fichiers .txt sont déplacés dans leurs sous-dossiers appropriés
- ✅ Aucune description courte ou "FICHIER NON TROUVÉ" ne subsiste
- ✅ Vérification exhaustive confirmée

### Structure Finale Attendue Complète
```
RÉPERTOIRE_COURANT/
├── [NOM_FICHIER_PRINCIPAL].py (fichier source original)
├── Descriptif.txt (3000+ lignes - documentation maître complète)
├── [Catégorie1]/
│   ├── Descriptif.txt (format identique, méthodes filtrées)
│   ├── methode1.txt (méthode déplacée depuis racine)
│   ├── methode2.txt
│   └── ...
├── [Catégorie2]/
│   ├── Descriptif.txt
│   ├── methode3.txt
│   └── ...
└── [CatégorieN]/
    ├── Descriptif.txt
    └── ...

RÉSULTAT FINAL : Plateforme de maintenance professionnelle avec :
- Localisation précise de chaque méthode
- Documentation exhaustive (20-30 lignes/méthode)
- Navigation intuitive par domaines fonctionnels
- Traçabilité complète code ↔ documentation
- Maintenance efficace et sécurisée
- Organisation complète depuis répertoire VSCode unique
```

# APPROCHE MÉTHODOLOGIQUE SÉQUENTIELLE
1. **Exploration Métadonnées** : Lecture 4 premières lignes de tous les fichiers
2. **Échantillonnage Représentatif** : Analyse 15-20 fichiers clés
3. **Compréhension Architecturale** : Identification classes et domaines
4. **Création Structure** : Définition catégories et création dossiers
5. **Traitement Systématique** : Analyse complète fichier par fichier
6. **Documentation** : Création descriptifs détaillés au fur et à mesure
7. **Classification** : Regroupement par domaines fonctionnels
8. **Organisation** : Déplacement fichiers dans sous-dossiers appropriés
9. **Vérification** : Contrôle qualité exhaustif de tous les descriptifs
10. **Validation Finale** : Confirmation 100% sans exception

# RAPPEL CRITIQUE
- COMMENCE TOUJOURS par lire les métadonnées (4 premières lignes)
- COMPRENDS l'architecture avant de créer les catégories
- Ne t'arrête JAMAIS avant d'avoir traité 100% des fichiers
- Privilégie la qualité exhaustive sur la rapidité
- Vérifie systématiquement tes propres affirmations de complétion
- Le résultat final doit être une plateforme de maintenance professionnelle permettant localisation précise et maintenance efficace de chaque méthode du système

# VARIABLES À REMPLACER SELON LE PROJET
- [TYPE_SYSTÈME] : ex. "prédiction ML", "optimisation Optuna", "système de trading"
- [NOM_FICHIER_PRINCIPAL] : ex. "optuna_optimizer", "trading_bot", "ml_predictor"

# UTILISATION SIMPLIFIÉE
1. Ouvrir le répertoire contenant le fichier .py et tous les fichiers .txt dans VSCode
2. Remplacer les variables [TYPE_SYSTÈME] et [NOM_FICHIER_PRINCIPAL] dans ce prompt
3. Lancer l'analyse avec ce prompt adapté
4. L'IA organisera automatiquement tous les fichiers du répertoire courant
