# TÂCHE : ANALYSE EXHAUSTIVE ET DOCUMENTATION DÉTAILLÉE DE MÉTHODES SYSTÈME ML
- Note : Si tu trouves des doublons tu dois les garder et mentionner qu'ils sont des doublons ou des doublons de doublons etc.
- Tout ce que tu écris dans le fichier descriptif.txt tu le copieras dans le fichier descriptif.txt correspondant d'un des sous-dossier à chaque fois que nécessaire
- Une fois que tu as fait l'analyse profonde et détaillée de chaque fichier texte tu dois le déplacer dans le bon dossier/sous-dossier.
- Puis t'assurer que le descriptif est bien écrit dans le fichier descriptif.txt correspondant et dans le fichier descriptif.txt principal.
- Tu dois copier et coller le descriptif du fichier descriptif.txt principal au fichier descriptif.txt du sous-dossier correspondant à chaque fois.

# CONTEXTE
- Tu dois analyser un système ML dont les méthodes ont été extraites en fichiers texte individuels.
- Le répertoire de travail VSCode contient déjà :
  * UN SEUL fichier Python principal (.py) - TU DOIS LE DÉTECTER AUTOMATIQUEMENT
  * Tous les fichiers .txt des méthodes extraites (dans le même répertoire)
- Ces fichiers doivent être intégralement lus et organisés dans des sous-dossiers catégoriels que tu créeras dans le même répertoire.
- Chaque fichier texte contient une méthode et porte le nom de cette méthode.
- IMPORTANT : Chaque fichier texte contient dans ses 4 PREMIÈRES LIGNES des informations cruciales sur la classe d'origine et le fichier source.

# RÉPERTOIRE DE TRAVAIL
- Tous les fichiers sont déjà présents dans le répertoire de travail VSCode actuel
- TU DOIS AUTOMATIQUEMENT :
  1. DÉTECTER le seul fichier .py présent dans le répertoire (c'est le fichier source principal)
  2. IDENTIFIER tous les fichiers .txt des méthodes extraites
- Tu travailleras directement dans ce répertoire pour créer la structure organisée

# DÉTECTION AUTOMATIQUE DU FICHIER PRINCIPAL
- ÉTAPE OBLIGATOIRE : Commence par lister les fichiers du répertoire de travail
- IDENTIFIE le fichier .py unique (ignore plateformeok3.txt et autres fichiers non-Python)
- UTILISE ce fichier .py comme [NOM_FICHIER_PRINCIPAL] pour toute la documentation

# OBJECTIF PRINCIPAL
- Créer une plateforme de maintenance précise et efficace du code contenu dans le fichier Python principal détecté
- Comprendre l'architecture globale du système à travers l'analyse des classes et méthodes
- Organiser tous les fichiers .txt du répertoire de travail dans une structure catégorielle
- Créer un fichier Descriptif.txt principal et des fichiers descriptifs dans chaque sous-dossier créé
- Consulter chaque fichier Descriptif.txt dans chaque dossier et/ou sous-dossier contenant un descriptif détaillé de CHAQUE méthode et de son fonctionnement

# INSTRUCTIONS OBLIGATOIRES

## PHASE 1 : EXPLORATION ET COMPRÉHENSION GLOBALE

### Lecture des Métadonnées
- Pour CHAQUE fichier .txt, lis OBLIGATOIREMENT les 4 PREMIÈRES LIGNES
- Ces lignes contiennent : classe d'origine, fichier source, numéros de lignes, contexte
- Format typique :
  ```
  # Classe: NomClasse
  # Fichier: nom_fichier.py
  # Lignes: X-Y
  # Contexte: Description courte
  ```


### Échantillonnage Représentatif
- Sélectionne 15-20 fichiers représentatifs couvrant différentes classes
- Assure-toi d'avoir au moins 2-3 méthodes par classe principale identifiée
- Lis intégralement ces fichiers pour comprendre :
* Architecture globale du système
* Relations entre classes
* Domaines fonctionnels principaux
* Patterns de nommage et organisation

### Analyse Architecturale
- Identifie toutes les classes présentes dans le système
- Comprends le rôle de chaque classe dans l'architecture globale
- Détermine les domaines fonctionnels principaux
- Identifie les dépendances et interactions entre classes

## PHASE 2 : CRÉATION STRUCTURE CATÉGORIELLE

### Définition des Catégories
Basé sur ton analyse architecturale, crée 6-10 catégories logiques :
- **Par domaine fonctionnel** (ex: Optimisation, Évaluation, Cache)
- **Par responsabilité** (ex: Analyse, Configuration, Utilitaires)
- **Par niveau d'abstraction** (ex: Classes principales, Callbacks, Internes)

### Exemples de catégories typiques :
- **AnalyseResultats** : Méthodes d'analyse, rapports, métriques
- **ClassesPrincipales** : Définitions de classes et constructeurs
- **GestionRessources** : Cache, mémoire, optimisations système
- **MethodesOptimisation** : Algorithmes d'optimisation et stratégies
- **UtilitairesInternes** : Fonctions d'aide et outils internes
- **ConfigurationEtudes** : Configuration et paramétrage
- **CallbacksGestionnaires** : Callbacks et gestionnaires d'événements
- **MethodesEvaluation** : Validation et évaluation de performance

### Catégorie Spéciale pour Fichiers "class"
- **Anciennesclasses** : Dossier spécial pour tous les fichiers dont le nom commence par "class"
- Ces fichiers seront traités EN DERNIER après toutes les autres catégories
- Ce dossier aura aussi son propre fichier Descriptif.txt avec le même format que les autres

### Création Structure Dossiers
```
RÉPERTOIRE_DE_TRAVAIL/
├── [NOM_FICHIER_PRINCIPAL].py (fichier source déjà présent)
├── fichier1.txt, fichier2.txt, ... (méthodes à organiser)
├── Descriptif.txt (documentation maître - À CRÉER)
├── [Catégorie1]/
│   └── Descriptif.txt (À CRÉER)
├── [Catégorie2]/
│   └── Descriptif.txt (À CRÉER)
├── [CatégorieN]/
│   └── Descriptif.txt (À CRÉER)
└── Anciennesclasses/
    ├── Descriptif.txt (À CRÉER)
    └── [fichiers class*.txt à déplacer EN DERNIER]
```

## PHASE 3 : ANALYSE DÉTAILLÉE SYSTÉMATIQUE

### Lecture Exhaustive
- Lis intégralement chaque fichier texte du répertoire de travail
- Utilise TOUJOURS le paramètre -Raw pour lecture complète
- Ne saute AUCUN fichier, traite 100% des fichiers .txt présents
- Lis chaque méthode autant de fois que nécessaire pour compréhension complète
- **IMPORTANT** : Traite d'abord TOUS les fichiers qui ne commencent PAS par "class"
- Les fichiers commençant par "class" seront traités EN DERNIER dans le dossier Anciennesclasses

### Analyse Détaillée Requise
Pour chaque méthode, documente :
- **FONCTION :** Description précise du rôle de la méthode
- **PARAMÈTRES :** Liste complète avec types et descriptions
- **FONCTIONNEMENT DÉTAILLÉ :** Analyse ligne par ligne des étapes principales
- **RETOUR :** Type et description de ce qui est retourné
- **UTILITÉ :** Contexte d'utilisation dans le système global
- **CLASSE D'ORIGINE :** Extraite des 4 premières lignes
- **NUMÉROS DE LIGNES :** Position exacte dans le fichier source

## PHASE 4 : DOCUMENTATION ET ORGANISATION

### Structure Descriptif.txt Principal
```
DESCRIPTIF DÉTAILLÉ DES MÉTHODES - [TYPE_SYSTÈME]
================================================================================

Ce fichier contient la description détaillée de toutes les méthodes du système
[TYPE_SYSTÈME], organisées par sections fonctionnelles.

STRUCTURE DU SYSTÈME (basée sur l'analyse architecturale) :
- **[Catégorie1]** : Description du domaine fonctionnel
- **[Catégorie2]** : Description du domaine fonctionnel
- **[CatégorieN]** : Description du domaine fonctionnel

TOTAL : [X] MÉTHODES ANALYSÉES

Dernière mise à jour: [DATE] - Création plateforme maintenance

================================================================================
SECTION 1 : [CATÉGORIE1] ([X] MÉTHODES)
================================================================================

Description du domaine fonctionnel de cette catégorie.

1. nom_fichier.txt (Classe.méthode - DESCRIPTION COURTE)
   - Lignes X-Y dans [NOM_FICHIER_PRINCIPAL].py (Z lignes)
   - FONCTION : Description précise du rôle de la méthode
   - PARAMÈTRES :
     * self - Instance de la classe
     * param1 (type) - Description paramètre 1
     * param2 (type, optionnel) - Description paramètre 2
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ÉTAPE 1 :** Description première étape avec détails techniques
     * **ÉTAPE 2 :** Description deuxième étape avec logique
     * **VALIDATION :** Vérifications et contrôles effectués
     * **TRAITEMENT :** Algorithme principal et calculs
     * **FINALISATION :** Préparation résultat et nettoyage
   - RETOUR : Type - Description de ce qui est retourné
   - UTILITÉ : Contexte d'utilisation dans le système global


2. [méthode suivante avec même format détaillé...]
```


### Documentation Sous-Dossiers
- **Format identique** au Descriptif.txt principal
- **Contenu filtré** : uniquement les méthodes de cette catégorie
- **Structure complète** : en-tête, description catégorie, méthodes détaillées
- **Numérotation continue** : 1, 2, 3... pour les méthodes de la catégorie
- **Même niveau de détail** : 20-30 lignes minimum par méthode

### Classification et Déplacement AVEC VÉRIFICATION OBLIGATOIRE
- Détermine catégorie appropriée pour chaque méthode basée sur :
  * Classe d'origine
  * Fonctionnalité
  * Domaine d'application
  * Niveau d'abstraction
- **PROCESSUS OBLIGATOIRE POUR CHAQUE MÉTHODE :**
  1. Analyse complète du fichier .txt (lecture intégrale)
  2. Création description détaillée (20-30 lignes minimum)
  3. Ajout au fichier Descriptif.txt principal
  4. **COPIE IMMÉDIATE** dans le Descriptif.txt du sous-dossier approprié
  5. Déplacement du fichier .txt vers le sous-dossier
  6. **VÉRIFICATION** que la description est présente dans les deux fichiers
- **INTERDICTION ABSOLUE** de descriptions courtes ou génériques
- **VALIDATION CONTINUE** : Vérifier chaque fichier Descriptif.txt après chaque ajout

### Traitement Spécial des Fichiers "class"
- **ORDRE DE TRAITEMENT** : Traite d'abord TOUS les fichiers qui ne commencent PAS par "class"
- **FICHIERS "class"** : Tous les fichiers dont le nom commence par "class" vont dans le dossier Anciennesclasses/
- **TRAITEMENT EN DERNIER** : Les fichiers "class" sont analysés et déplacés APRÈS tous les autres
- **MÊME PROCESSUS** : Même niveau de détail et même format pour les descriptions
- **DESCRIPTIF SPÉCIAL** : Le dossier Anciennesclasses/ aura son propre Descriptif.txt

## PHASE 5 : CRITÈRES DE QUALITÉ ET VÉRIFICATION

### Critères de Qualité Obligatoires
- **AUCUNE description courte** (ex: "Calcule la précision" ❌)
- **AUCUNE mention "FICHIER NON TROUVÉ"** sans descriptif détaillé
- **Descriptifs détaillés UNIQUEMENT** (minimum 20-30 lignes par méthode)
- **Analyse fonctionnelle complète** de chaque méthode avec étapes détaillées
- **Métadonnées complètes** (classe, lignes, fichier source)
- **Format standardisé** : FONCTION, PARAMÈTRES, FONCTIONNEMENT DÉTAILLÉ, RETOUR, UTILITÉ
- **Gestion doublons** : Conserver et mentionner "DOUBLON X" dans titre
- **Numéros de lignes précis** : Format "Lignes X-Y dans fichier.py (Z lignes)"
- **Paramètres structurés** : Format "* param (type) - Description"
- **Fonctionnement par étapes** : Format "* **ÉTAPE :** Description détaillée"

### Vérification et Correction RENFORCÉE
- **VÉRIFICATION IMMÉDIATE** : Après chaque ajout, lis le contenu du fichier Descriptif.txt pour confirmer présence
- **COMPTAGE SYSTÉMATIQUE** : Compte les méthodes dans chaque dossier et vérifie correspondance avec documentation
- **VALIDATION CONTENU** : Vérifie que chaque description fait minimum 20-30 lignes avec format complet
- **SYNCHRONISATION OBLIGATOIRE** : Assure que fichier principal et sous-dossiers contiennent mêmes descriptions
- **CORRECTION IMMÉDIATE** : Si description manquante ou incomplète, corrige avant de continuer
- **INTERDICTION PROGRESSION** : Ne passe JAMAIS à la méthode suivante sans validation complète de la précédente

### Gestion des Cas Spéciaux
- Si métadonnées manquantes : utilise codebase-retrieval pour analyse
- Si fichier source illisible : crée descriptif basé sur logique fonctionnelle
- Si méthode mal classée : déplace dans le bon dossier
- Si doublons : conserve et mentionne "DOUBLON X" dans description

## PHASE 6 : VALIDATION FINALE

### Critères de Complétion STRICTS
La tâche est terminée à 100% UNIQUEMENT quand :
- ✅ Exploration représentative effectuée (15-20 fichiers analysés)
- ✅ Architecture globale comprise et documentée
- ✅ Structure catégorielle créée (6-10 sous-dossiers)
- ✅ Tous les fichiers .txt ont été lus intégralement
- ✅ Chaque méthode a un descriptif détaillé complet (20-30 lignes minimum)
- ✅ Métadonnées extraites (classe, lignes, fichier) pour chaque méthode
- ✅ Descriptif.txt principal contient TOUTES les méthodes avec descriptions complètes
- ✅ Chaque sous-dossier a son Descriptif.txt avec TOUTES ses méthodes documentées
- ✅ **VÉRIFICATION FICHIER PAR FICHIER** : Lis le contenu de chaque Descriptif.txt pour confirmer
- ✅ **COMPTAGE EXACT** : Nombre de méthodes documentées = nombre de fichiers .txt dans chaque dossier
- ✅ Tous les fichiers .txt sont déplacés dans leurs sous-dossiers appropriés
- ✅ Aucune description courte ou "FICHIER NON TROUVÉ" ne subsiste
- ✅ **VALIDATION FINALE EXHAUSTIVE** : Lis intégralement chaque fichier Descriptif.txt créé
- ✅ **SYNCHRONISATION PARFAITE** : Descriptions identiques entre fichier principal et sous-dossiers

### Structure Finale Attendue Complète
```
RÉPERTOIRE_DE_TRAVAIL/
├── [NOM_FICHIER_PRINCIPAL].py (fichier source original)
├── Descriptif.txt (3000+ lignes - documentation maître complète)
├── [Catégorie1]/
│   ├── Descriptif.txt (format identique, méthodes filtrées)
│   ├── methode1.txt (méthode déplacée depuis racine)
│   ├── methode2.txt
│   └── ...
├── [Catégorie2]/
│   ├── Descriptif.txt
│   ├── methode3.txt
│   └── ...
├── [CatégorieN]/
│   ├── Descriptif.txt
│   └── ...
└── Anciennesclasses/
    ├── Descriptif.txt (format identique, fichiers class)
    ├── class_NomClasse1.txt (traités en dernier)
    ├── class_NomClasse2.txt
    └── ...

RÉSULTAT FINAL : Plateforme de maintenance professionnelle avec :
- Localisation précise de chaque méthode
- Documentation exhaustive (20-30 lignes/méthode)
- Navigation intuitive par domaines fonctionnels
- Traçabilité complète code ↔ documentation
- Maintenance efficace et sécurisée
- Organisation complète depuis répertoire VSCode unique
- Dossier spécial pour Anciennes classes (fichiers "class")
```

# APPROCHE MÉTHODOLOGIQUE SÉQUENTIELLE RENFORCÉE
1. **Exploration Métadonnées** : Lecture 4 premières lignes de tous les fichiers
2. **Échantillonnage Représentatif** : Analyse 15-20 fichiers clés
3. **Compréhension Architecturale** : Identification classes et domaines
4. **Création Structure** : Définition catégories et création dossiers (incluant Anciennesclasses/)
5. **Traitement Systématique AVEC VÉRIFICATION** (SAUF fichiers "class") :
   - Analyse complète fichier par fichier
   - Documentation immédiate (20-30 lignes minimum)
   - Ajout au fichier principal
   - **COPIE IMMÉDIATE** dans sous-dossier approprié
   - **VÉRIFICATION** présence dans les deux fichiers
6. **Classification et Déplacement** : Regroupement par domaines fonctionnels
7. **Validation Continue** : Contrôle qualité après chaque méthode
8. **Comptage Systématique** : Vérification nombre méthodes par catégorie
9. **Traitement Spécial Fichiers "class"** (EN DERNIER) :
   - Analyse des fichiers commençant par "class"
   - Documentation avec même niveau de détail
   - Déplacement vers Anciennesclasses/
   - Création Descriptif.txt dans Anciennesclasses/
10. **Validation Finale Exhaustive** :
    - Lecture intégrale de chaque fichier Descriptif.txt
    - Confirmation synchronisation parfaite
    - Comptage final et vérification 100% (incluant Anciennesclasses/)

# RAPPEL CRITIQUE RENFORCÉ
- COMMENCE TOUJOURS par lire les métadonnées (4 premières lignes)
- COMPRENDS l'architecture avant de créer les catégories
- **ORDRE IMPÉRATIF** : Traite d'abord TOUS les fichiers qui ne commencent PAS par "class"
- **FICHIERS "class" EN DERNIER** : Les fichiers commençant par "class" vont dans Anciennesclasses/ et sont traités EN DERNIER
- **VÉRIFICATION OBLIGATOIRE** : Après chaque ajout, lis le fichier Descriptif.txt pour confirmer
- **COPIE SYSTÉMATIQUE** : Chaque description du fichier principal DOIT être copiée dans le sous-dossier
- **INTERDICTION DESCRIPTIONS COURTES** : Minimum 20-30 lignes par méthode, AUCUNE exception
- **VALIDATION CONTINUE** : Ne passe JAMAIS à la méthode suivante sans vérifier la précédente
- **COMPTAGE OBLIGATOIRE** : Vérifie que nombre documenté = nombre de fichiers dans chaque dossier (incluant Anciennesclasses/)
- Ne t'arrête JAMAIS avant d'avoir traité 100% des fichiers avec validation exhaustive
- **LECTURE FINALE** : Lis intégralement chaque fichier Descriptif.txt avant de déclarer terminé (incluant Anciennesclasses/Descriptif.txt)
- Le résultat final doit être une plateforme de maintenance professionnelle avec synchronisation parfaite

# VARIABLES À REMPLACER SELON LE PROJET
- [TYPE_SYSTÈME] : ex. "prédiction ML", "optimisation Optuna", "système de trading"
- [NOM_FICHIER_PRINCIPAL] : ex. "optuna_optimizer", "trading_bot", "ml_predictor"

# UTILISATION SIMPLIFIÉE
1. Ouvrir le répertoire contenant le fichier .py et tous les fichiers .txt dans VSCode
2. Remplacer les variables [TYPE_SYSTÈME] et [NOM_FICHIER_PRINCIPAL] dans ce prompt
3. Lancer l'analyse avec ce prompt adapté
4. L'IA organisera automatiquement tous les fichiers du répertoire de travail
