# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8298 à 8414
# Type: Méthode de la classe HybridBaccaratPredictor

    def predict_with_lgbm(self, feature: Optional[List[float]]) -> Dict[str, float]:
        """
        Effectue une prédiction en utilisant le modèle LGBM calibré.

        Args:
            feature (Optional[List[float]]): Feature pour la prédiction LGBM.

        Returns:
            Dict[str, float]: Prédiction LGBM (probabilités pour 'player' et 'banker').
        """
        # Importer NotFittedError ici pour éviter les problèmes d'importation circulaire
        from sklearn.exceptions import NotFittedError
        if feature is None:
            logger.warning("predict_with_lgbm: Feature manquante (None). Retour valeurs par défaut.")
            default_player_prob = getattr(self.config, 'default_player_prob', 0.5)
            default_banker_prob = getattr(self.config, 'default_banker_prob', 0.5)
            return {'player': default_player_prob, 'banker': default_banker_prob}

        with self.model_lock:
            model_to_use = self.calibrated_lgbm
            model_name = "calibrated_lgbm"

            # Vérifier si nous sommes en phase d'entraînement ou d'optimisation Optuna
            is_training_phase = getattr(self, '_is_training', False)
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            log_method = logger.debug if (is_training_phase or is_optuna_phase) else logger.warning

            if model_to_use is None:
                # Vérifier si nous sommes en phase 0 d'optimisation Optuna
                is_optuna_phase0 = getattr(self, 'is_optuna_running', False) and getattr(self.config, 'lstm_epochs', 1) == 0

                # Ne pas logger si nous sommes en phase d'optimisation Optuna
                # ou si nous avons déjà affiché ce message récemment
                if not is_optuna_phase0 and not getattr(self, 'is_optuna_running', False):
                    # Vérifier si nous avons déjà affiché ce message récemment
                    current_time = time.time()
                    last_log_time = getattr(self, '_last_lgbm_not_initialized_log_time', 0)
                    log_interval = getattr(self.config, 'log_warning_interval', 60)  # Intervalle en secondes (défaut: 60s)

                    # N'afficher le message que si l'intervalle de temps est écoulé
                    if current_time - last_log_time > log_interval:
                        log_method(f"predict_with_lgbm: Modèle '{model_name}' non initialisé. Retour valeurs par défaut.")
                        # Mettre à jour le timestamp du dernier log
                        self._last_lgbm_not_initialized_log_time = current_time

                default_player_prob = getattr(self.config, 'default_player_prob', 0.5)
                default_banker_prob = getattr(self.config, 'default_banker_prob', 0.5)
                return {'player': default_player_prob, 'banker': default_banker_prob}
            if self.feature_scaler is None:
                # Vérifier si nous sommes en phase 0 d'optimisation Optuna
                is_optuna_phase0 = getattr(self, 'is_optuna_running', False) and getattr(self.config, 'lstm_epochs', 1) == 0

                # Ne pas logger si nous sommes en phase d'optimisation Optuna
                # ou si nous avons déjà affiché ce message récemment
                if not is_optuna_phase0 and not getattr(self, 'is_optuna_running', False):
                    # Vérifier si nous avons déjà affiché ce message récemment
                    current_time = time.time()
                    last_log_time = getattr(self, '_last_scaler_not_initialized_log_time', 0)
                    log_interval = getattr(self.config, 'log_warning_interval', 60)  # Intervalle en secondes (défaut: 60s)

                    # N'afficher le message que si l'intervalle de temps est écoulé
                    if current_time - last_log_time > log_interval:
                        log_method("predict_with_lgbm: Scaler non initialisé. Retour valeurs par défaut.")
                        # Mettre à jour le timestamp du dernier log
                        self._last_scaler_not_initialized_log_time = current_time

                default_player_prob = getattr(self.config, 'default_player_prob', 0.5)
                default_banker_prob = getattr(self.config, 'default_banker_prob', 0.5)
                return {'player': default_player_prob, 'banker': default_banker_prob}

            try:
                check_is_fitted(model_to_use)
                if not hasattr(self.feature_scaler, 'mean_'):
                    raise NotFittedError("Feature scaler n'est pas 'fit'.")

                feature_array = np.array(feature, dtype=np.float32).reshape(1, -1)
                feature_scaled_array = self.feature_scaler.transform(feature_array)
                probability = model_to_use.predict_proba(feature_scaled_array)[0]

                if len(probability) == 2:
                    # Utiliser UNIQUEMENT le système zero-based: 0 = Player, 1 = Banker (indices standards PyTorch)
                    # Importer la fonction de conversion depuis pytorch_standard_extensions
                    from optuna_optimizer import class_to_label

                    # Utiliser les indices standards de PyTorch
                    prob_player = float(probability[0])
                    prob_banker = float(probability[1])

                    # Mettre à jour la progression au lieu d'afficher un log
                    self._update_prediction_progress()

                    sum_probs = prob_player + prob_banker
                    prob_tolerance = getattr(self.config, 'probability_sum_tolerance', 1e-6)
                    if abs(sum_probs - 1.0) > prob_tolerance and sum_probs > prob_tolerance:
                        # Normaliser les probabilités
                        prob_player /= sum_probs
                        prob_banker /= sum_probs

                    # Nous avons déjà vérifié les classes du modèle LGBM plus haut si nécessaire
                    return {'player': prob_player, 'banker': prob_banker}
                else:
                    logger.error(f"predict_with_lgbm ({model_name}): Sortie predict_proba inattendue. Shape: {probability.shape}. Retour 50/50.")
                    return {'player': 0.5, 'banker': 0.5}

            except NotFittedError as nfe:
                # Utiliser la même logique de log que pour les autres messages
                is_training_phase = getattr(self, '_is_training', False)
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                log_method = logger.debug if (is_training_phase or is_optuna_phase) else logger.warning
                log_method(f"predict_with_lgbm: Échec car modèle '{model_name}' ou scaler non 'fit': {nfe}. Retour 50/50.")
                return {'player': 0.5, 'banker': 0.5}
            except ValueError as ve:
                logger.error(f"predict_with_lgbm: Erreur de valeur (probablement nbre feature ?) lors de transform/predict: {ve}. Retour 50/50.")
                return {'player': 0.5, 'banker': 0.5}
            except Exception as e:
                logger.error(f"predict_with_lgbm: Erreur inattendue: {e}", exc_info=True)
                return {'player': 0.5, 'banker': 0.5}