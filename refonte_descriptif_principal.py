# -*- coding: utf-8 -*-
"""
Script pour refaire le Descriptif.txt principal en consolidant 
tous les descriptifs des sous-dossiers (191 fichiers exactement)
"""
import os
import re

def refonte_descriptif_principal():
    base_dir = r"C:\Users\<USER>\Desktop\travail\OptimisationOptuna"
    main_descriptif = os.path.join(base_dir, "Descriptif.txt")
    backup_descriptif = os.path.join(base_dir, "Descriptif_backup_avant_refonte.txt")
    
    print("🔄 REFONTE DU DESCRIPTIF.TXT PRINCIPAL")
    print("=" * 50)
    
    # Sauvegarder l'ancien descriptif
    if os.path.exists(main_descriptif):
        with open(main_descriptif, 'r', encoding='utf-8') as f:
            old_content = f.read()
        with open(backup_descriptif, 'w', encoding='utf-8') as f:
            f.write(old_content)
        print(f"✅ Sauvegarde créée: Descriptif_backup_avant_refonte.txt")
    
    # Collecter toutes les méthodes de tous les sous-dossiers
    all_methods = {}
    total_files = 0
    
    # Parcourir tous les sous-dossiers
    for subdir in sorted(os.listdir(base_dir)):
        subdir_path = os.path.join(base_dir, subdir)
        if os.path.isdir(subdir_path):
            descriptif_path = os.path.join(subdir_path, "Descriptif.txt")
            
            if os.path.exists(descriptif_path):
                print(f"\n📁 Lecture du sous-dossier: {subdir}")
                
                # Lire le descriptif du sous-dossier
                with open(descriptif_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Extraire les méthodes
                current_method = None
                current_content = []
                method_count = 0
                
                for line in content.split('\n'):
                    if re.match(r'^\d+\. ', line):
                        # Sauvegarder la méthode précédente
                        if current_method and current_method not in all_methods:
                            all_methods[current_method] = '\n'.join(current_content)
                            method_count += 1
                        
                        # Extraire le nom du fichier de la nouvelle méthode
                        match = re.search(r'(\w+\.txt)', line)
                        if match:
                            current_method = match.group(1)
                            current_content = [line]
                        else:
                            current_method = None
                            current_content = []
                    else:
                        if current_method:
                            current_content.append(line)
                
                # Sauvegarder la dernière méthode
                if current_method and current_method not in all_methods:
                    all_methods[current_method] = '\n'.join(current_content)
                    method_count += 1
                
                print(f"   📄 Méthodes extraites: {method_count}")
                total_files += method_count
    
    print(f"\n📊 TOTAL MÉTHODES COLLECTÉES: {len(all_methods)}")
    
    # Trier les méthodes par nom de fichier
    sorted_methods = sorted(all_methods.items())
    
    # Créer le nouveau descriptif principal
    new_content = []
    new_content.append("DESCRIPTIF DÉTAILLÉ DES MÉTHODES - OPTIMISATION OPTUNA")
    new_content.append("=" * 60)
    new_content.append("")
    new_content.append("Ce fichier contient la description détaillée de toutes les méthodes")
    new_content.append("du système d'optimisation Optuna, consolidées depuis tous les sous-dossiers.")
    new_content.append("")
    new_content.append(f"Dernière mise à jour: Refonte automatique depuis {len(os.listdir(base_dir))-1} sous-dossiers")
    new_content.append("")
    
    # Ajouter chaque méthode avec numérotation globale
    method_number = 1
    for filename, description in sorted_methods:
        # Remplacer le numéro par le numéro global
        description = re.sub(r'^\d+\.', f'{method_number}.', description)
        new_content.append(description)
        new_content.append("")
        method_number += 1
    
    # Ajouter le total final
    new_content.append(f"TOTAL : {len(all_methods)} méthodes analysées et documentées")
    new_content.append("(Refonte automatique depuis les descriptifs des sous-dossiers)")
    
    # Écrire le nouveau descriptif principal
    with open(main_descriptif, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_content))
    
    print(f"\n✅ REFONTE TERMINÉE")
    print(f"📊 Nouveau Descriptif.txt principal:")
    print(f"   - Méthodes uniques: {len(all_methods)}")
    print(f"   - Numérotation: 1 à {len(all_methods)}")
    print(f"   - Objectif: 191 méthodes")
    print(f"   - Différence: {len(all_methods) - 191}")
    
    if len(all_methods) == 191:
        print("🎯 PARFAIT! Exactement 191 méthodes comme attendu!")
    elif len(all_methods) > 191:
        print(f"⚠️  {len(all_methods) - 191} méthodes en trop")
    else:
        print(f"❌ {191 - len(all_methods)} méthodes manquantes")
    
    return len(all_methods)

if __name__ == "__main__":
    refonte_descriptif_principal()
    print("✅ TERMINÉ")
