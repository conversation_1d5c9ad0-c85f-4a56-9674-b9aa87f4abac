#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script sécurisé pour renumeroter les fichiers Descriptif.txt
Préserve tous les caractères existants et ne modifie que les numéros
"""

import os
import re
import shutil
from pathlib import Path

def backup_file(filepath):
    """Crée une sauvegarde du fichier avant modification"""
    backup_path = f"{filepath}.backup"
    shutil.copy2(filepath, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def renumeroter_fichier(filepath):
    """Renumérote un fichier Descriptif.txt en préservant tous les caractères"""
    print(f"\n=== Traitement de {filepath} ===")
    
    # Créer une sauvegarde
    backup_path = backup_file(filepath)
    
    try:
        # Lire le fichier avec encodage UTF-8
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern pour détecter les lignes avec numéros de fichiers
        # Cherche: début de ligne + chiffres + point + espace + nom_fichier.txt
        pattern = r'^(\d+)\.\s+([a-zA-Z_][a-zA-Z0-9_]*\.txt)'
        
        # Trouver toutes les correspondances avec leurs positions
        matches = []
        for match in re.finditer(pattern, content, re.MULTILINE):
            matches.append({
                'start': match.start(),
                'end': match.end(),
                'old_num': int(match.group(1)),
                'filename': match.group(2),
                'full_match': match.group(0)
            })
        
        print(f"Trouvé {len(matches)} fichiers à renumeroter")
        
        if not matches:
            print("Aucun fichier à renumeroter trouvé")
            return True
        
        # Afficher les fichiers trouvés
        for i, match in enumerate(matches):
            print(f"  {match['old_num']} -> {i+1}: {match['filename']}")
        
        # Construire le nouveau contenu en remplaçant les numéros
        new_content = content
        offset = 0
        
        for i, match in enumerate(matches):
            new_num = i + 1
            old_text = match['full_match']
            new_text = f"{new_num}. {match['filename']}"
            
            # Position ajustée avec l'offset des modifications précédentes
            start_pos = match['start'] + offset
            end_pos = match['end'] + offset
            
            # Remplacer dans le contenu
            new_content = new_content[:start_pos] + new_text + new_content[end_pos:]
            
            # Mettre à jour l'offset
            offset += len(new_text) - len(old_text)
        
        # Écrire le nouveau contenu
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ Renumération terminée: {len(matches)} fichiers renumérés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du traitement de {filepath}: {e}")
        # Restaurer la sauvegarde en cas d'erreur
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, filepath)
            print(f"Fichier restauré depuis la sauvegarde")
        return False

def main():
    """Fonction principale"""
    print("🔧 Script de renumération des fichiers Descriptif.txt")
    print("=" * 60)
    
    # Liste des fichiers à traiter
    fichiers_a_traiter = [
        "Descriptif.txt",
        "CalculConfiance/Descriptif.txt",
        "ReseauxNeuronaux/Descriptif.txt",
        "EvaluationMetriques/Descriptif.txt",
        "GestionDonnees/Descriptif.txt",
        "OptimisationEntrainement/Descriptif.txt",
        "UtilitairesFonctions/Descriptif.txt",
        "anciennesclasses/Descriptif.txt"
    ]
    
    resultats = []
    
    for fichier in fichiers_a_traiter:
        if os.path.exists(fichier):
            succes = renumeroter_fichier(fichier)
            resultats.append((fichier, succes))
        else:
            print(f"⚠️  Fichier non trouvé: {fichier}")
            resultats.append((fichier, False))
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ FINAL")
    print("=" * 60)
    
    succes_count = 0
    for fichier, succes in resultats:
        status = "✅ SUCCÈS" if succes else "❌ ÉCHEC"
        print(f"{status}: {fichier}")
        if succes:
            succes_count += 1
    
    print(f"\n🎯 Résultat: {succes_count}/{len(resultats)} fichiers traités avec succès")
    
    if succes_count == len([r for r in resultats if os.path.exists(r[0])]):
        print("🎉 Tous les fichiers existants ont été renumérés avec succès!")
    else:
        print("⚠️  Certains fichiers n'ont pas pu être traités")

if __name__ == "__main__":
    main()
