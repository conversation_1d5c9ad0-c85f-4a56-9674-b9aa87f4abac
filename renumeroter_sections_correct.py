#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script sécurisé pour renumeroter les fichiers Descriptif.txt par sections
Remet la numérotation à 1 à chaque nouvelle section
"""

import os
import re
import shutil
from pathlib import Path

def backup_file(filepath):
    """Crée une sauvegarde du fichier avant modification"""
    backup_path = f"{filepath}.backup2"
    shutil.copy2(filepath, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def detecter_sections(content):
    """Détecte les sections dans le contenu"""
    # Pattern pour détecter les sections (lignes avec SECTION X : NOMCATEGORIE)
    section_pattern = r'^SECTION\s+\d+\s*:\s*[A-Z]+.*$'
    sections = []
    
    for match in re.finditer(section_pattern, content, re.MULTILINE):
        sections.append({
            'start': match.start(),
            'end': match.end(),
            'text': match.group(0).strip(),
            'line_num': content[:match.start()].count('\n') + 1
        })
    
    return sections

def renumeroter_fichier_par_sections(filepath):
    """Renumérote un fichier Descriptif.txt par sections"""
    print(f"\n=== Traitement de {filepath} ===")
    
    # Créer une sauvegarde
    backup_path = backup_file(filepath)
    
    try:
        # Lire le fichier avec encodage UTF-8
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Détecter les sections
        sections = detecter_sections(content)
        print(f"Sections détectées: {len(sections)}")
        for i, section in enumerate(sections):
            print(f"  Section {i+1} (ligne {section['line_num']}): {section['text'][:60]}...")
        
        # Pattern pour détecter les lignes avec numéros de fichiers
        pattern = r'^(\d+)\.\s+([a-zA-Z_][a-zA-Z0-9_]*\.txt)'
        
        # Trouver toutes les correspondances avec leurs positions
        matches = []
        for match in re.finditer(pattern, content, re.MULTILINE):
            line_num = content[:match.start()].count('\n') + 1
            matches.append({
                'start': match.start(),
                'end': match.end(),
                'old_num': int(match.group(1)),
                'filename': match.group(2),
                'full_match': match.group(0),
                'line_num': line_num,
                'section_index': -1  # À déterminer
            })
        
        # Associer chaque match à sa section
        for match in matches:
            match_line = match['line_num']
            current_section = -1
            
            # Trouver la section la plus proche avant ce match
            for i, section in enumerate(sections):
                if section['line_num'] < match_line:
                    current_section = i
                else:
                    break
            
            match['section_index'] = current_section
        
        print(f"Trouvé {len(matches)} fichiers à renumeroter")
        
        if not matches:
            print("Aucun fichier à renumeroter trouvé")
            return True
        
        # Grouper par section et renumeroter
        sections_matches = {}
        for match in matches:
            section_idx = match['section_index']
            if section_idx not in sections_matches:
                sections_matches[section_idx] = []
            sections_matches[section_idx].append(match)
        
        # Afficher les fichiers par section
        for section_idx in sorted(sections_matches.keys()):
            if section_idx >= 0:
                section_name = sections[section_idx]['text']
                print(f"\n📁 Section {section_idx + 1}: {section_name}")
            else:
                print(f"\n📁 Avant première section:")
            
            section_matches = sections_matches[section_idx]
            # Trier par position dans le fichier
            section_matches.sort(key=lambda x: x['start'])
            
            for i, match in enumerate(section_matches):
                new_num = i + 1
                print(f"  {match['old_num']} -> {new_num}: {match['filename']}")
        
        # Construire le nouveau contenu en remplaçant les numéros
        new_content = content
        offset = 0
        
        # Traiter section par section pour maintenir l'ordre
        for section_idx in sorted(sections_matches.keys()):
            section_matches = sections_matches[section_idx]
            
            # Trier les matches de cette section par position
            section_matches.sort(key=lambda x: x['start'])
            
            for i, match in enumerate(section_matches):
                new_num = i + 1
                old_text = match['full_match']
                new_text = f"{new_num}. {match['filename']}"
                
                # Position ajustée avec l'offset des modifications précédentes
                start_pos = match['start'] + offset
                end_pos = match['end'] + offset
                
                # Remplacer dans le contenu
                new_content = new_content[:start_pos] + new_text + new_content[end_pos:]
                
                # Mettre à jour l'offset
                offset += len(new_text) - len(old_text)
        
        # Écrire le nouveau contenu
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ Renumération terminée: {len(matches)} fichiers renumérés par sections")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du traitement de {filepath}: {e}")
        # Restaurer la sauvegarde en cas d'erreur
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, filepath)
            print(f"Fichier restauré depuis la sauvegarde")
        return False

def main():
    """Fonction principale"""
    print("🔧 Script de renumération par sections des fichiers Descriptif.txt (CORRIGÉ)")
    print("=" * 75)
    
    # Liste des fichiers à traiter
    fichiers_a_traiter = [
        "Descriptif.txt",
        "CalculConfiance/Descriptif.txt",
        "ReseauxNeuronaux/Descriptif.txt",
        "EvaluationMetriques/Descriptif.txt",
        "GestionDonnees/Descriptif.txt",
        "OptimisationEntrainement/Descriptif.txt",
        "UtilitairesFonctions/Descriptif.txt",
        "anciennesclasses/Descriptif.txt"
    ]
    
    resultats = []
    
    for fichier in fichiers_a_traiter:
        if os.path.exists(fichier):
            succes = renumeroter_fichier_par_sections(fichier)
            resultats.append((fichier, succes))
        else:
            print(f"⚠️  Fichier non trouvé: {fichier}")
            resultats.append((fichier, False))
    
    # Résumé final
    print("\n" + "=" * 75)
    print("📊 RÉSUMÉ FINAL")
    print("=" * 75)
    
    succes_count = 0
    for fichier, succes in resultats:
        status = "✅ SUCCÈS" if succes else "❌ ÉCHEC"
        print(f"{status}: {fichier}")
        if succes:
            succes_count += 1
    
    print(f"\n🎯 Résultat: {succes_count}/{len(resultats)} fichiers traités avec succès")
    
    if succes_count == len([r for r in resultats if os.path.exists(r[0])]):
        print("🎉 Tous les fichiers existants ont été renumérés par sections avec succès!")
    else:
        print("⚠️  Certains fichiers n'ont pas pu être traités")

if __name__ == "__main__":
    main()
