# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4714 à 4829
# Type: Méthode de la classe HybridBaccaratPredictor

    def reset_data(self, reset_type: Literal['soft', 'hard'] = 'soft', confirm: bool = True) -> None:
        """
        Réinitialise les données de session ('soft') ou l'état complet incluant les modèles ('hard').
        MODIFIÉ: Ajoute le vidage du cache LGBM pour le reset 'hard' APRÈS l'appel à init_ml_models.
        """
        logger.info(f"Demande de réinitialisation (type: {reset_type}, confirmation: {confirm}).")
        ui_available = self.is_ui_available()

        # Demander confirmation UI si nécessaire
        if confirm and ui_available:
            confirm_msg = f"Êtes-vous sûr de vouloir réinitialiser {'la session actuelle' if reset_type == 'soft' else 'TOUT (session ET modèles)'} ?"
            if not messagebox.askyesno("Confirmation Réinitialisation", confirm_msg):
                logger.info("Réinitialisation annulée par l'utilisateur.")
                return

        logger.warning(f"--- LANCEMENT RÉINITIALISATION ({reset_type.upper()}) ---")
        locks_acquired = False
        markov_lock_to_use = None
        try:
            # Acquérir tous les verrous pour garantir cohérence état pendant reset
            # Attention à l'ordre pour éviter deadlocks si possible (moins critique ici car reset)
            self.sequence_lock.acquire(); self.model_lock.acquire()
            if self.markov and hasattr(self.markov, 'lock'): markov_lock_to_use = self.markov.lock
            else:
                if not hasattr(self, '_fallback_markov_lock'): self._fallback_markov_lock = threading.RLock()
                markov_lock_to_use = self._fallback_markov_lock
            markov_lock_to_use.acquire(); self.training_lock.acquire(); self.weights_lock.acquire()
            locks_acquired = True; logger.debug("reset_data: Tous verrous acquis.")

            # --- Reset Commun ('soft' et 'hard') ---
            self.sequence = []
            self.prediction_history = []
            self.lgbm_cache = deque(maxlen=100) # Vider cache pour 'soft' aussi (nouvelle session vide)
             # Ne pas vider le cache ici pour 'soft' selon modif initiale, mais le faire semble plus propre quand la séquence change complètement ?
             # OK, on va suivre la règle stricte: PAS de vidage pour 'soft' ici. Commenté.
             # self.lgbm_cache = deque(maxlen=100) # << LIGNE COMMENTEE pour 'soft'
             # logger.info("Reset 'soft': Séquence et historique prédictions vidés.") # Log ajusté


            self.last_incremental_update_index = 0 # Réinitialiser index maj rapide
            if self.markov:
                 logger.debug("Réinitialisation Markov ('soft')...")
                 self.markov.reset(reset_type='soft') # Reset session models

            # --- Reset Spécifique 'hard' ---
            if reset_type == 'hard':
                logger.warning("--- Réinitialisation DURE EN COURS ---")
                # Réinitialiser les données persistantes
                self.loaded_historical = False
                self.historical_data = []
                logger.info("Reset 'hard': Données historiques ('loaded_historical', 'historical_data') réinitialisées.")

                # Réinitialiser les modèles ML via la méthode dédiée
                # (init_ml_models gère ses propres logs et potentiellement le vidage cache)
                init_ok = self.init_ml_models(reset_weights=True) # reset_weights=True pour hard reset
                if not init_ok:
                     logger.critical("ÉCHEC CRITIQUE de init_ml_models pendant le reset 'hard'. L'état des modèles est incertain.")
                     # Lever une erreur ou afficher un message critique ? Afficher message.
                     if ui_available: messagebox.showerror("Erreur Critique Reset", "Échec de la réinitialisation des modèles ML pendant le reset 'hard'. L'application pourrait être instable.")
                else:
                     logger.info("Reset 'hard': Modèles ML réinitialisés via init_ml_models.")


                # >>>>>>>>>>>>> MODIFICATION: VIDAGE CACHE EXPLICITE POUR 'HARD' <<<<<<<<<<<<<<<<
                # MÊME SI init_ml_models le fait déjà, on assure la consigne initiale
                # Pas besoin d'acquérir le verrou model_lock, on est déjà dessous.
                self.lgbm_cache = deque(maxlen=100)
                logger.info("Reset 'hard': Cache LGBM vidé explicitement APRES init_ml_models.")
                # >>>>>>>>>>>>> FIN MODIFICATION <<<<<<<<<<<<<<<<


                # Réinitialiser le Markov complètement
                if self.markov:
                    logger.info("Réinitialisation Markov ('hard')...")
                    self.markov.reset(reset_type='hard') # Reset tout, incluant modèles persistants

            # --- Reset Commun ('soft' et 'hard') - Suite ---
            # Réinitialiser les poids et perfs SEULEMENT si pas déjà fait par 'hard'/'init_ml_models'
            if reset_type == 'soft': # Ou si reset_weights était False dans init_ml_models (ce qui n'arrive pas pour hard)
                 self.weights = self.config.initial_weights.copy()
                 self._initialize_method_performance()
                 self.best_accuracy = 0.5
                 self.best_weights = self.weights.copy()
                 self.early_stopping_counter = 0
                 logger.info("Reset 'soft': Poids, performances et compteurs réinitialisés.")
                 # Vider le CACHE ici pour soft tel que demandé initialement? NON.
                 # self.lgbm_cache = deque(maxlen=100) # << LIGNE COMMENTEE pour 'soft'
                 # logger.info("Cache LGBM vidé pour reset 'soft'.") # << LOG COMMENTE


            # Déclencher recalcul/MAJ affichage depuis thread UI
            if ui_available:
                # Planifier la mise à jour complète
                self.root.after(0, self.update_display) # Gère graphique et labels principaux
                # Planifier MaJ spécifique poids
                self.root.after(50, self._update_weights_display)
                # Effacer messages/progression
                self.root.after(10, lambda: self._update_progress(0, "Prêt.")) # Taux remis à 0


            logger.warning(f"--- RÉINITIALISATION ({reset_type.upper()}) TERMINÉE ---")

        except Exception as e:
            logger.critical(f"Erreur majeure pendant reset_data ({reset_type}): {e}", exc_info=True)
            if ui_available:
                messagebox.showerror("Erreur Réinitialisation", f"Une erreur critique est survenue:\n{e}")
        finally:
            # Libérer tous les verrous
            if locks_acquired:
                 try:
                      self.weights_lock.release(); self.training_lock.release();
                      if markov_lock_to_use: markov_lock_to_use.release()
                      self.model_lock.release(); self.sequence_lock.release()
                      logger.debug("reset_data: Tous verrous libérés (finally).")
                 except RuntimeError as e_release:
                      logger.error(f"reset_data: Erreur libération verrou: {e_release}")