# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11871 à 12024
# Type: Méthode de la classe HybridBaccaratPredictor

    def run_full_retraining(self) -> None:
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        logger_instance.info("Demande de ré-entraînement complet...")
        ui_available = self.is_ui_available()

        with self.training_lock:
            if self.is_training or self.is_fast_updating:
                task_type = "entraînement" if self.is_training else "mise à jour rapide"
                if ui_available:
                    messagebox.showwarning("Action Impossible", f"Une tâche ({task_type}) est déjà en cours.")
                logger_instance.warning(f"Entr. complet: Tâche ML ({task_type}) déjà active.")
                return None

        data_available = False
        with self.sequence_lock:
            if self.loaded_historical and self.historical_data:
                data_available = True
                historical_data_copy = self.historical_data[:]
            else:
                if ui_available:
                    messagebox.showerror("Données Manquantes", "Aucune donnée historique chargée pour l'entraînement.")
                logger_instance.error("Entr. complet: Historique non chargé.")
                return None

        if ui_available and not messagebox.askyesno(
            "Confirmation Entraînement Complet",
            "Lancer l'entraînement complet ?\nCela peut prendre du temps et écrasera les modèles existants.\nLes modèles Markov globaux seront aussi recalculés."
        ):
            logger_instance.info("Entraînement complet annulé par l'utilisateur.")
            return None

        if ui_available:
            self._update_progress(0, "Préparation (MAJ Markov Global)...")
        markov_update_success = False
        if self.markov:
            start_markov_update = time.time()
            logger_instance.info("Début mise à jour modèle Markov global...")
            try:
                with self.markov_lock, self.sequence_lock:
                    self.markov.reset(reset_type='hard')
                    self.markov.update_global(historical_data_copy)
                markov_update_duration = time.time() - start_markov_update
                logger_instance.info(f"Mise à jour Markov global terminée ({markov_update_duration:.2f}s)")
                markov_update_success = True
                if ui_available:
                    self.root.after(0, lambda: self._update_progress(5, "MAJ Markov OK, Prépa données ML..."))
            except Exception as e_markov_update:
                logger_instance.error(f"Erreur mise à jour Markov global: {e_markov_update}", exc_info=True)
                if ui_available:
                    messagebox.showerror("Erreur Markov", f"Impossible de mettre à jour le Markov global:\n{e_markov_update}")
                    self._update_progress(0, "Erreur MAJ Markov")

        if ui_available:
            self._update_progress(10, "Préparation features ML...")
        training_data_package = None
        try:
            # MODIFICATION: Forcer l'utilisation de toutes les données historiques
            # en passant force_use_historical=True et en désactivant l'échantillonnage (max_games=None, sampling_fraction=None)
            logger_instance.info("MODIFICATION: Forcer l'utilisation de TOUTES les données historiques pour garantir les manches 31-60")
            training_data_package = self._prepare_training_data(force_use_historical=True, max_games=None, sampling_fraction=None)
            if (training_data_package is None or len(training_data_package) != 8 or
                training_data_package[0] is None or training_data_package[1] is None or
                training_data_package[2] is None or training_data_package[3] is None):
                raise ValueError("Données d'entraînement incomplètes après préparation")

            logger_instance.info(f"Données ML préparées: {len(training_data_package[1])} échantillons")

            # Vérification concise de l'utilisation des manches 31-60
            if len(training_data_package) >= 8 and training_data_package[7] is not None:
                origin_indices = training_data_package[7]
                if len(origin_indices) > 0:
                    # Convertir les indices d'origine en positions 1-indexées (approximation)
                    positions_1_indexed = np.array([idx % 60 + 1 if idx % 60 > 0 else 60 for idx in origin_indices])

                    # Compter les manches dans la plage 31-60
                    target_round_min = getattr(self.config, 'target_round_min', 31)
                    target_round_max = getattr(self.config, 'target_round_max', 60)
                    target_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)
                    target_count = np.sum(target_mask)

                    # Log uniquement si le nombre de manches 31-60 est insuffisant
                    if target_count < 100:  # Seuil arbitraire, à ajuster selon les besoins
                        logger_instance.warning(f"ATTENTION: Seulement {target_count}/{len(positions_1_indexed)} manches dans la plage 31-60 détectées!")
        except Exception as e_prep:
            logger_instance.error(f"Erreur préparation données ML: {str(e_prep)[:200]}", exc_info=True)
            if ui_available:
                messagebox.showerror("Erreur Préparation Données", str(e_prep))
                self._update_progress(0, "Erreur prépa données ML")
            return None

        logger_instance.info("Lancement du thread d'entraînement complet...")
        with self.training_lock:
            self.is_training = True
            self.stop_training = False

        if ui_available:
            self.toggle_training_controls(enabled=False)
            self._update_progress(30, "Lancement entraînement complet...")

        # Créer une fonction de rappel pour le ThreadedTrainer
        def on_training_complete(result):
            logger_instance.info(f"Entraînement terminé: {result['message']}")
            # Appeler finalize_training pour finaliser l'entraînement
            if hasattr(self, 'finalize_training'):
                try:
                    logger_instance.info("Appel de finalize_training pour finaliser l'entraînement")
                    self.finalize_training(result['success'], self.threaded_trainer.start_time, [])
                except Exception as e_finalize:
                    logger_instance.error(f"Erreur lors de l'appel à finalize_training: {e_finalize}", exc_info=True)

        # Créer une fonction de rappel pour les erreurs
        def on_training_error(error):
            logger_instance.error(f"Erreur lors de l'entraînement: {error}", exc_info=True)
            # Appeler finalize_training pour finaliser l'entraînement
            if hasattr(self, 'finalize_training'):
                try:
                    logger_instance.info("Appel de finalize_training pour finaliser l'entraînement (erreur)")
                    self.finalize_training(False, self.threaded_trainer.start_time if hasattr(self, 'threaded_trainer') else time.time(), [])
                except Exception as e_finalize:
                    logger_instance.error(f"Erreur lors de l'appel à finalize_training: {e_finalize}", exc_info=True)

        # Créer une fonction de rappel pour la progression
        def on_training_progress(progress, message):
            if ui_available:
                # Utiliser root.after pour mettre à jour la progression dans le thread UI
                # au lieu d'appeler directement self._update_progress
                self.root.after(0, lambda p=progress, m=message: self._update_progress(p, m))

        # Créer et démarrer le ThreadedTrainer
        self.threaded_trainer = ThreadedTrainer(
            trainer_instance=self,
            callback=on_training_complete,
            error_callback=on_training_error,
            progress_callback=on_training_progress
        )

        success = self.threaded_trainer.start(
            X_lgbm=training_data_package[0],  # X_lgbm_all
            y_lgbm=training_data_package[1],  # y_all
            X_lstm=training_data_package[2],  # X_lstm_all
            y_lstm=training_data_package[1],  # y_all
            config_override={}
        )

        if success:
            logger_instance.info("Thread d'entraînement complet démarré avec ThreadedTrainer.")
        else:
            logger_instance.error("Impossible de démarrer l'entraînement avec ThreadedTrainer.")
            # Réactiver les contrôles si l'entraînement n'a pas pu démarrer
            if ui_available:
                self.toggle_training_controls(enabled=True)
                self._update_progress(0, "Erreur démarrage entraînement")

        return None