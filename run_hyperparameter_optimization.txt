# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12836 à 13032
# Type: Méthode de la classe HybridBaccaratPredictor

    def run_hyperparameter_optimization(self, n_trials=20):
        """
        Lance l'optimisation des hyperparamètres avec la stratégie multi-niveaux adaptée pour CPU.
        Utilise OptunaThreadManager pour exécuter l'optimisation dans un thread séparé.

        Args:
            n_trials: Nombre d'essais à effectuer

        Returns:
            None
        """
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        logger_instance.info("Demande d'optimisation hyperparamètres (Stratégie multi-niveaux CPU)...")
        ui_available = self.is_ui_available()

        with self.training_lock:
            if self.is_training or self.is_fast_updating:
                task_type = "entraînement" if self.is_training else "mise à jour rapide/optuna"
                if ui_available:
                    messagebox.showwarning("Action Impossible", f"Une tâche ({task_type}) est déjà en cours.")
                logger_instance.warning(f"Optuna multi-niveaux: Tâche ML ({task_type}) déjà active.")
                return None

        if not self.loaded_historical or not self.historical_data:
            if ui_available:
                messagebox.showerror("Données Manquantes", "Aucune donnée historique chargée.")
            logger_instance.error("Optuna multi-niveaux: Historique non chargé.")
            return None

        # Vérifier si les données historiques sont suffisantes
        if len(self.historical_data) < 10:  # Minimum de 10 jeux pour l'optimisation
            if ui_available:
                messagebox.showerror("Données Insuffisantes",
                                    f"Les données historiques sont insuffisantes pour l'optimisation.\n"
                                    f"Nombre de jeux actuels: {len(self.historical_data)}\n"
                                    f"Minimum requis: 10")
            logger_instance.error(f"Optuna multi-niveaux: Données historiques insuffisantes ({len(self.historical_data)} jeux, min 10 requis).")
            return None

        if ui_available and not messagebox.askyesno(
            "Confirmation Optimisation",
            "L'optimisation multi-niveaux (LGBM+LSTM) peut prendre BEAUCOUP de temps.\nL'interface restera utilisable mais certaines actions seront bloquées.\n\nContinuer ?",
        ):
            logger_instance.info("Optimisation multi-niveaux annulée par l'utilisateur.")
            return None

        if ui_available:
            self._update_progress(0, "Préparation données pour Optuna multi-niveaux...")
        logger_instance.info("Préparation données historiques COMPLÈTES pour Optuna multi-niveaux (Manager)...")

        try:
            training_data_package = self._prepare_training_data(force_use_historical=True)
            if (training_data_package is None or len(training_data_package) != 8 or
                training_data_package[0] is None or training_data_package[1] is None or
                training_data_package[4] is None or training_data_package[5] is None):

                # Message d'erreur plus explicite
                error_msg = "Données/Indices manquants dans training_data_package. "
                error_msg += "Les données historiques sont probablement insuffisantes ou invalides pour générer des features."
                raise ValueError(error_msg)

            X_lgbm_all, y_all, X_lstm_all, _, train_idx, val_idx, _, _ = training_data_package

            # Vérifier si les données générées sont suffisantes
            if len(y_all) < 20:  # Minimum d'échantillons pour l'entraînement
                raise ValueError(f"Nombre d'échantillons générés insuffisant: {len(y_all)}. Minimum requis: 20.")

        except Exception as e_prep:
            logger_instance.error(f"Erreur préparation données: {str(e_prep)[:200]}", exc_info=True)
            if ui_available:
                self._update_progress(0, "Erreur prépa données")
                messagebox.showerror("Erreur Données", f"Échec préparation données:\n{str(e_prep)[:200]}")
            return None

        logger_instance.info("Initialisation du gestionnaire de thread pour l'optimisation Optuna...")
        with self.training_lock:
            self.is_training = True
            self.stop_training = False
            # Définir l'attribut is_optuna_running pour limiter les logs répétitifs
            self.is_optuna_running = True

            # Réinitialiser les flags de log pour permettre l'affichage des messages une fois au début de l'optimisation
            if hasattr(self, '_logged_sequence_length'):
                delattr(self, '_logged_sequence_length')
            if hasattr(self, '_logged_compat_sequence_length'):
                delattr(self, '_logged_compat_sequence_length')

        if ui_available:
            self.toggle_training_controls(enabled=False)
            self._update_progress(20, "Configuration Optuna multi-niveaux...")

        # Ajouter des options avancées pour l'optimisation multi-niveaux
        advanced_options = {
            'enable_multi_level': True,  # Activer l'optimisation multi-niveaux
            'enable_adaptive_regularization': True,  # Activer la régularisation adaptative
            'enable_swa': True,  # Activer Stochastic Weight Averaging
            'enable_meta_learning': True,  # Activer le méta-apprentissage
            'enable_temporal_cv': True,  # Activer la validation croisée temporelle
            'cpu_count': 8,  # Nombre de cœurs CPU disponibles
            'ram_gb': 28,  # RAM disponible en GB
            'batch_size': 1024  # Batch size fixe pour LSTM (2x512 pour bidirectionnel)
        }

        # Importer et initialiser OptunaThreadManager
        try:
            from optuna_optimizer import OptunaThreadManager

            # Créer une instance d'OptunaOptimizer
            try:
                from optuna_optimizer import OptunaOptimizer
                # Réinitialiser le compteur d'essais viables optimisés
                OptunaOptimizer.optimized_viable_trials_count = 0
                # Réinitialiser le compteur total de tentatives
                OptunaOptimizer.total_attempts_made = 0
                logger_instance.warning("=" * 80)
                logger_instance.warning("RÉINITIALISATION DES COMPTEURS OPTUNA POUR UNE NOUVELLE OPTIMISATION")
                logger_instance.warning(f"optimized_viable_trials_count = {OptunaOptimizer.optimized_viable_trials_count}")
                logger_instance.warning(f"total_attempts_made = {OptunaOptimizer.total_attempts_made}")
                logger_instance.warning("=" * 80)

                optimizer_instance = OptunaOptimizer(self.config)

                # Transmettre les options avancées à l'optimiseur
                if advanced_options:
                    optimizer_instance.advanced_options = advanced_options

                    # Définir les ressources disponibles
                    optimizer_instance.cpu_count = advanced_options.get('cpu_count', 8)
                    optimizer_instance.ram_gb = advanced_options.get('ram_gb', 28)
                    optimizer_instance.batch_size = advanced_options.get('batch_size', 1024)

                    logger_instance.warning("=" * 80)
                    logger_instance.warning("OPTIONS AVANCÉES POUR L'OPTIMISATION MULTI-NIVEAUX")
                    for key, value in advanced_options.items():
                        logger_instance.warning(f"{key}: {value}")
                    logger_instance.warning("=" * 80)
            except ImportError as e:
                logger_instance.error(f"Erreur lors de l'importation d'OptunaOptimizer: {e}")
                if ui_available:
                    messagebox.showerror("Erreur d'importation", f"Impossible d'importer OptunaOptimizer: {e}")
                return None

            # Définir la référence au prédicateur pour permettre l'échantillonnage des données historiques
            optimizer_instance.predictor_ref = self
            self.current_optimizer_instance = optimizer_instance

            # Assigner les données et indices pour l'optimisation
            optimizer_instance.X_lgbm_full = X_lgbm_all
            optimizer_instance.y_full = y_all
            optimizer_instance.X_lstm_full = X_lstm_all
            optimizer_instance.train_indices = train_idx
            optimizer_instance.val_indices = val_idx
            logger_instance.info("Données/Indices assignés à OptunaOptimizer multi-niveaux.")

            # Créer le gestionnaire de thread
            if not hasattr(self, 'optuna_thread_manager'):
                self.optuna_thread_manager = OptunaThreadManager()

            # Définir les callbacks
            def success_callback(best_params, duration):
                if ui_available:
                    self.root.after(0, lambda: self._finalize_optuna_optimization(
                        True, best_params, duration, None
                    ))
                else:
                    logger_instance.info(f"Optimisation terminée avec succès en {duration:.2f} secondes")
                    logger_instance.info(f"Meilleurs paramètres: {best_params}")

            def error_callback(error_msg, best_params, duration):
                if ui_available:
                    self.root.after(0, lambda: self._finalize_optuna_optimization(
                        False, best_params, duration, error_msg
                    ))
                else:
                    logger_instance.error(f"Erreur lors de l'optimisation: {error_msg}")

            def progress_callback(progress, message):
                if ui_available:
                    self.root.after(0, lambda: self._update_progress(progress, message))

            # Lancer l'optimisation dans un thread séparé
            self.optuna_thread_manager.run_optimization(
                optimizer_instance,
                n_trials,
                callback=success_callback,
                error_callback=error_callback,
                progress_callback=progress_callback
            )

            logger_instance.info(f"Gestionnaire de thread d'optimisation démarré pour {n_trials} essais")
            return None

        except ImportError as e:
            logger_instance.error(f"Erreur lors de l'importation d'OptunaThreadManager: {e}")
            if ui_available:
                messagebox.showerror("Erreur d'importation", f"Impossible d'importer OptunaThreadManager: {e}")
            return None