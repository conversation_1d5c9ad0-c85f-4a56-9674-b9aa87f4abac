# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1090 à 1119
# Type: Méthode de la classe HybridBaccaratPredictor

    def save_optimization_report(self, report, study_name=None):
        """
        Sauvegarde le rapport d'optimisation dans un fichier.

        Args:
            report (str): Le rapport d'optimisation
            study_name (str, optional): Le nom de l'étude

        Returns:
            str: Le chemin du fichier de rapport
        """
        try:
            # Créer un dossier pour les rapports d'optimisation
            report_dir = os.path.join(os.getcwd(), "optimization_reports")
            os.makedirs(report_dir, exist_ok=True)

            # Générer un nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            study_suffix = f"_{study_name}" if study_name else ""
            report_path = os.path.join(report_dir, f"optimization_report{study_suffix}_{timestamp}.txt")

            # Sauvegarder le rapport
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)

            logger.info(f"Rapport d'optimisation sauvegardé dans {report_path}")
            return report_path
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde du rapport d'optimisation: {e}", exc_info=True)
            return None