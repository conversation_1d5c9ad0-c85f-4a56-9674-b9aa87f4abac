# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11668 à 11697
# Type: Méthode de la classe HybridBaccaratPredictor

    def setup_auto_update(self) -> None:
        """Configure la mise à jour automatique des modèles."""
        if not hasattr(self, 'auto_update_enabled'):
            logger.error("Tentative setup auto-update avant init.")
            return

        enabled = self.auto_update_enabled.get()
        logger.info(f"Configuration auto-update: {'activé' if enabled else 'désactivé'}")

        # Initialiser l'index de mise à jour incrémentale si nécessaire
        if not hasattr(self, 'last_incremental_update_index'):
            self.last_incremental_update_index = 0

        # Initialiser le flag de mise à jour rapide si nécessaire
        if not hasattr(self, 'is_fast_updating'):
            self.is_fast_updating = False

        # Annuler tout job existant (nous n'utilisons plus de timer périodique)
        if hasattr(self, 'auto_update_job_id') and self.auto_update_job_id:
            try:
                self.root.after_cancel(self.auto_update_job_id)
                self.auto_update_job_id = None
                logger.debug("Job auto-update précédent annulé")
            except Exception as e:
                logger.error(f"Erreur annulation job auto-update: {e}")

        # Nous ne configurons plus de timer périodique ici
        # Les mises à jour seront déclenchées uniquement lors de l'enregistrement de nouveaux résultats

        logger.info("Configuration de l'auto-update terminée.")