# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 2158 à 2170
# Type: Méthode de la classe HybridBaccaratPredictor

    def toggle_auto_update(self):
        """Active ou désactive les mises à jour rapides automatiques."""
        is_enabled = self.auto_update_enabled.get()
        logger.info(f"Mises à jour rapides {'activées' if is_enabled else 'désactivées'} par l'utilisateur.")

        # Configurer les mises à jour automatiques en fonction de l'état de la case à cocher
        self.setup_auto_update()

        # Afficher un message à l'utilisateur
        if is_enabled:
            self._update_progress(100, "Mises à jour rapides activées")
        else:
            self._update_progress(0, "Mises à jour rapides désactivées")