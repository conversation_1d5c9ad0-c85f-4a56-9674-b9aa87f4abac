# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12808 à 12834
# Type: Méthode de la classe HybridBaccaratPredictor

    def toggle_stats_visibility(self) -> None:
        """Masque ou affiche le cadre contenant les statistiques."""
        # Vérifier que les éléments UI nécessaires existent
        stats_frame = getattr(self, 'stats_frame', None)
        toggle_button = getattr(self, 'toggle_stats_button', None)

        if not stats_frame or not toggle_button:
            logger.warning("Tentative de basculer la visibilité des stats avant initialisation complète des widgets.")
            return

        if self.stats_visible:
            # Cacher le panneau
            stats_frame.pack_forget() # Retire le widget de l'affichage géré par pack
            toggle_button.config(text="Afficher Statistiques") # Met à jour le texte du bouton
            self.stats_visible = False
            logger.debug("Panneau statistiques masqué.")
        else:
            # Afficher le panneau
            toggle_button.config(text="Masquer Statistiques") # Met à jour le texte du bouton
            # Repack le widget. Par défaut, pack(side=TOP) dans un frame vertical
            # le place à la fin (en dessous des widgets déjà packés).
            stats_frame.pack(fill=tk.X, pady=5)
            # Optionnel: S'assurer qu'il est bien en dessous du bouton toggle graph (si visible) ou pred_frame
            # Cela dépend de la structure exacte et de ce qui est visible.
            # Généralement, le `pack()` simple suffit s'il est appelé après les autres packs.
            self.stats_visible = True
            logger.debug("Panneau statistiques affiché.")