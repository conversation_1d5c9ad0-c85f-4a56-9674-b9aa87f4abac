# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11240 à 11290
# Type: Méthode de la classe HybridBaccaratPredictor

    def toggle_training_controls(self, enabled: bool):
        """Contrôle l'état de tous les widgets interactifs avec gestion améliorée."""
        target_state = tk.NORMAL if enabled else tk.DISABLED
        try:
            # Liste de tous les widgets à contrôler
            interactive_widgets = [
                getattr(self, 'load_historical_button', None),
                getattr(self, 'save_state_button', None),
                getattr(self, 'fast_update_button', None),
                getattr(self, 'full_retrain_button', None),
                getattr(self, 'run_optuna_button', None),
                getattr(self, 'visualize_optuna_button', None),
                getattr(self, 'reset_soft_button', None),
                getattr(self, 'reset_hard_button', None),
                getattr(self, 'toggle_graph_button', None),
                getattr(self, 'toggle_stats_button', None)
            ]

            # Gestion standard des widgets
            for widget in interactive_widgets:
                if widget and hasattr(widget, 'configure'):
                    try:
                        widget.configure(state=target_state)
                    except Exception as e:
                        logger.error(f"Erreur configuration {widget.winfo_class()}: {e}")

            # Gestion spécifique du bouton d'arrêt
            if self.stop_train_button:
                new_state = tk.DISABLED if enabled else tk.NORMAL
                # Utilisation de original_stop_text s'il est défini, sinon une valeur par défaut
                stop_text = getattr(self, 'original_stop_text', "Arrêter Tâche ML")
                self.stop_train_button.configure(
                    state=new_state,
                    text=stop_text  # Réaffectation systématique du texte
                )

            # Réactivation ciblée post-entraînement
            if enabled:
                critical_buttons = [
                    getattr(self, 'save_state_button', None),
                    getattr(self, 'load_historical_button', None),
                    getattr(self, 'predict_button', None)
                ]
                for btn in critical_buttons:
                    if btn and hasattr(btn, 'configure'):
                        btn.configure(state=tk.NORMAL)

        except Exception as e:
            logger.error(f"Erreur critique dans toggle_training_controls: {e}", exc_info=True)
            if hasattr(self, 'root'):
                messagebox.showerror("Erreur UI", f"Problème de contrôle interface:\n{e}")