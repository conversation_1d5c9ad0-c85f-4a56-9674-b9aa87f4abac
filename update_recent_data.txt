# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1442 à 1450
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

                def update_recent_data(self, recommendation, outcome):
                    """Met à jour les données récentes avec la nouvelle recommandation et son résultat."""
                    self.recent_recommendations.append(recommendation)
                    self.recent_outcomes.append(outcome)

                    # Limiter la taille de l'historique récent
                    if len(self.recent_recommendations) > self.max_recent_history:
                        self.recent_recommendations.pop(0)
                        self.recent_outcomes.pop(0)