# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11533 à 11664
# Type: Méthode de la classe HybridBaccaratPredictor

    def update_statistics(self) -> None:
        """Met à jour les labels de statistiques dans l'interface."""
        if not hasattr(self, 'stats_vars'): return

        with self.sequence_lock, self.model_lock: # Accéder sequence, history, performance
             current_sequence = self.sequence[:]
             history = self.prediction_history[:]
             performance = self.method_performance

             # 1. Série Actuelle
             if not current_sequence:
                 streak_text = "Série actuelle: -"
             else:
                 current_outcome = current_sequence[-1]
                 streak_len = 0
                 for outcome in reversed(current_sequence):
                     if outcome == current_outcome:
                         streak_len += 1
                     else:
                         break
                 streak_text = f"Série actuelle: {current_outcome.upper()} ({streak_len})"
             self.stats_vars['streak'].set(streak_text)

             # 2. Précision Session (basée sur les recommandations faites)
             correct_session = 0
             total_session = len(history) # Chaque élément de history est une prédiction faite
             if total_session > 0:
                   # La prédiction à l'index i correspond au résultat à l'index i de la séquence
                  for i in range(len(history)):
                      if i >= len(current_sequence): break # Eviter IndexError si décalage
                      pred_data = history[i]
                      actual = current_sequence[i]
                      rec = pred_data.get('recommendation')
                      if rec != 'wait': # Ne compter que si une recommandation a été faite
                         if (rec == 'player' and actual == 'player') or \
                            (rec == 'banker' and actual == 'banker'):
                              correct_session += 1
                      else:
                           total_session -= 1 # Ne pas pénaliser pour 'wait'

                  session_acc_str = f"{correct_session / total_session * 100:.1f}% ({correct_session}/{total_session})" if total_session > 0 else "N/A"
             else:
                  session_acc_str = "N/A"
             self.stats_vars['accuracy'].set(f"Précision Session: {session_acc_str}")


             # Vérifier si nous sommes dans la plage de manches cibles (31-60)
             target_round_min = getattr(self.config, 'target_round_min', 31)
             target_round_max = getattr(self.config, 'target_round_max', 60)
             current_round = len(current_sequence)
             is_target_round = target_round_min <= current_round <= target_round_max

             # 3. Précisions par Méthode et confiance
             method_acc_parts = []
             method_conf_parts = []
             for method, perf_data in performance.items():
                  acc = perf_data['correct'] / perf_data['total'] * 100 if perf_data['total'] > 0 else 0
                  method_acc_parts.append(f"{method.upper()}({acc:.1f}%)")

                  # Ajouter la confiance du modèle si disponible dans la dernière prédiction
                  if history and 'methods' in history[-1] and method in history[-1]['methods']:
                      method_conf = history[-1]['methods'][method].get('confidence', 0) * 100
                      method_conf_parts.append(f"{method.upper()}({method_conf:.1f}%)")

             self.stats_vars['method_acc'].set(f"Précisions Méthodes: {' | '.join(method_acc_parts)}" if method_acc_parts else "N/A")

             # Ajouter une nouvelle variable pour afficher la confiance des méthodes
             if not hasattr(self.stats_vars, 'method_conf'):
                 self.stats_vars['method_conf'] = tk.StringVar(value="Confiance Méthodes: N/A")

             # Adapter l'affichage de la confiance des méthodes en fonction de la manche
             if not is_target_round:
                 self.stats_vars['method_conf'].set(f"Confiance Méthodes: N/A (manche 1-30)")
             else:
                 self.stats_vars['method_conf'].set(f"Confiance Méthodes: {' | '.join(method_conf_parts)}" if method_conf_parts else "N/A")

             # 4. Statistiques de la partie actuelle (Player vs Banker counts)
             p_count = current_sequence.count('player')
             b_count = current_sequence.count('banker')
             total_rounds = len(current_sequence)
             p_ratio = p_count / total_rounds * 100 if total_rounds > 0 else 0
             b_ratio = b_count / total_rounds * 100 if total_rounds > 0 else 0
             self.stats_vars['game_stats'].set(f"Partie: P {p_count} ({p_ratio:.1f}%) | B {b_count} ({b_ratio:.1f}%)")

             # 5. Métriques d'incertitude détaillées
             if history and 'confidence_metrics' in history[-1]:
                 # Ajouter des variables pour les métriques détaillées si elles n'existent pas
                 if not hasattr(self.stats_vars, 'uncertainty_details'):
                     self.stats_vars['uncertainty_details'] = tk.StringVar(value="Incertitude Détaillée: N/A")

                 if not is_target_round:
                     # Si nous ne sommes pas dans la plage cible (31-60), indiquer que les métriques détaillées ne sont pas applicables
                     self.stats_vars['uncertainty_details'].set(
                         f"Incertitude Détaillée: N/A (manche 1-30)"
                     )
                 else:
                     metrics = history[-1]['confidence_metrics']
                     epistemic = metrics.get('epistemic_uncertainty', 0) * 100
                     aleatoric = metrics.get('aleatoric_uncertainty', 0) * 100
                     sensitivity = metrics.get('context_sensitivity', 0) * 100

                     self.stats_vars['uncertainty_details'].set(
                         f"Incertitude Détaillée: Épist({epistemic:.1f}%) | Aléa({aleatoric:.1f}%) | Sens({sensitivity:.1f}%)"
                     )

             # Ajouter le seuil adaptatif si disponible
             if history and 'adaptive_threshold' in history[-1]:
                 if not hasattr(self.stats_vars, 'adaptive_threshold'):
                     self.stats_vars['adaptive_threshold'] = tk.StringVar(value="Seuil Adaptatif: N/A")

                 if not is_target_round:
                     # Si nous ne sommes pas dans la plage cible (31-60), indiquer que le seuil adaptatif n'est pas applicable
                     self.stats_vars['adaptive_threshold'].set(f"Seuil Adaptatif: N/A (manche 1-30)")
                 else:
                     threshold = history[-1]['adaptive_threshold'] * 100
                     self.stats_vars['adaptive_threshold'].set(f"Seuil Adaptatif: {threshold:.2f}%")

             # 6. Poids bayésiens si disponibles
             if history and 'bayesian_weights' in history[-1]:
                 if not hasattr(self.stats_vars, 'bayesian_weights'):
                     self.stats_vars['bayesian_weights'] = tk.StringVar(value="Poids Bayésiens: N/A")

                 if not is_target_round:
                     # Si nous ne sommes pas dans la plage cible (31-60), indiquer que les poids bayésiens ne sont pas applicables
                     self.stats_vars['bayesian_weights'].set(f"Poids Bayésiens: N/A (manche 1-30)")
                 else:
                     bayesian_weights = history[-1]['bayesian_weights']
                     weights_parts = []
                     for method, weight in bayesian_weights.items():
                         weights_parts.append(f"{method.upper()}({weight*100:.1f}%)")

                     self.stats_vars['bayesian_weights'].set(f"Poids Bayésiens: {' | '.join(weights_parts)}" if weights_parts else "N/A")